# 管理面板导航系统设计文档

## 概述

新的管理面板导航系统采用了现代化的设计理念，支持路由和可伸缩的二级分类，提供了更好的用户体验和可扩展性。

## 设计特色

### 1. 分层导航结构
- **单级菜单**: 直接导航到页面的简单菜单项
- **分类菜单**: 包含子菜单的可展开分类
- **权限控制**: 基于用户权限动态显示菜单项

### 2. 现代化UI设计
- **渐变背景**: 使用紫色渐变背景，视觉效果现代
- **动画效果**: 平滑的展开/收起动画
- **响应式设计**: 支持桌面端、平板端和移动端
- **视觉层次**: 清晰的视觉层次和颜色编码

### 3. 交互体验
- **点击展开**: 点击分类标题展开/收起子菜单
- **状态保持**: 记住用户的展开状态
- **移动端优化**: 移动端自动收起侧边栏

## 技术实现

### 1. 数据结构

```elixir
# 导航菜单项结构
%{
  id: "category_id",           # 唯一标识符
  title: "分类名称",           # 显示标题
  icon: "fas fa-icon",         # FontAwesome图标
  type: :category,             # 类型: :single 或 :category
  visible: true,               # 是否可见（权限控制）
  children: [                  # 子菜单项（仅分类类型）
    %{
      id: "child_id",
      title: "子菜单名称",
      icon: "fas fa-child-icon",
      page: "page_route",      # 路由页面
      visible: true
    }
  ]
}
```

### 2. 权限控制

```elixir
# 权限检查示例
visible: AuthHelper.has_permission?(user, :admin)
visible: AuthHelper.has_permission?(user, :admin) or AuthHelper.has_permission?(user, :agent)
```

### 3. 状态管理

```elixir
# LiveView状态
assign(:expanded_categories, MapSet.new())  # 展开的分类集合

# 切换分类展开状态
def handle_event("toggle_category", %{"category" => category}, socket) do
  expanded_categories = socket.assigns.expanded_categories
  
  new_expanded = if MapSet.member?(expanded_categories, category) do
    MapSet.delete(expanded_categories, category)
  else
    MapSet.put(expanded_categories, category)
  end
  
  socket = assign(socket, :expanded_categories, new_expanded)
  {:noreply, socket}
end
```

## 菜单结构

### 1. 仪表盘
- **类型**: 单级菜单
- **权限**: 所有用户
- **功能**: 个人信息管理

### 2. 用户管理
- **类型**: 分类菜单
- **权限**: 管理员和代理
- **子菜单**:
  - 用户列表（仅管理员）
  - 下线管理（仅代理）

### 3. 财务管理
- **类型**: 分类菜单
- **权限**: 所有用户（子菜单权限不同）
- **子菜单**:
  - 股票持仓（仅管理员）
  - 退费管理（仅管理员）
  - 积分变动（管理员和代理）
  - 抽水记录（管理员和代理）

### 4. 交易记录
- **类型**: 分类菜单
- **权限**: 所有用户
- **子菜单**:
  - 股票买卖
  - 下注记录

## CSS样式设计

### 1. 分类菜单样式

```css
.nav-category-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 25px;
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  border-left: 4px solid transparent;
}

.nav-category-header:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: rgba(255, 255, 255, 0.3);
}

.nav-category-header.expanded {
  background: rgba(255, 255, 255, 0.15);
  border-left-color: rgba(255, 255, 255, 0.6);
}
```

### 2. 子菜单样式

```css
.nav-category-children {
  background: rgba(0, 0, 0, 0.1);
  border-left: 2px solid rgba(255, 255, 255, 0.2);
  margin-left: 25px;
  animation: slideDown 0.3s ease-out;
}

.nav-child-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 25px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  border-left: 3px solid transparent;
}
```

### 3. 动画效果

```css
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
}
```

## 响应式设计

### 1. 移动端优化
- **自动收起**: 导航后自动收起侧边栏
- **触摸友好**: 增大点击区域
- **简化布局**: 减少边距和字体大小

### 2. 平板端适配
- **中等尺寸**: 适中的侧边栏宽度
- **平衡布局**: 平衡的间距和字体

### 3. 大屏幕优化
- **宽敞布局**: 更大的侧边栏和间距
- **清晰显示**: 更大的字体和图标

## 扩展性设计

### 1. 添加新菜单项

```elixir
# 在 get_navigation_menu/1 函数中添加新的菜单项
%{
  id: "new_category",
  title: "新分类",
  icon: "fas fa-new-icon",
  type: :category,
  visible: true,
  children: [
    %{
      id: "new_item",
      title: "新功能",
      icon: "fas fa-feature",
      page: "new_feature",
      visible: AuthHelper.has_permission?(user, :admin)
    }
  ]
}
```

### 2. 权限扩展

```elixir
# 支持复杂的权限逻辑
visible: custom_permission_check(user, :complex_permission)
```

### 3. 动态菜单

```elixir
# 支持基于数据库配置的动态菜单
children: get_dynamic_menu_items(user, category_id)
```

## 最佳实践

### 1. 菜单设计原则
- **逻辑分组**: 按功能逻辑分组菜单项
- **权限最小化**: 只显示用户有权限的菜单
- **一致性**: 保持图标和命名的一致性
- **可发现性**: 重要功能放在显眼位置

### 2. 性能优化
- **懒加载**: 只在需要时加载子菜单
- **状态缓存**: 缓存展开状态
- **权限缓存**: 缓存权限检查结果

### 3. 用户体验
- **快速访问**: 常用功能放在顶层
- **视觉反馈**: 提供清晰的状态反馈
- **键盘支持**: 支持键盘导航
- **无障碍**: 支持屏幕阅读器

## 未来扩展

### 1. 个性化定制
- 用户自定义菜单顺序
- 收藏常用功能
- 主题切换

### 2. 高级功能
- 搜索菜单项
- 快捷键支持
- 面包屑导航

### 3. 分析统计
- 菜单使用统计
- 用户行为分析
- 性能监控
