# Phoenix LiveView 组件设计原则

## 🎯 概述

本文档定义了项目中 Phoenix LiveView 组件的设计原则和最佳实践，确保组件的一致性、可维护性和可测试性。

## 🏗️ 组件设计原则

### 1. 纯函数组件优先

**原则**: 优先使用纯函数组件，避免创建独立的 `.html.heex` 模板文件。

**优势**:
- 📦 **封装性**: 模板和逻辑在同一个文件中
- 🔧 **维护性**: 减少文件数量，便于维护
- 🧪 **测试性**: 更容易进行单元测试
- 🎯 **可读性**: 组件结构更清晰

**实现方式**:
```elixir
defmodule MyApp.Components.ExampleComponent do
  use Phoenix.LiveComponent
  use MyAppWeb, :html

  def render(assigns) do
    ~H"""
    <div class="example-component">
      <!-- 组件内容 -->
    </div>
    """
  end
end
```

### 2. 模块化子组件

**原则**: 将复杂的模板拆分为多个私有子组件函数。

**优势**:
- 🧩 **模块化**: 每个子组件负责特定功能
- 🔄 **复用性**: 子组件可以在同一组件内复用
- 📖 **可读性**: 主模板更简洁易读
- 🛠️ **维护性**: 便于修改和调试

**实现方式**:
```elixir
def render(assigns) do
  ~H"""
  <div class="main-component">
    <.header title={@title} />
    <.content data={@data} />
    <.footer actions={@actions} />
  </div>
  """
end

defp header(assigns) do
  ~H"""
  <header class="component-header">
    <h1><%= @title %></h1>
  </header>
  """
end
```

### 3. 样式组件化

**原则**: 将 CSS 样式作为组件的一部分，使用样式组件函数。

**优势**:
- 🎨 **封装性**: 样式与组件逻辑紧密结合
- 🚫 **避免冲突**: 减少全局样式冲突
- 📱 **响应式**: 便于实现组件级响应式设计
- 🔧 **维护性**: 样式修改更加集中

**实现方式**:
```elixir
def render(assigns) do
  ~H"""
  <div class="my-component">
    <!-- 组件内容 -->
    <.component_styles />
  </div>
  """
end

defp component_styles(assigns) do
  ~H"""
  <style>
  .my-component {
    /* 组件样式 */
  }
  </style>
  """
end
```

### 4. 事件处理集中化

**原则**: 在组件内部集中处理所有相关事件，保持事件处理的内聚性。

**优势**:
- 🎯 **内聚性**: 相关功能集中在一个组件中
- 🔍 **可追踪**: 事件流更容易追踪和调试
- 🧪 **测试性**: 便于进行事件处理测试
- 📋 **文档性**: 组件功能更加明确

**实现方式**:
```elixir
def handle_event("action_1", params, socket) do
  # 处理动作1
  {:noreply, socket}
end

def handle_event("action_2", params, socket) do
  # 处理动作2
  {:noreply, socket}
end
```

## 📋 组件结构模板

### 标准组件结构

```elixir
defmodule MyApp.Components.StandardComponent do
  @moduledoc """
  标准组件模板
  
  功能描述：
  - 功能1
  - 功能2
  - 功能3
  """

  use Phoenix.LiveComponent
  use MyAppWeb, :html
  
  # 依赖模块
  alias MyApp.SomeModule
  require Logger

  # 1. 生命周期函数
  def mount(socket) do
    {:ok, assign(socket, :loading, true)}
  end

  def update(assigns, socket) do
    socket = assign(socket, assigns)
    {:ok, socket}
  end

  # 2. 主渲染函数
  def render(assigns) do
    ~H"""
    <div class="standard-component">
      <.header title={@title} />
      <.content data={@data} loading={@loading} />
      <.footer />
      <.component_styles />
    </div>
    """
  end

  # 3. 事件处理函数
  def handle_event("action", params, socket) do
    {:noreply, socket}
  end

  # 4. 私有子组件
  defp header(assigns) do
    ~H"""
    <header>
      <h1><%= @title %></h1>
    </header>
    """
  end

  defp content(assigns) do
    ~H"""
    <%= if @loading do %>
      <div class="loading">Loading...</div>
    <% else %>
      <div class="content">
        <!-- 内容 -->
      </div>
    <% end %>
    """
  end

  defp footer(assigns) do
    ~H"""
    <footer>
      <!-- 页脚内容 -->
    </footer>
    """
  end

  # 5. 样式组件
  defp component_styles(assigns) do
    ~H"""
    <style>
    .standard-component {
      /* 组件样式 */
    }
    </style>
    """
  end

  # 6. 私有辅助函数
  defp helper_function(data) do
    # 辅助逻辑
  end
end
```

## 🎨 样式设计原则

### 1. 组件级样式

- 使用组件特定的 CSS 类名
- 避免全局样式污染
- 支持主题和响应式设计

### 2. 样式命名规范

```css
.component-name {
  /* 主容器样式 */
}

.component-name-header {
  /* 头部样式 */
}

.component-name-content {
  /* 内容样式 */
}

.component-name-footer {
  /* 页脚样式 */
}
```

### 3. 响应式设计

```css
/* 移动端 */
@media (max-width: 768px) {
  .component-name {
    /* 移动端样式 */
  }
}

/* 平板端 */
@media (max-width: 1024px) and (min-width: 769px) {
  .component-name {
    /* 平板端样式 */
  }
}
```

## 🧪 测试策略

### 1. 组件渲染测试

```elixir
test "renders component correctly" do
  assigns = %{title: "Test", data: []}
  
  html = render_component(MyComponent, assigns)
  
  assert html =~ "Test"
  assert html =~ "component-class"
end
```

### 2. 事件处理测试

```elixir
test "handles events correctly" do
  {:ok, view, _html} = live(conn, "/path")
  
  view
  |> element("[data-testid='action-button']")
  |> render_click()
  
  assert has_element?(view, "[data-testid='result']")
end
```

## 📚 最佳实践

### 1. 组件职责单一

- 每个组件只负责一个特定功能
- 避免组件过于复杂
- 保持组件的可复用性

### 2. 数据流清晰

- 使用 `assigns` 传递数据
- 避免组件间的直接依赖
- 保持数据流的单向性

### 3. 错误处理

- 优雅处理加载状态
- 提供有意义的错误信息
- 实现降级方案

### 4. 性能优化

- 避免不必要的重渲染
- 使用适当的缓存策略
- 优化大数据集的渲染

## 🔧 重构指南

### 从独立模板到组件化

1. **分析现有结构**
   - 识别模板文件和逻辑文件
   - 分析组件的功能边界

2. **创建主渲染函数**
   - 将 `.html.heex` 内容移到 `render/1` 函数
   - 保持原有的模板逻辑

3. **拆分子组件**
   - 识别可复用的模板片段
   - 创建私有子组件函数

4. **添加样式组件**
   - 将相关 CSS 移到样式组件
   - 确保样式的封装性

5. **测试验证**
   - 确保功能正常
   - 验证样式正确
   - 测试事件处理

## 📖 示例：积分变动组件重构

### 重构前
```
lib/
  racing_game/
    live/
      admin_panel/
        points_transactions_component.ex
        points_transactions_component.html.heex
```

### 重构后
```elixir
defmodule RacingGame.Live.AdminPanel.PointsTransactionsComponent do
  use Phoenix.LiveComponent
  use CypridinaWeb, :html

  def render(assigns) do
    ~H"""
    <div class="points-transactions-management">
      <.header title="积分变动记录" />
      <.filter_section filters={@filters} />
      <.data_table data={@data} />
      <.component_styles />
    </div>
    """
  end

  # 子组件和样式组件...
end
```

## 🎯 总结

通过遵循这些设计原则，我们可以创建：

- 🏗️ **结构清晰**的组件
- 🔧 **易于维护**的代码
- 🧪 **便于测试**的逻辑
- 🎨 **样式封装**的设计
- 📱 **响应式友好**的界面

这些原则确保了组件的质量和项目的长期可维护性。
