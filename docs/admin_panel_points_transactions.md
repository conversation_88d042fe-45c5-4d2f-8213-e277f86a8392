# 管理面板 - 积分变动记录功能

## 功能概述

积分变动记录功能是管理面板的新增功能，允许管理员和代理查看和管理系统中的所有积分变动记录。

## 功能特性

### 1. 记录类型筛选
支持按以下类型筛选积分变动记录：
- **全部类型** - 显示所有积分变动记录
- **买入股票** - 用户购买股票的积分扣除记录
- **卖出股票** - 用户卖出股票的积分收入记录
- **投注** - 用户下注的积分扣除记录
- **获奖** - 用户中奖的积分收入记录
- **抽水** - 代理获得的抽水收入记录
- **转账收入** - 用户接收转账的积分收入记录
- **转账支出** - 用户发送转账的积分支出记录
- **退费** - 退费相关的积分变动记录
- **系统调整** - 系统自动调整的积分变动记录
- **管理员增加** - 管理员手动增加用户积分的记录
- **管理员减少** - 管理员手动减少用户积分的记录
- **手动增加** - 其他手动增加积分的记录
- **手动减少** - 其他手动减少积分的记录
- **提现请求** - 用户提现请求的积分冻结记录

### 2. 用户搜索
- 支持按用户名搜索积分变动记录
- 实时搜索，输入用户名即可筛选相关记录

### 3. 数据展示
每条积分变动记录显示以下信息：
- **交易流水号** - 唯一的交易标识符
- **用户信息** - 用户名和用户ID
- **交易类型** - 带颜色标识的交易类型标签
- **变动金额** - 积分变动数量（正数为增加，负数为减少）
- **余额变化** - 显示变动前后的余额对比
- **时间** - 交易发生的本地时间
- **描述** - 交易的详细描述
- **详情** - 额外的交易详情信息（如有）

### 4. 分页功能
- 支持分页浏览，每页显示20条记录
- 显示当前页码、总页数和总记录数
- 提供上一页、下一页和直接跳转页码的功能

### 5. 权限控制
- **管理员** - 可以查看所有用户的积分变动记录
- **代理** - 只能查看自己下线用户的积分变动记录
- **普通用户** - 无法访问此功能

## 使用方法

### 1. 访问功能
1. 登录管理面板
2. 在左侧菜单中点击"积分变动记录"
3. 系统会自动加载积分变动记录列表

### 2. 筛选记录
1. 使用"交易类型"下拉框选择要查看的记录类型
2. 在"用户名搜索"框中输入用户名进行搜索
3. 系统会实时更新显示结果

### 3. 查看详情
1. 点击记录行中的详情图标（如有）
2. 查看交易的额外详情信息
3. 了解交易的完整上下文

### 4. 分页浏览
1. 使用底部的分页控件浏览不同页面的记录
2. 点击页码直接跳转到指定页面
3. 使用左右箭头进行上一页/下一页操作

## 技术实现

### 组件结构
- **主组件**: `RacingGame.Live.AdminPanel.PointsTransactionsComponent`
- **模板文件**: `points_transactions_component.html.heex`
- **数据源**: `RacingGame.PointsTransaction` Ash资源

### 关键功能
1. **权限过滤**: 使用 `PermissionFilter.apply_user_filter/3` 确保用户只能看到有权限的记录
2. **类型筛选**: 支持按 `transaction_type` 字段筛选记录
3. **用户搜索**: 通过关联的用户表进行用户名搜索
4. **时间显示**: 使用 `TimeHelper` 将UTC时间转换为本地时间显示
5. **分页查询**: 使用 Ash.Query.page 进行高效的分页查询

### 样式设计
- 使用 DaisyUI 组件库提供现代化的界面
- 响应式设计，支持移动端和桌面端
- 颜色编码的交易类型标签，便于快速识别
- 清晰的数据表格布局，信息一目了然

## 数据统计

功能页面顶部显示以下统计信息：
- **总记录数** - 当前筛选条件下的总记录数量
- **当前页** - 当前页码和总页数
- **筛选类型** - 当前选择的交易类型筛选条件

## 错误处理

- 数据加载失败时显示友好的错误提示
- 搜索无结果时显示相应的空状态提示
- 网络异常时提供重试机制

## 性能优化

- 使用分页查询避免一次性加载大量数据
- 实现权限级别的数据过滤，减少不必要的数据传输
- 使用索引优化的数据库查询，提高查询效率

## 未来扩展

计划中的功能扩展：
1. 导出功能 - 支持将筛选结果导出为Excel或CSV文件
2. 高级筛选 - 支持按时间范围、金额范围等条件筛选
3. 图表统计 - 提供积分变动的图表分析功能
4. 实时更新 - 支持实时显示新的积分变动记录
