# 🧪 Slot777 测试模式说明

## 📋 功能描述

临时添加了一个测试功能，让玩家在第5次旋转时必定中奖 jackpot（5个7）。这个功能是完全独立的，可以随时启用/禁用/删除。

## 🎮 测试模式特性

- **触发条件**: 玩家第5次旋转时自动触发
- **中奖结果**: 强制生成15个7的图标结果
- **奖金计算**: 
  - 基础奖金：下注金额 × 100倍 × 9条线
  - Jackpot奖金：当前奖池的80%
- **计数器**: 每个玩家独立计数，中奖后自动重置

## 🔧 控制命令

### 在服务器控制台中使用：

```elixir
# 启用测试模式（默认已启用）
Cypridina.Teen.GameSystem.Games.Slot777.Slot777Room.enable_test_mode()

# 禁用测试模式
Cypridina.Teen.GameSystem.Games.Slot777.Slot777Room.disable_test_mode()

# 重置所有玩家计数器
Cypridina.Teen.GameSystem.Games.Slot777.Slot777Room.reset_test_counters()
```

### 或者直接操作 Jackpot 管理器：

```elixir
# 启用测试模式
Cypridina.Teen.GameSystem.Games.Slot777.Slot777Jackpot.enable_test_mode()

# 禁用测试模式
Cypridina.Teen.GameSystem.Games.Slot777.Slot777Jackpot.disable_test_mode()

# 检查某个玩家是否应该中奖
Cypridina.Teen.GameSystem.Games.Slot777.Slot777Jackpot.should_trigger_test_jackpot(player_id)
```

## 📊 日志监控

测试模式会输出详细的日志信息：

```
🧪 [TEST_MODE] 测试模式已启用：玩家第5次旋转必中jackpot
🧪 [TEST_MODE] 玩家 123 旋转计数: 1/5
🧪 [TEST_MODE] 玩家 123 旋转计数: 2/5
...
🧪 [TEST_MODE] 玩家 123 第5次旋转，触发测试jackpot！
🧪 [TEST_JACKPOT] 生成测试jackpot结果 - 基础奖金: 90000, Jackpot奖金: 80000, 总变化: 169000
```

## 🗑️ 完全删除测试功能

如果要完全删除测试功能，需要删除以下代码：

### 1. 在 `slot777_jackpot.ex` 中删除：
- 第16-17行：`test_mode_enabled` 和 `player_spin_counts` 字段
- 第75-103行：测试模式相关的API函数
- 第115-116行：初始化时的测试模式设置
- 第175-197行：`handle_call({:should_trigger_test_jackpot, ...})`
- 第226-254行：测试模式相关的cast处理函数

### 2. 在 `slot777_room.ex` 中删除：
- 第596-605行：测试模式检查和强制结果生成
- 第1009-1047行：`generate_test_jackpot_result` 函数
- 第1051-1077行：测试控制函数

## ⚠️ 注意事项

1. **默认启用**: 测试模式默认是启用的，如果不需要请手动禁用
2. **独立计数**: 每个玩家的旋转次数是独立计算的
3. **自动重置**: 玩家中奖后计数器会自动重置为0
4. **不影响正常游戏**: 禁用测试模式后，游戏逻辑完全恢复正常
5. **临时功能**: 这是临时测试功能，可以随时删除

## 🎯 测试步骤

1. 确保测试模式已启用
2. 玩家进入 Slot777 房间
3. 连续旋转5次
4. 第5次旋转时会自动中奖 jackpot
5. 查看日志确认测试功能正常工作

## 📞 快速操作

```bash
# 进入服务器控制台
iex -S mix

# 启用测试模式
Cypridina.Teen.GameSystem.Games.Slot777.Slot777Room.enable_test_mode()

# 禁用测试模式  
Cypridina.Teen.GameSystem.Games.Slot777.Slot777Room.disable_test_mode()
```
