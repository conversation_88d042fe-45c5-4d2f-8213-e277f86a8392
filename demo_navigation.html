<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理面板导航系统演示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .user-info {
            padding: 30px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-details {
            flex: 1;
        }

        .username {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .user-role {
            font-size: 14px;
            opacity: 0.8;
            color: #e0e7ff;
        }

        .nav-menu {
            flex: 1;
            padding: 20px 0;
            overflow-y: auto;
        }

        .nav-item {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 25px;
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            border-left: 4px solid transparent;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-left-color: rgba(255, 255, 255, 0.5);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            border-left-color: #ffd700;
            font-weight: 600;
        }

        .nav-item i {
            width: 20px;
            text-align: center;
            font-size: 18px;
        }

        .nav-category {
            margin-bottom: 8px;
        }

        .nav-category-header {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 25px;
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            border-left: 4px solid transparent;
        }

        .nav-category-header:hover {
            background: rgba(255, 255, 255, 0.1);
            border-left-color: rgba(255, 255, 255, 0.3);
        }

        .nav-category-header.expanded {
            background: rgba(255, 255, 255, 0.15);
            border-left-color: rgba(255, 255, 255, 0.6);
        }

        .nav-category-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-category-title i {
            width: 20px;
            text-align: center;
            font-size: 18px;
        }

        .nav-category-arrow {
            font-size: 14px;
            transition: transform 0.3s ease;
        }

        .nav-category-header.expanded .nav-category-arrow {
            transform: rotate(180deg);
        }

        .nav-category-children {
            background: rgba(0, 0, 0, 0.1);
            border-left: 2px solid rgba(255, 255, 255, 0.2);
            margin-left: 25px;
            animation: slideDown 0.3s ease-out;
            display: none;
        }

        .nav-category-children.expanded {
            display: block;
        }

        .nav-child-item {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px 25px;
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.9);
            font-size: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            border-left: 3px solid transparent;
        }

        .nav-child-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-left-color: rgba(255, 255, 255, 0.4);
            color: white;
        }

        .nav-child-item.active {
            background: rgba(255, 255, 255, 0.2);
            border-left-color: #ffd700;
            font-weight: 600;
            color: white;
        }

        .nav-child-item i {
            width: 18px;
            text-align: center;
            font-size: 16px;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                max-height: 500px;
                transform: translateY(0);
            }
        }

        .main-content {
            flex: 1;
            padding: 40px;
            background: white;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .demo-title {
            font-size: 32px;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }

        .demo-description {
            font-size: 16px;
            color: #718096;
            line-height: 1.6;
            margin-bottom: 30px;
            text-align: center;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .feature-card h3 {
            color: #2d3748;
            margin-bottom: 10px;
        }

        .feature-card p {
            color: #718096;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="user-info">
            <div class="user-avatar">A</div>
            <div class="user-details">
                <div class="username">管理员</div>
                <div class="user-role">超级管理员</div>
            </div>
        </div>

        <nav class="nav-menu">
            <!-- 仪表盘 -->
            <button class="nav-item active">
                <i class="fas fa-tachometer-alt"></i>
                <span>仪表盘</span>
            </button>

            <!-- 用户管理 -->
            <div class="nav-category">
                <button class="nav-category-header" onclick="toggleCategory('user-management')">
                    <div class="nav-category-title">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </div>
                    <i class="fas fa-chevron-down nav-category-arrow"></i>
                </button>
                <div class="nav-category-children" id="user-management">
                    <button class="nav-child-item">
                        <i class="fas fa-user-friends"></i>
                        <span>用户列表</span>
                    </button>
                    <button class="nav-child-item">
                        <i class="fas fa-sitemap"></i>
                        <span>下线管理</span>
                    </button>
                </div>
            </div>

            <!-- 财务管理 -->
            <div class="nav-category">
                <button class="nav-category-header" onclick="toggleCategory('financial-management')">
                    <div class="nav-category-title">
                        <i class="fas fa-chart-line"></i>
                        <span>财务管理</span>
                    </div>
                    <i class="fas fa-chevron-down nav-category-arrow"></i>
                </button>
                <div class="nav-category-children" id="financial-management">
                    <button class="nav-child-item">
                        <i class="fas fa-chart-area"></i>
                        <span>股票持仓</span>
                    </button>
                    <button class="nav-child-item">
                        <i class="fas fa-dollar-sign"></i>
                        <span>退费管理</span>
                    </button>
                    <button class="nav-child-item">
                        <i class="fas fa-coins"></i>
                        <span>积分变动</span>
                    </button>
                    <button class="nav-child-item">
                        <i class="fas fa-percentage"></i>
                        <span>抽水记录</span>
                    </button>
                </div>
            </div>

            <!-- 交易记录 -->
            <div class="nav-category">
                <button class="nav-category-header" onclick="toggleCategory('transaction-records')">
                    <div class="nav-category-title">
                        <i class="fas fa-history"></i>
                        <span>交易记录</span>
                    </div>
                    <i class="fas fa-chevron-down nav-category-arrow"></i>
                </button>
                <div class="nav-category-children" id="transaction-records">
                    <button class="nav-child-item">
                        <i class="fas fa-exchange-alt"></i>
                        <span>股票买卖</span>
                    </button>
                    <button class="nav-child-item">
                        <i class="fas fa-ticket-alt"></i>
                        <span>下注记录</span>
                    </button>
                </div>
            </div>
        </nav>
    </div>

    <div class="main-content">
        <h1 class="demo-title">🎯 管理面板导航系统演示 - PWA路由版</h1>
        <p class="demo-description">
            这是一个现代化的管理面板导航系统，支持真实URL路由和可伸缩的二级分类。
            点击左侧的分类标题来展开或收起子菜单，点击菜单项会改变URL地址。
            这是PWA（Progressive Web App）的最佳实践！
        </p>

        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;">
            <h3 style="color: #1976d2; margin: 0 0 10px 0;">🌐 URL路由示例</h3>
            <p style="margin: 0; color: #424242;">
                • <code>/admin_panel/profile</code> - 个人信息<br>
                • <code>/admin_panel/users</code> - 用户管理<br>
                • <code>/admin_panel/stock_transactions</code> - 股票交易记录<br>
                • <code>/admin_panel/commission_records</code> - 抽水记录
            </p>
        </div>

        <div class="feature-list">
            <div class="feature-card">
                <h3>🎨 现代化设计</h3>
                <p>采用渐变背景、圆角设计和阴影效果，提供现代化的视觉体验。</p>
            </div>
            <div class="feature-card">
                <h3>📱 响应式布局</h3>
                <p>支持桌面端、平板端和移动端，在不同设备上都有良好的显示效果。</p>
            </div>
            <div class="feature-card">
                <h3>🔐 权限控制</h3>
                <p>基于用户权限动态显示菜单项，确保用户只能看到有权限的功能。</p>
            </div>
            <div class="feature-card">
                <h3>⚡ 流畅动画</h3>
                <p>平滑的展开/收起动画，提供优秀的交互体验。</p>
            </div>
            <div class="feature-card">
                <h3>🌐 URL路由</h3>
                <p>真实的URL路径，支持页面刷新、链接分享和浏览器历史导航。</p>
            </div>
            <div class="feature-card">
                <h3>📱 PWA兼容</h3>
                <p>符合PWA最佳实践，支持离线访问和应用安装。</p>
            </div>
            <div class="feature-card">
                <h3>🎯 分层结构</h3>
                <p>支持单级菜单和分类菜单，可以灵活组织不同类型的功能。</p>
            </div>
            <div class="feature-card">
                <h3>🔧 易于扩展</h3>
                <p>模块化设计，可以轻松添加新的菜单项和分类。</p>
            </div>
        </div>
    </div>

    <script>
        function toggleCategory(categoryId) {
            const category = document.getElementById(categoryId);
            const header = category.previousElementSibling;
            
            if (category.classList.contains('expanded')) {
                category.classList.remove('expanded');
                header.classList.remove('expanded');
            } else {
                category.classList.add('expanded');
                header.classList.add('expanded');
            }
        }

        // 添加点击效果
        document.querySelectorAll('.nav-item, .nav-child-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除其他项的active状态
                document.querySelectorAll('.nav-item, .nav-child-item').forEach(i => {
                    i.classList.remove('active');
                });
                // 添加当前项的active状态
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
