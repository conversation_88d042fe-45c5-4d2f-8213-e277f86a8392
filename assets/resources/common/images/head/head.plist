﻿<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>frames</key>
		<dict>
			<key>head_01.png</key>
			<dict>
				<key>frame</key>
				<string>{{1,1},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_02.png</key>
			<dict>
				<key>frame</key>
				<string>{{1,123},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_03.png</key>
			<dict>
				<key>frame</key>
				<string>{{1,245},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_04.png</key>
			<dict>
				<key>frame</key>
				<string>{{1,367},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_05.png</key>
			<dict>
				<key>frame</key>
				<string>{{1,489},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_06.png</key>
			<dict>
				<key>frame</key>
				<string>{{123,1},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_07.png</key>
			<dict>
				<key>frame</key>
				<string>{{245,1},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_08.png</key>
			<dict>
				<key>frame</key>
				<string>{{367,1},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_09.png</key>
			<dict>
				<key>frame</key>
				<string>{{489,1},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_10.png</key>
			<dict>
				<key>frame</key>
				<string>{{611,1},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_11.png</key>
			<dict>
				<key>frame</key>
				<string>{{123,123},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_12.png</key>
			<dict>
				<key>frame</key>
				<string>{{123,245},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_13.png</key>
			<dict>
				<key>frame</key>
				<string>{{123,367},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_14.png</key>
			<dict>
				<key>frame</key>
				<string>{{123,489},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_15.png</key>
			<dict>
				<key>frame</key>
				<string>{{245,123},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_16.png</key>
			<dict>
				<key>frame</key>
				<string>{{367,123},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_17.png</key>
			<dict>
				<key>frame</key>
				<string>{{489,123},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_18.png</key>
			<dict>
				<key>frame</key>
				<string>{{611,123},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_19.png</key>
			<dict>
				<key>frame</key>
				<string>{{245,245},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_20.png</key>
			<dict>
				<key>frame</key>
				<string>{{245,367},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_21.png</key>
			<dict>
				<key>frame</key>
				<string>{{245,489},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_22.png</key>
			<dict>
				<key>frame</key>
				<string>{{367,245},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_23.png</key>
			<dict>
				<key>frame</key>
				<string>{{489,245},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_24.png</key>
			<dict>
				<key>frame</key>
				<string>{{611,245},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_25.png</key>
			<dict>
				<key>frame</key>
				<string>{{367,367},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_26.png</key>
			<dict>
				<key>frame</key>
				<string>{{367,489},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_27.png</key>
			<dict>
				<key>frame</key>
				<string>{{489,367},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_28.png</key>
			<dict>
				<key>frame</key>
				<string>{{611,367},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_29.png</key>
			<dict>
				<key>frame</key>
				<string>{{489,489},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
			<key>head_30.png</key>
			<dict>
				<key>frame</key>
				<string>{{611,489},{120,120}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false />
				<key>sourceSize</key>
				<string>{120,120}</string>
			</dict>
		</dict>
		<key>metadata</key>
		<dict>
			<key>format</key>
			<integer>2</integer>
			<key>textureFileName</key>
			<string>head.png</string>
			<key>realTextureFileName</key>
			<string>head.png</string>
			<key>size</key>
			<string>{732,610}</string>
		</dict>
		<key>texture</key>
		<dict>
			<key>width</key>
			<integer>732</integer>
			<key>height</key>
			<integer>610</integer>
		</dict>
	</dict>
</plist>