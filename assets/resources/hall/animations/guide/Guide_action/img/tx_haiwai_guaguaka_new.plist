<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>SC_hezi.png</key>
            <dict>
                <key>frame</key>
                <string>{{1002,2},{313,267}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{313,267}}</string>
                <key>sourceSize</key>
                <string>{313,267}</string>
            </dict>
            <key>SC_hezi1.png</key>
            <dict>
                <key>frame</key>
                <string>{{1157,317},{300,141}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{300,141}}</string>
                <key>sourceSize</key>
                <string>{300,141}</string>
            </dict>
            <key>SC_icon_shouzi.png</key>
            <dict>
                <key>frame</key>
                <string>{{1002,317},{163,153}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{163,153}}</string>
                <key>sourceSize</key>
                <string>{163,153}</string>
            </dict>
            <key>SC_icon_szg.png</key>
            <dict>
                <key>frame</key>
                <string>{{1459,268},{136,136}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{136,136}}</string>
                <key>sourceSize</key>
                <string>{136,136}</string>
            </dict>
            <key>SC_ka.png</key>
            <dict>
                <key>frame</key>
                <string>{{1271,2},{597,264}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{597,264}}</string>
                <key>sourceSize</key>
                <string>{597,264}</string>
            </dict>
            <key>SC_ka1.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{482,246}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{482,246}}</string>
                <key>sourceSize</key>
                <string>{482,246}</string>
            </dict>
            <key>tx_haiwai_guaguaka_guang_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{250,2},{750,400}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{750,400}}</string>
                <key>sourceSize</key>
                <string>{750,400}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>tx_haiwai_guaguaka_new.png</string>
            <key>size</key>
            <string>{1870,486}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:0c0c0ff2f08e2e0160dd5326bffe406e:bf81f96b19a82d9fc506d4bd44f60b87:8ba7a6e63b0a4b660f95a6651b500c80$</string>
            <key>textureFileName</key>
            <string>tx_haiwai_guaguaka_new.png</string>
        </dict>
    </dict>
</plist>
