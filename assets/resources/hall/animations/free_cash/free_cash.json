{"skeleton": {"hash": "f0L/kzqmjpnH6WgCBRie3Sj4LV4", "spine": "3.8.99", "x": -43.42, "y": -33, "width": 87.16, "height": 104.57, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "images/freeBonus_jb2", "parent": "root", "x": -0.59, "y": 63.37}, {"name": "images/DT_icon_free-cash", "parent": "root", "x": -4.42, "y": -9.14}, {"name": "images/DT_icon_free-cash2", "parent": "images/DT_icon_free-cash", "x": -2.13, "y": 24.07}, {"name": "images/freeBonus_jb3", "parent": "root", "x": -0.59, "y": 63.37}, {"name": "images/freeBonus_jb4", "parent": "root", "x": -0.59, "y": 63.37}, {"name": "images/freeBonus_jb5", "parent": "root", "x": -0.59, "y": 63.37, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb6", "parent": "root", "x": -0.59, "y": 63.37, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb7", "parent": "root", "x": -0.59, "y": 63.37, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb8", "parent": "root", "x": -0.59, "y": 63.37, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb9", "parent": "root", "x": -0.59, "y": 63.37, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb10", "parent": "root", "x": -0.59, "y": 63.37, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb11", "parent": "root", "x": -0.59, "y": 61.12, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb12", "parent": "root", "x": -0.59, "y": 61.12, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb13", "parent": "root", "x": -0.59, "y": 61.12, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb14", "parent": "root", "x": -0.59, "y": 63.37, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb15", "parent": "root", "x": -0.59, "y": 61.12, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb16", "parent": "root", "x": -0.59, "y": 61.12, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb17", "parent": "root", "x": -0.59, "y": 61.12, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb18", "parent": "root", "x": -0.59, "y": 63.37, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb19", "parent": "root", "x": -0.59, "y": 61.12, "scaleX": 1.2, "scaleY": 1.2}, {"name": "images/freeBonus_jb20", "parent": "root", "x": -0.59, "y": 61.12, "scaleX": 1.2, "scaleY": 1.2}, {"name": "bone2", "parent": "root", "x": 83.32, "y": 0.66}], "slots": [{"name": "images/DT_icon_free-cash", "bone": "images/DT_icon_free-cash", "attachment": "images/DT_icon_free-cash"}, {"name": "images/tx_DT_icon_free-cash_00000", "bone": "bone", "attachment": "images/tx_DT_icon_free-cash_00001"}, {"name": "images/freeBonus_jb2", "bone": "images/freeBonus_jb2", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb5", "bone": "images/freeBonus_jb5", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb6", "bone": "images/freeBonus_jb6", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb7", "bone": "images/freeBonus_jb7", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb9", "bone": "images/freeBonus_jb9", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb10", "bone": "images/freeBonus_jb10", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb14", "bone": "images/freeBonus_jb14", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb11", "bone": "images/freeBonus_jb11", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb19", "bone": "images/freeBonus_jb19", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb20", "bone": "images/freeBonus_jb20", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb12", "bone": "images/freeBonus_jb12", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb15", "bone": "images/freeBonus_jb15", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb16", "bone": "images/freeBonus_jb16", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb17", "bone": "images/freeBonus_jb17", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb13", "bone": "images/freeBonus_jb13", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb3", "bone": "images/freeBonus_jb3", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb8", "bone": "images/freeBonus_jb8", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb18", "bone": "images/freeBonus_jb18", "attachment": "images/freeBonus_jb2"}, {"name": "images/freeBonus_jb4", "bone": "images/freeBonus_jb4", "attachment": "images/freeBonus_jb2"}, {"name": "R1", "bone": "root", "attachment": "R1"}, {"name": "R2", "bone": "root", "attachment": "R1"}], "path": [{"name": "R1", "bones": ["images/freeBonus_jb2"], "target": "R1"}, {"name": "R2", "order": 1, "bones": ["images/freeBonus_jb5"], "target": "R2"}], "skins": [{"name": "default", "attachments": {"R1": {"R1": {"type": "path", "lengths": [27.43, 55.03, 102.72], "vertexCount": 9, "vertices": [0.02, 63.1, 0.02, 63.1, 0.02, 63.1, 4.77, 76.55, 23.71, 71.69, 34.53, 68.91, 39.5, 64.55, 46.45, 56.9, 52.92, 49.78]}}, "R2": {"R1": {"type": "path", "lengths": [29.8, 102.56, 169.3], "vertexCount": 9, "vertices": [0.02, 63.1, 0.02, 63.1, 0.02, 63.1, -0.24, 88.86, 16.95, 79.54, 27.5, 73.81, 39.45, 42.87, 43.82, 13.27, 45.22, 3.75]}}, "images/DT_icon_free-cash": {"images/DT_icon_free-cash": {"type": "mesh", "uvs": [0.32284, 0.9866, 0.24414, 0.97233, 0.23187, 0.98178, 0.19914, 0.97844, 0.16141, 0.95733, 0.13005, 0.929, 0.0705, 0.87011, 0.05005, 0.79511, 0.02959, 0.63178, 0.01187, 0.40545, 0.00959, 0.33546, 0.02187, 0.30046, 0.0555, 0.26157, 0.1055, 0.22157, 0.14558, 0.20605, 0.18922, 0.20938, 0.19193, 0.19354, 0.20012, 0.17382, 0.20352, 0.15687, 0.21513, 0.1494, 0.24445, 0.13523, 0.24717, 0.12079, 0.25581, 0.11912, 0.25831, 0.10773, 0.28422, 0.09801, 0.31513, 0.09773, 0.39036, 0.09829, 0.4133, 0.10912, 0.41944, 0.12884, 0.43353, 0.10968, 0.44921, 0.09551, 0.50762, 0.04912, 0.56194, 0.01107, 0.59012, 0.0094, 0.6133, 0.02162, 0.64944, 0.05107, 0.70436, 0.11504, 0.75141, 0.12282, 0.77209, 0.14671, 0.7704, 0.19745, 0.80063, 0.24189, 0.82561, 0.29141, 0.8963, 0.2478, 0.91507, 0.25825, 0.97198, 0.41173, 0.97039, 0.44895, 0.91289, 0.46923, 0.92584, 0.51256, 1, 0.57506, 1, 0.65411, 1, 0.66478, 0.99493, 0.65531, 0.99266, 0.68034, 0.98682, 0.68445, 0.97637, 0.70945, 0.98227, 0.74278, 0.98046, 0.81778, 0.97137, 0.84834, 0.95773, 0.87278, 0.81601, 0.97799, 0.77896, 0.97354, 0.72669, 0.95049, 0.69919, 0.92716, 0.68783, 0.92382, 0.68419, 0.93549, 0.63419, 0.97327, 0.61646, 0.97827, 0.60556, 0.97215, 0.49556, 0.98882, 0.38329, 1, 0.27467, 0.1019], "triangles": [12, 9, 10, 43, 44, 41, 43, 41, 42, 44, 46, 41, 11, 12, 10, 13, 14, 15, 9, 12, 13, 30, 40, 41, 24, 70, 23, 35, 36, 31, 40, 30, 39, 22, 70, 24, 23, 70, 22, 22, 24, 25, 22, 25, 20, 20, 21, 22, 25, 28, 20, 32, 33, 34, 31, 32, 34, 31, 34, 35, 36, 30, 31, 19, 20, 17, 28, 15, 20, 63, 28, 29, 39, 37, 38, 15, 8, 9, 39, 36, 37, 36, 39, 30, 19, 17, 18, 17, 15, 16, 17, 20, 15, 26, 28, 25, 27, 28, 26, 45, 46, 44, 51, 47, 48, 49, 51, 48, 51, 49, 50, 51, 53, 47, 52, 53, 51, 54, 47, 53, 54, 55, 58, 47, 54, 62, 63, 46, 47, 55, 56, 58, 58, 62, 54, 1, 8, 15, 28, 63, 68, 28, 0, 1, 28, 68, 0, 63, 41, 46, 58, 56, 57, 4, 5, 1, 61, 62, 58, 63, 65, 67, 64, 65, 63, 60, 61, 58, 59, 60, 58, 66, 67, 65, 3, 4, 1, 2, 3, 1, 63, 67, 68, 69, 0, 68, 1, 5, 7, 63, 47, 62, 5, 6, 7, 1, 7, 8, 41, 63, 29, 28, 1, 15, 41, 29, 30, 13, 15, 9], "vertices": [1, 3, -11.44, -22.84, 1, 1, 3, -18.36, -21.81, 1, 1, 3, -19.44, -22.49, 1, 1, 3, -22.32, -22.25, 1, 1, 3, -25.64, -20.73, 1, 1, 3, -28.4, -18.69, 1, 1, 3, -33.64, -14.45, 1, 2, 3, -35.44, -9.05, 0.88, 4, -33.31, -33.12, 0.12, 2, 3, -37.24, 2.7, 0.88, 4, -35.11, -21.36, 0.12, 2, 3, -38.8, 19, 0.88, 4, -36.67, -5.07, 0.12, 2, 3, -39, 24.04, 0.88, 4, -36.87, -0.03, 0.12, 2, 3, -37.92, 26.56, 0.88, 4, -35.79, 2.49, 0.12, 2, 3, -34.96, 29.36, 0.88, 4, -32.83, 5.29, 0.12, 2, 3, -30.56, 32.24, 0.79, 4, -28.43, 8.17, 0.21, 2, 3, -27.04, 33.36, 0.79, 4, -24.91, 9.29, 0.21, 2, 3, -23.2, 33.12, 0.79, 4, -21.07, 9.05, 0.21, 2, 3, -22.96, 34.26, 0.79, 4, -20.83, 10.19, 0.21, 2, 3, -22.24, 35.68, 0.79, 4, -20.11, 11.61, 0.21, 2, 3, -21.94, 36.9, 0.79, 4, -19.81, 12.83, 0.21, 2, 3, -20.92, 37.44, 0.79, 4, -18.79, 13.37, 0.21, 2, 3, -18.34, 38.46, 0.79, 4, -16.21, 14.39, 0.21, 2, 3, -18.1, 39.5, 0.79, 4, -15.97, 15.43, 0.21, 2, 3, -17.34, 39.62, 0.79, 4, -15.21, 15.55, 0.21, 2, 3, -17.12, 40.44, 0.79, 4, -14.99, 16.37, 0.21, 2, 3, -14.84, 41.14, 0.79, 4, -12.71, 17.07, 0.21, 2, 3, -12.12, 41.16, 0.79, 4, -9.99, 17.09, 0.21, 2, 3, -5.5, 41.12, 0.79, 4, -3.37, 17.05, 0.21, 2, 3, -3.48, 40.34, 0.79, 4, -1.35, 16.27, 0.21, 2, 3, -2.94, 38.92, 0.79, 4, -0.81, 14.85, 0.21, 2, 3, -1.7, 40.3, 0.79, 4, 0.43, 16.23, 0.21, 2, 3, -0.32, 41.32, 0.79, 4, 1.81, 17.25, 0.21, 2, 3, 4.82, 44.66, 0.79, 4, 6.95, 20.59, 0.21, 2, 3, 9.6, 47.4, 0.79, 4, 11.73, 23.33, 0.21, 2, 3, 12.08, 47.52, 0.79, 4, 14.21, 23.45, 0.21, 2, 3, 14.12, 46.64, 0.79, 4, 16.25, 22.57, 0.21, 2, 3, 17.3, 44.52, 0.79, 4, 19.43, 20.45, 0.21, 2, 3, 22.14, 39.91, 0.79, 4, 24.27, 15.84, 0.21, 2, 3, 26.28, 39.35, 0.79, 4, 28.41, 15.28, 0.21, 2, 3, 28.1, 37.63, 0.79, 4, 30.23, 13.56, 0.21, 2, 3, 27.95, 33.98, 0.79, 4, 30.08, 9.91, 0.21, 2, 3, 30.61, 30.78, 0.79, 4, 32.74, 6.71, 0.21, 2, 3, 32.81, 27.21, 0.88, 4, 34.94, 3.14, 0.12, 2, 3, 39.03, 30.35, 0.88, 4, 41.16, 6.28, 0.12, 2, 3, 40.68, 29.6, 0.88, 4, 42.81, 5.53, 0.12, 2, 3, 45.69, 18.55, 0.88, 4, 47.82, -5.52, 0.12, 2, 3, 45.55, 15.87, 0.88, 4, 47.68, -8.2, 0.12, 2, 3, 40.49, 14.41, 0.88, 4, 42.62, -9.66, 0.12, 2, 3, 41.63, 11.29, 0.88, 4, 43.76, -12.78, 0.12, 2, 3, 48.15, 6.79, 0.88, 4, 50.28, -17.28, 0.12, 2, 3, 48.15, 1.1, 0.88, 4, 50.28, -22.97, 0.12, 2, 3, 48.15, 0.33, 0.88, 4, 50.28, -23.74, 0.12, 2, 3, 47.71, 1.01, 0.88, 4, 49.84, -23.06, 0.12, 2, 3, 47.51, -0.79, 0.88, 4, 49.64, -24.86, 0.12, 2, 3, 46.99, -1.09, 0.88, 4, 49.12, -25.16, 0.12, 2, 3, 46.07, -2.89, 0.88, 4, 48.2, -26.96, 0.12, 2, 3, 46.59, -5.29, 0.88, 4, 48.72, -29.36, 0.12, 1, 3, 46.43, -10.69, 1, 1, 3, 45.63, -12.89, 1, 1, 3, 44.43, -14.65, 1, 1, 3, 31.96, -22.22, 1, 1, 3, 28.7, -21.9, 1, 1, 3, 24.1, -20.24, 1, 1, 3, 21.68, -18.56, 1, 1, 3, 20.68, -18.32, 1, 1, 3, 20.36, -19.16, 1, 1, 3, 15.96, -21.88, 1, 1, 3, 14.4, -22.24, 1, 1, 3, 13.44, -21.8, 1, 1, 3, 3.76, -23, 1, 1, 3, -6.12, -23.81, 1, 2, 3, -15.68, 40.86, 0.79, 4, -13.55, 16.79, 0.21], "hull": 70, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 138, 138, 136, 136, 134, 134, 132, 132, 130, 130, 128, 128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 140, 140, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 46, 48, 96, 98, 98, 100, 102, 98], "width": 88, "height": 72}}, "images/freeBonus_jb2": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb3": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb4": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb5": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb6": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb7": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb8": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb9": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb10": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb11": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb12": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb13": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb14": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb15": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb16": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb17": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb18": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb19": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/freeBonus_jb20": {"images/freeBonus_jb2": {"type": "mesh", "uvs": [0.75318, 0.01742, 0.83969, 0.04473, 0.87672, 0.07922, 0.91077, 0.13371, 0.94645, 0.20038, 0.95726, 0.25777, 0.94753, 0.32038, 0.92428, 0.38415, 0.89888, 0.44675, 0.85942, 0.52385, 0.82699, 0.5769, 0.78104, 0.63458, 0.67074, 0.74266, 0.6129, 0.79194, 0.54858, 0.82962, 0.48425, 0.85513, 0.40317, 0.87947, 0.34047, 0.88875, 0.25939, 0.88933, 0.20561, 0.86527, 0.09486, 0.76912, 0.05378, 0.70304, 0.04027, 0.64449, 0.04567, 0.57782, 0.05919, 0.5129, 0.09378, 0.44101, 0.14135, 0.35116, 0.19589, 0.27249, 0.27481, 0.19829, 0.36238, 0.12814, 0.4667, 0.06322, 0.57697, 0.01974, 0.66616, 0.0122, 0.4929, 0.85049, 0.72587, 0.68585, 0.7978, 0.03169], "triangles": [0, 1, 35, 6, 4, 5, 6, 7, 3, 6, 3, 4, 1, 3, 7, 3, 1, 2, 1, 8, 35, 7, 8, 1, 35, 9, 0, 8, 9, 35, 0, 10, 32, 9, 10, 0, 32, 11, 31, 10, 11, 32, 20, 21, 23, 22, 23, 21, 11, 34, 31, 34, 30, 31, 34, 29, 30, 34, 28, 29, 12, 28, 34, 27, 28, 12, 13, 26, 27, 13, 25, 26, 12, 34, 11, 25, 19, 20, 24, 25, 20, 20, 23, 24, 13, 27, 12, 13, 14, 25, 14, 33, 25, 25, 33, 16, 15, 33, 14, 19, 25, 17, 33, 15, 16, 16, 17, 25, 18, 19, 17], "vertices": [5.24, 6.74, 6.84, 6.27, 7.53, 5.68, 8.16, 4.74, 8.82, 3.59, 9.02, 2.6, 8.84, 1.52, 8.41, 0.42, 7.94, -0.66, 7.21, -1.99, 6.61, -2.91, 5.76, -3.9, 3.72, -5.77, 2.65, -6.62, 1.46, -7.27, 0.27, -7.71, -1.23, -8.13, -2.39, -8.29, -3.89, -8.3, -4.89, -7.88, -6.94, -6.22, -7.7, -5.08, -7.95, -4.07, -7.85, -2.92, -7.6, -1.8, -6.96, -0.56, -6.08, 0.99, -5.07, 2.34, -3.61, 3.62, -1.99, 4.83, -0.06, 5.95, 1.98, 6.7, 3.63, 6.83, 0.43, -7.63, 4.74, -4.79, 6.07, 6.5], "hull": 33, "edges": [0, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 66, 28, 28, 26, 26, 24, 24, 68, 68, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 70, 70, 0, 28, 30, 22, 24, 0, 2], "width": 74, "height": 69}}, "images/tx_DT_icon_free-cash_00000": {"images/tx_DT_icon_free-cash_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [29, -32, -29, -32, -29, 32, 29, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 64}, "images/tx_DT_icon_free-cash_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [36, -33, -36, -33, -36, 33, 36, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 72, "height": 66}, "images/tx_DT_icon_free-cash_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -34, -40, -34, -40, 34, 40, 34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 68}, "images/tx_DT_icon_free-cash_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -36, -43, -36, -43, 36, 43, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 72}, "images/tx_DT_icon_free-cash_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -36, -44, -36, -44, 36, 44, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 72}, "images/tx_DT_icon_free-cash_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -35, -43, -35, -43, 35, 43, 35], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 70}}}}], "animations": {"animation": {"slots": {"images/freeBonus_jb2": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00"}]}, "images/freeBonus_jb3": {"color": [{"color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffff"}]}, "images/freeBonus_jb4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0333, "color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00"}]}, "images/freeBonus_jb5": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00"}]}, "images/freeBonus_jb6": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "images/freeBonus_jb7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.4, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "images/freeBonus_jb8": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 2.8667, "color": "ffffffff"}]}, "images/freeBonus_jb9": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "images/freeBonus_jb10": {"color": [{"time": 0.2, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}]}, "images/freeBonus_jb11": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1333, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5333, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffff00"}]}, "images/freeBonus_jb12": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "images/freeBonus_jb13": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00"}]}, "images/freeBonus_jb14": {"color": [{"time": 0.2, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2667, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "images/freeBonus_jb15": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "images/freeBonus_jb16": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "images/freeBonus_jb17": {"color": [{"time": 0.1333, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7667, "color": "ffffff00"}, {"time": 2.8, "color": "ffffffff"}]}, "images/freeBonus_jb18": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3667, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 2.8667, "color": "ffffffff"}]}, "images/freeBonus_jb19": {"color": [{"color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4333, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9667, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}]}, "images/freeBonus_jb20": {"color": [{"color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4333, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9667, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}]}, "images/tx_DT_icon_free-cash_00000": {"attachment": [{"name": "images/tx_DT_icon_free-cash_00000"}, {"time": 0.0667, "name": "images/tx_DT_icon_free-cash_00001"}, {"time": 0.1333, "name": "images/tx_DT_icon_free-cash_00002"}, {"time": 0.2, "name": "images/tx_DT_icon_free-cash_00003"}, {"time": 0.2667, "name": "images/tx_DT_icon_free-cash_00004"}, {"time": 0.3333, "name": "images/tx_DT_icon_free-cash_00005"}, {"time": 0.4, "name": "images/tx_DT_icon_free-cash_00006"}, {"time": 0.4667, "name": "images/tx_DT_icon_free-cash_00007"}, {"time": 0.5333, "name": "images/tx_DT_icon_free-cash_00008"}, {"time": 0.6, "name": "images/tx_DT_icon_free-cash_00009"}, {"time": 0.6667, "name": "images/tx_DT_icon_free-cash_00010"}, {"time": 0.7333, "name": "images/tx_DT_icon_free-cash_00011"}, {"time": 0.8, "name": "images/tx_DT_icon_free-cash_00012"}, {"time": 0.8667, "name": "images/tx_DT_icon_free-cash_00013"}, {"time": 0.9333, "name": "images/tx_DT_icon_free-cash_00014"}, {"time": 1, "name": "images/tx_DT_icon_free-cash_00015"}, {"time": 1.0667, "name": "images/tx_DT_icon_free-cash_00016"}, {"time": 1.1333, "name": "images/tx_DT_icon_free-cash_00017"}, {"time": 1.2, "name": "images/tx_DT_icon_free-cash_00018"}, {"time": 1.2667, "name": "images/tx_DT_icon_free-cash_00019"}, {"time": 1.3333, "name": null}]}}, "bones": {"bone": {"translate": [{"x": 0.91, "y": 6.66}]}, "images/freeBonus_jb2": {"translate": [{"x": 3.33, "y": -0.59}]}, "images/DT_icon_free-cash2": {"translate": [{"y": 9.57}, {"time": 0.5, "y": -0.98}, {"time": 1.5, "y": 9.57}, {"time": 2, "y": -0.98}, {"time": 3, "y": 9.57}]}, "images/DT_icon_free-cash": {"translate": [{"y": 4}]}, "images/freeBonus_jb3": {"rotate": [{"angle": -35.43}, {"time": 0.0667, "angle": -48.42}, {"time": 0.1333, "angle": -53.69}, {"time": 0.2333, "angle": -61.41}, {"time": 0.3, "angle": -62.56}, {"time": 1.3333}, {"time": 1.3667, "angle": 46.88}, {"time": 1.4, "angle": 24.05}, {"time": 1.4333, "angle": 13.35}, {"time": 1.4667, "angle": -1.88}, {"time": 1.5, "angle": -8.1}, {"time": 1.5333, "angle": -14.84}, {"time": 1.5667, "angle": -26.16}, {"time": 1.6333, "angle": -35.43}, {"time": 1.7333, "angle": -48.42}, {"time": 1.8, "angle": -53.69}, {"time": 1.9, "angle": -61.41}, {"time": 1.9667, "angle": -62.56}, {"time": 2.7}, {"time": 2.7333, "angle": 46.88}, {"time": 2.7667, "angle": 24.05}, {"time": 2.8, "angle": 13.35}, {"time": 2.8333, "angle": -1.88}, {"time": 2.8667, "angle": -8.1}, {"time": 2.9, "angle": -14.84}, {"time": 2.9333, "angle": -26.16}, {"time": 3, "angle": -35.43}], "translate": [{"x": 30.39, "y": 10.68}, {"time": 0.0667, "x": 35.14, "y": 6.2}, {"time": 0.1333, "x": 37.81, "y": 2.66}, {"time": 0.2333, "x": 41.54, "y": -3.38}, {"time": 0.2667, "x": 43.39, "y": -6.47}, {"time": 0.3, "x": 46.41, "y": -12.59}, {"time": 1.3333, "x": 0.47, "y": -0.59}, {"time": 1.3667, "x": 6.2, "y": 8.85}, {"time": 1.4, "x": 10.97, "y": 12.56}, {"time": 1.4333, "x": 14.59, "y": 13.7}, {"time": 1.4667, "x": 18.16, "y": 14.18}, {"time": 1.5, "x": 21.22, "y": 14.04}, {"time": 1.5333, "x": 23.87, "y": 13.85}, {"time": 1.5667, "x": 26.74, "y": 12.42}, {"time": 1.6333, "x": 30.39, "y": 10.68}, {"time": 1.7333, "x": 35.14, "y": 6.2}, {"time": 1.8, "x": 37.81, "y": 2.66}, {"time": 1.9, "x": 41.54, "y": -3.38}, {"time": 1.9333, "x": 43.39, "y": -6.47}, {"time": 1.9667, "x": 46.41, "y": -12.59}, {"time": 2.7, "x": 0.47, "y": -0.59}, {"time": 2.7333, "x": 6.2, "y": 8.85}, {"time": 2.7667, "x": 10.97, "y": 12.56}, {"time": 2.8, "x": 14.59, "y": 13.7}, {"time": 2.8333, "x": 18.16, "y": 14.18}, {"time": 2.8667, "x": 21.22, "y": 14.04}, {"time": 2.9, "x": 23.87, "y": 13.85}, {"time": 2.9333, "x": 26.74, "y": 12.42}, {"time": 3, "x": 30.39, "y": 10.68}]}, "images/freeBonus_jb4": {"rotate": [{"time": 0.0667}, {"time": 0.1, "angle": 46.88}, {"time": 0.1333, "angle": 24.05}, {"time": 0.1667, "angle": 13.35}, {"time": 0.2, "angle": -1.88}, {"time": 0.2333, "angle": -8.1}, {"time": 0.2667, "angle": -14.84}, {"time": 0.3, "angle": -26.16}, {"time": 0.3667, "angle": -35.43}, {"time": 0.4667, "angle": -48.42}, {"time": 0.5333, "angle": -53.69}, {"time": 0.6333, "angle": -61.41}, {"time": 0.7, "angle": -62.56}, {"time": 1.5667}, {"time": 1.6, "angle": 46.88}, {"time": 1.6333, "angle": 24.05}, {"time": 1.6667, "angle": 13.35}, {"time": 1.7, "angle": -1.88}, {"time": 1.7333, "angle": -8.1}, {"time": 1.7667, "angle": -14.84}, {"time": 1.8, "angle": -26.16}, {"time": 1.8667, "angle": -35.43}, {"time": 1.9667, "angle": -48.42}, {"time": 2.0333, "angle": -53.69}, {"time": 2.1333, "angle": -61.41}, {"time": 2.2, "angle": -62.56}], "translate": [{"time": 0.0333, "x": -7.17, "y": -35.16, "curve": "stepped"}, {"time": 0.0667, "x": -7.17, "y": -35.16}, {"time": 0.1, "x": -1.44, "y": -25.73}, {"time": 0.1333, "x": 3.33, "y": -22.01}, {"time": 0.1667, "x": 6.95, "y": -20.88}, {"time": 0.2, "x": 10.52, "y": -20.39}, {"time": 0.2333, "x": 13.58, "y": -20.53}, {"time": 0.2667, "x": 16.23, "y": -20.72}, {"time": 0.3, "x": 19.1, "y": -22.15}, {"time": 0.3667, "x": 22.75, "y": -23.9}, {"time": 0.4667, "x": 27.51, "y": -28.37}, {"time": 0.5333, "x": 30.17, "y": -31.92}, {"time": 0.6333, "x": 33.91, "y": -37.95}, {"time": 0.6667, "x": 35.75, "y": -41.04}, {"time": 0.7, "x": 38.77, "y": -47.16}, {"time": 1.5333, "x": -7.17, "y": -35.16, "curve": "stepped"}, {"time": 1.5667, "x": -7.17, "y": -35.16}, {"time": 1.6, "x": -1.44, "y": -25.73}, {"time": 1.6333, "x": 3.33, "y": -22.01}, {"time": 1.6667, "x": 6.95, "y": -20.88}, {"time": 1.7, "x": 10.52, "y": -20.39}, {"time": 1.7333, "x": 13.58, "y": -20.53}, {"time": 1.7667, "x": 16.23, "y": -20.72}, {"time": 1.8, "x": 19.1, "y": -22.15}, {"time": 1.8667, "x": 22.75, "y": -23.9}, {"time": 1.9667, "x": 27.51, "y": -28.37}, {"time": 2.0333, "x": 30.17, "y": -31.92}, {"time": 2.1333, "x": 33.91, "y": -37.95}, {"time": 2.1667, "x": 35.75, "y": -41.04}, {"time": 2.2, "x": 38.77, "y": -47.16}]}, "images/freeBonus_jb5": {"rotate": [{}, {"time": 0.2, "angle": -39.63}, {"time": 0.6333}], "translate": [{"x": 3.33, "y": -0.59}]}, "images/freeBonus_jb6": {"rotate": [{"angle": 26.76}, {"time": 0.1667, "angle": 135.96}, {"time": 0.3333, "angle": -161.27}, {"time": 0.6333, "angle": -126.59}, {"time": 1.7, "angle": 26.76}, {"time": 1.8667, "angle": 135.96}, {"time": 2.0333, "angle": -161.27}, {"time": 2.3333, "angle": -126.59}], "translate": [{"x": -12.18, "y": 1.53}, {"time": 0.0667, "x": -10.67, "y": 12.85}, {"time": 0.1, "x": -8.27, "y": 17.83}, {"time": 0.1333, "x": -3.47, "y": 19.72}, {"time": 0.1667, "x": -0.04, "y": 20.85}, {"time": 0.2333, "x": 10.86, "y": 13.09}, {"time": 0.3333, "x": 17.8, "y": 0.16}, {"time": 0.5, "x": 28.2, "y": -27.86}, {"time": 0.6333, "x": 31.6, "y": -48.74, "curve": "stepped"}, {"time": 1.6667, "x": 31.6, "y": -48.74}, {"time": 1.7, "x": -12.18, "y": 1.53}, {"time": 1.7667, "x": -10.67, "y": 12.85}, {"time": 1.8, "x": -8.27, "y": 17.83}, {"time": 1.8333, "x": -3.47, "y": 19.72}, {"time": 1.8667, "x": -0.04, "y": 20.85}, {"time": 1.9333, "x": 10.86, "y": 13.09}, {"time": 2.0333, "x": 17.8, "y": 0.16}, {"time": 2.2, "x": 28.2, "y": -27.86}, {"time": 2.3333, "x": 31.6, "y": -48.74}]}, "images/freeBonus_jb7": {"rotate": [{"time": 0.6667, "angle": 26.76}, {"time": 0.8333, "angle": 135.96}, {"time": 1, "angle": -161.27}, {"time": 1.3, "angle": -126.59}, {"time": 2.3667, "angle": 26.76}, {"time": 2.5333, "angle": 135.96}, {"time": 2.7, "angle": -161.27}, {"time": 3, "angle": -126.59}], "translate": [{"time": 0.6667, "x": -28.53, "y": -48.39}, {"time": 0.7333, "x": -27.02, "y": -37.07}, {"time": 0.7667, "x": -24.62, "y": -32.1}, {"time": 0.8, "x": -19.82, "y": -30.2}, {"time": 0.8333, "x": -16.39, "y": -29.07}, {"time": 0.9, "x": -5.49, "y": -36.83}, {"time": 1, "x": 1.45, "y": -49.76}, {"time": 1.1667, "x": 11.85, "y": -77.78}, {"time": 1.3, "x": 15.25, "y": -98.67, "curve": "stepped"}, {"time": 2.3333, "x": 15.25, "y": -98.67}, {"time": 2.3667, "x": -28.53, "y": -48.39}, {"time": 2.4333, "x": -27.02, "y": -37.07}, {"time": 2.4667, "x": -24.62, "y": -32.1}, {"time": 2.5, "x": -19.82, "y": -30.2}, {"time": 2.5333, "x": -16.39, "y": -29.07}, {"time": 2.6, "x": -5.49, "y": -36.83}, {"time": 2.7, "x": 1.45, "y": -49.76}, {"time": 2.8667, "x": 11.85, "y": -77.78}, {"time": 3, "x": 15.25, "y": -98.67}]}, "images/freeBonus_jb8": {"rotate": [{"angle": -8.1}, {"time": 0.0333, "angle": -14.84}, {"time": 0.0667, "angle": -26.16}, {"time": 0.1333, "angle": -35.43}, {"time": 0.2333, "angle": -48.42}, {"time": 0.3, "angle": -53.69}, {"time": 0.4, "angle": -61.41}, {"time": 0.4667, "angle": -62.56}, {"time": 0.6333}, {"time": 0.6667, "angle": 46.88}, {"time": 0.7, "angle": 24.05}, {"time": 0.7333, "angle": 13.35}, {"time": 0.7667, "angle": -1.88}, {"time": 0.8, "angle": -8.1}, {"time": 0.8333, "angle": -14.84}, {"time": 0.8667, "angle": -26.16}, {"time": 0.9333, "angle": -35.43}, {"time": 1.0333, "angle": -48.42}, {"time": 1.1, "angle": -53.69}, {"time": 1.2, "angle": -61.41}, {"time": 1.2667, "angle": -62.56}, {"time": 1.7}, {"time": 1.7333, "angle": 46.88}, {"time": 1.7667, "angle": 24.05}, {"time": 1.8, "angle": 13.35}, {"time": 1.8333, "angle": -1.88}, {"time": 1.8667, "angle": -8.1}, {"time": 1.9, "angle": -14.84}, {"time": 1.9333, "angle": -26.16}, {"time": 2, "angle": -35.43}, {"time": 2.1, "angle": -48.42}, {"time": 2.1667, "angle": -53.69}, {"time": 2.2667, "angle": -61.41}, {"time": 2.3333, "angle": -62.56}, {"time": 2.8667}, {"time": 2.9, "angle": 46.88}, {"time": 2.9333, "angle": 24.05}, {"time": 2.9667, "angle": 13.35}, {"time": 3, "angle": -1.88}], "translate": [{"x": 9.79, "y": -27.27}, {"time": 0.0333, "x": 12.44, "y": -27.47}, {"time": 0.0667, "x": 15.31, "y": -28.89}, {"time": 0.1333, "x": 18.96, "y": -30.64}, {"time": 0.2333, "x": 23.72, "y": -35.11}, {"time": 0.3, "x": 26.39, "y": -38.66}, {"time": 0.4, "x": 30.12, "y": -44.69}, {"time": 0.4333, "x": 31.96, "y": -47.78}, {"time": 0.4667, "x": 34.99, "y": -53.9}, {"time": 0.6333, "x": -10.95, "y": -41.9}, {"time": 0.6667, "x": -5.23, "y": -32.47}, {"time": 0.7, "x": -0.46, "y": -28.75}, {"time": 0.7333, "x": 3.16, "y": -27.62}, {"time": 0.7667, "x": 6.73, "y": -27.13}, {"time": 0.8, "x": 9.79, "y": -27.27}, {"time": 0.8333, "x": 12.44, "y": -27.47}, {"time": 0.8667, "x": 15.31, "y": -28.89}, {"time": 0.9333, "x": 18.96, "y": -30.64}, {"time": 1.0333, "x": 23.72, "y": -35.11}, {"time": 1.1, "x": 26.39, "y": -38.66}, {"time": 1.2, "x": 30.12, "y": -44.69}, {"time": 1.2333, "x": 31.96, "y": -47.78}, {"time": 1.2667, "x": 34.99, "y": -53.9}, {"time": 1.7, "x": -10.95, "y": -41.9}, {"time": 1.7333, "x": -5.23, "y": -32.47}, {"time": 1.7667, "x": -0.46, "y": -28.75}, {"time": 1.8, "x": 3.16, "y": -27.62}, {"time": 1.8333, "x": 6.73, "y": -27.13}, {"time": 1.8667, "x": 9.79, "y": -27.27}, {"time": 1.9, "x": 12.44, "y": -27.47}, {"time": 1.9333, "x": 15.31, "y": -28.89}, {"time": 2, "x": 18.96, "y": -30.64}, {"time": 2.1, "x": 23.72, "y": -35.11}, {"time": 2.1667, "x": 26.39, "y": -38.66}, {"time": 2.2667, "x": 30.12, "y": -44.69}, {"time": 2.3, "x": 31.96, "y": -47.78}, {"time": 2.3333, "x": 34.99, "y": -53.9}, {"time": 2.8667, "x": -10.95, "y": -41.9}, {"time": 2.9, "x": -5.23, "y": -32.47}, {"time": 2.9333, "x": -0.46, "y": -28.75}, {"time": 2.9667, "x": 3.16, "y": -27.62}, {"time": 3, "x": 6.73, "y": -27.13}]}, "images/freeBonus_jb9": {"rotate": [{"time": 0.2667, "angle": 26.76}, {"time": 0.4333, "angle": 135.96}, {"time": 0.6, "angle": -161.27}, {"time": 0.9, "angle": -126.59}, {"time": 1.7, "angle": 26.76}, {"time": 1.8667, "angle": 135.96}, {"time": 2.0333, "angle": -161.27}, {"time": 2.3333, "angle": -126.59}], "translate": [{"time": 0.2667, "x": 3.35, "y": -44.21}, {"time": 0.3333, "x": 4.86, "y": -32.89}, {"time": 0.3667, "x": 7.26, "y": -27.91}, {"time": 0.4, "x": 12.06, "y": -26.01}, {"time": 0.4333, "x": 15.49, "y": -24.88}, {"time": 0.5, "x": 26.39, "y": -32.65}, {"time": 0.6, "x": 33.32, "y": -45.57}, {"time": 0.7667, "x": 43.73, "y": -73.6}, {"time": 0.9, "x": 47.13, "y": -94.48, "curve": "stepped"}, {"time": 1.6667, "x": 47.13, "y": -94.48}, {"time": 1.7, "x": 3.35, "y": -44.21}, {"time": 1.7667, "x": 4.86, "y": -32.89}, {"time": 1.8, "x": 7.26, "y": -27.91}, {"time": 1.8333, "x": 12.06, "y": -26.01}, {"time": 1.8667, "x": 15.49, "y": -24.88}, {"time": 1.9333, "x": 26.39, "y": -32.65}, {"time": 2.0333, "x": 33.32, "y": -45.57}, {"time": 2.2, "x": 43.73, "y": -73.6}, {"time": 2.3333, "x": 47.13, "y": -94.48}]}, "images/freeBonus_jb10": {"rotate": [{"angle": 26.76}], "translate": [{"x": 11.63, "y": -5.43, "curve": 0, "c2": 0.23, "c3": 0.409, "c4": 0.6}, {"time": 0.1, "x": 8.2, "y": -15.91, "curve": 0.302, "c2": 0.47, "c3": 0.791}, {"time": 0.3667, "x": 2.02, "y": -32.39}, {"time": 1.8667, "x": 11.63, "y": -5.43, "curve": 0, "c2": 0.23, "c3": 0.409, "c4": 0.6}, {"time": 1.9667, "x": 8.2, "y": -15.91, "curve": 0.302, "c2": 0.47, "c3": 0.791}, {"time": 2.2333, "x": 2.02, "y": -32.39}]}, "images/freeBonus_jb11": {"rotate": [{"angle": 26.76}, {"time": 0.0667, "angle": -43.21}, {"time": 0.1, "angle": 4.53}, {"time": 0.1333, "angle": -22.96}, {"time": 0.3333, "angle": -166.64}, {"time": 0.6333, "angle": 115.96}, {"time": 1.1667, "angle": 26.76}, {"time": 1.2333, "angle": -43.21}, {"time": 1.2667, "angle": 4.53}, {"time": 1.3, "angle": -22.96}, {"time": 1.5, "angle": -166.64}, {"time": 1.8, "angle": 115.96}, {"time": 2.2, "angle": 26.76}, {"time": 2.2667, "angle": -43.21}, {"time": 2.3, "angle": 4.53}, {"time": 2.3333, "angle": -22.96}, {"time": 2.5333, "angle": -166.64}, {"time": 2.8333, "angle": 115.96}], "translate": [{"x": 12.53, "y": 16.65}, {"time": 0.1, "x": 3.28, "y": 21.86}, {"time": 0.3333, "x": -4.44, "y": 5.73}, {"time": 0.6333, "x": -9.8, "y": -32.45}, {"time": 1.1667, "x": 12.53, "y": 16.65}, {"time": 1.2667, "x": 3.28, "y": 21.86}, {"time": 1.5, "x": -4.44, "y": 5.73}, {"time": 1.8, "x": -9.8, "y": -32.45}, {"time": 2.2, "x": 12.53, "y": 16.65}, {"time": 2.3, "x": 3.28, "y": 21.86}, {"time": 2.5333, "x": -4.44, "y": 5.73}, {"time": 2.8333, "x": -9.8, "y": -32.45}]}, "images/freeBonus_jb12": {"rotate": [{"time": 0.1667, "angle": 26.76}, {"time": 0.2333, "angle": -43.21}, {"time": 0.2667, "angle": 4.53}, {"time": 0.3, "angle": -22.96}, {"time": 0.5, "angle": -166.64}, {"time": 0.8, "angle": 115.96}, {"time": 1.3333, "angle": 26.76}, {"time": 1.4, "angle": -43.21}, {"time": 1.4333, "angle": 4.53}, {"time": 1.4667, "angle": -22.96}, {"time": 1.6667, "angle": -166.64}, {"time": 1.9667, "angle": 115.96}, {"time": 2.3667, "angle": 26.76}, {"time": 2.4333, "angle": -43.21}, {"time": 2.4667, "angle": 4.53}, {"time": 2.5, "angle": -22.96}, {"time": 2.7, "angle": -166.64}, {"time": 3, "angle": 115.96}], "translate": [{"time": 0.1667, "x": -11.52, "y": -47.92}, {"time": 0.2667, "x": -22.35, "y": -32.31}, {"time": 0.5, "x": -30.07, "y": -48.44}, {"time": 0.8, "x": -35.44, "y": -86.62}, {"time": 1.3333, "x": -13.1, "y": -37.52}, {"time": 1.4333, "x": -22.35, "y": -32.31}, {"time": 1.6667, "x": -30.07, "y": -48.44}, {"time": 1.9667, "x": -35.44, "y": -86.62}, {"time": 2.3667, "x": -13.1, "y": -37.52}, {"time": 2.4667, "x": -22.35, "y": -32.31}, {"time": 2.7, "x": -30.07, "y": -48.44}, {"time": 3, "x": -35.44, "y": -86.62}]}, "images/freeBonus_jb13": {"rotate": [{"time": 0.1, "angle": 26.76}, {"time": 0.1667, "angle": -43.21}, {"time": 0.2, "angle": 4.53}, {"time": 0.2333, "angle": -22.96}, {"time": 0.4333, "angle": -166.64}, {"time": 0.7333, "angle": 115.96}, {"time": 1.2, "angle": 26.76}, {"time": 1.2667, "angle": -43.21}, {"time": 1.3, "angle": 4.53}, {"time": 1.3333, "angle": -22.96}, {"time": 1.5333, "angle": -166.64}, {"time": 1.8333, "angle": 115.96}, {"time": 2.2333, "angle": 26.76}, {"time": 2.3, "angle": -43.21}, {"time": 2.3333, "angle": 4.53}, {"time": 2.3667, "angle": -22.96}, {"time": 2.5667, "angle": -166.64}, {"time": 2.8667, "angle": 115.96}], "translate": [{"time": 0.1, "x": -0.11, "y": -22.87}, {"time": 0.2, "x": -10.94, "y": -7.26}, {"time": 0.4333, "x": -18.66, "y": -23.39}, {"time": 0.7333, "x": -24.03, "y": -61.57}, {"time": 1.2, "x": -1.7, "y": -12.47}, {"time": 1.3, "x": -10.94, "y": -7.26}, {"time": 1.5333, "x": -18.66, "y": -23.39}, {"time": 1.8333, "x": -24.03, "y": -61.57}, {"time": 2.2333, "x": -1.7, "y": -12.47}, {"time": 2.3333, "x": -10.94, "y": -7.26}, {"time": 2.5667, "x": -18.66, "y": -23.39}, {"time": 2.8667, "x": -24.03, "y": -61.57}]}, "images/freeBonus_jb14": {"rotate": [{"angle": 26.76}], "translate": [{"x": -32.2, "y": -45.36, "curve": 0, "c2": 0.28, "c3": 0.355, "c4": 0.62}, {"time": 0.0333, "x": -32.3, "y": -41.21, "curve": 0.236, "c2": 0.3, "c3": 0.577, "c4": 0.64}, {"time": 0.0667, "x": -34.52, "y": -40.23, "curve": 0.284, "c2": 0.32, "c3": 0.623, "c4": 0.66}, {"time": 0.1, "x": -37.02, "y": -39.49, "curve": 0.288, "c2": 0.34, "c3": 0.642, "c4": 0.69}, {"time": 0.2, "x": -41.52, "y": -47.76, "curve": 0.388, "c2": 0.57, "c3": 0.784}, {"time": 0.3667, "x": -41.81, "y": -72.32}, {"time": 0.8667, "x": -32.2, "y": -45.36, "curve": 0, "c2": 0.28, "c3": 0.355, "c4": 0.62}, {"time": 0.9, "x": -32.3, "y": -41.21, "curve": 0.236, "c2": 0.3, "c3": 0.577, "c4": 0.64}, {"time": 0.9333, "x": -34.52, "y": -40.23, "curve": 0.284, "c2": 0.32, "c3": 0.623, "c4": 0.66}, {"time": 0.9667, "x": -37.02, "y": -39.49, "curve": 0.288, "c2": 0.34, "c3": 0.642, "c4": 0.69}, {"time": 1.0667, "x": -41.52, "y": -47.76, "curve": 0.388, "c2": 0.57, "c3": 0.784}, {"time": 1.2333, "x": -41.81, "y": -72.32}, {"time": 2.3, "x": -32.2, "y": -45.36, "curve": 0, "c2": 0.28, "c3": 0.355, "c4": 0.62}, {"time": 2.3333, "x": -32.3, "y": -41.21, "curve": 0.236, "c2": 0.3, "c3": 0.577, "c4": 0.64}, {"time": 2.3667, "x": -34.52, "y": -40.23, "curve": 0.284, "c2": 0.32, "c3": 0.623, "c4": 0.66}, {"time": 2.4, "x": -37.02, "y": -39.49, "curve": 0.288, "c2": 0.34, "c3": 0.642, "c4": 0.69}, {"time": 2.5, "x": -41.52, "y": -47.76, "curve": 0.388, "c2": 0.57, "c3": 0.784}, {"time": 2.6667, "x": -41.81, "y": -72.32}]}, "images/freeBonus_jb15": {"rotate": [{"time": 0.1667, "angle": 26.76}, {"time": 0.2333, "angle": -43.21}, {"time": 0.2667, "angle": 4.53}, {"time": 0.3, "angle": -22.96}, {"time": 0.5, "angle": -166.64}, {"time": 0.8, "angle": 115.96}, {"time": 1.3333, "angle": 26.76}, {"time": 1.4, "angle": -43.21}, {"time": 1.4333, "angle": 4.53}, {"time": 1.4667, "angle": -22.96}, {"time": 1.6667, "angle": -166.64}, {"time": 1.9667, "angle": 115.96}, {"time": 2.3667, "angle": 26.76}, {"time": 2.4333, "angle": -43.21}, {"time": 2.4667, "angle": 4.53}, {"time": 2.5, "angle": -22.96}, {"time": 2.7, "angle": -166.64}, {"time": 3, "angle": 115.96}], "translate": [{"time": 0.1667, "x": -14.88, "y": 7.04}, {"time": 0.2667, "x": -25.71, "y": 22.65}, {"time": 0.5, "x": -33.43, "y": 6.52}, {"time": 0.8, "x": -38.79, "y": -31.67}, {"time": 1.3333, "x": -16.46, "y": 17.43}, {"time": 1.4333, "x": -25.71, "y": 22.65}, {"time": 1.6667, "x": -33.43, "y": 6.52}, {"time": 1.9667, "x": -38.79, "y": -31.67}, {"time": 2.3667, "x": -16.46, "y": 17.43}, {"time": 2.4667, "x": -25.71, "y": 22.65}, {"time": 2.7, "x": -33.43, "y": 6.52}, {"time": 3, "x": -38.79, "y": -31.67}]}, "images/freeBonus_jb16": {"rotate": [{"angle": 26.76}, {"time": 0.5, "angle": 125.24}, {"time": 1.8333, "angle": 26.76}, {"time": 2.3333, "angle": 125.24}], "translate": [{"x": -14.88, "y": 7.04}, {"time": 0.1, "x": -21.4, "y": 12.28}, {"time": 0.5, "x": -35.07, "y": -3.3}, {"time": 1.8333, "x": -14.88, "y": 7.04}, {"time": 1.9333, "x": -21.4, "y": 12.28}, {"time": 2.3333, "x": -35.07, "y": -3.3}]}, "images/freeBonus_jb17": {"rotate": [{"angle": 66.15}, {"time": 0.3, "angle": 125.24}, {"time": 0.4, "angle": 26.76}, {"time": 0.9, "angle": 125.24}, {"time": 1.6667, "angle": 26.76}, {"time": 2.1667, "angle": 125.24}, {"time": 2.8, "angle": 26.76}, {"time": 3, "angle": 66.15}], "translate": [{"x": -3.12, "y": -0.65}, {"time": 0.3, "x": -13.37, "y": -12.34}, {"time": 0.4, "x": 6.82, "y": -2}, {"time": 0.5, "x": 0.3, "y": 3.24}, {"time": 0.9, "x": -13.37, "y": -12.34}, {"time": 1.6667, "x": 6.82, "y": -2}, {"time": 1.7667, "x": 0.3, "y": 3.24}, {"time": 2.1667, "x": -13.37, "y": -12.34}, {"time": 2.8, "x": 6.82, "y": -2}, {"time": 2.9, "x": 0.3, "y": 3.24}, {"time": 3, "x": -3.12, "y": -0.65}]}, "images/freeBonus_jb18": {"rotate": [{"angle": -8.1}, {"time": 0.0333, "angle": -14.84}, {"time": 0.0667, "angle": -26.16}, {"time": 0.1333, "angle": -35.43}, {"time": 0.2333, "angle": -48.42}, {"time": 0.3, "angle": -53.69}, {"time": 0.4, "angle": -61.41}, {"time": 0.4667, "angle": -62.56}, {"time": 0.6333}, {"time": 0.6667, "angle": 46.88}, {"time": 0.7, "angle": 24.05}, {"time": 0.7333, "angle": 13.35}, {"time": 0.7667, "angle": -1.88}, {"time": 0.8, "angle": -8.1}, {"time": 0.8333, "angle": -14.84}, {"time": 0.8667, "angle": -26.16}, {"time": 0.9333, "angle": -35.43}, {"time": 1.0333, "angle": -48.42}, {"time": 1.1, "angle": -53.69}, {"time": 1.2, "angle": -61.41}, {"time": 1.2667, "angle": -62.56}, {"time": 2.0333}, {"time": 2.0667, "angle": 46.88}, {"time": 2.1, "angle": 24.05}, {"time": 2.1333, "angle": 13.35}, {"time": 2.1667, "angle": -1.88}, {"time": 2.2, "angle": -8.1}, {"time": 2.2333, "angle": -14.84}, {"time": 2.2667, "angle": -26.16}, {"time": 2.3333, "angle": -35.43}, {"time": 2.4333, "angle": -48.42}, {"time": 2.5, "angle": -53.69}, {"time": 2.6, "angle": -61.41}, {"time": 2.6667, "angle": -62.56}, {"time": 2.8667}, {"time": 2.9, "angle": 46.88}, {"time": 2.9333, "angle": 24.05}, {"time": 2.9667, "angle": 13.35}, {"time": 3, "angle": -1.88}], "translate": [{"x": 9.11, "y": 7.08}, {"time": 0.0333, "x": 11.77, "y": 6.89}, {"time": 0.0667, "x": 14.63, "y": 5.46}, {"time": 0.1333, "x": 18.28, "y": 3.71}, {"time": 0.2333, "x": 23.04, "y": -0.76}, {"time": 0.3, "x": 25.71, "y": -4.3}, {"time": 0.4, "x": 29.44, "y": -10.34}, {"time": 0.4333, "x": 31.29, "y": -13.43}, {"time": 0.4667, "x": 34.31, "y": -19.55}, {"time": 0.6333, "x": -11.63, "y": -7.55}, {"time": 0.6667, "x": -5.91, "y": 1.89}, {"time": 0.7, "x": -1.14, "y": 5.6}, {"time": 0.7333, "x": 2.48, "y": 6.74}, {"time": 0.7667, "x": 6.05, "y": 7.22}, {"time": 0.8, "x": 9.11, "y": 7.08}, {"time": 0.8333, "x": 11.77, "y": 6.89}, {"time": 0.8667, "x": 14.63, "y": 5.46}, {"time": 0.9333, "x": 18.28, "y": 3.71}, {"time": 1.0333, "x": 23.04, "y": -0.76}, {"time": 1.1, "x": 25.71, "y": -4.3}, {"time": 1.2, "x": 29.44, "y": -10.34}, {"time": 1.2333, "x": 31.29, "y": -13.43}, {"time": 1.2667, "x": 34.31, "y": -19.55}, {"time": 2.0333, "x": -11.63, "y": -7.55}, {"time": 2.0667, "x": -5.91, "y": 1.89}, {"time": 2.1, "x": -1.14, "y": 5.6}, {"time": 2.1333, "x": 2.48, "y": 6.74}, {"time": 2.1667, "x": 6.05, "y": 7.22}, {"time": 2.2, "x": 9.11, "y": 7.08}, {"time": 2.2333, "x": 11.77, "y": 6.89}, {"time": 2.2667, "x": 14.63, "y": 5.46}, {"time": 2.3333, "x": 18.28, "y": 3.71}, {"time": 2.4333, "x": 23.04, "y": -0.76}, {"time": 2.5, "x": 25.71, "y": -4.3}, {"time": 2.6, "x": 29.44, "y": -10.34}, {"time": 2.6333, "x": 31.29, "y": -13.43}, {"time": 2.6667, "x": 34.31, "y": -19.55}, {"time": 2.8667, "x": -11.63, "y": -7.55}, {"time": 2.9, "x": -5.91, "y": 1.89}, {"time": 2.9333, "x": -1.14, "y": 5.6}, {"time": 2.9667, "x": 2.48, "y": 6.74}, {"time": 3, "x": 6.05, "y": 7.22}]}, "images/freeBonus_jb19": {"rotate": [{"angle": -166.64}, {"time": 0.3, "angle": 115.96}, {"time": 0.4667, "angle": 26.76}, {"time": 0.5333, "angle": -43.21}, {"time": 0.5667, "angle": 4.53}, {"time": 0.6, "angle": -22.96}, {"time": 0.8, "angle": -166.64}, {"time": 1.1, "angle": 115.96}, {"time": 1.6333, "angle": 26.76}, {"time": 1.7, "angle": -43.21}, {"time": 1.7333, "angle": 4.53}, {"time": 1.7667, "angle": -22.96}, {"time": 1.9667, "angle": -166.64}, {"time": 2.2667, "angle": 115.96}, {"time": 2.6667, "angle": 26.76}, {"time": 2.7333, "angle": -43.21}, {"time": 2.7667, "angle": 4.53}, {"time": 2.8, "angle": -22.96}, {"time": 3, "angle": -166.64}], "translate": [{"x": -20.54, "y": -17.74}, {"time": 0.3, "x": -25.9, "y": -55.92}, {"time": 0.4667, "x": -3.57, "y": -6.82}, {"time": 0.5667, "x": -12.82, "y": -1.6}, {"time": 0.8, "x": -20.54, "y": -17.74}, {"time": 1.1, "x": -25.9, "y": -55.92}, {"time": 1.6333, "x": -3.57, "y": -6.82}, {"time": 1.7333, "x": -12.82, "y": -1.6}, {"time": 1.9667, "x": -20.54, "y": -17.74}, {"time": 2.2667, "x": -25.9, "y": -55.92}, {"time": 2.6667, "x": -3.57, "y": -6.82}, {"time": 2.7667, "x": -12.82, "y": -1.6}, {"time": 3, "x": -20.54, "y": -17.74}]}, "images/freeBonus_jb20": {"rotate": [{"angle": -166.64}, {"time": 0.3, "angle": 115.96}, {"time": 0.4667, "angle": 26.76}, {"time": 0.5333, "angle": -43.21}, {"time": 0.5667, "angle": 4.53}, {"time": 0.6, "angle": -22.96}, {"time": 0.8, "angle": -166.64}, {"time": 1.1, "angle": 115.96}, {"time": 1.6333, "angle": 26.76}, {"time": 1.7, "angle": -43.21}, {"time": 1.7333, "angle": 4.53}, {"time": 1.7667, "angle": -22.96}, {"time": 1.9667, "angle": -166.64}, {"time": 2.2667, "angle": 115.96}, {"time": 2.6667, "angle": 26.76}, {"time": 2.7333, "angle": -43.21}, {"time": 2.7667, "angle": 4.53}, {"time": 2.8, "angle": -22.96}, {"time": 3, "angle": -166.64}], "translate": [{"x": -21.94, "y": 7.61}, {"time": 0.3, "x": -27.3, "y": -30.57}, {"time": 0.4667, "x": -4.97, "y": 18.52}, {"time": 0.5667, "x": -14.22, "y": 23.74}, {"time": 0.8, "x": -21.94, "y": 7.61}, {"time": 1.1, "x": -27.3, "y": -30.57}, {"time": 1.6333, "x": -4.97, "y": 18.52}, {"time": 1.7333, "x": -14.22, "y": 23.74}, {"time": 1.9667, "x": -21.94, "y": 7.61}, {"time": 2.2667, "x": -27.3, "y": -30.57}, {"time": 2.6667, "x": -4.97, "y": 18.52}, {"time": 2.7667, "x": -14.22, "y": 23.74}, {"time": 3, "x": -21.94, "y": 7.61}]}}, "path": {"R1": {"position": [{"curve": 0.024, "c2": 0.49, "c4": 0.74}, {"time": 0.6333, "position": 1}]}, "R2": {"position": [{}, {"time": 0.6333, "position": 1}]}}, "deform": {"default": {"R1": {"R1": [{"offset": 6, "vertices": [0.3, 4.94999, 0.3, 4.94999, 0.3, 4.94999, 0.75, -3.14999, -0.60001, -6.14998, -2.5925, -7.54487]}]}, "R2": {"R1": [{"vertices": [-13.31999, -16.20597, -13.31999, -16.20597, -13.31999, -16.20597, -13.31999, -16.20597, -13.31999, -16.20597, -13.31999, -16.20597, -13.31999, -16.20597, -13.31999, -16.20597, -13.31999, -16.20597]}]}, "images/freeBonus_jb6": {"images/freeBonus_jb2": [{"vertices": [-12.1074, -2.628, -13.22198, -0.56089, -13.30566, 0.71356, -12.98988, 2.2748, -12.49383, 4.07411, -11.70204, 5.25493, -10.44375, 6.14494, -8.91781, 6.80477, -7.37222, 7.40479, -5.31906, 7.99251, -3.80968, 8.29905, -1.9726, 8.4349, 1.91359, 8.2416, 3.82363, 8.01379, 5.65257, 7.46783, 7.27153, 6.71384, 9.1776, 5.63001, 10.48685, 4.6286, 11.98294, 3.13859, 12.55376, 1.73249, 12.92516, -1.95945, 12.53818, -3.84884, 11.77592, -5.09947, 10.52692, -6.13882, 9.15929, -6.99847, 7.28526, -7.58704, 4.86352, -8.2427, 2.50685, -8.57818, -0.21957, -8.38641, -3.03448, -7.96528, -6.06653, -7.145, -8.83762, -5.84818, -10.60233, -4.32705, 7.03301, 6.79456, -0.07691, 8.29063, -12.67914, -1.55877]}]}, "images/freeBonus_jb7": {"images/freeBonus_jb2": [{"time": 0.6667, "vertices": [-12.1074, -2.628, -13.22198, -0.56089, -13.30566, 0.71356, -12.98988, 2.2748, -12.49383, 4.07411, -11.70204, 5.25493, -10.44375, 6.14494, -8.91781, 6.80477, -7.37222, 7.40479, -5.31906, 7.99251, -3.80968, 8.29905, -1.9726, 8.4349, 1.91359, 8.2416, 3.82363, 8.01379, 5.65257, 7.46783, 7.27153, 6.71384, 9.1776, 5.63001, 10.48685, 4.6286, 11.98294, 3.13859, 12.55376, 1.73249, 12.92516, -1.95945, 12.53818, -3.84884, 11.77592, -5.09947, 10.52692, -6.13882, 9.15929, -6.99847, 7.28526, -7.58704, 4.86352, -8.2427, 2.50685, -8.57818, -0.21957, -8.38641, -3.03448, -7.96528, -6.06653, -7.145, -8.83762, -5.84818, -10.60233, -4.32705, 7.03301, 6.79456, -0.07691, 8.29063, -12.67914, -1.55877]}]}, "images/freeBonus_jb9": {"images/freeBonus_jb2": [{"time": 0.2667, "vertices": [-12.1074, -2.628, -13.22198, -0.56089, -13.30566, 0.71356, -12.98988, 2.2748, -12.49383, 4.07411, -11.70204, 5.25493, -10.44375, 6.14494, -8.91781, 6.80477, -7.37222, 7.40479, -5.31906, 7.99251, -3.80968, 8.29905, -1.9726, 8.4349, 1.91359, 8.2416, 3.82363, 8.01379, 5.65257, 7.46783, 7.27153, 6.71384, 9.1776, 5.63001, 10.48685, 4.6286, 11.98294, 3.13859, 12.55376, 1.73249, 12.92516, -1.95945, 12.53818, -3.84884, 11.77592, -5.09947, 10.52692, -6.13882, 9.15929, -6.99847, 7.28526, -7.58704, 4.86352, -8.2427, 2.50685, -8.57818, -0.21957, -8.38641, -3.03448, -7.96528, -6.06653, -7.145, -8.83762, -5.84818, -10.60233, -4.32705, 7.03301, 6.79456, -0.07691, 8.29063, -12.67914, -1.55877]}]}, "images/freeBonus_jb10": {"images/freeBonus_jb2": [{"vertices": [-12.1074, -2.628, -13.22198, -0.56089, -13.30566, 0.71356, -12.98988, 2.2748, -12.49383, 4.07411, -11.70204, 5.25493, -10.44375, 6.14494, -8.91781, 6.80477, -7.37222, 7.40479, -5.31906, 7.99251, -3.80968, 8.29905, -1.9726, 8.4349, 1.91359, 8.2416, 3.82363, 8.01379, 5.65257, 7.46783, 7.27153, 6.71384, 9.1776, 5.63001, 10.48685, 4.6286, 11.98294, 3.13859, 12.55376, 1.73249, 12.92516, -1.95945, 12.53818, -3.84884, 11.77592, -5.09947, 10.52692, -6.13882, 9.15929, -6.99847, 7.28526, -7.58704, 4.86352, -8.2427, 2.50685, -8.57818, -0.21957, -8.38641, -3.03448, -7.96528, -6.06653, -7.145, -8.83762, -5.84818, -10.60233, -4.32705, 7.03301, 6.79456, -0.07691, 8.29063, -12.67914, -1.55877]}, {"time": 0.3667, "vertices": [-13.5083, -6.8223, -15.46037, -4.72036, -15.95038, -3.23924, -16.05612, -1.29646, -16.02176, 0.98309, -15.44797, 2.62216, -14.23339, 4.06054, -12.63171, 5.30891, -10.98846, 6.49261, -8.74133, 7.81749, -7.05087, 8.64309, -4.92054, 9.36743, -0.26652, 10.33139, 2.06164, 10.64815, 4.39153, 10.56388, 6.53701, 10.16922, 9.12316, 9.47269, 10.97839, 8.69048, 13.20446, 7.38791, 14.31081, 5.9006, 15.88281, 1.64955, 16.00507, -0.70303, 15.48762, -2.41558, 14.32981, -4.02766, 12.97666, -5.4637, 10.9416, -6.73465, 8.27951, -8.25296, 5.59613, -9.37279, 2.31382, -9.98264, -1.14349, -10.34853, -4.98002, -10.30913, -8.65425, -9.62618, -11.20748, -8.36927, 6.23024, 10.19147, -2.63498, 9.77854, -14.51236, -5.73358]}, {"time": 1.8667, "vertices": [-12.1074, -2.628, -13.22198, -0.56089, -13.30566, 0.71356, -12.98988, 2.2748, -12.49383, 4.07411, -11.70204, 5.25493, -10.44375, 6.14494, -8.91781, 6.80477, -7.37222, 7.40479, -5.31906, 7.99251, -3.80968, 8.29905, -1.9726, 8.4349, 1.91359, 8.2416, 3.82363, 8.01379, 5.65257, 7.46783, 7.27153, 6.71384, 9.1776, 5.63001, 10.48685, 4.6286, 11.98294, 3.13859, 12.55376, 1.73249, 12.92516, -1.95945, 12.53818, -3.84884, 11.77592, -5.09947, 10.52692, -6.13882, 9.15929, -6.99847, 7.28526, -7.58704, 4.86352, -8.2427, 2.50685, -8.57818, -0.21957, -8.38641, -3.03448, -7.96528, -6.06653, -7.145, -8.83762, -5.84818, -10.60233, -4.32705, 7.03301, 6.79456, -0.07691, 8.29063, -12.67914, -1.55877]}, {"time": 2.2333, "vertices": [-13.5083, -6.8223, -15.46037, -4.72036, -15.95038, -3.23924, -16.05612, -1.29646, -16.02176, 0.98309, -15.44797, 2.62216, -14.23339, 4.06054, -12.63171, 5.30891, -10.98846, 6.49261, -8.74133, 7.81749, -7.05087, 8.64309, -4.92054, 9.36743, -0.26652, 10.33139, 2.06164, 10.64815, 4.39153, 10.56388, 6.53701, 10.16922, 9.12316, 9.47269, 10.97839, 8.69048, 13.20446, 7.38791, 14.31081, 5.9006, 15.88281, 1.64955, 16.00507, -0.70303, 15.48762, -2.41558, 14.32981, -4.02766, 12.97666, -5.4637, 10.9416, -6.73465, 8.27951, -8.25296, 5.59613, -9.37279, 2.31382, -9.98264, -1.14349, -10.34853, -4.98002, -10.30913, -8.65425, -9.62618, -11.20748, -8.36927, 6.23024, 10.19147, -2.63498, 9.77854, -14.51236, -5.73358]}]}, "images/freeBonus_jb11": {"images/freeBonus_jb2": [{"time": 0.1, "vertices": [-12.48947, -3.7719, -13.83245, -1.69529, -14.02695, -0.36448, -13.82612, 1.30082, -13.45599, 3.23111, -12.72365, 4.5369, -11.47729, 5.57647, -9.93069, 6.39681, -8.35847, 7.15601, -6.2524, 7.94478, -4.69364, 8.39288, -2.77658, 8.68923, 1.31902, 8.81155, 3.34309, 8.73225, 5.30865, 8.31221, 7.0712, 7.65621, 9.16276, 6.67802, 10.62091, 5.73639, 12.31609, 4.2975, 13.03295, 2.86925, 13.73179, -0.97517, 13.4837, -2.99089, 12.7882, -4.3675, 11.56407, -5.56305, 10.20039, -6.5799, 8.28244, -7.35457, 5.79515, -8.2455, 3.34938, -8.79489, 0.47136, -8.82174, -2.51876, -8.61526, -5.77021, -8.00795, -8.78761, -6.87854, -10.76737, -5.42947, 6.81407, 7.72099, -0.77457, 8.69642, -13.17911, -2.69736]}]}, "images/freeBonus_jb12": {"images/freeBonus_jb2": [{"time": 0.1667, "vertices": [-12.48947, -3.7719, -13.83245, -1.69529, -14.02695, -0.36448, -13.82612, 1.30082, -13.45599, 3.23111, -12.72365, 4.5369, -11.47729, 5.57647, -9.93069, 6.39681, -8.35847, 7.15601, -6.2524, 7.94478, -4.69364, 8.39288, -2.77658, 8.68923, 1.31902, 8.81155, 3.34309, 8.73225, 5.30865, 8.31221, 7.0712, 7.65621, 9.16276, 6.67802, 10.62091, 5.73639, 12.31609, 4.2975, 13.03295, 2.86925, 13.73179, -0.97517, 13.4837, -2.99089, 12.7882, -4.3675, 11.56407, -5.56305, 10.20039, -6.5799, 8.28244, -7.35457, 5.79515, -8.2455, 3.34938, -8.79489, 0.47136, -8.82174, -2.51876, -8.61526, -5.77021, -8.00795, -8.78761, -6.87854, -10.76737, -5.42947, 6.81407, 7.72099, -0.77457, 8.69642, -13.17911, -2.69736]}]}, "images/freeBonus_jb13": {"images/freeBonus_jb2": [{"time": 0.2, "vertices": [-12.48947, -3.7719, -13.83245, -1.69529, -14.02695, -0.36448, -13.82612, 1.30082, -13.45599, 3.23111, -12.72365, 4.5369, -11.47729, 5.57647, -9.93069, 6.39681, -8.35847, 7.15601, -6.2524, 7.94478, -4.69364, 8.39288, -2.77658, 8.68923, 1.31902, 8.81155, 3.34309, 8.73225, 5.30865, 8.31221, 7.0712, 7.65621, 9.16276, 6.67802, 10.62091, 5.73639, 12.31609, 4.2975, 13.03295, 2.86925, 13.73179, -0.97517, 13.4837, -2.99089, 12.7882, -4.3675, 11.56407, -5.56305, 10.20039, -6.5799, 8.28244, -7.35457, 5.79515, -8.2455, 3.34938, -8.79489, 0.47136, -8.82174, -2.51876, -8.61526, -5.77021, -8.00795, -8.78761, -6.87854, -10.76737, -5.42947, 6.81407, 7.72099, -0.77457, 8.69642, -13.17911, -2.69736]}]}, "images/freeBonus_jb14": {"images/freeBonus_jb2": [{"vertices": [-12.1074, -2.628, -13.22198, -0.56089, -13.30566, 0.71356, -12.98988, 2.2748, -12.49383, 4.07411, -11.70204, 5.25493, -10.44375, 6.14494, -8.91781, 6.80477, -7.37222, 7.40479, -5.31906, 7.99251, -3.80968, 8.29905, -1.9726, 8.4349, 1.91359, 8.2416, 3.82363, 8.01379, 5.65257, 7.46783, 7.27153, 6.71384, 9.1776, 5.63001, 10.48685, 4.6286, 11.98294, 3.13859, 12.55376, 1.73249, 12.92516, -1.95945, 12.53818, -3.84884, 11.77592, -5.09947, 10.52692, -6.13882, 9.15929, -6.99847, 7.28526, -7.58704, 4.86352, -8.2427, 2.50685, -8.57818, -0.21957, -8.38641, -3.03448, -7.96528, -6.06653, -7.145, -8.83762, -5.84818, -10.60233, -4.32705, 7.03301, 6.79456, -0.07691, 8.29063, -12.67914, -1.55877]}]}, "images/freeBonus_jb15": {"images/freeBonus_jb2": [{"time": 0.3, "vertices": [-12.48947, -3.7719, -13.83245, -1.69529, -14.02695, -0.36448, -13.82612, 1.30082, -13.45599, 3.23111, -12.72365, 4.5369, -11.47729, 5.57647, -9.93069, 6.39681, -8.35847, 7.15601, -6.2524, 7.94478, -4.69364, 8.39288, -2.77658, 8.68923, 1.31902, 8.81155, 3.34309, 8.73225, 5.30865, 8.31221, 7.0712, 7.65621, 9.16276, 6.67802, 10.62091, 5.73639, 12.31609, 4.2975, 13.03295, 2.86925, 13.73179, -0.97517, 13.4837, -2.99089, 12.7882, -4.3675, 11.56407, -5.56305, 10.20039, -6.5799, 8.28244, -7.35457, 5.79515, -8.2455, 3.34938, -8.79489, 0.47136, -8.82174, -2.51876, -8.61526, -5.77021, -8.00795, -8.78761, -6.87854, -10.76737, -5.42947, 6.81407, 7.72099, -0.77457, 8.69642, -13.17911, -2.69736]}]}, "images/freeBonus_jb16": {"images/freeBonus_jb2": [{"vertices": [-12.48947, -3.7719, -13.83245, -1.69529, -14.02695, -0.36448, -13.82612, 1.30082, -13.45599, 3.23111, -12.72365, 4.5369, -11.47729, 5.57647, -9.93069, 6.39681, -8.35847, 7.15601, -6.2524, 7.94478, -4.69364, 8.39288, -2.77658, 8.68923, 1.31902, 8.81155, 3.34309, 8.73225, 5.30865, 8.31221, 7.0712, 7.65621, 9.16276, 6.67802, 10.62091, 5.73639, 12.31609, 4.2975, 13.03295, 2.86925, 13.73179, -0.97517, 13.4837, -2.99089, 12.7882, -4.3675, 11.56407, -5.56305, 10.20039, -6.5799, 8.28244, -7.35457, 5.79515, -8.2455, 3.34938, -8.79489, 0.47136, -8.82174, -2.51876, -8.61526, -5.77021, -8.00795, -8.78761, -6.87854, -10.76737, -5.42947, 6.81407, 7.72099, -0.77457, 8.69642, -13.17911, -2.69736]}]}, "images/freeBonus_jb17": {"images/freeBonus_jb2": [{"time": 0.9, "vertices": [-12.48947, -3.7719, -13.83245, -1.69529, -14.02695, -0.36448, -13.82612, 1.30082, -13.45599, 3.23111, -12.72365, 4.5369, -11.47729, 5.57647, -9.93069, 6.39681, -8.35847, 7.15601, -6.2524, 7.94478, -4.69364, 8.39288, -2.77658, 8.68923, 1.31902, 8.81155, 3.34309, 8.73225, 5.30865, 8.31221, 7.0712, 7.65621, 9.16276, 6.67802, 10.62091, 5.73639, 12.31609, 4.2975, 13.03295, 2.86925, 13.73179, -0.97517, 13.4837, -2.99089, 12.7882, -4.3675, 11.56407, -5.56305, 10.20039, -6.5799, 8.28244, -7.35457, 5.79515, -8.2455, 3.34938, -8.79489, 0.47136, -8.82174, -2.51876, -8.61526, -5.77021, -8.00795, -8.78761, -6.87854, -10.76737, -5.42947, 6.81407, 7.72099, -0.77457, 8.69642, -13.17911, -2.69736]}]}, "images/freeBonus_jb19": {"images/freeBonus_jb2": [{"time": 3, "vertices": [-12.48947, -3.7719, -13.83245, -1.69529, -14.02695, -0.36448, -13.82612, 1.30082, -13.45599, 3.23111, -12.72365, 4.5369, -11.47729, 5.57647, -9.93069, 6.39681, -8.35847, 7.15601, -6.2524, 7.94478, -4.69364, 8.39288, -2.77658, 8.68923, 1.31902, 8.81155, 3.34309, 8.73225, 5.30865, 8.31221, 7.0712, 7.65621, 9.16276, 6.67802, 10.62091, 5.73639, 12.31609, 4.2975, 13.03295, 2.86925, 13.73179, -0.97517, 13.4837, -2.99089, 12.7882, -4.3675, 11.56407, -5.56305, 10.20039, -6.5799, 8.28244, -7.35457, 5.79515, -8.2455, 3.34938, -8.79489, 0.47136, -8.82174, -2.51876, -8.61526, -5.77021, -8.00795, -8.78761, -6.87854, -10.76737, -5.42947, 6.81407, 7.72099, -0.77457, 8.69642, -13.17911, -2.69736]}]}, "images/freeBonus_jb20": {"images/freeBonus_jb2": [{"time": 3, "vertices": [-12.48947, -3.7719, -13.83245, -1.69529, -14.02695, -0.36448, -13.82612, 1.30082, -13.45599, 3.23111, -12.72365, 4.5369, -11.47729, 5.57647, -9.93069, 6.39681, -8.35847, 7.15601, -6.2524, 7.94478, -4.69364, 8.39288, -2.77658, 8.68923, 1.31902, 8.81155, 3.34309, 8.73225, 5.30865, 8.31221, 7.0712, 7.65621, 9.16276, 6.67802, 10.62091, 5.73639, 12.31609, 4.2975, 13.03295, 2.86925, 13.73179, -0.97517, 13.4837, -2.99089, 12.7882, -4.3675, 11.56407, -5.56305, 10.20039, -6.5799, 8.28244, -7.35457, 5.79515, -8.2455, 3.34938, -8.79489, 0.47136, -8.82174, -2.51876, -8.61526, -5.77021, -8.00795, -8.78761, -6.87854, -10.76737, -5.42947, 6.81407, 7.72099, -0.77457, 8.69642, -13.17911, -2.69736]}]}, "images/tx_DT_icon_free-cash_00000": {"images/tx_DT_icon_free-cash_00000": [{"vertices": [-15.73198, -3.762, -15.73198, -3.762, -15.73198, -3.762, -15.73198, -3.762]}], "images/tx_DT_icon_free-cash_00001": [{"time": 0.0667, "vertices": [-9.80399, -2.394, -9.80399, -2.394, -9.80399, -2.394, -9.80399, -2.394]}], "images/tx_DT_icon_free-cash_00002": [{"time": 0.1333, "vertices": [-5.69998, -1.71001, -5.69998, -1.71001, -5.69998, -1.71001, -5.69998, -1.71001]}], "images/tx_DT_icon_free-cash_00003": [{"time": 0.2, "vertices": [-2.62201, 0, -2.62201, 0, -2.62201, 0, -2.62201]}], "images/tx_DT_icon_free-cash_00004": [{"time": 0.2667, "vertices": [-1.53399, 0, -1.53399, 0, -1.53399, 0, -1.53399]}], "images/tx_DT_icon_free-cash_00019": [{"time": 1.2667, "vertices": [0.632, 1.02, 0.632, 1.02, 0.632, 1.02, 0.632, 1.02]}], "images/tx_DT_icon_free-cash_00018": [{"time": 1.2, "offset": 1, "vertices": [1.062, 0, 1.062, 0, 1.062, 0, 1.062]}], "images/tx_DT_icon_free-cash_00017": [{"time": 1.1333, "offset": 1, "vertices": [1.165, 2e-05, 1.165, 2e-05, 1.165, 2e-05, 1.165]}], "images/tx_DT_icon_free-cash_00016": [{"time": 1.0667, "vertices": [-0.139, 1.112, -0.139, 1.112, -0.139, 1.112, -0.139, 1.112]}], "images/tx_DT_icon_free-cash_00015": [{"time": 1, "vertices": [-0.08601, 0.973, -0.08601, 0.973, -0.08601, 0.973, -0.08601, 0.973]}], "images/tx_DT_icon_free-cash_00014": [{"time": 0.9333, "offset": 1, "vertices": [0.97299, 0, 0.97299, 0, 0.97299, 0, 0.97299]}], "images/tx_DT_icon_free-cash_00013": [{"time": 0.8667, "offset": 1, "vertices": [0.695, 0, 0.695, 0, 0.695, 0, 0.695]}], "images/tx_DT_icon_free-cash_00012": [{"time": 0.8, "offset": 1, "vertices": [0.781, 0, 0.781, 0, 0.781, 0, 0.781]}], "images/tx_DT_icon_free-cash_00011": [{"time": 0.7333, "offset": 1, "vertices": [0.695, 0, 0.695, 0, 0.695, 0, 0.695]}], "images/tx_DT_icon_free-cash_00010": [{"time": 0.6667, "offset": 1, "vertices": [0.55599, 0, 0.55599, 0, 0.55599, 0, 0.55599]}], "images/tx_DT_icon_free-cash_00009": [{"time": 0.6, "offset": 1, "vertices": [0.417, 0, 0.417, 0, 0.417, 0, 0.417]}], "images/tx_DT_icon_free-cash_00008": [{"time": 0.5333, "offset": 1, "vertices": [0.417, 0, 0.417, 0, 0.417, 0, 0.417]}], "images/tx_DT_icon_free-cash_00007": [{"time": 0.4667, "offset": 1, "vertices": [0.278, 0, 0.278, 0, 0.278, 0, 0.278]}], "images/tx_DT_icon_free-cash_00006": [{"time": 0.4, "offset": 1, "vertices": [0.27801, 0, 0.27801, 0, 0.27801, 0, 0.27801]}], "images/tx_DT_icon_free-cash_00005": [{"time": 0.3333, "offset": 1, "vertices": [0.417, 0, 0.417, 0, 0.417, 0, 0.417]}]}}}}}}