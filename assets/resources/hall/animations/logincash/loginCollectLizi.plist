<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>angle</key>
    <integer>0</integer>
    <key>angleVariance</key>
    <integer>360</integer>
    <key>duration</key>
    <integer>-1</integer>
    <key>startParticleSize</key>
    <integer>45</integer>
    <key>startParticleSizeVariance</key>
    <integer>25</integer>
    <key>finishParticleSize</key>
    <integer>0</integer>
    <key>finishParticleSizeVariance</key>
    <integer>0</integer>
    <key>gravityx</key>
    <integer>0</integer>
    <key>gravityy</key>
    <integer>50</integer>
    <key>maxParticles</key>
    <integer>20</integer>
    <key>maxRadius</key>
    <integer>0</integer>
    <key>maxRadiusVariance</key>
    <integer>0</integer>
    <key>minRadius</key>
    <integer>0</integer>
    <key>particleLifespan</key>
    <integer>1</integer>
    <key>particleLifespanVariance</key>
    <real>0.2</real>
    <key>rotatePerSecond</key>
    <integer>0</integer>
    <key>rotatePerSecondVariance</key>
    <integer>0</integer>
    <key>rotationEnd</key>
    <integer>0</integer>
    <key>rotationEndVariance</key>
    <integer>0</integer>
    <key>rotationStart</key>
    <integer>0</integer>
    <key>rotationStartVariance</key>
    <integer>0</integer>
    <key>sourcePositionVariancex</key>
    <integer>75</integer>
    <key>sourcePositionVariancey</key>
    <integer>0</integer>
    <key>sourcePositionx</key>
    <integer>203</integer>
    <key>sourcePositiony</key>
    <integer>175</integer>
    <key>speed</key>
    <integer>0</integer>
    <key>speedVariance</key>
    <integer>0</integer>
    <key>startColorAlpha</key>
    <real>0.29411764705882354</real>
    <key>startColorBlue</key>
    <real>0.6823529411764706</real>
    <key>startColorGreen</key>
    <real>0.6823529411764706</real>
    <key>startColorRed</key>
    <real>0.6823529411764706</real>
    <key>startColorVarianceAlpha</key>
    <real>0.29411764705882354</real>
    <key>startColorVarianceBlue</key>
    <real>0.5019607843137255</real>
    <key>startColorVarianceGreen</key>
    <real>0.5019607843137255</real>
    <key>startColorVarianceRed</key>
    <real>0.5215686274509804</real>
    <key>finishColorAlpha</key>
    <real>0.29411764705882354</real>
    <key>finishColorBlue</key>
    <real>0.5568627450980392</real>
    <key>finishColorGreen</key>
    <real>0.5568627450980392</real>
    <key>finishColorRed</key>
    <real>0.5568627450980392</real>
    <key>finishColorVarianceAlpha</key>
    <real>0.29411764705882354</real>
    <key>finishColorVarianceBlue</key>
    <real>0.592156862745098</real>
    <key>finishColorVarianceGreen</key>
    <real>0.592156862745098</real>
    <key>finishColorVarianceRed</key>
    <real>0.596078431372549</real>
    <key>tangentialAccelVariance</key>
    <integer>0</integer>
    <key>tangentialAcceleration</key>
    <integer>0</integer>
    <key>radialAccelVariance</key>
    <integer>0</integer>
    <key>radialAcceleration</key>
    <integer>0</integer>
    <key>blendFuncSource</key>
    <integer>770</integer>
    <key>blendFuncDestination</key>
    <integer>1</integer>
    <key>emitterType</key>
    <integer>0</integer>
    <key>spriteFrameUuid</key>
    <string>d4786123-f66c-4425-80ef-a0d0abf41308</string>
    <key>positionType</key>
    <integer>1</integer>
    <key>rotationIsDir</key>
    <false/>
    <key>minRadiusVariance</key>
    <integer>0</integer>
    <key>emissionRate</key>
    <integer>20</integer>
  </dict>
</plist>