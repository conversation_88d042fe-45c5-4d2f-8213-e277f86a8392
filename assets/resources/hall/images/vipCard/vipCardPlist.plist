<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>bg_lacebottom.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,186}</string>
                <key>spriteSourceSize</key>
                <string>{178,186}</string>
                <key>textureRect</key>
                <string>{{616,137},{178,186}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_ribbon.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{420,294}</string>
                <key>spriteSourceSize</key>
                <string>{420,294}</string>
                <key>textureRect</key>
                <string>{{0,323},{420,294}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btnClose3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{43,43}</string>
                <key>spriteSourceSize</key>
                <string>{43,43}</string>
                <key>textureRect</key>
                <string>{{165,0},{43,43}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_green5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{264,96}</string>
                <key>spriteSourceSize</key>
                <string>{264,96}</string>
                <key>textureRect</key>
                <string>{{342,0},{264,96}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_yellow5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{378,138}</string>
                <key>spriteSourceSize</key>
                <string>{378,138}</string>
                <key>textureRect</key>
                <string>{{0,137},{378,138}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>font_days.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{134,61}</string>
                <key>spriteSourceSize</key>
                <string>{134,61}</string>
                <key>textureRect</key>
                <string>{{208,0},{134,61}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_30block.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{442,319}</string>
                <key>spriteSourceSize</key>
                <string>{442,319}</string>
                <key>textureRect</key>
                <string>{{0,642},{442,319}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_30days.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{238,155}</string>
                <key>spriteSourceSize</key>
                <string>{238,155}</string>
                <key>textureRect</key>
                <string>{{378,137},{238,155}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_7block.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{442,319}</string>
                <key>spriteSourceSize</key>
                <string>{442,319}</string>
                <key>textureRect</key>
                <string>{{420,323},{442,319}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_7days.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{201,137}</string>
                <key>spriteSourceSize</key>
                <string>{201,137}</string>
                <key>textureRect</key>
                <string>{{606,0},{201,137}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_diapoint.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{16,16}</string>
                <key>spriteSourceSize</key>
                <string>{16,16}</string>
                <key>textureRect</key>
                <string>{{0,0},{16,16}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_gold1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{56,40}</string>
                <key>spriteSourceSize</key>
                <string>{56,40}</string>
                <key>textureRect</key>
                <string>{{109,0},{56,40}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_slash.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{93,24}</string>
                <key>spriteSourceSize</key>
                <string>{93,24}</string>
                <key>textureRect</key>
                <string>{{16,0},{93,24}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>vipCardPlist.png</string>
            <key>size</key>
            <string>{862,961}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:cd2171854b91e59eeca98ca33e1504c5:6839887790f8852c5fae9fb3944e2f23:385b708367f6edd574dd9c97d1b5e111$</string>
            <key>textureFileName</key>
            <string>vipCardPlist.png</string>
        </dict>
    </dict>
</plist>
