{"skeleton": {"hash": "ZKHj8T+WOX6wfri4xBWkqzsLgvw", "spine": "3.8.99", "x": -214.6, "y": -102.24, "width": 1072.58, "height": 533.24, "images": "D:/共享/完成动画/主界面大厅/cards/导出/里/img", "audio": "E:/工作文件/印度/大厅交互/VIP卡"}, "bones": [{"name": "root"}, {"name": "pian", "parent": "root", "rotation": 25.73, "x": -69.89, "y": 76.25, "scaleX": 0.534, "scaleY": 0.534}, {"name": "ka", "parent": "root", "x": -5.1, "y": 102.72}, {"name": "quan", "parent": "ka", "x": 453.33, "y": -74.51, "scaleX": 1.237, "scaleY": 1.237}, {"name": "pian2", "parent": "root", "rotation": 25.73, "x": -123.21, "y": 67.54, "scaleX": 0.534, "scaleY": 0.534}, {"name": "pian3", "parent": "root", "rotation": 25.73, "x": 113.36, "y": 91.35, "scaleX": 0.534, "scaleY": 0.534}, {"name": "pian4", "parent": "root", "rotation": 25.73, "x": -46.76, "y": 53.15, "scaleX": 0.461, "scaleY": 0.461}, {"name": "pian5", "parent": "root", "rotation": 25.73, "x": 22.98, "y": 63.99, "scaleX": 0.534, "scaleY": 0.534}, {"name": "pian6", "parent": "root", "rotation": 23.14, "x": -14.3, "y": 65.28, "scaleX": 0.276, "scaleY": 0.276}, {"name": "pian7", "parent": "root", "rotation": 25.73, "x": 56.38, "y": 71.99, "scaleX": 0.534, "scaleY": 0.534}, {"name": "guangxian", "parent": "root", "x": 696.45, "y": -77.96, "scaleX": 1.509, "scaleY": 0.99}, {"name": "guangxian2", "parent": "root", "rotation": -9.61, "x": 732.93, "y": -77.96, "scaleX": 1.197, "scaleY": 0.99}, {"name": "guangxian3", "parent": "root", "rotation": 9.4, "x": 656.64, "y": -77.96, "scaleX": 1.197, "scaleY": 0.99}, {"name": "xing0001", "parent": "root", "x": 372.68, "y": 395.48}, {"name": "xing1", "parent": "root", "x": 372.68, "y": 395.48}], "slots": [{"name": "Cash_Weekly_bg", "bone": "root"}, {"name": "guangxian", "bone": "guangxian", "attachment": "guangxian", "blend": "additive"}, {"name": "guangxian2", "bone": "guangxian2", "attachment": "guangxian", "blend": "additive"}, {"name": "guangxian3", "bone": "guangxian3", "attachment": "guangxian", "blend": "additive"}, {"name": "VIP_Monthly", "bone": "ka", "attachment": "VIP_Weekly"}, {"name": "VIP_Monthly_font1", "bone": "ka", "attachment": "VIP_Weekly_font1"}, {"name": "quan", "bone": "quan", "attachment": "quan", "blend": "additive"}, {"name": "pian", "bone": "pian", "attachment": "pian", "blend": "additive"}, {"name": "pian7", "bone": "pian7", "attachment": "pian", "blend": "additive"}, {"name": "pian6", "bone": "pian6", "attachment": "pian", "blend": "additive"}, {"name": "pian5", "bone": "pian5", "attachment": "pian", "blend": "additive"}, {"name": "pian4", "bone": "pian4", "attachment": "pian", "blend": "additive"}, {"name": "pian3", "bone": "pian3", "attachment": "pian", "blend": "additive"}, {"name": "pian2", "bone": "pian2", "attachment": "pian", "blend": "additive"}, {"name": "xing0001", "bone": "xing0001", "attachment": "xing0014", "blend": "additive"}, {"name": "xing1", "bone": "xing1", "attachment": "xing0014", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"VIP_Monthly": {"VIP_Monthly": {"width": 419, "height": 308}, "VIP_Weekly": {"width": 419, "height": 307}}, "VIP_Monthly_font1": {"VIP_Monthly_font1": {"x": 8.01, "y": -21.17, "rotation": 0.62, "width": 120, "height": 50}, "VIP_Monthly_font2": {"x": 8.01, "y": -25.67, "rotation": 0.62, "width": 120, "height": 50}, "VIP_Weekly_font1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74.41, -50.35, -45.59, -51.65, -46.13, -1.65, 73.87, -0.35], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 50}, "VIP_Weekly_font2": {"x": 14.65, "y": -33.71, "rotation": 0.62, "width": 120, "height": 50}}, "guangxian": {"guangxian": {"type": "mesh", "uvs": [0.85381, 0.99999, 0.14619, 0.99999, 0, 0, 1, 0], "triangles": [0, 2, 3, 1, 2, 0], "vertices": [47.35, -15.18, -43.93, -15.18, -62.79, 283.82, 66.21, 283.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 299}}, "guangxian2": {"guangxian": {"type": "mesh", "uvs": [0.85381, 0.99999, 0.14619, 0.99999, 0, 0, 1, 0], "triangles": [0, 2, 3, 1, 2, 0], "vertices": [47.35, -15.18, -43.93, -15.18, -62.79, 283.82, 66.21, 283.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 299}}, "guangxian3": {"guangxian": {"type": "mesh", "uvs": [0.85381, 0.99999, 0.14619, 0.99999, 0, 0, 1, 0], "triangles": [0, 2, 3, 1, 2, 0], "vertices": [47.35, -15.18, -43.93, -15.18, -62.79, 283.82, 66.21, 283.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 299}}, "pian": {"pian": {"x": -15.56, "y": 1.56, "width": 261, "height": 238}}, "pian2": {"pian": {"x": -2.22, "y": -6.96, "scaleX": 0.597, "scaleY": 0.597, "rotation": 67.58, "width": 261, "height": 238}}, "pian3": {"pian": {"x": -13.18, "y": 5.1, "scaleX": 0.882, "scaleY": 0.882, "rotation": -12.51, "width": 261, "height": 238}}, "pian4": {"pian": {"x": -12.55, "y": 3.21, "rotation": -12.22, "width": 261, "height": 238}}, "pian5": {"pian": {"x": -1.17, "y": -3.28, "scaleX": 0.281, "scaleY": 0.281, "rotation": 73.05, "width": 261, "height": 238}}, "pian6": {"pian": {"x": -7.53, "y": -10.33, "rotation": 47.85, "width": 261, "height": 238}}, "pian7": {"pian": {"x": -0.47, "y": 9, "scaleX": 0.573, "scaleY": 0.573, "rotation": -79.82, "width": 261, "height": 238}}, "quan": {"quan": {"x": 0.46, "y": 0.42, "width": 87, "height": 87}}, "xing0001": {"xing0001": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0002": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0003": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0004": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0005": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0006": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0007": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0008": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0009": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0010": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0011": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0012": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0013": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0014": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}}, "xing1": {"xing0001": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0002": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0003": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0004": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0005": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0006": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0007": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0008": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0009": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0010": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0011": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0012": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0013": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}, "xing0014": {"x": 0.47, "y": 0.52, "width": 70, "height": 70}}}}], "animations": {"yueka": {"slots": {"VIP_Monthly": {"attachment": [{"name": "VIP_Monthly"}]}, "VIP_Monthly_font1": {"attachment": [{"name": "VIP_Monthly_font1"}]}, "guangxian": {"color": [{"color": "ffffffa5"}, {"time": 0.4667, "color": "ffffff78"}, {"time": 1.8, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffffa5"}]}, "guangxian2": {"color": [{"color": "ffffffff"}, {"time": 1.3333, "color": "ffffff78"}, {"time": 2.6667, "color": "ffffffff"}]}, "guangxian3": {"color": [{"color": "ffffffa5"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff78"}, {"time": 2.6667, "color": "ffffffa5"}]}, "pian": {"color": [{"time": 0.3, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "pian2": {"color": [{"color": "ffffff88"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff88"}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff98"}]}, "pian3": {"color": [{"color": "ffffffdd"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffffdd"}, {"time": 2.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffffed"}]}, "pian4": {"color": [{"color": "ffffff11"}, {"time": 0.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff11"}, {"time": 1.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff21"}]}, "pian5": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 2.4, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff"}]}, "pian6": {"color": [{"color": "ffffff55"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff55"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff65"}]}, "pian7": {"color": [{"color": "ffffffaa"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffffaa"}, {"time": 2.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffffba"}]}, "quan": {"color": [{"color": "ffffff11"}, {"time": 0.7333, "color": "ffffff33"}, {"time": 1.3333, "color": "ffffff11"}, {"time": 2.0667, "color": "ffffff33"}, {"time": 2.6667, "color": "ffffff11"}]}, "xing0001": {"attachment": [{"name": null}]}, "xing1": {"attachment": [{"name": null}]}}, "bones": {"ka": {"translate": [{"y": -1.93}, {"time": 1.5333, "y": 3.5}, {"time": 2.6667, "y": -1.93}], "shear": [{"x": 1.43, "y": -0.57}, {"time": 0.3667, "x": 2}, {"time": 1.7333, "y": -2}, {"time": 2.6667, "x": 1.43, "y": -0.57}]}, "guangxian": {"translate": [{"x": -714.73, "y": 96.44}], "scale": [{"x": 0.927, "y": 0.927}]}, "guangxian2": {"rotate": [{"angle": 19.28}], "translate": [{"x": -563.43, "y": 117.3}], "scale": [{"x": 0.82, "y": 0.82}]}, "guangxian3": {"translate": [{"x": -803.26, "y": 59.11}], "scale": [{"x": 0.543, "y": 0.543}]}, "pian": {"translate": [{"x": -9.3, "y": -8.85}, {"time": 1.3333, "x": -6.09, "y": 180.88, "curve": "stepped"}, {"time": 1.3667, "x": -9.3, "y": -8.85}, {"time": 2.6667, "x": -6.09, "y": 180.88}], "scale": [{"x": 0.175, "y": 0.175}, {"time": 0.2, "x": 1.164, "y": 1.164}, {"time": 0.2667, "x": 0.629, "y": 0.629}, {"time": 0.3, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3667, "x": 0.175, "y": 0.175}, {"time": 1.5667, "x": 1.164, "y": 1.164}, {"time": 1.6333, "x": 0.629, "y": 0.629}, {"time": 1.7333, "x": 0.702, "y": 0.702}]}, "pian2": {"translate": [{"y": 103.39}, {"time": 0.5333, "y": 180.88, "curve": "stepped"}, {"time": 0.5667, "y": -12.85}, {"time": 1.3333, "y": 103.39}, {"time": 1.8667, "y": 180.88, "curve": "stepped"}, {"time": 1.9, "y": -12.85}, {"time": 2.6667, "y": 93.7}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.5667, "x": 0.175, "y": 0.175}, {"time": 0.7667, "x": 1.164, "y": 1.164}, {"time": 0.8333, "x": 0.629, "y": 0.629}, {"time": 1, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.9, "x": 0.175, "y": 0.175}, {"time": 2.1, "x": 1.164, "y": 1.164}, {"time": 2.2333, "x": 0.629, "y": 0.629}, {"time": 2.3333, "x": 0.702, "y": 0.702}]}, "pian3": {"translate": [{"x": 2.03, "y": 57.56}, {"time": 0.8333, "y": 145.82, "curve": "stepped"}, {"time": 1, "x": 3.12, "y": 10.03}, {"time": 1.3333, "x": 2.03, "y": 57.56}, {"time": 2.2333, "y": 145.82, "curve": "stepped"}, {"time": 2.3333, "x": 3.12, "y": 10.03}, {"time": 2.6667, "x": 2.18, "y": 50.77}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1, "x": 0.175, "y": 0.175}, {"time": 1.0667, "x": 1.164, "y": 1.164}, {"time": 1.2667, "x": 0.629, "y": 0.629}, {"time": 1.3, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.3333, "x": 0.175, "y": 0.175}, {"time": 2.4, "x": 1.164, "y": 1.164}, {"time": 2.6, "x": 0.629, "y": 0.629}, {"time": 2.6333, "x": 0.702, "y": 0.702}]}, "pian4": {"rotate": [{"angle": 9.19}, {"time": 0.0333, "angle": 9.68, "curve": "stepped"}, {"time": 0.2}, {"time": 1.3333, "angle": 9.19}, {"time": 1.3667, "angle": 9.68, "curve": "stepped"}, {"time": 1.5333}, {"time": 2.6667, "angle": 8.71}], "translate": [{"y": 151.87}, {"time": 0.0333, "y": 164.12}, {"time": 0.2, "y": -1.19}, {"time": 1.3333, "y": 151.87}, {"time": 1.3667, "y": 164.12}, {"time": 1.5333, "y": -1.19}, {"time": 2.6667, "y": 151.87}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.2, "x": 0.175, "y": 0.175}, {"time": 0.2667, "x": 1.164, "y": 1.164}, {"time": 0.4667, "x": 0.629, "y": 0.629}, {"time": 0.5, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.5333, "x": 0.175, "y": 0.175}, {"time": 1.6, "x": 1.164, "y": 1.164}, {"time": 1.8, "x": 0.629, "y": 0.629}, {"time": 1.8333, "x": 0.702, "y": 0.702}]}, "pian5": {"translate": [{"x": -8.66, "y": 35.69}, {"time": 1.0667, "x": -6.09, "y": 213.84, "curve": "stepped"}, {"time": 1.1, "x": -9.3, "y": -8.85}, {"time": 1.3333, "x": -8.66, "y": 35.69}, {"time": 2.4, "x": -6.09, "y": 213.84, "curve": "stepped"}, {"time": 2.5, "x": -9.3, "y": -8.85}, {"time": 2.6667, "x": -8.82, "y": 24.56}], "scale": [{"x": 0.629, "y": 0.629}, {"time": 0.0333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1, "x": 0.175, "y": 0.175}, {"time": 1.3, "x": 1.164, "y": 1.164}, {"time": 1.3333, "x": 0.629, "y": 0.629}, {"time": 1.3667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.5, "x": 0.175, "y": 0.175}, {"time": 2.6333, "x": 1.164, "y": 1.164}, {"time": 2.6667, "x": 0.896, "y": 0.896}]}, "pian6": {"translate": [{"x": -6.89, "y": 121.05}, {"time": 0.3, "x": -6.09, "y": 164.35, "curve": "stepped"}, {"time": 0.4667, "x": -9.3, "y": -8.85}, {"time": 1.3333, "x": -6.89, "y": 121.05}, {"time": 1.6333, "x": -6.09, "y": 164.35, "curve": "stepped"}, {"time": 1.8, "x": -9.3, "y": -8.85}, {"time": 2.6667, "x": -7.05, "y": 112.39}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.4667, "x": 0.175, "y": 0.175}, {"time": 0.5333, "x": 1.164, "y": 1.164}, {"time": 0.7333, "x": 0.629, "y": 0.629}, {"time": 0.7667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.8, "x": 0.175, "y": 0.175}, {"time": 1.8667, "x": 1.164, "y": 1.164}, {"time": 2.0667, "x": 0.629, "y": 0.629}, {"time": 2.1, "x": 0.702, "y": 0.702}]}, "pian7": {"translate": [{"x": 4.64, "y": 70.95}, {"time": 0.7333, "x": 18.57, "y": 150.74, "curve": "stepped"}, {"time": 0.7667, "x": -9.3, "y": -8.85}, {"time": 1.3333, "x": 4.64, "y": 70.95}, {"time": 2.0667, "x": 18.57, "y": 150.74, "curve": "stepped"}, {"time": 2.1, "x": -9.3, "y": -8.85}, {"time": 2.6667, "x": 3.24, "y": 62.97}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.7667, "x": 0.175, "y": 0.175}, {"time": 0.8333, "x": 1.164, "y": 1.164}, {"time": 1.0333, "x": 0.629, "y": 0.629}, {"time": 1.0667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.1, "x": 0.175, "y": 0.175}, {"time": 2.2333, "x": 1.164, "y": 1.164}, {"time": 2.3667, "x": 0.629, "y": 0.629}, {"time": 2.4, "x": 0.702, "y": 0.702}]}, "quan": {"rotate": [{"angle": -15.07}], "translate": [{"x": -461.03, "y": 80.79}], "scale": [{"x": 3.199, "y": 2.108}]}}, "deform": {"default": {"guangxian": {"guangxian": [{}, {"time": 1.3333, "offset": 4, "vertices": [-35.896, 0, 42.94695]}, {"time": 2.6667}]}, "guangxian2": {"guangxian": [{"offset": 4, "vertices": [-23.93062, -3e-05, 28.63136]}, {"time": 0.8667}, {"time": 2.2667, "offset": 4, "vertices": [-35.89597, -3e-05, 42.94701]}, {"time": 2.6667, "offset": 4, "vertices": [-23.93062, -3e-05, 28.63136]}]}, "guangxian3": {"guangxian": [{"offset": 4, "vertices": [-23.93063, -3e-05, 28.6313]}, {"time": 0.4667, "offset": 4, "vertices": [-35.89597, -3e-05, 42.94695]}, {"time": 1.8}, {"time": 2.6667, "offset": 4, "vertices": [-23.93063, -3e-05, 28.6313]}]}}}}, "yueka2": {"slots": {"VIP_Monthly": {"attachment": [{"name": "VIP_Monthly"}]}, "VIP_Monthly_font1": {"attachment": [{"name": "VIP_Monthly_font2"}]}, "guangxian": {"color": [{"color": "ffffffa5"}, {"time": 0.4667, "color": "ffffff78"}, {"time": 1.7667, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffffa5"}]}, "guangxian2": {"color": [{"color": "ffffffff"}, {"time": 1.3333, "color": "ffffff78"}, {"time": 2.6667, "color": "ffffffff"}]}, "guangxian3": {"color": [{"color": "ffffffa5"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff78"}, {"time": 2.6667, "color": "ffffffa5"}]}, "pian": {"color": [{"time": 0.3, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "pian2": {"color": [{"color": "ffffff88"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff88"}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff98"}]}, "pian3": {"color": [{"color": "ffffffdd"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffffdd"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffffed"}]}, "pian4": {"color": [{"color": "ffffff11"}, {"time": 0.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff11"}, {"time": 1.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff21"}]}, "pian5": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 2.4, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}]}, "pian6": {"color": [{"color": "ffffff55"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff55"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff65"}]}, "pian7": {"color": [{"color": "ffffffaa"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffffaa"}, {"time": 2.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffffba"}]}, "quan": {"color": [{"color": "ffffff11"}, {"time": 0.7333, "color": "ffffff33"}, {"time": 1.3333, "color": "ffffff11"}, {"time": 2.0333, "color": "ffffff33"}, {"time": 2.6667, "color": "ffffff11"}]}, "xing0001": {"attachment": [{"name": null}]}, "xing1": {"attachment": [{"name": null}]}}, "bones": {"ka": {"translate": [{"y": -1.93}, {"time": 1.5333, "y": 3.5}, {"time": 2.6667, "y": -1.93}], "shear": [{"x": 1.43, "y": -0.57}, {"time": 0.3667, "x": 2}, {"time": 1.7, "y": -2}, {"time": 2.6667, "x": 1.43, "y": -0.57}]}, "guangxian": {"translate": [{"x": -714.73, "y": 96.44}], "scale": [{"x": 0.927, "y": 0.927}]}, "guangxian2": {"rotate": [{"angle": 19.28}], "translate": [{"x": -563.43, "y": 117.3}], "scale": [{"x": 0.82, "y": 0.82}]}, "guangxian3": {"translate": [{"x": -803.26, "y": 59.11}], "scale": [{"x": 0.543, "y": 0.543}]}, "pian": {"translate": [{"x": -9.3, "y": -8.85}, {"time": 1.3333, "x": -6.09, "y": 180.88, "curve": "stepped"}, {"time": 1.3667, "x": -9.3, "y": -8.85}, {"time": 2.6667, "x": -6.09, "y": 180.88}], "scale": [{"x": 0.175, "y": 0.175}, {"time": 0.2, "x": 1.164, "y": 1.164}, {"time": 0.2667, "x": 0.629, "y": 0.629}, {"time": 0.3, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3667, "x": 0.175, "y": 0.175}, {"time": 1.5667, "x": 1.164, "y": 1.164}, {"time": 1.6333, "x": 0.629, "y": 0.629}, {"time": 1.7, "x": 0.702, "y": 0.702}]}, "pian2": {"translate": [{"y": 103.39}, {"time": 0.5333, "y": 180.88, "curve": "stepped"}, {"time": 0.5667, "y": -12.85}, {"time": 1.3333, "y": 103.39}, {"time": 1.8667, "y": 180.88, "curve": "stepped"}, {"time": 1.9, "y": -12.85}, {"time": 2.6667, "y": 93.7}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.5667, "x": 0.175, "y": 0.175}, {"time": 0.7667, "x": 1.164, "y": 1.164}, {"time": 0.8333, "x": 0.629, "y": 0.629}, {"time": 1, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.9, "x": 0.175, "y": 0.175}, {"time": 2.1, "x": 1.164, "y": 1.164}, {"time": 2.2, "x": 0.629, "y": 0.629}, {"time": 2.3, "x": 0.702, "y": 0.702}]}, "pian3": {"translate": [{"x": 2.03, "y": 57.56}, {"time": 0.8333, "y": 145.82, "curve": "stepped"}, {"time": 1, "x": 3.12, "y": 10.03}, {"time": 1.3333, "x": 2.03, "y": 57.56}, {"time": 2.2, "y": 145.82, "curve": "stepped"}, {"time": 2.3, "x": 3.12, "y": 10.03}, {"time": 2.6667, "x": 2.18, "y": 50.77}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1, "x": 0.175, "y": 0.175}, {"time": 1.0667, "x": 1.164, "y": 1.164}, {"time": 1.2667, "x": 0.629, "y": 0.629}, {"time": 1.3, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.3, "x": 0.175, "y": 0.175}, {"time": 2.4, "x": 1.164, "y": 1.164}, {"time": 2.5667, "x": 0.629, "y": 0.629}, {"time": 2.6, "x": 0.702, "y": 0.702}]}, "pian4": {"rotate": [{"angle": 9.19}, {"time": 0.0333, "angle": 9.68, "curve": "stepped"}, {"time": 0.2}, {"time": 1.3333, "angle": 9.19}, {"time": 1.3667, "angle": 9.68, "curve": "stepped"}, {"time": 1.5333}, {"time": 2.6667, "angle": 8.71}], "translate": [{"y": 151.87}, {"time": 0.0333, "y": 164.12}, {"time": 0.2, "y": -1.19}, {"time": 1.3333, "y": 151.87}, {"time": 1.3667, "y": 164.12}, {"time": 1.5333, "y": -1.19}, {"time": 2.6667, "y": 151.87}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.2, "x": 0.175, "y": 0.175}, {"time": 0.2667, "x": 1.164, "y": 1.164}, {"time": 0.4667, "x": 0.629, "y": 0.629}, {"time": 0.5, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.5333, "x": 0.175, "y": 0.175}, {"time": 1.6, "x": 1.164, "y": 1.164}, {"time": 1.7667, "x": 0.629, "y": 0.629}, {"time": 1.8333, "x": 0.702, "y": 0.702}]}, "pian5": {"translate": [{"x": -8.66, "y": 35.69}, {"time": 1.0667, "x": -6.09, "y": 213.84, "curve": "stepped"}, {"time": 1.1, "x": -9.3, "y": -8.85}, {"time": 1.3333, "x": -8.66, "y": 35.69}, {"time": 2.4, "x": -6.09, "y": 213.84, "curve": "stepped"}, {"time": 2.4667, "x": -9.3, "y": -8.85}, {"time": 2.6667, "x": -8.82, "y": 24.56}], "scale": [{"x": 0.629, "y": 0.629}, {"time": 0.0333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1, "x": 0.175, "y": 0.175}, {"time": 1.3, "x": 1.164, "y": 1.164}, {"time": 1.3333, "x": 0.629, "y": 0.629}, {"time": 1.3667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.4667, "x": 0.175, "y": 0.175}, {"time": 2.6, "x": 1.164, "y": 1.164}, {"time": 2.6667, "x": 0.896, "y": 0.896}]}, "pian6": {"translate": [{"x": -6.89, "y": 121.05}, {"time": 0.3, "x": -6.09, "y": 164.35, "curve": "stepped"}, {"time": 0.4667, "x": -9.3, "y": -8.85}, {"time": 1.3333, "x": -6.89, "y": 121.05}, {"time": 1.6333, "x": -6.09, "y": 164.35, "curve": "stepped"}, {"time": 1.7667, "x": -9.3, "y": -8.85}, {"time": 2.6667, "x": -7.05, "y": 112.39}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.4667, "x": 0.175, "y": 0.175}, {"time": 0.5333, "x": 1.164, "y": 1.164}, {"time": 0.7333, "x": 0.629, "y": 0.629}, {"time": 0.7667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.7667, "x": 0.175, "y": 0.175}, {"time": 1.8667, "x": 1.164, "y": 1.164}, {"time": 2.0333, "x": 0.629, "y": 0.629}, {"time": 2.1, "x": 0.702, "y": 0.702}]}, "pian7": {"translate": [{"x": 4.64, "y": 70.95}, {"time": 0.7333, "x": 18.57, "y": 150.74, "curve": "stepped"}, {"time": 0.7667, "x": -9.3, "y": -8.85}, {"time": 1.3333, "x": 4.64, "y": 70.95}, {"time": 2.0333, "x": 18.57, "y": 150.74, "curve": "stepped"}, {"time": 2.1, "x": -9.3, "y": -8.85}, {"time": 2.6667, "x": 3.24, "y": 62.97}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.7667, "x": 0.175, "y": 0.175}, {"time": 0.8333, "x": 1.164, "y": 1.164}, {"time": 1.0333, "x": 0.629, "y": 0.629}, {"time": 1.0667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.1, "x": 0.175, "y": 0.175}, {"time": 2.2, "x": 1.164, "y": 1.164}, {"time": 2.3667, "x": 0.629, "y": 0.629}, {"time": 2.4, "x": 0.702, "y": 0.702}]}, "quan": {"rotate": [{"angle": -15.07}], "translate": [{"x": -461.03, "y": 80.79}], "scale": [{"x": 3.199, "y": 2.108}]}}, "deform": {"default": {"guangxian": {"guangxian": [{}, {"time": 1.3333, "offset": 4, "vertices": [-35.896, 0, 42.94695]}, {"time": 2.6667}]}, "guangxian2": {"guangxian": [{"offset": 4, "vertices": [-23.93062, -3e-05, 28.63136]}, {"time": 0.8667}, {"time": 2.2333, "offset": 4, "vertices": [-35.89597, -3e-05, 42.94701]}, {"time": 2.6667, "offset": 4, "vertices": [-23.93062, -3e-05, 28.63136]}]}, "guangxian3": {"guangxian": [{"offset": 4, "vertices": [-23.93063, -3e-05, 28.6313]}, {"time": 0.4667, "offset": 4, "vertices": [-35.89597, -3e-05, 42.94695]}, {"time": 1.7667}, {"time": 2.6667, "offset": 4, "vertices": [-23.93063, -3e-05, 28.6313]}]}}}}, "zhouka": {"slots": {"guangxian": {"color": [{"color": "ffffffa5"}, {"time": 0.4, "color": "ffffff78"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffa5"}], "attachment": [{"name": null}]}, "guangxian2": {"color": [{"color": "ffffffff"}, {"time": 1.1667, "color": "ffffff78"}, {"time": 2.3333, "color": "ffffffff"}], "attachment": [{"name": null}]}, "guangxian3": {"color": [{"color": "ffffffa5"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff78"}, {"time": 2.3333, "color": "ffffffa5"}], "attachment": [{"name": null}]}, "pian": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "pian2": {"color": [{"color": "ffffff88"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff88"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff98"}], "attachment": [{"name": null}]}, "pian3": {"color": [{"color": "ffffffdd"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffffdd"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffed"}], "attachment": [{"name": null}]}, "pian4": {"color": [{"color": "ffffff11"}, {"time": 0.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff11"}, {"time": 1.2, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff21"}], "attachment": [{"name": null}]}, "pian5": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff"}], "attachment": [{"name": null}]}, "pian6": {"color": [{"color": "ffffff55"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff55"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff65"}], "attachment": [{"name": null}]}, "pian7": {"color": [{"color": "ffffffaa"}, {"time": 0.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffffaa"}, {"time": 1.8, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffba"}], "attachment": [{"name": null}]}, "quan": {"color": [{"color": "ffffff11"}, {"time": 0.7333, "color": "ffffff2b"}, {"time": 1.3333, "color": "ffffff11"}, {"time": 2.0667, "color": "ffffff2b"}, {"time": 2.6667, "color": "ffffff11"}]}, "xing0001": {"attachment": [{"name": null}, {"time": 0.0667, "name": "xing0001"}, {"time": 0.1, "name": "xing0002"}, {"time": 0.1333, "name": "xing0003"}, {"time": 0.1667, "name": "xing0004"}, {"time": 0.2333, "name": "xing0005"}, {"time": 0.3, "name": "xing0006"}, {"time": 0.3667, "name": "xing0007"}, {"time": 0.4333, "name": "xing0008"}, {"time": 0.5, "name": "xing0009"}, {"time": 0.5667, "name": "xing0010"}, {"time": 0.6, "name": "xing0011"}, {"time": 0.6333, "name": "xing0012"}, {"time": 0.6667, "name": "xing0013"}, {"time": 0.7, "name": "xing0014"}, {"time": 0.7333, "name": null}, {"time": 1.5, "name": "xing0001"}, {"time": 1.5333, "name": "xing0002"}, {"time": 1.5667, "name": "xing0003"}, {"time": 1.6, "name": "xing0004"}, {"time": 1.6667, "name": "xing0005"}, {"time": 1.7333, "name": "xing0006"}, {"time": 1.8, "name": "xing0007"}, {"time": 1.8667, "name": "xing0008"}, {"time": 1.9333, "name": "xing0009"}, {"time": 2, "name": "xing0010"}, {"time": 2.0333, "name": "xing0011"}, {"time": 2.0667, "name": "xing0012"}, {"time": 2.1, "name": "xing0013"}, {"time": 2.1333, "name": "xing0014"}, {"time": 2.1667, "name": null}]}, "xing1": {"attachment": [{"name": null}, {"time": 0.5, "name": "xing0001"}, {"time": 0.5333, "name": "xing0002"}, {"time": 0.5667, "name": "xing0003"}, {"time": 0.6, "name": "xing0004"}, {"time": 0.6667, "name": "xing0005"}, {"time": 0.7333, "name": "xing0006"}, {"time": 0.8, "name": "xing0007"}, {"time": 0.8667, "name": "xing0008"}, {"time": 0.9333, "name": "xing0009"}, {"time": 1, "name": "xing0010"}, {"time": 1.0333, "name": "xing0011"}, {"time": 1.0667, "name": "xing0012"}, {"time": 1.1, "name": "xing0013"}, {"time": 1.1333, "name": "xing0014"}, {"time": 1.1667, "name": null}]}}, "bones": {"guangxian": {"translate": [{"x": -714.73, "y": 96.44}], "scale": [{"x": 0.927, "y": 0.927}]}, "guangxian2": {"rotate": [{"angle": -4.53}], "translate": [{"x": -624.8, "y": 92.98}], "scale": [{"x": 0.82, "y": 0.82}]}, "guangxian3": {"translate": [{"x": -815.99, "y": 148.27}], "scale": [{"x": 0.543, "y": 0.543}]}, "pian": {"translate": [{"x": -9.3, "y": -8.85}, {"time": 1.1667, "x": -6.09, "y": 180.88, "curve": "stepped"}, {"time": 1.2, "x": -9.3, "y": -8.85}, {"time": 2.3333, "x": -6.09, "y": 180.88}], "scale": [{"x": 0.175, "y": 0.175}, {"time": 0.1667, "x": 1.164, "y": 1.164}, {"time": 0.2333, "x": 0.629, "y": 0.629}, {"time": 0.2667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.2, "x": 0.175, "y": 0.175}, {"time": 1.3667, "x": 1.164, "y": 1.164}, {"time": 1.4333, "x": 0.629, "y": 0.629}, {"time": 1.5, "x": 0.702, "y": 0.702}]}, "pian2": {"translate": [{"y": 103.39}, {"time": 0.4667, "y": 180.88, "curve": "stepped"}, {"time": 0.5, "y": -12.85}, {"time": 1.1667, "y": 103.39}, {"time": 1.6333, "y": 180.88, "curve": "stepped"}, {"time": 1.6667, "y": -12.85}, {"time": 2.3333, "y": 93.7}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.5, "x": 0.175, "y": 0.175}, {"time": 0.6667, "x": 1.164, "y": 1.164}, {"time": 0.7333, "x": 0.629, "y": 0.629}, {"time": 0.8667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.6667, "x": 0.175, "y": 0.175}, {"time": 1.8333, "x": 1.164, "y": 1.164}, {"time": 1.9333, "x": 0.629, "y": 0.629}, {"time": 2.0333, "x": 0.702, "y": 0.702}]}, "pian3": {"translate": [{"x": 2.03, "y": 57.56}, {"time": 0.7333, "y": 145.82, "curve": "stepped"}, {"time": 0.8667, "x": 3.12, "y": 10.03}, {"time": 1.1667, "x": 2.03, "y": 57.56}, {"time": 1.9333, "y": 145.82, "curve": "stepped"}, {"time": 2.0333, "x": 3.12, "y": 10.03}, {"time": 2.3333, "x": 2.18, "y": 50.77}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.8667, "x": 0.175, "y": 0.175}, {"time": 0.9333, "x": 1.164, "y": 1.164}, {"time": 1.1, "x": 0.629, "y": 0.629}, {"time": 1.1333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.0333, "x": 0.175, "y": 0.175}, {"time": 2.1, "x": 1.164, "y": 1.164}, {"time": 2.2667, "x": 0.629, "y": 0.629}, {"time": 2.3, "x": 0.702, "y": 0.702}]}, "pian4": {"rotate": [{"angle": 9.19}, {"time": 0.0333, "angle": 9.68, "curve": "stepped"}, {"time": 0.1667}, {"time": 1.1667, "angle": 9.19}, {"time": 1.2, "angle": 9.68, "curve": "stepped"}, {"time": 1.3333}, {"time": 2.3333, "angle": 8.71}], "translate": [{"y": 151.87}, {"time": 0.0333, "y": 164.12}, {"time": 0.1667, "y": -1.19}, {"time": 1.1667, "y": 151.87}, {"time": 1.2, "y": 164.12}, {"time": 1.3333, "y": -1.19}, {"time": 2.3333, "y": 151.87}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.1667, "x": 0.175, "y": 0.175}, {"time": 0.2333, "x": 1.164, "y": 1.164}, {"time": 0.4, "x": 0.629, "y": 0.629}, {"time": 0.4333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.175, "y": 0.175}, {"time": 1.4, "x": 1.164, "y": 1.164}, {"time": 1.5667, "x": 0.629, "y": 0.629}, {"time": 1.6, "x": 0.702, "y": 0.702}]}, "pian5": {"translate": [{"x": -8.66, "y": 35.69}, {"time": 0.9333, "x": -6.09, "y": 213.84, "curve": "stepped"}, {"time": 0.9667, "x": -9.3, "y": -8.85}, {"time": 1.1667, "x": -8.66, "y": 35.69}, {"time": 2.1, "x": -6.09, "y": 213.84, "curve": "stepped"}, {"time": 2.1667, "x": -9.3, "y": -8.85}, {"time": 2.3333, "x": -8.82, "y": 24.56}], "scale": [{"x": 0.629, "y": 0.629}, {"time": 0.0333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.9667, "x": 0.175, "y": 0.175}, {"time": 1.1333, "x": 1.164, "y": 1.164}, {"time": 1.1667, "x": 0.629, "y": 0.629}, {"time": 1.2, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.1667, "x": 0.175, "y": 0.175}, {"time": 2.3, "x": 1.164, "y": 1.164}, {"time": 2.3333, "x": 0.896, "y": 0.896}]}, "pian6": {"translate": [{"x": -6.89, "y": 121.05}, {"time": 0.2667, "x": -6.09, "y": 164.35, "curve": "stepped"}, {"time": 0.4, "x": -9.3, "y": -8.85}, {"time": 1.1667, "x": -6.89, "y": 121.05}, {"time": 1.4333, "x": -6.09, "y": 164.35, "curve": "stepped"}, {"time": 1.5667, "x": -9.3, "y": -8.85}, {"time": 2.3333, "x": -7.05, "y": 112.39}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.4, "x": 0.175, "y": 0.175}, {"time": 0.4667, "x": 1.164, "y": 1.164}, {"time": 0.6333, "x": 0.629, "y": 0.629}, {"time": 0.6667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.5667, "x": 0.175, "y": 0.175}, {"time": 1.6333, "x": 1.164, "y": 1.164}, {"time": 1.8, "x": 0.629, "y": 0.629}, {"time": 1.8333, "x": 0.702, "y": 0.702}]}, "pian7": {"translate": [{"x": 4.64, "y": 70.95}, {"time": 0.6333, "x": 18.57, "y": 150.74, "curve": "stepped"}, {"time": 0.6667, "x": -9.3, "y": -8.85}, {"time": 1.1667, "x": 4.64, "y": 70.95}, {"time": 1.8, "x": 18.57, "y": 150.74, "curve": "stepped"}, {"time": 1.8333, "x": -9.3, "y": -8.85}, {"time": 2.3333, "x": 3.24, "y": 62.97}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.6667, "x": 0.175, "y": 0.175}, {"time": 0.7333, "x": 1.164, "y": 1.164}, {"time": 0.9, "x": 0.629, "y": 0.629}, {"time": 0.9333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.8333, "x": 0.175, "y": 0.175}, {"time": 1.9333, "x": 1.164, "y": 1.164}, {"time": 2.0667, "x": 0.629, "y": 0.629}, {"time": 2.1, "x": 0.702, "y": 0.702}]}, "quan": {"rotate": [{"angle": 9.84}], "translate": [{"x": -447.84, "y": 82.19}], "scale": [{"x": 3.502, "y": 2.308}]}, "xing0001": {"translate": [{"x": -387.23, "y": -219.48, "curve": "stepped"}, {"time": 1.5, "x": -453.65, "y": -329.61}], "scale": [{"x": 1.246, "y": 1.246, "curve": "stepped"}, {"time": 1.5, "x": 0.886, "y": 0.886}]}, "xing1": {"translate": [{"time": 0.4333, "x": -281.65, "y": -259.36}], "scale": [{"time": 0.4333, "x": 1.246, "y": 1.246}]}, "ka": {"translate": [{"y": 2.55}, {"time": 0.2667, "y": 3.5}, {"time": 1.4333, "y": -1.93}, {"time": 2.6667, "y": 2.55}], "shear": [{"x": 0.46, "y": -1.54}, {"time": 1.0333, "x": 2}, {"time": 2.3667, "y": -2}, {"time": 2.6667, "x": 0.46, "y": -1.54}]}}, "deform": {"default": {"guangxian": {"guangxian": [{}, {"time": 1.1667, "offset": 4, "vertices": [-35.896, 0, 42.94695]}, {"time": 2.3333}]}, "guangxian2": {"guangxian": [{"offset": 4, "vertices": [-23.93062, -3e-05, 28.63136]}, {"time": 0.7667}, {"time": 1.9667, "offset": 4, "vertices": [-35.89597, -3e-05, 42.94701]}, {"time": 2.3333, "offset": 4, "vertices": [-23.93062, -3e-05, 28.63136]}]}, "guangxian3": {"guangxian": [{"offset": 4, "vertices": [-23.93063, -3e-05, 28.6313]}, {"time": 0.4, "offset": 4, "vertices": [-35.89597, -3e-05, 42.94695]}, {"time": 1.5667}, {"time": 2.3333, "offset": 4, "vertices": [-23.93063, -3e-05, 28.6313]}]}}}}, "zhouka2": {"slots": {"VIP_Monthly_font1": {"attachment": [{"name": "VIP_Weekly_font2"}]}, "guangxian": {"color": [{"color": "ffffffa5"}, {"time": 0.4, "color": "ffffff78"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffa5"}], "attachment": [{"name": null}]}, "guangxian2": {"color": [{"color": "ffffffff"}, {"time": 1.1667, "color": "ffffff78"}, {"time": 2.3333, "color": "ffffffff"}], "attachment": [{"name": null}]}, "guangxian3": {"color": [{"color": "ffffffa5"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff78"}, {"time": 2.3333, "color": "ffffffa5"}], "attachment": [{"name": null}]}, "pian": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "pian2": {"color": [{"color": "ffffff88"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff88"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff98"}], "attachment": [{"name": null}]}, "pian3": {"color": [{"color": "ffffffdd"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffffdd"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffed"}], "attachment": [{"name": null}]}, "pian4": {"color": [{"color": "ffffff11"}, {"time": 0.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff11"}, {"time": 1.2, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff21"}], "attachment": [{"name": null}]}, "pian5": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff"}], "attachment": [{"name": null}]}, "pian6": {"color": [{"color": "ffffff55"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff55"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff65"}], "attachment": [{"name": null}]}, "pian7": {"color": [{"color": "ffffffaa"}, {"time": 0.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffffaa"}, {"time": 1.8, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffba"}], "attachment": [{"name": null}]}, "quan": {"color": [{"color": "ffffff11"}, {"time": 0.7333, "color": "ffffff2b"}, {"time": 1.3333, "color": "ffffff11"}, {"time": 2.0667, "color": "ffffff2b"}, {"time": 2.6667, "color": "ffffff11"}]}, "xing0001": {"attachment": [{"name": null}, {"time": 0.0667, "name": "xing0001"}, {"time": 0.1, "name": "xing0002"}, {"time": 0.1333, "name": "xing0003"}, {"time": 0.1667, "name": "xing0004"}, {"time": 0.2333, "name": "xing0005"}, {"time": 0.3, "name": "xing0006"}, {"time": 0.3667, "name": "xing0007"}, {"time": 0.4333, "name": "xing0008"}, {"time": 0.5, "name": "xing0009"}, {"time": 0.5667, "name": "xing0010"}, {"time": 0.6, "name": "xing0011"}, {"time": 0.6333, "name": "xing0012"}, {"time": 0.6667, "name": "xing0013"}, {"time": 0.7, "name": "xing0014"}, {"time": 0.7333, "name": null}, {"time": 1.5, "name": "xing0001"}, {"time": 1.5333, "name": "xing0002"}, {"time": 1.5667, "name": "xing0003"}, {"time": 1.6, "name": "xing0004"}, {"time": 1.6667, "name": "xing0005"}, {"time": 1.7333, "name": "xing0006"}, {"time": 1.8, "name": "xing0007"}, {"time": 1.8667, "name": "xing0008"}, {"time": 1.9333, "name": "xing0009"}, {"time": 2, "name": "xing0010"}, {"time": 2.0333, "name": "xing0011"}, {"time": 2.0667, "name": "xing0012"}, {"time": 2.1, "name": "xing0013"}, {"time": 2.1333, "name": "xing0014"}, {"time": 2.1667, "name": null}]}, "xing1": {"attachment": [{"name": null}, {"time": 0.5, "name": "xing0001"}, {"time": 0.5333, "name": "xing0002"}, {"time": 0.5667, "name": "xing0003"}, {"time": 0.6, "name": "xing0004"}, {"time": 0.6667, "name": "xing0005"}, {"time": 0.7333, "name": "xing0006"}, {"time": 0.8, "name": "xing0007"}, {"time": 0.8667, "name": "xing0008"}, {"time": 0.9333, "name": "xing0009"}, {"time": 1, "name": "xing0010"}, {"time": 1.0333, "name": "xing0011"}, {"time": 1.0667, "name": "xing0012"}, {"time": 1.1, "name": "xing0013"}, {"time": 1.1333, "name": "xing0014"}, {"time": 1.1667, "name": null}]}}, "bones": {"ka": {"translate": [{"y": 2.55}, {"time": 0.2667, "y": 3.5}, {"time": 1.4, "y": -1.93}, {"time": 2.6667, "y": 2.55}], "shear": [{"x": 0.46, "y": -1.54}, {"time": 1.0333, "x": 2}, {"time": 2.3667, "y": -2}, {"time": 2.6667, "x": 0.46, "y": -1.54}]}, "guangxian": {"translate": [{"x": -714.73, "y": 96.44}], "scale": [{"x": 0.927, "y": 0.927}]}, "guangxian2": {"rotate": [{"angle": -4.53}], "translate": [{"x": -624.8, "y": 92.98}], "scale": [{"x": 0.82, "y": 0.82}]}, "guangxian3": {"translate": [{"x": -815.99, "y": 148.27}], "scale": [{"x": 0.543, "y": 0.543}]}, "pian": {"translate": [{"x": -9.3, "y": -8.85}, {"time": 1.1667, "x": -6.09, "y": 180.88, "curve": "stepped"}, {"time": 1.2, "x": -9.3, "y": -8.85}, {"time": 2.3333, "x": -6.09, "y": 180.88}], "scale": [{"x": 0.175, "y": 0.175}, {"time": 0.1667, "x": 1.164, "y": 1.164}, {"time": 0.2333, "x": 0.629, "y": 0.629}, {"time": 0.2667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.2, "x": 0.175, "y": 0.175}, {"time": 1.3667, "x": 1.164, "y": 1.164}, {"time": 1.4333, "x": 0.629, "y": 0.629}, {"time": 1.5, "x": 0.702, "y": 0.702}]}, "pian2": {"translate": [{"y": 103.39}, {"time": 0.4667, "y": 180.88, "curve": "stepped"}, {"time": 0.5, "y": -12.85}, {"time": 1.1667, "y": 103.39}, {"time": 1.6333, "y": 180.88, "curve": "stepped"}, {"time": 1.6667, "y": -12.85}, {"time": 2.3333, "y": 93.7}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.5, "x": 0.175, "y": 0.175}, {"time": 0.6667, "x": 1.164, "y": 1.164}, {"time": 0.7333, "x": 0.629, "y": 0.629}, {"time": 0.8667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.6667, "x": 0.175, "y": 0.175}, {"time": 1.8333, "x": 1.164, "y": 1.164}, {"time": 1.9333, "x": 0.629, "y": 0.629}, {"time": 2.0333, "x": 0.702, "y": 0.702}]}, "pian3": {"translate": [{"x": 2.03, "y": 57.56}, {"time": 0.7333, "y": 145.82, "curve": "stepped"}, {"time": 0.8667, "x": 3.12, "y": 10.03}, {"time": 1.1667, "x": 2.03, "y": 57.56}, {"time": 1.9333, "y": 145.82, "curve": "stepped"}, {"time": 2.0333, "x": 3.12, "y": 10.03}, {"time": 2.3333, "x": 2.18, "y": 50.77}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.8667, "x": 0.175, "y": 0.175}, {"time": 0.9333, "x": 1.164, "y": 1.164}, {"time": 1.1, "x": 0.629, "y": 0.629}, {"time": 1.1333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.0333, "x": 0.175, "y": 0.175}, {"time": 2.1, "x": 1.164, "y": 1.164}, {"time": 2.2667, "x": 0.629, "y": 0.629}, {"time": 2.3, "x": 0.702, "y": 0.702}]}, "pian4": {"rotate": [{"angle": 9.19}, {"time": 0.0333, "angle": 9.68, "curve": "stepped"}, {"time": 0.1667}, {"time": 1.1667, "angle": 9.19}, {"time": 1.2, "angle": 9.68, "curve": "stepped"}, {"time": 1.3333}, {"time": 2.3333, "angle": 8.71}], "translate": [{"y": 151.87}, {"time": 0.0333, "y": 164.12}, {"time": 0.1667, "y": -1.19}, {"time": 1.1667, "y": 151.87}, {"time": 1.2, "y": 164.12}, {"time": 1.3333, "y": -1.19}, {"time": 2.3333, "y": 151.87}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.1667, "x": 0.175, "y": 0.175}, {"time": 0.2333, "x": 1.164, "y": 1.164}, {"time": 0.4, "x": 0.629, "y": 0.629}, {"time": 0.4333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.3333, "x": 0.175, "y": 0.175}, {"time": 1.4, "x": 1.164, "y": 1.164}, {"time": 1.5667, "x": 0.629, "y": 0.629}, {"time": 1.6, "x": 0.702, "y": 0.702}]}, "pian5": {"translate": [{"x": -8.66, "y": 35.69}, {"time": 0.9333, "x": -6.09, "y": 213.84, "curve": "stepped"}, {"time": 0.9667, "x": -9.3, "y": -8.85}, {"time": 1.1667, "x": -8.66, "y": 35.69}, {"time": 2.1, "x": -6.09, "y": 213.84, "curve": "stepped"}, {"time": 2.1667, "x": -9.3, "y": -8.85}, {"time": 2.3333, "x": -8.82, "y": 24.56}], "scale": [{"x": 0.629, "y": 0.629}, {"time": 0.0333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.9667, "x": 0.175, "y": 0.175}, {"time": 1.1333, "x": 1.164, "y": 1.164}, {"time": 1.1667, "x": 0.629, "y": 0.629}, {"time": 1.2, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 2.1667, "x": 0.175, "y": 0.175}, {"time": 2.3, "x": 1.164, "y": 1.164}, {"time": 2.3333, "x": 0.896, "y": 0.896}]}, "pian6": {"translate": [{"x": -6.89, "y": 121.05}, {"time": 0.2667, "x": -6.09, "y": 164.35, "curve": "stepped"}, {"time": 0.4, "x": -9.3, "y": -8.85}, {"time": 1.1667, "x": -6.89, "y": 121.05}, {"time": 1.4333, "x": -6.09, "y": 164.35, "curve": "stepped"}, {"time": 1.5667, "x": -9.3, "y": -8.85}, {"time": 2.3333, "x": -7.05, "y": 112.39}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.4, "x": 0.175, "y": 0.175}, {"time": 0.4667, "x": 1.164, "y": 1.164}, {"time": 0.6333, "x": 0.629, "y": 0.629}, {"time": 0.6667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.5667, "x": 0.175, "y": 0.175}, {"time": 1.6333, "x": 1.164, "y": 1.164}, {"time": 1.8, "x": 0.629, "y": 0.629}, {"time": 1.8333, "x": 0.702, "y": 0.702}]}, "pian7": {"translate": [{"x": 4.64, "y": 70.95}, {"time": 0.6333, "x": 18.57, "y": 150.74, "curve": "stepped"}, {"time": 0.6667, "x": -9.3, "y": -8.85}, {"time": 1.1667, "x": 4.64, "y": 70.95}, {"time": 1.8, "x": 18.57, "y": 150.74, "curve": "stepped"}, {"time": 1.8333, "x": -9.3, "y": -8.85}, {"time": 2.3333, "x": 3.24, "y": 62.97}], "scale": [{"x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 0.6667, "x": 0.175, "y": 0.175}, {"time": 0.7333, "x": 1.164, "y": 1.164}, {"time": 0.9, "x": 0.629, "y": 0.629}, {"time": 0.9333, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.1667, "x": 0.702, "y": 0.702, "curve": "stepped"}, {"time": 1.8333, "x": 0.175, "y": 0.175}, {"time": 1.9333, "x": 1.164, "y": 1.164}, {"time": 2.0667, "x": 0.629, "y": 0.629}, {"time": 2.1, "x": 0.702, "y": 0.702}]}, "quan": {"rotate": [{"angle": 9.84}], "translate": [{"x": -447.84, "y": 82.19}], "scale": [{"x": 3.502, "y": 2.308}]}, "xing0001": {"translate": [{"x": -387.23, "y": -219.48, "curve": "stepped"}, {"time": 1.5, "x": -453.65, "y": -329.61}], "scale": [{"x": 1.246, "y": 1.246, "curve": "stepped"}, {"time": 1.5, "x": 0.886, "y": 0.886}]}, "xing1": {"translate": [{"time": 0.4333, "x": -281.65, "y": -259.36}], "scale": [{"time": 0.4333, "x": 1.246, "y": 1.246}]}}, "deform": {"default": {"guangxian": {"guangxian": [{}, {"time": 1.1667, "offset": 4, "vertices": [-35.896, 0, 42.94695]}, {"time": 2.3333}]}, "guangxian2": {"guangxian": [{"offset": 4, "vertices": [-23.93062, -3e-05, 28.63136]}, {"time": 0.7667}, {"time": 1.9667, "offset": 4, "vertices": [-35.89597, -3e-05, 42.94701]}, {"time": 2.3333, "offset": 4, "vertices": [-23.93062, -3e-05, 28.63136]}]}, "guangxian3": {"guangxian": [{"offset": 4, "vertices": [-23.93063, -3e-05, 28.6313]}, {"time": 0.4, "offset": 4, "vertices": [-35.89597, -3e-05, 42.94695]}, {"time": 1.5667}, {"time": 2.3333, "offset": 4, "vertices": [-23.93063, -3e-05, 28.6313]}]}}}}}}