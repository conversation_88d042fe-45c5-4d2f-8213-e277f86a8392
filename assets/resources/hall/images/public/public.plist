<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>BONUS_btn_guanbi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{43,43}</string>
                <key>spriteSourceSize</key>
                <string>{43,43}</string>
                <key>textureRect</key>
                <string>{{526,537},{43,43}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>DTgonggao.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{828,42}</string>
                <key>spriteSourceSize</key>
                <string>{828,42}</string>
                <key>textureRect</key>
                <string>{{1,1},{828,42}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>DTshengyin.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{34,34}</string>
                <key>spriteSourceSize</key>
                <string>{34,34}</string>
                <key>textureRect</key>
                <string>{{593,84},{34,34}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_base_brown2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{86,82}</string>
                <key>spriteSourceSize</key>
                <string>{86,82}</string>
                <key>textureRect</key>
                <string>{{477,676},{86,82}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_base_brown3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,74}</string>
                <key>spriteSourceSize</key>
                <string>{72,74}</string>
                <key>textureRect</key>
                <string>{{609,737},{72,74}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_base_support.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{81,82}</string>
                <key>spriteSourceSize</key>
                <string>{81,82}</string>
                <key>textureRect</key>
                <string>{{510,453},{81,82}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_brown1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{73,74}</string>
                <key>spriteSourceSize</key>
                <string>{73,74}</string>
                <key>textureRect</key>
                <string>{{561,661},{73,74}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_brown2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{77,75}</string>
                <key>spriteSourceSize</key>
                <string>{77,75}</string>
                <key>textureRect</key>
                <string>{{541,582},{77,75}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_brown3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,50}</string>
                <key>spriteSourceSize</key>
                <string>{71,50}</string>
                <key>textureRect</key>
                <string>{{453,553},{71,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_green1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{127,134}</string>
                <key>spriteSourceSize</key>
                <string>{127,134}</string>
                <key>textureRect</key>
                <string>{{1,691},{127,134}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_triangle.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{33,21}</string>
                <key>spriteSourceSize</key>
                <string>{33,21}</string>
                <key>textureRect</key>
                <string>{{191,400},{33,21}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_triangle1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{28,46}</string>
                <key>spriteSourceSize</key>
                <string>{28,46}</string>
                <key>textureRect</key>
                <string>{{343,800},{28,46}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_brown1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{211,76}</string>
                <key>spriteSourceSize</key>
                <string>{211,76}</string>
                <key>textureRect</key>
                <string>{{375,396},{211,76}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_brown2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{130,65}</string>
                <key>spriteSourceSize</key>
                <string>{130,65}</string>
                <key>textureRect</key>
                <string>{{174,677},{130,65}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_close.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{68,69}</string>
                <key>spriteSourceSize</key>
                <string>{68,69}</string>
                <key>textureRect</key>
                <string>{{471,605},{68,69}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_close1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{43,43}</string>
                <key>spriteSourceSize</key>
                <string>{43,43}</string>
                <key>textureRect</key>
                <string>{{571,537},{43,43}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_close2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{33,33}</string>
                <key>spriteSourceSize</key>
                <string>{33,33}</string>
                <key>textureRect</key>
                <string>{{389,765},{33,33}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_close3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{34,34}</string>
                <key>spriteSourceSize</key>
                <string>{34,34}</string>
                <key>textureRect</key>
                <string>{{636,81},{34,34}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_close4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{60,60}</string>
                <key>spriteSourceSize</key>
                <string>{60,60}</string>
                <key>textureRect</key>
                <string>{{476,765},{60,60}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_green3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{174,55}</string>
                <key>spriteSourceSize</key>
                <string>{174,55}</string>
                <key>textureRect</key>
                <string>{{453,377},{174,55}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_green4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{133,42}</string>
                <key>spriteSourceSize</key>
                <string>{133,42}</string>
                <key>textureRect</key>
                <string>{{130,691},{133,42}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_green5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{243,86}</string>
                <key>spriteSourceSize</key>
                <string>{243,86}</string>
                <key>textureRect</key>
                <string>{{229,45},{243,86}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_grey3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{117,48}</string>
                <key>spriteSourceSize</key>
                <string>{117,48}</string>
                <key>textureRect</key>
                <string>{{141,291},{117,48}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_grey4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{100,26}</string>
                <key>spriteSourceSize</key>
                <string>{100,26}</string>
                <key>textureRect</key>
                <string>{{241,802},{100,26}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_orange1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{235,79}</string>
                <key>spriteSourceSize</key>
                <string>{235,79}</string>
                <key>textureRect</key>
                <string>{{405,45},{235,79}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_orange3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{104,55}</string>
                <key>spriteSourceSize</key>
                <string>{104,55}</string>
                <key>textureRect</key>
                <string>{{364,290},{104,55}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_orange4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{1,0}</string>
                <key>spriteSize</key>
                <string>{244,86}</string>
                <key>spriteSourceSize</key>
                <string>{246,86}</string>
                <key>textureRect</key>
                <string>{{141,45},{244,86}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_yellow.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{243,86}</string>
                <key>spriteSourceSize</key>
                <string>{243,86}</string>
                <key>textureRect</key>
                <string>{{317,45},{243,86}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_yellow5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{378,138}</string>
                <key>spriteSourceSize</key>
                <string>{378,138}</string>
                <key>textureRect</key>
                <string>{{1,45},{378,138}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>dialog_close_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{25,40}</string>
                <key>spriteSourceSize</key>
                <string>{25,40}</string>
                <key>textureRect</key>
                <string>{{262,398},{25,40}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>hongdian.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{20,20}</string>
                <key>spriteSourceSize</key>
                <string>{20,20}</string>
                <key>textureRect</key>
                <string>{{174,809},{20,20}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_feiji.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{37,38}</string>
                <key>spriteSourceSize</key>
                <string>{37,38}</string>
                <key>textureRect</key>
                <string>{{593,45},{37,38}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_img.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{62,50}</string>
                <key>spriteSourceSize</key>
                <string>{62,50}</string>
                <key>textureRect</key>
                <string>{{424,765},{62,50}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_kefu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{100,106}</string>
                <key>spriteSourceSize</key>
                <string>{100,106}</string>
                <key>textureRect</key>
                <string>{{262,290},{100,106}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_loading.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{123,32}</string>
                <key>spriteSourceSize</key>
                <string>{123,32}</string>
                <key>textureRect</key>
                <string>{{241,677},{123,32}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>img_toast.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{264,130}</string>
                <key>spriteSourceSize</key>
                <string>{264,130}</string>
                <key>textureRect</key>
                <string>{{1,425},{264,130}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>jgg_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{69,65}</string>
                <key>spriteSourceSize</key>
                <string>{69,65}</string>
                <key>textureRect</key>
                <string>{{538,764},{69,65}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>jgg_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{93,62}</string>
                <key>spriteSourceSize</key>
                <string>{93,62}</string>
                <key>textureRect</key>
                <string>{{421,282},{93,62}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>jgg_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{94,65}</string>
                <key>spriteSourceSize</key>
                <string>{94,65}</string>
                <key>textureRect</key>
                <string>{{375,609},{94,65}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>jgg_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{84,83}</string>
                <key>spriteSourceSize</key>
                <string>{84,83}</string>
                <key>textureRect</key>
                <string>{{485,282},{84,83}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>jgg_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,72}</string>
                <key>spriteSourceSize</key>
                <string>{72,72}</string>
                <key>textureRect</key>
                <string>{{486,45},{72,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>jgg_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{87,86}</string>
                <key>spriteSourceSize</key>
                <string>{87,86}</string>
                <key>textureRect</key>
                <string>{{389,676},{87,86}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>jgg_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{121,112}</string>
                <key>spriteSourceSize</key>
                <string>{121,112}</string>
                <key>textureRect</key>
                <string>{{275,677},{121,112}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>jgg_7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{83,75}</string>
                <key>spriteSourceSize</key>
                <string>{83,75}</string>
                <key>textureRect</key>
                <string>{{510,368},{83,75}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>jgg_8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{73,72}</string>
                <key>spriteSourceSize</key>
                <string>{73,72}</string>
                <key>textureRect</key>
                <string>{{683,45},{73,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>jgg_9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{107,69}</string>
                <key>spriteSourceSize</key>
                <string>{107,69}</string>
                <key>textureRect</key>
                <string>{{191,291},{107,69}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>kongbai.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-14.5,14.5}</string>
                <key>spriteSize</key>
                <string>{3,3}</string>
                <key>spriteSourceSize</key>
                <string>{32,32}</string>
                <key>textureRect</key>
                <string>{{130,826},{3,3}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>loading_bg.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,240}</string>
                <key>spriteSourceSize</key>
                <string>{250,240}</string>
                <key>textureRect</key>
                <string>{{133,425},{250,240}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>title_arrow.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{34,40}</string>
                <key>spriteSourceSize</key>
                <string>{34,40}</string>
                <key>textureRect</key>
                <string>{{636,45},{34,40}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>public.png</string>
            <key>size</key>
            <string>{830,830}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:e058841e1dc6db62d25ed2b306aa6f10:8d81a2e1d7a20d6f175aff5f6e13e300:063f20810f63c58bcc5b88a4f1a41587$</string>
            <key>textureFileName</key>
            <string>public.png</string>
        </dict>
    </dict>
</plist>
