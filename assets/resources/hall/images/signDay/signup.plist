<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>DR_icon_fenge 1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{103,31}</string>
                <key>spriteSourceSize</key>
                <string>{103,31}</string>
                <key>textureRect</key>
                <string>{{714,74},{103,31}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>sign_btn_close.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{43,43}</string>
                <key>spriteSourceSize</key>
                <string>{43,43}</string>
                <key>textureRect</key>
                <string>{{714,1},{43,43}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>sign_btn_get.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{316,94}</string>
                <key>spriteSourceSize</key>
                <string>{316,94}</string>
                <key>textureRect</key>
                <string>{{329,370},{316,94}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>sign_btn_get_gray.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,4}</string>
                <key>spriteSize</key>
                <string>{324,94}</string>
                <key>spriteSourceSize</key>
                <string>{324,102}</string>
                <key>textureRect</key>
                <string>{{233,370},{324,94}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>sign_flag.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{188,190}</string>
                <key>spriteSourceSize</key>
                <string>{188,190}</string>
                <key>textureRect</key>
                <string>{{425,372},{188,190}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>sign_flag_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{229,382}</string>
                <key>spriteSourceSize</key>
                <string>{229,382}</string>
                <key>textureRect</key>
                <string>{{1,1},{229,382}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>sign_got_flag.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{106,107}</string>
                <key>spriteSourceSize</key>
                <string>{106,107}</string>
                <key>textureRect</key>
                <string>{{646,305},{106,107}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>sign_guang_0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{189,260}</string>
                <key>spriteSourceSize</key>
                <string>{189,260}</string>
                <key>textureRect</key>
                <string>{{452,1},{189,260}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>sign_guang_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{111,111}</string>
                <key>spriteSourceSize</key>
                <string>{111,111}</string>
                <key>textureRect</key>
                <string>{{646,192},{111,111}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>sign_guang_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{26,42}</string>
                <key>spriteSourceSize</key>
                <string>{26,42}</string>
                <key>textureRect</key>
                <string>{{714,46},{26,42}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>sign_item_0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,192}</string>
                <key>spriteSourceSize</key>
                <string>{178,192}</string>
                <key>textureRect</key>
                <string>{{452,192},{178,192}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>sign_item_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{230,379}</string>
                <key>spriteSourceSize</key>
                <string>{230,379}</string>
                <key>textureRect</key>
                <string>{{1,385},{230,379}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>sign_mask.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{168,181}</string>
                <key>spriteSourceSize</key>
                <string>{168,181}</string>
                <key>textureRect</key>
                <string>{{425,562},{168,181}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>sign_mask_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{218,367}</string>
                <key>spriteSourceSize</key>
                <string>{218,367}</string>
                <key>textureRect</key>
                <string>{{232,1},{218,367}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>sign_reward_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{85,59}</string>
                <key>spriteSourceSize</key>
                <string>{85,59}</string>
                <key>textureRect</key>
                <string>{{233,696},{85,59}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>sign_reward_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{171,135}</string>
                <key>spriteSourceSize</key>
                <string>{171,135}</string>
                <key>textureRect</key>
                <string>{{617,413},{171,135}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>signup.png</string>
            <key>size</key>
            <string>{765,765}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:86206cedbdb6b9c8412a91d97bc3126e:a99a88582553a00f1fb3821d906de43b:94a2daef6034817b064643f7535718a2$</string>
            <key>textureFileName</key>
            <string>signup.png</string>
        </dict>
    </dict>
</plist>
