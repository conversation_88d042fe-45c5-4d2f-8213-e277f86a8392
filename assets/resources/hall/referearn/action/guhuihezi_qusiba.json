{"skeleton": {"hash": "o+h+n7gppSnhpUtwg4DuYzhndqs", "spine": "3.8.99", "x": -255.2, "y": -223.85, "width": 520.09, "height": 601.68, "images": "./img/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 64.55, "rotation": 90.18, "x": 8.13, "y": -40.41}, {"name": "bone2", "parent": "root", "x": 11.87, "y": 112.75}, {"name": "bone3", "parent": "root", "x": -212.07, "y": 340.72}, {"name": "bone4", "parent": "root", "x": 215.93, "y": 350.91}], "slots": [{"name": "image 808", "bone": "root"}, {"name": "light", "bone": "bone2", "attachment": "light"}, {"name": "table (1) 1", "bone": "root", "attachment": "table (1) 1"}, {"name": "treasure_collect", "bone": "bone", "attachment": "treasure_collect"}, {"name": "Group 443461395", "bone": "root", "attachment": "Group 443461395"}, {"name": "singleGold 1", "bone": "bone4", "attachment": "singleGold 1"}, {"name": "singleGold 2", "bone": "bone3", "attachment": "singleGold 1"}], "skins": [{"name": "default", "attachments": {"Group 443461395": {"Group 443461395": {"x": 2.98, "y": -11.46, "width": 416, "height": 149}}, "light": {"light": {"x": -5.49, "y": -73.11, "width": 517, "height": 527}}, "singleGold 1": {"singleGold 1": {"x": -0.93, "y": -3.58, "width": 87, "height": 61}}, "singleGold 2": {"singleGold 1": {"x": 0.37, "y": -5.81, "width": 87, "height": 61}}, "table (1) 1": {"table (1) 1": {"x": 6.41, "y": -68.05, "width": 500, "height": 178}}, "treasure_collect": {"treasure_collect": {"x": 134.14, "y": 2.71, "rotation": -90.18, "width": 473, "height": 398}}}}], "animations": {"animation": {"bones": {"bone": {"rotate": [{}, {"time": 0.3333, "angle": 9.82, "curve": "stepped"}, {"time": 0.4, "angle": 9.82}, {"time": 0.8333, "angle": -8.18, "curve": "stepped"}, {"time": 0.9, "angle": -8.18}, {"time": 1.3667, "angle": 4.82, "curve": "stepped"}, {"time": 1.4333, "angle": 4.82}, {"time": 1.7667, "angle": -5.18, "curve": "stepped"}, {"time": 1.8333, "angle": -5.18}, {"time": 2.1, "angle": 2.32, "curve": "stepped"}, {"time": 2.1667, "angle": 2.32}, {"time": 2.3333, "angle": -2.15, "curve": "stepped"}, {"time": 2.3667, "angle": -2.15}, {"time": 2.5, "angle": -0.18, "curve": "stepped"}, {"time": 2.5333, "angle": -0.18}, {"time": 2.6333, "angle": -0.68, "curve": "stepped"}, {"time": 2.6667, "angle": -0.68}, {"time": 2.7333, "angle": -0.18}]}, "bone2": {"translate": [{"y": -28.29}, {"time": 1.1, "y": -3.54}, {"time": 2.1667, "y": -28.29}, {"time": 3.2667, "y": -3.54}, {"time": 4.3333, "y": -28.29}], "scale": [{"time": 0.5333}, {"time": 1.1, "x": 1.077, "y": 1.077}, {"time": 1.6333, "curve": "stepped"}, {"time": 2.7}, {"time": 3.2667, "x": 1.077, "y": 1.077}, {"time": 3.8}]}, "bone3": {"translate": [{"x": 18.6, "y": -67.52}, {"time": 0.8333, "x": 201.45, "y": -183.09}, {"time": 0.8667, "x": 18.6, "y": -67.52}, {"time": 1.7333, "x": 201.45, "y": -183.09}, {"time": 1.7667, "x": 18.6, "y": -67.52}, {"time": 2.6, "x": 201.45, "y": -183.09}, {"time": 2.6333, "x": 18.6, "y": -67.52}, {"time": 3.5, "x": 201.45, "y": -183.09}, {"time": 3.5333, "x": 18.6, "y": -67.52}, {"time": 4.3667, "x": 201.45, "y": -183.09}], "scale": [{}, {"time": 0.8333, "x": 0.952, "y": 0.952}, {"time": 0.8667}, {"time": 1.7333, "x": 0.952, "y": 0.952}, {"time": 1.7667}, {"time": 2.6, "x": 0.952, "y": 0.952}, {"time": 2.6333}, {"time": 3.5, "x": 0.952, "y": 0.952}, {"time": 3.5333}, {"time": 4.3667, "x": 0.952, "y": 0.952}]}, "bone4": {"translate": [{"x": -25.03, "y": -66.6}, {"time": 0.8333, "x": -170.34, "y": -192.57}, {"time": 0.8667, "x": 18.6, "y": -67.52}, {"time": 1.7333, "x": -170.34, "y": -192.57}, {"time": 1.7667, "x": 18.6, "y": -67.52}, {"time": 2.6, "x": -170.34, "y": -192.57}, {"time": 2.6333, "x": -25.03, "y": -66.6}, {"time": 3.5, "x": -170.34, "y": -192.57}, {"time": 3.5333, "x": -25.03, "y": -66.6}, {"time": 4.3667, "x": -170.34, "y": -192.57}], "scale": [{}, {"time": 0.8333, "x": 0.965, "y": 0.965}, {"time": 0.8667}, {"time": 1.7333, "x": 0.965, "y": 0.965}, {"time": 1.7667}, {"time": 2.6, "x": 0.965, "y": 0.965}, {"time": 2.6333}, {"time": 3.5, "x": 0.965, "y": 0.965}, {"time": 3.5333}, {"time": 4.3667, "x": 0.965, "y": 0.965}]}}, "drawOrder": [{"offsets": [{"slot": "singleGold 1", "offset": 1}]}]}}}