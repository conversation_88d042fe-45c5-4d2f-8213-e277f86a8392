const XDD =
{
	//协议
	CMD:
	{
		SC_XOCDIA_CONFIG_P: 3900,							//发送配置

		SC_XOCDIA_OPTTIME_P: 3901,								//下注亮牌的[[可操作？]]时间
		CS_XOCDIA_BUYHORSE_P: 3902,								//请求下注
		CS_XOCDIA_REQUEST_ZHUANG_P: 3903,							//请求上庄
		SC_XOCDIA_ZHUANG_LIST_P: 3904,							//上庄列表
		SC_XOCDIA_ZHUANG_INFO_P: 3905,							//庄家信息
		SC_XOCDIA_NO_ZHUANG_P: 3906,								//下庄公告
		SC_XOCDIA_SETTLEMENT_P: 3907,								//结算
		SC_XOCDIA_OPER_ERROR_P: 3908,								//服务端返回操作错误码
		CS_XOCDIA_HISTORY_P: 3909,								//请求历史信息
		SC_XOCDIA_HISTORY_P: 3910,								//返回历史信息 
		CS_XOCDIA_FOLLOW_BUY_P: 3911,								//请求续投
		CS_XOCDIA_ZHUANG_OFF_P: 3912,								//当前庄请求下庄

		CS_XOCDIA_ALLLIST_P: 3913,								//请求玩家列表（前20个）
		SC_XOCDIA_ALLLIST_P: 3914,								//返回玩家列表
		CS_XOCDIA_REQUEST_ZHUANG_LIST_P: 3915,					//请求上庄列表
		SC_XOCDIA_ROBOT_INIT_P: 3916,
		SC_XOCDIA_ROBOT_CONCHANGE_P: 3917,

		SC_XOCDIA_BET_SUCCESS: 3918,								//下注成功
		SC_XOCDIA_BET_SYNC: 3919,									//筹码增量信息			    
	},

	//场景状态
	XDD_GameState:
	{
		XDD_GameState_None: 0,	            //无状态
		XDD_GameState_Start: 1,              //开始阶段
		XDD_GameState_BuyHorse: 2,	            //下注阶段
		XDD_GameState_Combine: 3,	            //亮牌阶段	
	},

	//色碟下注方位
	XDDPos:
	{
		XDDPos_None: 0,  //无效
		XDDPos_Le: 1, // 单
		XDDPos_Chan: 2,	  // 双
		XDDPos_OneWhite: 3,  // 1白
		XDDPos_OneRed: 4, // 1红
		XDDPos_AllWhite: 5, // 全白
		XDDPos_AllRed: 6, // 全红
	},

	//游戏错误, 由命令SC_XOCDIA_OPER_ERROR_P返回
	GAME_ERROR:
	{
		GAME_ERROR_NOT_MONEY: 0,	        // 没有足够的金币
		GAME_ERROR_BUY_LIMIT: 1,	        // 下注上限
		GAME_ERROR_NOT_ROUND: 2,           // 当前不能换庄, 小于最小做庄次数(下庄失败)
		GAME_ERROR_ZHUANG_NO_MONEY: 3,	        // 上庄金钱不足
		GAME_ERROR_NEXT_ROUND: 4,	        // 下轮下庄
		GAME_ERROR_OFFZHUANG_WUNIU: 5,	        // 无牛下庄
		GAME_ERROR_APPLYZHUANG_OK: 6,	        // 申请上庄成功
		GAME_ERROR_NOT_MONEY_TO_BET: 7,           // 金钱不足不能下注
		GAME_ERROR_FOLLOW_TO_BET: 8,	        // 没有续投的记录
		GAME_ERROR_FOLLOW_LIMIT: 9,	        //续投超出房间限制
		GAME_ERROR_FOLLOW_NOT_MONEY: 10,          //续投个人金钱不足
		GAME_ERROR_SLIENTCE_TOMANNY: 11,	        //沉默次数太多
		GAME_ERROR_BET_TOOMORE: 12,          //下注超出限额
		GAME_ERROR_STATE_ERROR: 13,          //下注失败
		GAME_ERROR_BUY_POS_ERROR: 14,	        //下注区域错误
		GAME_ERROR_BANKER_BET: 15,		    //庄家下注
		GAME_ERROR_ILLEGAL_BET: 16,		    //非法筹码
		GAME_ERROR_BET_TOOFAST: 17,		//下注太快
	},
	LangData:{
		GOLD_BET_ERR: "金币不足，下注失败",
		GOLD_BET_MAX_ERR: "当前下注已达上限",
		GAME_MAIN_MIN_ERR: "坐庄轮次不足不能下庄",
		GAME_GOLD_BET_MIN_TIP: "金币大于{0}元才可以下注，请充值",
		GAME_BET_XT_ERR: "续投失败,没有投注记录",
		GAME_BET_XT_MIN_ERR: "金币不足，续投失败",
		GAME_EXIT_MIN_JU_TIP: "您已{0}局没有参与游戏，感谢关注",
		GAME_BET_MONEY_MAX_TIP: "下注额已到达上限",
		GAME_BET_ADD_ERR: "下注失败",
		GAME_EXIT_ROOM_TIP: "您已被踢出房间",
		GAME_BET_ING_TIP: "当前正在下注",
		GAME_BET_SCORE_ERR: "下注数据错误：{0}",
		GAME_SERVER_CONFIG_ERR: "获取服务器配置失败",
		GAME_PLEASE_SELECT_GOLD_TIP: "请选择下注金币",
		GAME_BET_MIN_ERR: "剩余金币不足{0}元，无法下注",
		GAME_X2BET_MIN_ERR: "剩余金币不足{0}元，无法下注x2",
		GAME_XUTOUBET_MIN_ERR: "剩余金币不足{0}元，无法续投",
		GAME_CITY_NOT_TEXT: "未知",
		GAME_SET_BET_MAX_ERR: "最多选择5个筹码，再次点击筹码可以取消选定。",
		GAME_SET_BET_MIN_ERR: "您需要选择5个筹码。",
		GAME_API_RESULT_ERR: "数据加载失败。",
		GAME_BET_EXIT_ERR: "您当前已投注，退出游戏系统会自动帮您托管，不影响金币结算，确定退出游戏吗？",
	} 
}

let TextVN = {
	GOLD_BET_ERR: "Tiền xu không đủ，đặt cược không thành công",
	//GOLD_BET_ERR: "金币不足，下注失败",
	GOLD_BET_MAX_ERR: "Mức cược hiện tại đã đạt đến giới hạn",
	//GOLD_BET_MAX_ERR: "当前下注已达上限",
	GAME_MAIN_MIN_ERR: "Không thể đặt cược nếu không đủ số vòng nhà cái",
	//GAME_MAIN_MIN_ERR: "坐庄轮次不足不能下庄"
	GAME_GOLD_BET_MIN_TIP: "Tiền xu lớn hơn{0}VND mới có thể đặt cược，Vui lòng nạp tiền",
	//GAME_GOLD_BET_MIN_TIP: "金币大于{0}元才可以下注，请充值",
	GAME_BET_XT_ERR: "Tiếp tục đặt cược không thành công,Không có lịch sử đặt cược",
	//GAME_BET_XT_ERR: "续投失败,没有投注记录",
	GAME_BET_XT_MIN_ERR: "Tiền xu không đủ，không thể tiếp tục đặt cược",
	//GAME_BET_XT_MIN_ERR: "金币不足，续投失败",
	GAME_EXIT_MIN_JU_TIP: "Bạn có{0}vòng không tham gia trò chơi，cảm ơn theo dõi",
	//GAME_EXIT_MIN_JU_TIP: "您已{0}局没有参与游戏，感谢关注",
	GAME_BET_MONEY_MAX_TIP: "Số tiền đặt cược đã đạt đến giới hạn",
	//GAME_BET_MONEY_MAX_TIP: "下注额已到达上限",
	GAME_BET_ADD_ERR: "Đặt cược không thành công",
	//GAME_BET_ADD_ERR: "下注失败",
	GAME_EXIT_ROOM_TIP: "Bạn đã bị mời ra khỏi phòng game",
	//GAME_EXIT_ROOM_TIP: "您已被踢出房间",
	GAME_BET_ING_TIP: "Hiện đang đặt cược",
	//GAME_BET_ING_TIP: "当前正在下注",
	GAME_BET_SCORE_ERR: "Lỗi dữ liệu đặt cược：{0}",
	//GAME_BET_SCORE_ERR: "下注数据错误：{0}",
	GAME_SERVER_CONFIG_ERR: "Không nhận được cấu hình máy chủ",
	//GAME_SERVER_CONFIG_ERR: "获取服务器配置失败",
	GAME_PLEASE_SELECT_GOLD_TIP: "Vui lòng chọn số tiền xu đặt cược",
	//GAME_PLEASE_SELECT_GOLD_TIP: "请选择下注金币",
	GAME_BET_MIN_ERR: "Số dư tiền xu không đủ{0}VND，Không thể đặt cược",
	//GAME_BET_MIN_ERR: "剩余金币不足{0}元，无法下注",
	GAME_X2BET_MIN_ERR: "Số dư tiền xu không đủ{0}VND，Không thể đặt cược x2",
	//GAME_X2BET_MIN_ERR: "剩余金币不足{0}元，无法下注x2",
	GAME_XUTOUBET_MIN_ERR: "Số dư tiền xu không đủ{0}VND，không thể tiếp tục đặt cược",
	//GAME_XUTOUBET_MIN_ERR: "剩余金币不足{0}元，无法续投",
	GAME_CITY_NOT_TEXT: "Không xác định",
	//GAME_CITY_NOT_TEXT: "未知",
	GAME_SET_BET_MAX_ERR: "Chọn tối đa 5 chip，có thể nhấp lại vào chip để hủy chọn.",
	//GAME_SET_BET_MAX_ERR: "最多选择5个筹码，再次点击筹码可以取消选定。",
	GAME_SET_BET_MIN_ERR: "Bạn cần chọn 5 chip.",
	//GAME_SET_BET_MIN_ERR: "您需要选择5个筹码。",
	GAME_API_RESULT_ERR: "Tải dữ liệu thất bại.",
	//GAME_API_RESULT_ERR: "数据加载失败。",
	GAME_BET_EXIT_ERR: "Bạn đã đặt cược，khi thoát khỏi game hệ thống sẽ tự động giúp bạn lưu trữ，không ảnh hưởng đến việc thanh toán tiền xu，bạn có chắc chắn thoát khỏi game không？",
	//GAME_BET_EXIT_ERR: "您当前已投注，退出游戏系统会自动帮您托管，不影响金币结算，确定退出游戏吗？",
}
if(!window["ENABLE_CHINESE"]){
	XDD.LangData =TextVN;
}
export = XDD;