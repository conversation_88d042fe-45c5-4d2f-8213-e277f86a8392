const { ccclass, property } = cc._decorator;
import Common from "../../../../script/frame/common/Common";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
@ccclass
export default class CrashAudioMng extends cc.Component {

    playMusic() {
        let path = "res/sound/bg";
        AudioHelper.instance.playMusic(path, true);

    }
    pauseMusic() {
        AudioHelper.instance.pauseMusic();
    }

    resumeMusic() {
        AudioHelper.instance.resumeMusic();
    }

    _loadPlaySFX(path) {
        AudioHelper.instance.playEffect("res/sound/" + path);
    }
    _loadPlayRandomSFX(pathArr) {
        let index = Math.floor(Math.random() * (pathArr.length));
        this._loadPlaySFX(pathArr[index]);
    }

    //按钮
    playClick() {
        this._loadPlaySFX("button");
    }

    //开始下注
    playStart() {
        this._loadPlaySFX("start_woman");
    }

    //倒计时
    playCountDown() {
        this._loadPlaySFX("countdown3");
    }

    //停止下注
    playStopBet() {
        this._loadPlaySFX("stop_woman");
    }

    //摇碗
    playYaoWan() {
        this._loadPlaySFX("yaowan");
    }

    //开局
    playKaiJu() {
        this._loadPlaySFX("kaiju");
    }


    //下注
    playBet() {
        this._loadPlaySFX("chip_" + Common.random(0, 4));
    }

    //开
    playKai() {
        this._loadPlaySFX("sd_yx_kaijiang");
    }

    //结算飞金币
    playWinBet() {
        this._loadPlaySFX("bet");
    }

    //哨子
    playSaozi() {
        this._loadPlaySFX("countdown32");
    }

    //结果播放
    playResult(point: number) {
        if (point == 2) {
            this._loadPlaySFX("sedie_lianghong");
        } else if (point == 0) {
            this._loadPlaySFX("sedie_sibai");
        } else if (point == 1) {
            this._loadPlaySFX("sedie_sanbai");
        } else if (point == 3) {
            this._loadPlaySFX("sedie_sanhong");
        } else if (point == 4) {
            this._loadPlaySFX("sedie_sihong");
        }
    }

    //赢
    playWin() {
        this._loadPlaySFX("win_bet");
    }

    //输
    playLose() {
        this._loadPlaySFX("lose");
    }
}

