import Common from "../../../../script/frame/common/Common";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import GameCore from "../../../../script/frame/model/GameCore";
import XDGameView from "../view/CrashGameView";
import { XDDPos, XDD_GameState } from "./CrashDefine";
import XDD = require("./CrashDefine");

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class CrashGameCore extends GameCore {

    private _gameView: XDGameView = null; //游戏视图对象 

    //////////////////////////////////////////////////////////////////////////////
    public difen: number = 0; //房间底分
    public roundid: number = 0; // 回合ID
    public betNeed: number = 0; // 下注最小金额
    public betMax: number = 0; //下注最大金额
    public lastbet: number[] = []; //前一局玩家各位置下注总数
    public chipsScore = [];//下注项
    public odds = [];//下注赔率
    public gameState: number = 0;//游戏状态
    public isBetMax = false;//是否达到下注上线
    public betMoneyValue = 0;//最小下注数
    public notBetCount = 0;//多少局没下注
    public isStartBet: boolean = false;    /** 是否开始下注 */

    public otherRoom = -1;

    public chipsRes: any = ["bet_chip_1k", "bet_chip_5k", "bet_chip_10k", "bet_chip_50k", "bet_chip_100k", "bet_chip_500k", "bet_chip_1000k"
        , "bet_chip_5000k"];  //筹码资源

    //////////////////////////////////////////////////////////////////////////////

    onLoad() {
        this._gameView = cc.Canvas.instance.getComponent("XDGameView");
        super.onLoad();
    }

    start() {
        this.bindGameMessage(XDD.CMD.SC_XOCDIA_CONFIG_P, this.onConfigs, this);               //配置
        this.bindGameMessage(XDD.CMD.SC_XOCDIA_OPTTIME_P, this.onOperateTime, this);          //下注亮牌操作时间
        this.bindGameMessage(XDD.CMD.SC_XOCDIA_SETTLEMENT_P, this.onResult, this);             //游戏结算
        this.bindGameMessage(XDD.CMD.SC_XOCDIA_OPER_ERROR_P, this.onError, this);              //错误消息
        this.bindGameMessage(XDD.CMD.SC_XOCDIA_HISTORY_P, this.onHistory, this);               //历史记录
        this.bindGameMessage(XDD.CMD.SC_XOCDIA_ALLLIST_P, this.onAllPlayerList, this);        //玩家列表
        this.bindGameMessage(XDD.CMD.SC_XOCDIA_BET_SUCCESS, this.onBetSuccess, this);           //下注成功
        this.bindGameMessage(XDD.CMD.SC_XOCDIA_BET_SYNC, this.onBetSYNC, this);//筹码增量信息
        super.start()
    }

    exit() {
        this.unbindGameMessage(XDD.CMD.SC_XOCDIA_CONFIG_P, this.onConfigs, this);               //配置
        this.unbindGameMessage(XDD.CMD.SC_XOCDIA_OPTTIME_P, this.onOperateTime, this);          //下注亮牌操作时间
        this.unbindGameMessage(XDD.CMD.SC_XOCDIA_SETTLEMENT_P, this.onResult, this);             //游戏结算
        this.unbindGameMessage(XDD.CMD.SC_XOCDIA_OPER_ERROR_P, this.onError, this);              //错误消息
        this.unbindGameMessage(XDD.CMD.SC_XOCDIA_HISTORY_P, this.onHistory, this);               //历史记录
        this.unbindGameMessage(XDD.CMD.SC_XOCDIA_ALLLIST_P, this.onAllPlayerList, this);        //玩家列表
        this.unbindGameMessage(XDD.CMD.SC_XOCDIA_BET_SUCCESS, this.onBetSuccess, this); //下注成功
        this.unbindGameMessage(XDD.CMD.SC_XOCDIA_BET_SYNC, this.onBetSYNC, this);//筹码增量信息
        super.exit()
    }

    /**
     * 配置信息
     * 	send["Odds"] 下注赔率,(数组类型)
        send["Bet"] 下注项,(数组类型)
        send["Zhuang"] = { 上庄信息
            zhuang["Need"] = 上庄所需金币
            zhuang["MinTurn"] = 座庄最小局数
            zhuang["MaxTurn"] = 座庄最大局数
        }
        send["BetNeed"] = 下注所需最小金币
        send["BetMax"] = 限红
        send["difen"] = 房间底分
        send["roundid"] = 回合ID
     */
    private onConfigs(info) {
        this.difen = Common.toInt(info["difen"]);
        this.roundid = Common.toInt(info["roundid"]);
        this.betMax = Common.toInt(info["BetMax"]);
        this.betNeed = Common.toInt(info["BetNeed"]);

        // 下注按钮面值
        if (info.Bet) {
            this.chipsScore = [];
            for (var key in info.Bet) {
                if (info.Bet[key]) {
                    this.chipsScore.push(Common.toInt(info.Bet[key]));
                }
            }
        }
        //赔率
        if (info.Odds) {
            this.odds = [];
            for (var key in info.Odds) {
                if (info.Odds[key]) {
                    this.odds.push(Common.toInt(info.Odds[key]));
                }
            }
        }

        this._gameView.onConfigs();
    }

    /**
     * 下注亮牌操作时间
     * 	send["waittime"] 当前操作剩余时间
        send["roomstate"] 游戏状态
        send["roundid"] = 回合ID
     * @param info 
     */
    private onOperateTime(info) {
        this.gameState = Common.toInt(info["roomstate"]);
        this.roundid = Common.toInt(info["roundid"]);
        let waittime = Common.toInt(info["waittime"]);
        if (this.gameState == XDD.XDD_GameState.XDD_GameState_Start) {  //开始状态
        } else if (this.gameState == XDD.XDD_GameState.XDD_GameState_BuyHorse) {   //下注状态
            this._gameView.onStartYaoWan();
            this._gameView.onStartBet(waittime);
        } else if (this.gameState == XDD.XDD_GameState.XDD_GameState_Combine) {   //亮牌状态
            this._gameView.onStopBet(waittime);
        }
    }

    /**
     * 下注成功
     * 	send["odds"] = 下注的筹码
        send["times"] = 倍数, (1: x1, 2: x2)
        send["direction"] = 下注位置, 参考 emXOCPos
        send["chouma"] = 玩家金币
        send["totalbet"] = 下注总额
        send["roundid"] = 回合ID
        send["leftbet"] = 下注限制,(数组类型)
        send["allbet"] = 所有人各位置下注总数,(数组类型)
        send["bet"] = 玩家各位置下注总数,(数组类型)
     * @param info 
     */
    private onBetSuccess(info) {
        if (info.chouma) {
            super.updatePlayerMoney({ coin: info.chouma, playerid: info._playerid });
        }
        this._gameView.myBetSuccess(info);
    }

    /**
     * 筹码增量信息
     * 	send["playerlist"] = { 玩家列表,(数组类型)
            player["playerid"] 玩家ID
            player["seatid"]  座位号
            player["betnum"] 各位置下注增量,(数组类型)
            player["chouma"] 玩家金币
        }
        send["roundid"] = 回合ID
        send["allbet"] = 所有人各位置下注总数,(数组类型)
     * @param info 
     * @returns 
     */
    private onBetSYNC(info) {

        let tabArr: number[] = [0, 4, 8, 12, 16, 20, 24, 28];

        let selectConfig = this.chipsScore;
        let allbet = info.allbet;
        let playerlist = info.playerlist;

        for (let key in playerlist) {
            let val = playerlist[key];
            let playerid = val.playerid;
            let betnum = val.betnum;
            for (let i = 0; i < tabArr.length; i++) {
                for (let area in betnum) {
                    let _rshift = (betnum[area] >> tabArr[i]);
                    let _band = (_rshift & 0x0000000f);
                    if (_band > 0) {
                        this._gameView.updateChipMsg(playerid, _band, selectConfig[i], val, Common.toInt(area), allbet);
                    }
                }

            }
        }

    }

    /**
     * 结算消息
     * 	send["winpos"] = 赢的区域,(数组类型)
        send["whitepoint"] = 白色点数
        send["redpoint"] = 红色点数
        send["roundid"] = 回合ID
        send["zhuang"] = { 庄家数据
            zhuang["playerid"] = 庄家playerID
            zhuang["playercoin"] = 庄家金币
            zhuang["changemoney"] = 输赢金币
        }
        send["other"] = { 玩家列表,(数组类型)
            [玩家ID] = "身上金币,输赢金币"  -- 此数据可参考龙虎
        }
     * @param info 
     */
    private onResult(info) {
        let redpoint = info.redpoint;
        let resultMsg: any = {};
        resultMsg._playerid = info._playerid;
        resultMsg.point = redpoint; //0:四白 1：三白一红 2：2白2红 3：1白3红 4：4红
        resultMsg.redpoint = info.redpoint;
        resultMsg.whitepoint = info.whitepoint;
        resultMsg.roundid = info.roundid;
        resultMsg.others = [];
        if (info.other) {
            for (let key in info.other) {
                let item = info.other[key];
                let tmp = item.split(",");
                let userData: any = {};
                userData.playercoin = parseInt(tmp[0]);
                userData.nChange = parseInt(tmp[1]);
                userData.playerid = key;
                resultMsg.others.push(userData)
            }

        }
        resultMsg.winposarr = [];
        if (redpoint % 2 == 0) {
            resultMsg.winposarr.push(XDDPos.XDDPos_Chan);
        } else {
            resultMsg.winposarr.push(XDDPos.XDDPos_Le);
        }
        if (redpoint == 0) resultMsg.winposarr.push(XDDPos.XDDPos_AllWhite);
        if (redpoint == 1) resultMsg.winposarr.push(XDDPos.XDDPos_OneRed);
        if (redpoint == 3) resultMsg.winposarr.push(XDDPos.XDDPos_OneWhite);
        if (redpoint == 4) resultMsg.winposarr.push(XDDPos.XDDPos_AllRed);
        this._gameView.onResult(resultMsg);
    }

    /**
     * 错误提示消息
     * 	send["code"] = 错误代码, 参考EM_XOCDIA_GAME_ERROR
        send["value"] = 错误参数,默认为0, 当code=EM_GAME_ERROR_NOT_MONEY_TO_BET时， 返回的是最小下注数
        send["roundid"] = 回合ID
    * @param info 
    */
    private onError(info) {
        console.log("onError",info);
        let code = info.code;
        if (code == XDD.GAME_ERROR.GAME_ERROR_NOT_MONEY) {
            ToastHelper.show(XDD.LangData.GOLD_BET_ERR);
        } else if (code == XDD.GAME_ERROR.GAME_ERROR_BUY_LIMIT) {
            ToastHelper.show(XDD.LangData.GOLD_BET_MAX_ERR);
            this.isBetMax = true;
        } else if (code == XDD.GAME_ERROR.GAME_ERROR_NOT_ROUND) {
            ToastHelper.show(XDD.LangData.GAME_MAIN_MIN_ERR);
        } else if (code == XDD.GAME_ERROR.GAME_ERROR_ZHUANG_NO_MONEY) {

        } else if (code == XDD.GAME_ERROR.GAME_ERROR_NEXT_ROUND) {

        } else if (code == XDD.GAME_ERROR.GAME_ERROR_OFFZHUANG_WUNIU) {
            ToastHelper.show(XDD.LangData.GOLD_BET_MAX_ERR);
            this.isBetMax = true;

        } else if (code == XDD.GAME_ERROR.GAME_ERROR_APPLYZHUANG_OK) {

        } else if (code == XDD.GAME_ERROR.GAME_ERROR_NOT_MONEY_TO_BET) {
            this.betMoneyValue = info.value;
            let moneyTxt = Common.moneyString(info.value, 6, 0);
            ToastHelper.show(Common.stringFormat(XDD.LangData.GAME_GOLD_BET_MIN_TIP, [moneyTxt]));
        } else if (code == XDD.GAME_ERROR.GAME_ERROR_FOLLOW_TO_BET) {
            ToastHelper.show(XDD.LangData.GAME_BET_XT_ERR);
        } else if (code == XDD.GAME_ERROR.GAME_ERROR_FOLLOW_LIMIT) {
            ToastHelper.show(XDD.LangData.GAME_BET_XT_MIN_ERR);
        } else if (code == XDD.GAME_ERROR.GAME_ERROR_SLIENTCE_TOMANNY) {      //10局没下注踢出房间
            this.notBetCount = info.value;
        } else if (code == XDD.GAME_ERROR.GAME_ERROR_BET_TOOMORE) {
            ToastHelper.show(XDD.LangData.GAME_BET_MONEY_MAX_TIP);
            this.isBetMax = true;
        } else if
            (
            code == XDD.GAME_ERROR.GAME_ERROR_STATE_ERROR
            || code == XDD.GAME_ERROR.GAME_ERROR_BUY_POS_ERROR
            || code == XDD.GAME_ERROR.GAME_ERROR_BANKER_BET
            || code == XDD.GAME_ERROR.GAME_ERROR_ILLEGAL_BET
            || code == XDD.GAME_ERROR.GAME_ERROR_BET_TOOFAST

        ) {
            ToastHelper.show(XDD.LangData.GAME_BET_ADD_ERR);
        }
    }

    // 进入房间，房间信息
    onRoomInfo(info) {
        super.onRoomInfo(info)
        this._gameView.onSaveUserInfo(info.playerlist);
    }

    // 玩家加入
    onPlayerEnter(info) {
        super.onPlayerEnter(info);
        this._gameView.onUserEnter(info);
    }

    // 玩家离开
    onPlayerQuit(info) {
        this._gameView.onUserLeave(info);
    }

    //离开游戏
    onQuitGame(info) {
        let self = this;
        if (this.notBetCount) {
            AlertHelper.show(Common.stringFormat(XDD.LangData.GAME_EXIT_MIN_JU_TIP, [this.notBetCount])
                , function () {
                    self.quitGame();
                }, true);
        } else {
            UIHelper.clearAll();
            super.onQuitGame(info);
        }
    }

    // 断线重连
    onToOtherRoom(info) {
        super.onToOtherRoom(info);
        this.otherRoom = 1;

        this.difen = Common.toInt(info["difen"]);
        this.roundid = Common.toInt(info["roundid"]);
        this.gameState = Common.toInt(info["state"]);
        let waittime = Common.toInt(info["waittime"]);
        let allbet = info["allbet"];
        let playerlist = info["playerlist"];
        let bet = info["bet"];
        let lastbet = info["lastbet"];
        //config
        let config = info["config"];
        this.betMax = Common.toInt(config["BetMax"]);
        this.betNeed = Common.toInt(config["BetNeed"]);
        if (config.Bet) {
            this.chipsScore = [];
            for (var key in config.Bet) {
                if (config.Bet[key]) {
                    this.chipsScore.push(Common.toInt(config.Bet[key]));
                }
            }
        }
        if (config.Odds) {
            this.odds = [];
            for (var key in config.Odds) {
                if (config.Odds[key]) {
                    this.odds.push(Common.toInt(config.Odds[key]));
                }
            }
        }

        this._gameView.onConfigs();

        //下注信息
        if (allbet) {
            let alltabbet = [];
            for (let key in allbet) {
                alltabbet.push(allbet[key]);
            }
            for (let i = alltabbet.length - 1; i >= 0; i--) {
                this._gameView.setBetAreaTotal(i, alltabbet[i]);
                this._gameView.restoreDeskChips(i, alltabbet[i]);
            }
        }
        if (bet) {
            for (let betkey in bet) {
                let area = Common.toInt(betkey) - 1;
                this._gameView.setSelfBetArea(area, bet[betkey], this.getMeMoney());
            }
        }
        if (lastbet) {
            for (let betkey in lastbet) {
                this.lastbet.push(lastbet[betkey]);
            }
        }
        if (playerlist) {
            for (let key in playerlist) {
                let player = playerlist[key];
                this._gameView.updateUserScore(player);
                if (player["playerid"] == this.playerid) {
                    this.userInfo.money = player.coin;
                }
            }
            // this.updatePlayerMoney({ playerid: this.playerid, coin: this.userInfo.money });
            this._gameView.updateMyScore();
            this._gameView.updatePlayerScore();
        }

        if (this.gameState == XDD_GameState.XDD_GameState_Start) {
         
        } else if (info.state == XDD.XDD_GameState.XDD_GameState_BuyHorse) {
            if (waittime >= 3) {
                this._gameView.onStartYaoWan(true);
                // this._gameView.onStartBet(waittime);
            }
            this._gameView.startBetOut(waittime);
            this._gameView.betTimeOut();
        } else if (info.state == XDD.XDD_GameState.XDD_GameState_Combine) {
            this._gameView.showTipWait(waittime);
            this._gameView.showWanState(false);
        }

        //历史
        if (info.data) {
            this._gameView.onUpdateHistoryIcon(info.data);
            this._gameView.updateTrendData(info.data);
        }

    }

    // 房间状态
    onRoomState(info) {
    }

    // 更新玩家金币
    updatePlayerMoney(info) {
        let myPlayerid = this.playerid;
        if (info.playerid == myPlayerid) {
            if (this.otherRoom != 1) {
                this.userInfo.money = info.coin;
                super.updatePlayerMoney(info);
            }
        }
        this._gameView.updateUserScore(info);
        this.otherRoom = 0;
    }


    // 玩家续押
    onFollowBet(info) {
        //console.log(info, "玩家续押")
    }


    // 走势图返回
    onHistory(info) {
        if (info["data"]) {
            this._gameView.onUpdateHistoryIcon(info.data);
            this._gameView.updateTrendData(info.data);
        }
    }

    // 玩家列表(请求下发)
    onAllPlayerList(info) {
        this._gameView.onUpdateUserData(info);
        this._gameView.initPlayerListData(info);
    }

    /////////////////////////////////发送协议//////////////////////////////////// 
    /**
     * 下注
     * @param area 
     * @param times  倍数, 0:不启用 1：启用x2
     * @param money 
     */
    requestBet(area: number, times: number, money: number) {
        let info = {};
        info["odds"] = money;
        info["times"] = times;
        info["direction"] = area;
        super.sendGameMessage(XDD.CMD.CS_XOCDIA_BUYHORSE_P, info);
    }

    // 续押
    requestFollowHistoryBet() {
        super.sendGameMessage(XDD.CMD.CS_XOCDIA_FOLLOW_BUY_P)
    }


    // 请求玩家列表
    requestAllPlayerList() {
        super.sendGameMessage(XDD.CMD.CS_XOCDIA_ALLLIST_P)
    }


    //请求历史记录
    requestHistory() {
        super.sendGameMessage(XDD.CMD.CS_XOCDIA_HISTORY_P);
    }


    //获取自己的钱
    public getMeMoney() {
        return this.userInfo.money;
    }
}
