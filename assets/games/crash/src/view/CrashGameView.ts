import Common from "../../../../script/frame/common/Common";
import { <PERSON><PERSON><PERSON><PERSON> } from "../../../../script/frame/common/Define";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import XDBetLayer from "./CrashBetLayer";
import XDChipsLayer from "./CrashChipsLayer";
import XDD = require("../core/CrashDefine");
import XDGameCore from "../core/CrashGameCore";
import XDModel from "../core/CrashModel";
import XDAudioMng from "../core/CrashAudioMng";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import XDOnlineLayer from "./CrashOnlineLayer";
import XDZouShi from "./CrashZouShi";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class CrashGameView extends BaseLayer {

    @property(cc.Node)
    mainLayer: cc.Node = null;

    @property(cc.Node)
    bottomLayer: cc.Node = null;

    @property(cc.Node)
    alterLayer: cc.Node = null;

    @property(cc.Prefab)
    onlineLayerPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    historyPrefab: cc.Prefab = null;

    @property(cc.Node)
    btnOnline: cc.Node = null;

    @property(cc.Prefab)
    moveStar: cc.Prefab = null;

    /** 在线玩家列表 */
    private _playerListData: any;
    /** 房间所有用户信息 */
    private _allUsers = {};
    /** 桌面玩家数据 */
    public _playersList: XDModel.Player[] = [];
    /** 我的信息界面 */
    public myInfo: XDModel.MyInfo = new XDModel.MyInfo;
    /** 牌桌对象 包含 下注区域 下注总额  */
    public deskTopBet: XDModel.DesktopBet[] = [];

    private _centerLayer: cc.Node = null;
    private _tipWaitPanel: cc.Node = null;
    private _onlineUserLayer: cc.Node = null;
    private _historyLayer: cc.Node = null;
    private _statePanel: cc.Node = null;
    private _gameWan: cc.Node = null;//摇碟 初始图片
    private _gameWanAni: cc.Node = null;//摇碟 动画
    private _spCountDown: sp.Skeleton = null;//倒计时动画
    private _historyNode: cc.Node = null;//历史node
    private _resultNode: cc.Node = null;//历史node
    private _betLayer: XDBetLayer = null;    /** 下注筹码脚本 */

    private _betScheduler: any;
    private _updateScheduler: any;

    private _historyListData = [];

    private _userBetMsgTemp: any[];

    private _flyingBets: any[];

    private _currentTimeOut: number;
    private _movingOnlineUserHead: boolean = false;

    private _gameChip: XDChipsLayer;  //游戏筹码
    private _audioMng: XDAudioMng;  //声音
    private _gameCore: XDGameCore;    //core

    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent(XDGameCore);
        this._gameChip = cc.Canvas.instance.getComponent(XDChipsLayer);
        this._audioMng = cc.Canvas.instance.getComponent(XDAudioMng);
        this._betLayer = this.bottomLayer.getChildByName("BetPanel").getComponent(XDBetLayer);

        this._tipWaitPanel = this.alterLayer.getChildByName("TipWaitPanel");
        this._statePanel = this.mainLayer.getChildByName("StatePanel");
        this._centerLayer = this.mainLayer.getChildByName("CenterLayer");
        this._resultNode = this.mainLayer.getChildByName("ResultLayer");
        this._gameWan = this._centerLayer.getChildByName("StartPanel").getChildByName("GameCard");
        this._gameWan.active = false;
        this._gameWanAni = this._centerLayer.getChildByName("AreaPanel").getChildByName("spTou");
        this._gameWanAni.active = false;
        this._spCountDown = this._centerLayer.getChildByName("spCountDown").getComponent(sp.Skeleton);
        this._spCountDown.node.active = false;

        this.initDesktopUI();
        this.initPlayerUI();
        this.initMyinfoUI();
        this.initHistory();
    }

    start() {
        this.initSelfInfo();
        this.cleanTable();
        this._audioMng.playMusic();
    }

    /**
     * 初始化自己界面
     */
    private initMyinfoUI() {
        //--自己的信息
        let myinfoPanel: cc.Node = this.bottomLayer.getChildByName("LeftPanel");
        this.myInfo.node = myinfoPanel;
        let myHeadNode: cc.Node = this.myInfo.node.getChildByName("HeadBg");
        this.myInfo.head = myHeadNode.getChildByName("HeadImg");
        this.myInfo.headFrame = myHeadNode.getChildByName("HeadImg").getChildByName("HeadFrame");
        this.myInfo.headBg = myHeadNode;
        this.myInfo.score = this.myInfo.node.getChildByName("InfoPanel").getChildByName("GoldPanel").getChildByName("GoldValue").getComponent(cc.Label);
        this.myInfo.nickName = this.myInfo.node.getChildByName("InfoPanel").getChildByName("GameIdLabel").getComponent(cc.Label);
        this.myInfo.result = this._resultNode.getChildByName("result").getComponent(cc.Label);
        this.myInfo.result.node.active = false;
        this.myInfo.resultPos = this.myInfo.result.node.getPosition();
    }

    /**
     * 初始化桌面玩家界面
     */
    private initPlayerUI() {
        for (let i = 0; i < 8; i++) {
            var playerNode = this.mainLayer.getChildByName("UserInfo" + i);
            let playerData: XDModel.Player = new XDModel.Player();

            playerData.node = playerNode;

            let headNode = playerNode.getChildByName("HeadBg");
            playerData.headBg = headNode;
            playerData.head = headNode.getChildByName("HeadImg");
            playerData.headFrame = headNode.getChildByName("HeadImg").getChildByName("Headframe");

            playerData.chairId = i;
            playerData.score = playerNode.getChildByName("GoldPanel").getChildByName("GoldValue").getComponent(cc.Label);
            playerData.result = this._resultNode.getChildByName("result" + i).getComponent(cc.Label);
            playerData.resultPos = playerData.result.node.getPosition();
            playerData.result.node.active = false;

            if (i == 0 || i == 3 || i == 5 || i == 7) {
                playerData.dirction = 0;
            } else {
                playerData.dirction = 1;
            }

            playerNode.active = false;
            this._playersList.push(playerData);
        }
    }

    /**
     * 弹出显示用户信息
     */
    private openUserInfo(target, customData) {
        let index = Common.toInt(customData);
        if (!this._playersList[index]) return;
        let playerData = this._playersList[index];
        if (!playerData.isOpenUserInfo) {
            let showNode = UIHelper.createPlayerDetails(playerData.userInfo, function () {
                playerData.isOpenUserInfo = false;
            });
            playerData.isOpenUserInfo = true;

            let width = playerData.headBg.width;
            let showPos = playerData.headBg.getPosition().clone();
            let contentSize = showNode.getContentSize();
            if (playerData.dirction == 1) {
                showPos.x += (width + contentSize.width) / 2 + 10;
            }
            else if (playerData.dirction == 0) {
                showPos.x -= (width + contentSize.width) / 2 + 10;
            }

            let parent = this.mainLayer;
            if (parent) {// 需将坐标设置为中心点在转化
                let sceneSize = SceneManager.instance.sceneSize;
                showPos.x -= sceneSize.width / 2;
                showPos.y -= sceneSize.height / 2;
                showPos = playerData.node.convertToWorldSpaceAR(showPos);
                parent.addChild(showNode);
                showNode.setPosition(showPos);
            }
        }
    }

    /**
     * 获取用户信息
     * @param playerid 用户id
     * @returns 用户信息
     */
    private getUserInfo(playerid: any): any {
        return this._allUsers[playerid];
    }

    /**
     * 初始化桌面UI
     */
    private initDesktopUI() {

        let areaPanel = this.mainLayer.getChildByName("CenterLayer").getChildByName("AreaPanel");

        for (let i = 0; i < 6; i++) {
            let deskTopBet = new XDModel.DesktopBet();
            let baseNode = areaPanel.getChildByName("Panel" + (i + 1));
            deskTopBet.node = baseNode;

            deskTopBet.starLucky = baseNode.getChildByName("StarImg");
            deskTopBet.totaLabel = baseNode.getChildByName("Totalbg").getChildByName("TotalLabel").getComponent(cc.Label);
            deskTopBet.selfLabel = baseNode.getChildByName("SelfLabel").getComponent(cc.Label);
            deskTopBet.chipArea = baseNode.getChildByName("ChipArea");
            deskTopBet.winBg = baseNode.getChildByName("WinBg");
            deskTopBet.starLucky.active = false;
            deskTopBet.totaLabel.string = "0";
            deskTopBet.selfLabel.string = "0";
            deskTopBet.selfLabel.node.active = false;
            deskTopBet.total = 0;
            deskTopBet.self = 0;

            let btn = baseNode.getComponent(cc.Button);
            if (!btn) {
                btn = baseNode.addComponent(cc.Button);
                let eventHandler = new cc.Component.EventHandler();
                eventHandler.component = cc.js.getClassName(this);
                eventHandler.handler = 'onClickBet';
                eventHandler.target = this.node;
                eventHandler.customEventData = i + '';
                btn.clickEvents.push(eventHandler);
            }
            this.deskTopBet.push(deskTopBet);
        }
    }

    //初始化历史记录
    private initHistory() {
        // this._historyNode = this.mainLayer.getChildByName("HistoryLayer").getChildByName("MainBox");
        // this._historyNode.removeAllChildren();

        // this._gameZoushi.initzoushiUI(self);
    }

    //更新自己的信息
    private initSelfInfo() {
        if (!this._gameCore.userInfo) return;
        this.myInfo.money = this._gameCore.userInfo.money;
        let userInfo = this._gameCore.userInfo;
        this.setPlayerHead(this.myInfo.head.getComponent(cc.Sprite), userInfo.headid);
        this.setPlayerFrame(this.myInfo.headFrame.getComponent(cc.Sprite), userInfo.headframeid);
        this.myInfo.nickName.string = Common.textClamp(userInfo.nickname, 10);
        this.myInfo.score.string = Common.moneyString(this.myInfo.money, 7, 0);
        this.myInfo.playerid = userInfo.playerid;
    }

    //初始化在线玩家数据
    public initPlayerListData(msg: any) {
        let tempTb = msg;
        //--保存前六个玩家显示到桌子上
        let deskPlayerList = [];
        for (let i = 0; i < 8; i++) {
            deskPlayerList.push(msg[i + 1])
        }
        this._playerListData = tempTb;

        this.onUpdateDeskUserInfo(deskPlayerList);

        //在线人数
        let count = this.btnOnline.getChildByName("count").getComponent(cc.Label);
        count.string = msg.totalplayernum || 0 + "";

        // if (this._onlineUserLayer) {
        //     this._onlineUserLayer.getComponent(XDOnlineLayer).loadPlayerList(this._playerListData);
        //     return;
        // }
    }

    //修改桌位玩家数据
    private onUpdateDeskUserInfo(msg: any[]) {
        for (let i = 0; i < this._playersList.length; i++) {
            let val = this._playersList[i];
            let key = i;
            if (val && msg[key] && msg[key].playerid > 0) {
                val.node.active = true;
                let userInfo = this.getUserInfo(msg[key].playerid);
                val.userInfo = userInfo;
                this.setPlayerHead(val.head.getComponent(cc.Sprite), msg[key].headid);
                this.setPlayerFrame(val.headFrame.getComponent(cc.Sprite), msg[key].headframeid);
                if (val.score) {
                    val.score.string = Common.moneyString(msg[key].coin, 7, 0);
                }
                val.playerid = msg[key].playerid;
            } else {
                val.node.active = false;
                val.playerid = 0;
            }
        }
    }

    private onClickBet(target, customData) {
        let index = Common.toInt(customData);
        this._betLayer.onClickBet(index);
        this._audioMng.playClick();
    }

    /**
     * 点击续压筹码
     */
    private onClickContiueBet() {
        //throw new Error("Method not implemented.");
    }

    /**
     * 返回大厅
     */
    private onClickBack() {
        let self = this;
        let text = XDD.LangData.GAME_BET_EXIT_ERR;
        if (self.myInfo.isBet) {
            AlertHelper.confirm(text, function () {
                self._centerLayer.stopAllActions();
                self._gameCore.quitGame();
            }, null);
            return;
        }
        this._centerLayer.stopAllActions();
        this.closeUpdateScheduler();
        this._gameCore.quitGame();
    }

    /**
     * 弹出右侧菜单面板
     */
    private onClickMenu() {
        UIHelper.showGameMenu(this.alterLayer, [1, 2, 4], (menuIndex: number) => {
            if (menuIndex == 1) {
                AudioHelper.instance.openSetupUI();
            }
            else if (menuIndex == 2) {
                let gameModel = this._gameCore;
                UIHelper.showGameBetHistory(gameModel.roomInfo.gameid, gameModel.roomInfo.serverid);
            }
            else if (menuIndex == 4) {
                UIHelper.showGameRule(this._gameCore.bundleName);
            }
        });

    }

    //显示游戏状态提示框
    public showTipWait(time: number) {
        if (this._tipWaitPanel && !this._tipWaitPanel.active) {
            this._tipWaitPanel.active = true;
            let labTime = this._tipWaitPanel.getChildByName("MainBg").getChildByName("clock").getChildByName("TimeValue").getComponent(cc.Label);
            labTime.string = time + "";
            let self=this;
            cc.tween(this._tipWaitPanel)
                .then(
                    cc.tween().delay(1)
                        .call(function () {
                            time--;
                            time = time < 0 ? 0 : time;
                            labTime.string = time + "";

                            if(time<4){
                                self._audioMng.playCountDown();
                            }
                        })
                )
                .repeat(Math.ceil(time))
                .start();
        }
    }
    //隐藏游戏状态提示框
    public hideTipWait() {
        if (this._tipWaitPanel) {
            this._tipWaitPanel.stopAllActions();
            this._tipWaitPanel.active = false;
        }
    }
    //显示历史记录
    public showHistory() {
        this._historyLayer = cc.instantiate(this.historyPrefab);
        this.node.addChild(this._historyLayer);
        this._historyLayer.getComponent(XDZouShi).updateHistory(this._historyListData);
    }

    //显示在线玩家
    public showOnlineUser() {
        if(this.onlineLayerPrefab==null)return;
        this._onlineUserLayer = cc.instantiate(this.onlineLayerPrefab);
        this.node.addChild(this._onlineUserLayer);
        if (this._playerListData) {
            this._onlineUserLayer.getComponent(XDOnlineLayer).loadPlayerList(this._playerListData);
        }

    }

    public showWanState(show: boolean) {
        this._gameWan.active = show;
    }

    //////////////////////////////////////////////////////////////////////////////

    //////////////////////////////网络回调方法///////////////////////////////////

    /**
     * 保存玩家数据
     * @param playerlist 
     */
    public onSaveUserInfo(playerlist: any) {
        for (let i in playerlist) {
            this._allUsers[playerlist[i].playerid] = playerlist[i];
        }
        //--请求玩家列表
        this._gameCore.requestAllPlayerList();
    }
    /**
     * 玩家进入
     * @param info 
     */
    public onUserEnter(info: any) {
        let user = this.getUserInfo(info.palyerid);
        if (!user) {
            this._allUsers[info.playerid] = info;
        }
    }
    /**
     * 玩家离开
     * @param info 
     */
    public onUserLeave(info: any) {
        let user = this.getUserInfo(info.palyerid);
        if (user) {
            this._allUsers[info.playerid] = null;
        }
    }

    public updateUserScore(msg: any) {
        let userInfo = this._allUsers[msg.playerid];
        if (userInfo) {
            userInfo.coin = msg.coin;
            userInfo.money = msg.coin;
        }
    }
    public updatePlayerScore() {
        for (let i = 0; i < this._playersList.length; i++) {
            let player = this._playersList[i];
            let playerid = player.playerid;
            let userInfo = this._allUsers[playerid];
            if (player.score && userInfo) {
                player.score.string = Common.moneyString(userInfo.money, 7, 0);
            }

        }
    }
    public updateMyScore() {
        let score = this._gameCore.userInfo.money;
        this.myInfo.money = score;
        this.myInfo.score.string = Common.moneyString(score, 7, 0);
        if (this._gameCore.gameState == XDD.XDD_GameState.XDD_GameState_BuyHorse) {
            this._betLayer.autoSelectChip();
        }
        this._betLayer.updateNotTipBox();
    }

    //配置信息
    public onConfigs() {
        this._betLayer.initBet();
        //清理桌子
        this.cleanTable();
    }

    //摇碗
    public onStartYaoWan(reconnect:boolean=false) {
        this._gameWan.active = false;
        this._gameWanAni.active = true;
        let vsSpine: sp.Skeleton = this._gameWanAni.getComponent(sp.Skeleton);
        vsSpine.animation = "yao_" + Common.random(1, 2);
 
        
        let startAniTime = 1.33;
        let self = this;
        cc.tween(this._gameWanAni)
        .delay(startAniTime)
        .call(function () {
            self.setGameStateTip(true, 1);
        })
        .start();
        
        this._audioMng.playYaoWan();
        if(reconnect){
            self._audioMng.playStart();
        }else{
            this._audioMng.playSaozi();
        }
    }

    /**
     * 开始下注
     */
    public onStartBet(betTime: number) {

        let stateSpine: sp.Skeleton = this._statePanel.getComponent(sp.Skeleton);
        let self = this;

        cc.tween(this._statePanel)
            .call(function () {

                stateSpine.animation = "start_ani";
                self._statePanel.active = true;

                let startAniTime = 1.33;
                betTime = betTime - 0.1;

                self.startBetOut(betTime);

                cc.tween(self._statePanel)
                    .delay(startAniTime)
                    .call(function () {
                        self._statePanel.active = false;
                        self.betTimeOut();
                        self._audioMng.playKaiJu();
                    })
                    .start();
                self._audioMng.playStart();
            })
            .start();
    }

    // 下注倒计时
    public betTimeOut() {
        this._gameCore.isStartBet = true;
        this._betLayer.autoSelectChip();
        this._betLayer.setXuTouState(true);
        // this.setGameStateTip(true, 1);
        let gameClock = this._centerLayer.getChildByName("GameTime").getChildByName("clock").getComponent(sp.Skeleton);
        gameClock.animation = "animation";
    }

    public startBetOut(betTime: number) {
        let self = this;
        let gameClock: cc.Node = this._centerLayer.getChildByName("GameTime").getChildByName("clock");
        let gameTimeVale: cc.Label = gameClock.getChildByName("TimeValue").getComponent(cc.Label)

        this.stopBetOut();

        this._currentTimeOut = betTime;
        gameTimeVale.string = Math.floor(this._currentTimeOut + 1) + "";
        gameClock.active = true;

        this._betScheduler = function () {
            self._currentTimeOut = self._currentTimeOut - 1;
            gameTimeVale.string = Math.floor(self._currentTimeOut + 1) + "";
   
            if (self._currentTimeOut < 3) {
                self._spCountDown.node.active = Math.floor(self._currentTimeOut) >= 0;
                self._spCountDown.animation = "num_" + Math.floor(self._currentTimeOut + 1);

                let gameClock = self._centerLayer.getChildByName("GameTime").getChildByName("clock").getComponent(sp.Skeleton);
                gameClock.animation = "animation";

                self._audioMng.playCountDown();
            }
            // //--yg:提前一秒不能再下注，因为丢筹码预先表现了，放在最后一秒下注导致最后数据不一致
            if (self._currentTimeOut < 1.1) {
                self._gameCore.isStartBet = false;
            }

            if (self._currentTimeOut < 0 && self._betScheduler) {
                gameClock.active = false;
                self.stopBetOut();
            }

        }
        this.schedule(this._betScheduler, 1);
    }

    public setGameStateTip(show: boolean, type: number) {
        let bg: cc.Node = this._centerLayer.getChildByName("GameTime").getChildByName("bg");
        let sprite = bg.getComponent(cc.Sprite);
        bg.active = show;
        if (type == 1) {
            this.setSpriteFrame(sprite, "res/images/bet_text");
        } else if (type == 2) {
            this.setSpriteFrame(sprite, "res/images/jiesuan");
        } else if (type == 3) {
            this.setSpriteFrame(sprite, "res/images/wait");
        }
    }

    //停止倒计时
    public stopBetOut() {
        if (this._betScheduler) {
            this.unschedule(this._betScheduler);
            this._betScheduler = null;
        }
        this._currentTimeOut = 0;
        this._spCountDown.node.active = false;
        let gameclock: cc.Node = this._centerLayer.getChildByName("GameTime").getChildByName("clock");
        gameclock.active = false;
    }

    // 停止下注
    public onStopBet(time: number = 0) {
        let self = this;
        this._statePanel.active = true;
        this._gameCore.isStartBet = false;
        this._betLayer.disableChips(true);
        this.stopBetOut();

        let stateSpine: sp.Skeleton = this._statePanel.getComponent(sp.Skeleton);
        stateSpine.animation = "stop_ani";

        this._audioMng.playStopBet();

        cc.tween(this._statePanel)
            .delay(0.6)
            .call(function () {
                self.setGameStateTip(true, 2);
                self._audioMng.playSaozi();
            })
            .delay(0.8)
            .call(function () {
                self._statePanel.active = false;
            })
            .start();

    }

    /**
     * 开碗
     */
    openResult(point: number) {
        this._gameWan.active = false;
        this._gameWanAni.active = true;
        this.closeUpdateScheduler();

        let vsSpine: sp.Skeleton = this._gameWanAni.getComponent(sp.Skeleton);
        vsSpine.animation = "kai_" + point;

        this._audioMng.playKai();
    }

    //结算
    onResult(result: any) {
        let self = this;
        let others = result.others || [];
        let posArray = [];
        let labArray = [];
        let scoreArray = [];
        let isOnlineCount = 0;
        let meWin = false;
        let melose = false;
        for (let i = 0; i < others.length; i++) {
            let other = others[i];
            if (other.playerid == this.myInfo.playerid) {
                if (other.nChange > 0) {
                    let player = this.myInfo.head;
                    let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
                    posArray.push(playerPos);
                    scoreArray.push(other.nChange);

                    this.myInfo.result.node.setPosition(this.myInfo.resultPos);
                    labArray.push(this.myInfo.result);
                    meWin = true;
                } else if (other.nChange < 0) {
                    melose = true;
                }
            } else {
                let hasExists = false;
                for (let j = 0; j < this._playersList.length; j++) {
                    let playerModel = this._playersList[j];
                    if (other.playerid == playerModel.playerid) {
                        if (other.nChange > 0) {
                            let player = playerModel.head;
                            let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
                            posArray.push(playerPos);
                            scoreArray.push(other.nChange);

                            playerModel.result.node.setPosition(playerModel.resultPos);
                            labArray.push(playerModel.result);
                        }
                        hasExists = true;
                    }
                }
                isOnlineCount += hasExists == false ? 1 : 0
            }
        }
        if (isOnlineCount > 0 || posArray.length == 0) {
            let player = this.btnOnline;
            let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
            posArray.push(playerPos);
            scoreArray.push(0);
            labArray.push(player);
        }

        cc.tween(self._centerLayer)
            .delay(2.0)
            .call(function () {
                self.openResult(result.point);
            })
            .delay(2.5)
            .call(function () {
                //设置赢的区域闪烁
                let winposarr = result.winposarr || [];
                for (let i = 0; i < winposarr.length; i++) {
                    let area = winposarr[i] - 1;
                    let winNode = self.deskTopBet[area];
                    winNode.winBg.active = true;
                    winNode.winBg.opacity = 0;
                    cc.tween(winNode.winBg)
                        .then(cc.tween().to(0.5, { opacity: 255 }).to(0.5, { opacity: 0 }))
                        .repeat(3)
                        .call(function () {
                            winNode.winBg.active = false;
                        }).start();
                }
                self.myInfo.isBet = false;
                // self.addHistoryItem(result.point, true);
                self.updateResultTrend(result.redpoint, result.whitepoint);
                self.setGameStateTip(true, 3);
                self._audioMng.playResult(result.point);
            })
            .delay(1.0)
            .call(function () {
                self._gameChip.flyChips(posArray, function () {
                });
                self.settleScoreAni(labArray, scoreArray);
                self.updateMyScore();
                self.updatePlayerScore();

                if (others.length > 0) {
                    self.onUpdateUserData(others);
                }

                // if (meWin) self._audioMng.playWin();
                // if (melose) self._audioMng.playLose();
            })
            .delay(2.5)
            .call(function () {
                self._gameCore.requestAllPlayerList();
            })
            .start();

    }

    public settleScoreAni(labArr: cc.Label[], scoreArray: number[]) {
        for (let i = 0; i < scoreArray.length; i++) {
            let score = scoreArray[i];
            if (score > 0) {
                let lab = labArr[i];
                lab.string = "+" + Common.moneyString(score, 12, 0);
                lab.node.active = true;
                cc.tween(lab.node)
                    .by(2.5, { y: 23 })
                    .delay(0.1)
                    .call(function () {
                        lab.node.active = false;
                    })
                    .start();
            }
        }
    }

    /**
     * 修改在线用户数据
     * @param msg 
     */
    onUpdateUserData(msg: any) {
        let length = msg.totalplayernum ? (msg.totalplayernum + 1) : msg.length;
        let start = msg.totalplayernum ? 1 : 0;
        for (let i = start; i < length; i++) {
            if (msg[i]) {
                let user = this._allUsers[msg[i].playerid];
                if (user) {
                    user.money = msg[i].coin && msg[i].coin || msg[i].playercoin;
                    user.headid = msg[i].headid && msg[i].headid || user.headid;
                    user.nickname = msg[i].name && msg[i].name || user.nickname;
                    if (user.isOpenUserInfo) {
                        //self.setUserInfo(user);
                    }
                }
            }
        }
    }

    //更新走势
    public updateTrendData(data) {
        let reclist = data.reclist;
        this._historyListData = [];
        if (reclist) {
            for (let key in reclist) {
                this._historyListData.push(reclist[key]);
            }
        }

        if (cc.isValid(this._historyLayer)) {
            this._historyLayer.getComponent(XDZouShi).updateHistory(this._historyListData);
        }
    }

    //结算更新走势
    public updateResultTrend(redpoint: number, whitepoint: number) {
        this._historyListData.push({ redpoint: redpoint, whitepoint: whitepoint });
        if (cc.isValid(this._historyLayer)) {
            this._historyLayer.getComponent(XDZouShi).updateHistory(this._historyListData, true);
        }
    }

    //更新游戏走势小图
    public onUpdateHistoryIcon(msg: any) {
        // let keys = [];
        // let reclist = msg["reclist"] || {};
        // for (let key in reclist) {
        //     keys.push(reclist[key]["redpoint"]);
        // }
        // if (keys.length > 20) {
        //     keys.splice(0, keys.length - 20);
        // }
        // this._historyNode.removeAllChildren();
        // for (let i = 0; i < keys.length; i++) {
        //     this.addHistoryItem(keys[i]);
        // }
    }

    //结算更新历史记录
    public addHistoryItem(winpoint: number, animation: boolean = false) {
        let children = this._historyNode.children;
        let childrenCount = this._historyNode.childrenCount;
        if (childrenCount >= 20) {
            let firstchild = children[0];
            if (cc.isValid(firstchild)) {
                cc.tween(firstchild).to(0.2, { opacity: 0 }).call(() => {
                    firstchild.removeFromParent();
                    this.addAndMoveTrendPoints(winpoint, animation);
                }).start();

            }
        } else {
            this.addAndMoveTrendPoints(winpoint, animation);
        }

    }

    public addAndMoveTrendPoints(winpoint: number, needmove: boolean = false) {
        let childrenCount = this._historyNode.childrenCount;
        let res = "res/images/history/smallnum" + winpoint;
        let offsetX = 35.2;
        let posX = childrenCount * offsetX + 15;
        let sprite = this.createSprite(res, this._historyNode, cc.v2(posX, 0));
        let children = this._historyNode.children;
        childrenCount = this._historyNode.childrenCount;
        if (needmove) {
            sprite.node.opacity = 0;
            cc.tween(sprite.node).to(0.03, { opacity: 255 }).start();
            for (let i = 0; i < childrenCount - 1; i++) {
                cc.tween(children[i]).to(0.1, { x: i * offsetX + 15 }).start();
            }

        } else {
            for (let i = 0; i < childrenCount; i++) {
                children[i].x = i * offsetX + 15;
            }
        }
    }

    //下注成功
    public myBetSuccess(msg: any) {
        let chouma = msg.chouma;
        let allbet = msg.allbet;
        let bet = msg.bet;
        if (bet) {
            for (let key in bet) {
                this.setSelfBetArea(Common.toInt(key) - 1, bet[key], chouma);
            }
        }
        if (allbet) {
            for (let key in allbet) {
                this.setBetAreaTotal(Common.toInt(key) - 1, allbet[key]);
            }
        }

    }

    /**
     * 修改筹码回调
     */
    private updateChipCall() {
        let self = this;
        let lengthVal = self._flyingBets.length;

        // if (lengthVal > 0) {
        //     let val = self._flyingBets[0];
        //     let startPos = val.player.convertToWorldSpaceAR(val.player.getAnchorPoint());
        //     this._gameChip.onlineBet(val.score, startPos, val.area, val.callback);
        //     self._flyingBets.splice(0, 1);
        // }
        if (lengthVal <= 0) return;

        let allScore = [0, 0, 0, 0, 0, 0];
        let startPos = this.btnOnline.convertToWorldSpaceAR(this.btnOnline.getAnchorPoint());
        let lastval = [];
        for (let i = 0; i < lengthVal; i++) {
            let val = self._flyingBets[i];
            if (val.score) {
                allScore[val.area] += val.score;
                lastval[val.area] = val;
            }
        }
        for (let i = 0; i < 6; i++) {
            let count = i < 2 ? 2 : 1;
            let chipScore = this._gameChip.splitUserBetChipScore(allScore[i], count);
            for (let j = 0; j < chipScore.length; j++) {
                this._gameChip.onlineBet(chipScore[j], startPos, i, function () {
                    if (lastval[i] && lastval[i].callback) {
                        lastval[i].callback();
                    }
                });
            }
        }
        self._flyingBets = [];
    }

    private openUpdateScheduler() {
        if (!this._updateScheduler) {
            this._updateScheduler = this.schedule(this.updateChipCall, 0);
        }
    }

    private closeUpdateScheduler() {
        if (this._updateScheduler) {
            this.unschedule(this._updateScheduler);
            this._updateScheduler = null;
        }
    }

    /**
     * 同步玩家下注筹码
     * @param playerid 
     * @param count 
     * @param chipNum 
     * @param betinfo 
     * @param area 
     * @param total 
     * @returns 
     */
    public updateChipMsg(playerid: any, count: number, chipNum: number, betinfo: any, area: number, total: {}) {
        let gameMyInfo = this._gameCore.userInfo;

        if (playerid == gameMyInfo.playerid) return;

        // if (this._flyingBets.length > 0) {
        //     this.updateChipCall();
        // }

        this.openUpdateScheduler();

        let msg: any = {};
        msg.playerid = playerid;
        msg.chouma = betinfo.chouma;
        msg.odds = chipNum;
        msg.direction = area - 1;
        msg.dirctionall = total[area];
        this.onUpdateUserBetUserInfo(msg);

        let player = null;
        if (msg.playerid != gameMyInfo.playerid) {
            for (let userindex = 0; userindex < this._playersList.length; userindex++) {
                if (msg.playerid == this._playersList[userindex].playerid) {
                    player = this._playersList[userindex];
                }
            }

        }

        //在线玩家
        if (player == null) {
            this._userBetMsgTemp.push(msg);
            this.onOtherUserBet();
            this.setUserBetAreaInfo(msg);
            return;
        }

        //座位玩家
        let self = this;
        if (count > 0) {
            if (msg.playerid != gameMyInfo.playerid) {
                let startPos = player.head.convertToWorldSpaceAR(player.head.getAnchorPoint());
                this._gameChip.playerBet(msg.odds, startPos, msg.direction, function () {
                    self.setBetAreaTotal(msg.direction, msg.dirctionall);
                });

                if (player.score) {
                    player.score.string = Common.moneyString(msg.chouma, 7, 0) + "";
                }

                // self.userBetHeadMove(player);
            } else {
                self.setBetAreaTotal(msg.direction, msg.dirctionall);
            }
        } else {
            self.setBetAreaTotal(msg.direction, msg.dirctionall);
        }

        self.shenSZBet(msg.playerid, msg.direction);
    }

    /**
     * 玩家下注同步玩家数据
     * @param msg 
     */
    public onUpdateUserBetUserInfo(msg: any) {
        let val = this._allUsers[msg.playerid];
        if (val) {
            val.money = msg.chouma;
        }
    }
    /**
     * 玩家头像移动
     * @param player 
     */
    public userBetHeadMove(player: XDModel.Player) {
        if (!player.isMove) {
            player.isMove = true;
            let movePos = 20;
            if (player.chairId == 0 || player.chairId == 3 || player.chairId == 5) {
                movePos = -20;
            }
            let oldSize = player.headBg.getPosition();
            cc.tween(player.headBg)
                .to(0.04, { position: cc.v3(player.headBg.x + movePos, player.headBg.y) })
                .to(0.04, { position: cc.v3(oldSize.x, oldSize.y) })
                .delay(0.02)
                .call(function () {
                    player.isMove = false;
                }).start();
        }
    }

    /**
     * 在线用户头像移动
     */
    public onlineUserBetHeadMove() {
        let self = this;
        if (!this._movingOnlineUserHead) {
            this._movingOnlineUserHead = true;
            let movePosX = -20;
            let movePosY = -20;
            let startPos = this.btnOnline.getPosition()

            let callFun = function () {
                self._movingOnlineUserHead = false;
            };
            cc.tween(this.btnOnline)
                .to(0.05, { position: cc.v3(startPos.x + movePosX, startPos.y + movePosY) })
                .to(0.05, { position: cc.v3(startPos.x, startPos.y) })
                .call(callFun)
                .start();
        }
    }
    /**
     * 玩家下注
     */
    public onOtherUserBet() {
        let self = this;
        for (let i = 0; i < self._userBetMsgTemp.length; i++) {
            let key = i;
            let val = self._userBetMsgTemp[i];
            if (val) {
                if (val.direction == 2) {

                }
                if (val.odds > 16000) {
                    let userBet = this._gameChip.splitUserBetChipScore(val.odds, 3);

                    for (let j = 0; j < userBet.length; j++) {
                        self._flyingBets.push({ score: userBet[j], player: self.btnOnline, area: val.direction })
                    }

                } else {
                    self._flyingBets.push({
                        score: val.odds, player: self.btnOnline, area: val.direction, callback: function () {
                            self.setUserBetAreaInfo(val);
                        }
                    })

                }
            }
            self._userBetMsgTemp.splice(key, 1);
            i--;
        }

    }
    /**
     * 设置下注区域筹码数据显示
     * @param msg 
     */
    public setUserBetAreaInfo(msg: any) {
        let gameMyInfo = this._gameCore.userInfo;
        let direction = msg.direction;
        let areaAll = msg.dirctionall;
        this.setBetAreaTotal(direction, areaAll);

        if (msg.playerid == gameMyInfo.playerid) {
            this.setSelfBetArea(direction, msg.buyall, msg.chouma)
        }
    }

    //更新总注
    public setBetAreaTotal(area: number, total: number) {
        let curAreaAll = this.deskTopBet[area].total;
        if (total >= curAreaAll) {
            this.deskTopBet[area].total = total;     //--这个区域下的总注
            this.deskTopBet[area].totaLabel.string = Common.moneyString(total, 7, 0) + "";//--这个区域下的总注
        }
    }

    //更新自己下注
    public setSelfBetArea(area: number, score: number, chouma: number) {
        this.deskTopBet[area].selfLabel.node.active = score > 0;
        if (score <= 0) return;
        this.deskTopBet[area].self = score;
        this.deskTopBet[area].selfLabel.string = Common.moneyString(score, 7, 0) + "";   //--我在这个区域下的总注
        this.myInfo.score.string = Common.moneyString(chouma, 7, 0) + "";
        this.myInfo.isBet = true;
        this._betLayer.setX2State(true);

        let gameMyInfo = this._gameCore.userInfo;
        gameMyInfo.money = chouma;
        this._betLayer.autoSelectChip();
    }

    //更新自己下注
    public addSelfBetArea(area: number, score: number) {
        if (!this.deskTopBet[area]) return;
        let gameMyInfo = this._gameCore.userInfo;
        this.deskTopBet[area].self += score;
        this.deskTopBet[area].total += score;
        let curAreaAll = this.deskTopBet[area].total;

        this.setSelfBetArea(area, this.deskTopBet[area].self, gameMyInfo.money);
        this.setBetAreaTotal(area, curAreaAll);
    }

    //自己下注总额
    public getSelfBet() {
        let selfTotal = 0;
        for (let i = 0; i < this.deskTopBet.length; i++) {
            let val = this.deskTopBet[i];
            if (val) {
                selfTotal += val.self;
            }
        }
        return selfTotal;
    }

    //获取区域自己下注
    public getSelfAreaBet(area: number) {
        let selfTotal = 0;
        let val = this.deskTopBet[area];
        if (val) {
            selfTotal += val.self;
        }
        return selfTotal;
    }

    /**
     * 神算子下注显示星
     * @param playerid 
     * @param area 
     */
    shenSZBet(playerid: any, area: any) {
        let self = this;

        if (playerid == self._playersList[0].playerid) {
            let lucky: cc.Node = self.deskTopBet[area].starLucky;
            // let luckyNode = self._centerLayer.getChildByName("MoveStar");
            // if (luckyNode.active == false && lucky.active == false) {
            if (lucky.active == false) {
                let player = self._playersList[0];

                let startPos = cc.v2(self._centerLayer.convertToNodeSpaceAR(player.head.convertToWorldSpaceAR(player.head.getAnchorPoint())));
                let speed = 700
                let endPos = self._centerLayer.convertToNodeSpaceAR(self.deskTopBet[area].node.convertToWorldSpaceAR(lucky.getPosition()));
                let time = endPos.sub(startPos).mag() / speed
                let p1 = cc.v2(startPos.x, startPos.y)
                let p2 = cc.v2(startPos.x + (endPos.x - startPos.x) * 0.5 - 100, startPos.y + (endPos.y - startPos.y) * 0.6)

                let bezier = [p1, p2, cc.v2(endPos.x, endPos.y)];

                let luckyNode = cc.instantiate(this.moveStar);
                self._centerLayer.addChild(luckyNode);

                let particleSystem = luckyNode.getComponent(cc.ParticleSystem);
                particleSystem.autoRemoveOnFinish = true;
                particleSystem.resetSystem();

                luckyNode.setPosition(startPos);
                luckyNode.active = true;
                lucky.active = true;
                lucky.opacity = 0;
                cc.tween(luckyNode).then(cc.bezierTo(time, bezier)).call(function () {
                    lucky.opacity = 255;

                }).removeSelf().start();


            }
        }
    }

    public restoreDeskChips(area: number, score: number) {
        this._gameChip.createChips(area, score, 15);
    }

    /**
     * 清除游戏数据
     */
    cleanTable() {
        this._gameCore.lastbet = [];
        for (let i = 0; i < this.deskTopBet.length; i++) {
            let val = this.deskTopBet[i];
            if (val) {
                //记录上局
                this._gameCore.lastbet[i] = val.self;

                val.total = 0;
                val.self = 0;
                val.totaLabel.string = "0";
                val.selfLabel.string = "0";
                val.starLucky.active = false;
                val.selfLabel.node.active = false;
            }
        }
        this._userBetMsgTemp = [];
        this._flyingBets = [];
        this.closeUpdateScheduler();
        this._gameChip.resetChips();

        this._gameWan.active = true;
        this._gameWanAni.active = false;

        this.hideTipWait();

        this.myInfo.isBet = false;

        this.stopBetOut();
        //清理分数效果
        // this._resultNode.removeAllChildren();
        this._centerLayer.stopAllActions();
        this._statePanel.stopAllActions();
        this._statePanel.active = false;

        this.setGameStateTip(false, 0);
        this._spCountDown.node.active = false;
    }
    //////////////////////////////////////////////////////////////////////////////
}