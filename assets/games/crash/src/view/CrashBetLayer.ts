import Common from "../../../../script/frame/common/Common";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import XDChipsLayer from "./CrashChipsLayer";
import XDDefine = require("../core/CrashDefine");
import XDGameCore from "../core/CrashGameCore";
import XDGameView from "./CrashGameView";
import XDModel from "../core/CrashModel";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import XDAudioMng from "../core/CrashAudioMng";
import BaseLayer from "../../../../script/frame/component/BaseLayer";

const { ccclass, property } = cc._decorator;

/**
 * 筹码选择 下注 组件
 */
@ccclass
export default class CrashBetLayer extends BaseLayer {

    @property(cc.Node)
    notTipBox: cc.Node = null;

    @property(cc.ScrollView)
    svChips: cc.ScrollView = null;

    @property(cc.Node)
    left: cc.Node = null;

    @property(cc.Node)
    right: cc.Node = null;

    @property(cc.Node)
    btnX2: cc.Node = null;

    @property(cc.Node)
    btnXuTou: cc.Node = null;

    @property(cc.Prefab)
    betChipPrefab: cc.Prefab = null;

    //选中筹码索引
    private _selectIndex = 0;
    //筹码Y
    private _betChipY = -11;
    //选中筹码Y
    private _selBetChipY = -5;
    //所有筹码节点
    private _btnChips: cc.Node[] = [];
    //游戏core层
    private _gameCore: XDGameCore;
    //游戏视图层
    private _gameView: XDGameView;
    //声音
    private _audioMng: XDAudioMng;
    //游戏筹码
    private _gameChip: XDChipsLayer;

    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent(XDGameCore);
        this._gameView = cc.Canvas.instance.getComponent(XDGameView);
        this._gameChip = cc.Canvas.instance.getComponent(XDChipsLayer);
        this._audioMng = cc.Canvas.instance.getComponent(XDAudioMng);
        this.notTipBox.active = false;
    }

    public get selectIndex(): number {
        return this._selectIndex;
    }

    public initBet() {
        if (this._btnChips.length > 0) return;
        let chipsScore = this._gameCore.chipsScore;
        let chipsRes = this._gameCore.chipsRes;
        let betNeed = this._gameCore.betNeed;
        let myMoney: number = this._gameCore.getMeMoney();
        let length = chipsScore.length;
        this._selectIndex = 0;

        this.svChips.content.removeAllChildren();
        this.svChips.content.width = (length - 1) * 105 + 52 * 2;

        for (var i = 0; i < chipsScore.length; i++) {

            let chipItem = cc.instantiate(this.betChipPrefab);
            let sprite = chipItem.getChildByName("chip").getComponent(cc.Sprite);
            chipItem.x = 56 + i * 105;
            chipItem.y = this._betChipY;
            chipItem.getChildByName("select").getComponent(sp.Skeleton).animation = "animation";
            chipItem.getChildByName("select").active = false;
            this.setSpriteFrame(sprite, "res/images/chip/" + chipsRes[i]);

            let btn = chipItem.getChildByName("chip").getComponent(cc.Button);

            let eventHandler = new cc.Component.EventHandler();
            eventHandler.component = cc.js.getClassName(this);
            eventHandler.handler = 'onClickChip';
            eventHandler.target = this.node;
            eventHandler.customEventData = i + '';
            btn.clickEvents = [];
            btn.clickEvents.push(eventHandler);
            btn.enableAutoGrayEffect = true;

            this.svChips.content.addChild(chipItem);
            this._btnChips.push(chipItem);
        }

        this.svChips.node.on("scrolling", () => {
            this.adjustArrow();
        });
        this.svChips.node.on("scroll-ended", () => {
            this.adjustScroll();
            this.adjustArrow();
        });

        this.right.active = true;
        this.left.active = false;

        this.notTipBox.getChildByName("MinLabel").getComponent(cc.Label).string = Common.moneyString(betNeed, 6, 0);
        if (myMoney < betNeed) {
            this.notTipBox.active = true;
        }

        this.disableChips(true);
    }

    private adjustScroll() {
        let x = Math.abs(this.svChips.content.x);
        let width = this.svChips.content.width;
        let nodewidth = this.svChips.node.width;
        let offset = width - nodewidth;
        let m = Math.floor(3 * x / offset) * 105;
        let n = Math.ceil(3 * x / offset) * 105;
        let k = Math.abs(m - x) > Math.abs(n - x) ? n : m;
        this.svChips.content.x = -k;
    }

    private adjustArrow() {
        let width = this.svChips.content.width;
        let nodewidth = this.svChips.node.width;
        let offset = width - nodewidth;
        let x = this.svChips.content.x;
        this.right.active = x > -offset;
        this.left.active = x < 0;
    }

    public scrollLeft() {
        this.svChips.scrollToLeft(0.3);
    }

    public scrollRight() {
        this.svChips.scrollToRight(0.3);
    }

    public onClickChip(event, customData) {
        let index = Common.toInt(customData);
        this.setSelectChips(index);
        this._audioMng.playClick();
    }

    //x2状态
    public setX2State(enable: boolean) {
        this.btnX2.getComponent(cc.Button).interactable = enable;
    }

    //续投状态
    public setXuTouState(enable: boolean) {

        let lastbet = this._gameCore.lastbet;
        if (!lastbet || lastbet.length == 0) enable = false;

        let lastAllbet = 0;
        for (let i = 0; i < lastbet.length; i++) {
            lastAllbet += lastbet[i];
        }
        if (lastAllbet == 0) enable = false;

        this.btnXuTou.getComponent(cc.Button).interactable = enable;
        // this.btnXuTou.color = enable ? cc.color(255, 255, 255) : this._disableColor;
    }

    public setArrowState(enable: boolean) {
        // this.right.color = enable ? cc.color(255, 255, 255) : this._disableColor;
        // this.left.color = enable ? cc.color(255, 255, 255) : this._disableColor;
    }

    /**
    设置筹码筹码选中
    @param index 下注区域筹码索引
    */
    private setSelectChips(index: number) {

        let selectScore = 0;
        if (index < this._gameCore.chipsScore.length && this._gameCore.chipsScore[index]) {
            selectScore = this._gameCore.chipsScore[index];
        }
        let myMoney: number = this._gameCore.getMeMoney();

        if (myMoney < selectScore) return;

        this._selectIndex = index;
        for (var i = 0; i < this._btnChips.length; ++i) {
            let chip = this._btnChips[i];
            if (myMoney < this._gameCore.chipsScore[i]) {
                this.setDisableChips(i, true);
            } else {
                let select = chip.getChildByName("select");
                if (index == i) {
                    chip.y = this._selBetChipY;
                    chip.scale = 1.1;
                    select.active = true;
                } else {
                    chip.y = this._betChipY;
                    chip.scale = 1;
                    select.active = false;
                }
                chip.getChildByName("chip").getComponent(cc.Button).interactable = true;
            }
        }
        return true;
    }

    /**
    * 设置所有筹码是否可选
    * @param index 
    * @param hasEnabled boolean true 不可选 false 可选
    * @returns 
    */
    public disableChips(hasEnabled: boolean) {
        for (var i = 0; i < this._btnChips.length; ++i) {
            this.setDisableChips(i, hasEnabled);
        }

        if (hasEnabled) {
            this.setX2State(false);
            this.setXuTouState(false);
            this.setArrowState(false);
        }
    }

    /**
     * 设置单个筹码是否可选
     * @param index 
     * @param hasEnabled boolean true 不可选 false 可选
     * @returns 
     */
    private setDisableChips(index: number, hasEnabled: boolean) {
        if (index >= this._btnChips.length) {
            return;
        }
        let chip = this._btnChips[index];
        chip.y = this._betChipY;
        chip.scale = hasEnabled ? 1 : 1.1;
        chip.getChildByName("chip").getComponent(cc.Button).interactable = !hasEnabled;
        let select = chip.getChildByName("select");
        select.active = !hasEnabled;
    }

    public updateNotTipBox() {
        this.notTipBox.active = this._gameCore.getMeMoney() < this._gameCore.betNeed;
    }

    /**
     *根据用户金额，设置筹码是否可选
     */
    public autoSelectChip() {
        let myMoney: number = this._gameCore.getMeMoney();
        if (myMoney < this._gameCore.betNeed) {
            this.disableChips(true);
            this.notTipBox.active = true;
            return;
        }
        this.updateNotTipBox();

        let selectScore: number = this._gameCore.chipsScore[this._selectIndex];
        let selectIndex = this._selectIndex;
        //当前选中筹码，大于用户金额
        if (selectScore > myMoney) {
            //设置可选最大金额
            for (let i = 0; i < this._gameCore.chipsScore.length; i++) {
                if (myMoney >= this._gameCore.chipsScore[i]) {
                    selectIndex = i;
                }
            }
        }
        this.setSelectChips(selectIndex);
        this.setArrowState(true);
    }

    /**
     * 点击下注区域
     * @param area 下注区域
     * @returns 
     */
    public onClickBet(area: number) {
        let gameCore = this._gameCore;
        let isStartBet: boolean = gameCore.isStartBet;
        if (!isStartBet) return;
        let myMoney: number = gameCore.userInfo.money;
        let betNeed: number = gameCore.betNeed;
        let betMax: number = gameCore.betMax;
        if (this._selectIndex > -1) {
            let selectMoney = this._gameCore.chipsScore[this._selectIndex];
            if (myMoney < betNeed) {
                ToastHelper.show(Common.stringFormat(XDDefine.LangData.GAME_BET_MIN_ERR, [betNeed]));
                return;
            }

            if (myMoney >= selectMoney) {
                gameCore.requestBet(area + 1, 0, selectMoney);

                let selfAllBet = this._gameView.getSelfBet();
                if ((selfAllBet + selectMoney) > betMax) return;

                this._gameView.addSelfBetArea(area, selectMoney);

                let player = this._gameView.myInfo.head;
                let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
                this._gameChip.bet(selectMoney, playerPos, area, false, function () { });

                this.autoSelectChip();
            }
        } else {
            ToastHelper.show(XDDefine.LangData.GAME_PLEASE_SELECT_GOLD_TIP);
        }

    }

    //点击X2
    public onClickX2() {
        let gameCore = this._gameCore;

        let isStartBet: boolean = gameCore.isStartBet;
        if (!isStartBet) return;

        let selfAllBet = this._gameView.getSelfBet();
        if (selfAllBet == 0) return;

        let myMoney: number = gameCore.userInfo.money;
        if (myMoney < selfAllBet) {
            ToastHelper.show(Common.stringFormat(XDDefine.LangData.GAME_X2BET_MIN_ERR, [selfAllBet]));
            return;
        }

        let betMax: number = gameCore.betMax;

        if (selfAllBet * 2 > betMax) {
            ToastHelper.show(Common.stringFormat(XDDefine.LangData.GOLD_BET_MAX_ERR, [selfAllBet]));
            return;
        }

        for (let i = 0; i < 6; i++) {
            let areaSelf = this._gameView.getSelfAreaBet(i);

            this._gameView.addSelfBetArea(i, areaSelf);

            let player = this._gameView.myInfo.head;
            let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
            this._gameChip.mineMuchBet(areaSelf, playerPos, i);
        }

        gameCore.requestBet(0, 1, 0);
        this.autoSelectChip();
    }

    //点击续投
    public onClickXuTou() {
        let gameCore = this._gameCore;

        let isStartBet: boolean = gameCore.isStartBet;
        if (!isStartBet) return;

        let lastbet = this._gameCore.lastbet;
        if (!lastbet || lastbet.length == 0) return;

        let lastAllbet = 0;
        for (let i = 0; i < lastbet.length; i++) {
            lastAllbet += lastbet[i];
        }
        if (lastAllbet == 0) {
            console.log("lastallbet is 0");
            return;
        }

        let myMoney: number = gameCore.userInfo.money;
        if (myMoney < lastAllbet) {
            ToastHelper.show(Common.stringFormat(XDDefine.LangData.GAME_XUTOUBET_MIN_ERR, [lastAllbet]));
            return;
        }

        let betMax: number = gameCore.betMax;
        let selfAllBet = this._gameView.getSelfBet();
        if ((selfAllBet + lastAllbet) > betMax) {
            ToastHelper.show(XDDefine.LangData.GOLD_BET_MAX_ERR);
            return;
        }

        for (let i = 0; i < lastbet.length; i++) {
            let score = lastbet[i];
            if (score > 0) {
                this._gameView.addSelfBetArea(i, score);

                let player = this._gameView.myInfo.head;
                let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
                this._gameChip.mineMuchBet(score, playerPos, i);
            }

        }

        gameCore.requestFollowHistoryBet();
        this.autoSelectChip();
        this.setXuTouState(false);
    }

}
