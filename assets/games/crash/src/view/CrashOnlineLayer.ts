import Common from "../../../../script/frame/common/Common";
import ListView from "../../../../script/frame/component/ListView";
import PopupLayer from "../../../../script/frame/component/PopupLayer";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class CrashOnlineLayer extends PopupLayer {
    //////////////////////////////////////////////////////////////////////////////
    @property(ListView)
    listView: ListView = null;

    private _playerListData = [];

    private _init: boolean = false;

    start() {
        this.show();
        this.refreshListView();
        this._init = true;
    }

    private refreshListView() {
        let eventHandler = new cc.Component.EventHandler();
        eventHandler.component = cc.js.getClassName(this);
        eventHandler.handler = 'refreshItem';
        eventHandler.target = this.node;
        this.listView.refreshScrollView(this._playerListData, eventHandler);
    }

    private refreshItem(node, data, index) {
        let bg = node.getChildByName("Bg");
        let rankBox = bg.getChildByName("RankBox");
        let headBox = bg.getChildByName("HeadBox");
        let goldBox = bg.getChildByName("GoldBox");
        let roundBox = bg.getChildByName("RoundBox");
        let winBox = roundBox.getChildByName("WinBox");
        let rankImg = rankBox.getChildByName("RankImg");
        let rankLabBg = rankBox.getChildByName("RankLabBg");
        let headBg = headBox.getChildByName("HeadBg");
        let headImg = headBox.getChildByName("HeadImg");
        let gameLab = goldBox.getChildByName("GameLabel");
        let goldLab = goldBox.getChildByName("GoldLabel");
        let betLab = roundBox.getChildByName("BetLabel");
        let winLab = winBox.getChildByName("WinLabel");

        if (index < 2) {
            this.setSpriteFrame(bg.getComponent(cc.Sprite), "res/images/rank_cell_l");
        } else {
            this.setSpriteFrame(bg.getComponent(cc.Sprite), "res/images/rank_cell_d");
        }

        if (index < 9) {
            rankImg.active = true;
            rankLabBg.active = false;
            this.setSpriteFrame(rankImg.getComponent(cc.Sprite), "res/images/rich_" + index);
        } else {
            rankImg.active = false;
            rankLabBg.active = true;
            rankLabBg.getChildByName("RankLabel").getComponent(cc.Label).string = index + '';
        }

        this.setPlayerFrame(headBg.getComponent(cc.Sprite), data.headframeid);
        this.setPlayerHead(headImg.getComponent(cc.Sprite), data.headid);
        gameLab.getComponent(cc.Label).string = Common.textClamp(data.nickname, 20);
        goldLab.getComponent(cc.Label).string = Common.moneyString(data.coin, 6, 0);
        betLab.getComponent(cc.Label).string = Common.moneyString(data.bet, 6, 0);
        winLab.getComponent(cc.Label).string = data.winnum + '';
    }


    public loadPlayerList(playerListData: any) {
        let totalplayernum = playerListData.totalplayernum || 0;
        this._playerListData = [];
        for (let i = 1; i <= totalplayernum; i++) {
            if(playerListData[i]){
                this._playerListData.push(playerListData[i]);
            }
            if (i == 50) break;
        }
        if(this._init){
            this.refreshListView();
        }
    }

    // 点击关闭按钮
    private onClickClose() {
        this.close();
    }

}
