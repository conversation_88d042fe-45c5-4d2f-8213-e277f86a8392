/**
 * 筹码结算相关方法
 * 筹码移动
 * 筹码分拆
 */
import Common from "../../../../script/frame/common/Common";
import XDGameView from "./CrashGameView";
import XDModel from "../core/CrashModel";
import XDGameCore from "../core/CrashGameCore";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import XDAudioMng from "../core/CrashAudioMng";

const { ccclass, property } = cc._decorator;

@ccclass
export default class CrashChipsLayer extends BaseLayer {

    @property(cc.Prefab)
    chipPrefab: cc.Prefab = null;

    @property(cc.Node)
    chipParent: cc.Node = null;

    //游戏core层
    private _gameCore: XDGameCore;
    /** 游戏视图 */
    private _gameView: XDGameView;
    //声音
    private _audioMng: XDAudioMng;

    //筹码集合
    private _chipsArr = [];

    private chipsPool: cc.NodePool = null;
    private initCount: number = 150;
    private maxCount: number = 500;
    private chipTime: number = 0;

    public chipsSmallRes: any = ["chip_1k", "chip_5k", "chip_10k", "chip_50k", "chip_100k", "chip_500k", "chip_1000k", "chip_5000k"];  //筹码资源
    public preChipRes = ["jd", "jz", "small", "tm", "yel"];

    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent(XDGameCore);
        this._gameView = cc.Canvas.instance.getComponent(XDGameView);
        this._audioMng = cc.Canvas.instance.getComponent(XDAudioMng);

        this.chipsPool = new cc.NodePool();
        for (let i = 0; i < this.initCount; ++i) {
            let chip = cc.instantiate(this.chipPrefab);
            this.chipsPool.put(chip);
        }
    }

    onDestroy() {
        this.chipsPool.clear();
    }

    public resetChips() {
        this.stopPlayFlyGoldSound();
        for (let i = 0; i < this._chipsArr.length; ++i) {
            this.chipsPool.put(this._chipsArr[i]);
        }
        this._chipsArr = [];
        this.chipParent.stopAllActions();
        this.maxCount = 500;
    }

    /**
     * 根据下注总分数 拆分筹码分数
     * @param score 
     * @param count 
     * @returns 
     */
    public splitUserBetChipScore(score: any, count: number): number[] {
        let publicScore = this._gameCore.chipsScore;
        let chipScore = score;
        let tempCount = 0;
        let userScore: number[] = [];
        for (let i = 0; i < publicScore.length; i++) {
            tempCount = Math.floor(chipScore / publicScore[i]);
            chipScore = publicScore[i];
            if (tempCount > count) {
                tempCount = Math.floor(count / (i + 1));
            }
            for (let j = 0; j < tempCount; j++) {
                userScore.push(chipScore);
            }

            chipScore = score - (userScore.length * 100);
        }
        return userScore;
    }

    //恢复桌上筹码
    public createChips(area, score, count) {
        let chipScore = this.splitUserBetChipScore(score, count);
        if (!chipScore || chipScore.length <= 0) {
            return;
        }
        let length = chipScore.length;
        if (length > this.maxCount) {
            this.maxCount = length + 20;
        }
        for (let i = 0; i < length; i++) {
            let chip = this.createChip(chipScore[i], false);
            let endPos = this.getChipFinalPos(chip, area);
            chip.setPosition(endPos);
            chip.scale = 0.92;
        }
    }

    public onlineBet(score: number, startPos: cc.Vec2, area: number, callback: Function) {
        let playSound = Common.random(1, 100) > 80;
        this.bet(score, startPos, area, playSound, callback);
    }

    public playerBet(score: number, startPos: cc.Vec2, area: number, callback: Function) {
        let playSound = Common.random(1, 100) > 30;
        this.bet(score, startPos, area, playSound, callback);
    }

    //自己同时下注多个
    public mineMuchBet(score: number, startPos: cc.Vec2, area: number) {
        let chipScore = this.splitUserBetChipScore(score, 10);
        let self = this;
        for (let i = 0; i < chipScore.length; i++) {
            let value = chipScore[i];
            cc.tween(this.chipParent)
                .delay(0.1 * i).call(function () {
                    self.bet(value, startPos, area, true, null);
                }).start();
        }
    }

    public bet(value: number, startPos: cc.Vec2, area: number, playSound: boolean, callback: Function) {
        let chip = this.createChip(value);
        if (chip) {
            startPos = cc.v2(this.chipParent.convertToNodeSpaceAR(startPos));
            chip.x = startPos.x;
            chip.y = startPos.y;

            let endPos = this.getChipFinalPos(chip, area);
            this.chipAction(chip, startPos, endPos, playSound, callback);
        }
    }

    private chipAction(chip: cc.Node, startPos: cc.Vec2, endPos: cc.Vec2, playSound: boolean, callback: Function) {
        let self = this;
        let speed = 1300;
        let time = cc.Vec2.distance(startPos, endPos) / speed;

        cc.tween(chip)
            // .to(time, { x: endPos.x, y: endPos.y, scale: 1.1 }, { easing: (dt: number) => cc.easeOut(1.0).easing(dt) })
            // .to(time, { x: endPos.x, y: endPos.y }, { easing:"quadOut" })
            .to(time, { x: endPos.x, y: endPos.y }, { easing: (dt: number) => cc.easeOut(2.0).easing(dt) })
            .to(0.4, { scale: 0.92 }, { easing: (dt: number) => cc.easeOut(1.0).easing(dt) })
            .call(function () {
                if (playSound) {
                    let play = false;
                    if (self.chipTime = 0) {
                        play = true;
                        self.chipTime = Common.getCTime();
                    } else {
                        let curTime = Common.getCTime();
                        if ((curTime - self.chipTime) > 300) {
                            play = true;
                            self.chipTime = curTime;
                        }
                    }
                    if (play) {
                        self._audioMng.playBet();
                    }
                }

                if (callback) {
                    callback();
                }
            })
            .start()
    }

    private createChip(value: number, clearOver: boolean = true) {
        let index = this._gameCore.chipsScore.indexOf(value);
        if (index < 0) return;

        let chip = this.chipsPool.get();
        if (!chip) {
            chip = cc.instantiate(this.chipPrefab);
        }

        chip.stopAllActions();
        chip.parent = this.chipParent;
        chip.angle = Math.floor(Math.random() * 140) - 70;
        chip.scale = 1;
        chip.active = true;

        let sprite = chip.getChildByName("chip").getComponent(cc.Sprite);

        this.setSpriteFrame(sprite, "res/images/chip/" + this.getPreChipRes()+ "_" + this.chipsSmallRes[index]);

        this._chipsArr.push(chip);
        chip.zIndex = 10 + this._chipsArr.length;

        if (clearOver) {
            this.clearOverChip();
        }

        return chip;
    }

    private clearOverChip() {
        let chipsLength = this._chipsArr.length;
        if (chipsLength > this.maxCount) {
            let chip = this._chipsArr.shift();
            if (chip) {
                this.chipsPool.put(chip);
            }
        }
    }

    private getPreChipRes() {
        let num = Common.random(0, 100);
        let index = 0;
        if (num > 90)
            index = 4;
        else if (num > 80)
            index = 3;
        else if (num > 60)
            index = 2;
        else if (num > 30)
            index = 1;
        else
            index = 0;
        return this.preChipRes[index];
    }

    private getChipFinalPos(chip: cc.Node, area: number) {
        let chipPosNode = this._gameView.deskTopBet[area].chipArea;
        let rangPos = cc.v2(this.chipParent.convertToNodeSpaceAR(chipPosNode.convertToWorldSpaceAR(chipPosNode.getAnchorPoint())));
        let subNum = chipPosNode.width * 0.5 - chip.width * 0.5;
        let minX = rangPos.x - subNum;
        let maxX = rangPos.x + subNum;

        subNum = chipPosNode.height * 0.5 - chip.height * 0.5;
        let minY = rangPos.y - subNum;
        let maxY = rangPos.y + subNum;
        let endPos: cc.Vec2 = cc.v2(Common.random(minX, maxX), Common.random(minY, maxY));
        return endPos;
    }

    public flyChips(posArray: any[], callBack = null) {
        let beforeTime = Common.getCTime();
        let chips = this._chipsArr;
        let self = this;

        if (chips.length < 1) {
            if (callBack) {
                callBack(Common.getCTime() - beforeTime)
            }
            return
        }

        // this.playFlyGoldSound();
        this._audioMng.playWinBet();

        let gapNum = posArray.length;

        let speed = 1000
        let delayGap = 0.015

        if (chips.length > 150) {
            speed = 1400
            delayGap = 0.007
        } else if (chips.length > 50) {
            speed = 1300
            delayGap = 0.01
        }

        let curIndex = chips.length - 1

        let curNum = chips.length
        let m = curIndex;
        let index = 0;
        let len = chips.length;

        for (let i = 0; i < posArray.length; i++) {
            posArray[i] = cc.v2(this.chipParent.convertToNodeSpaceAR(posArray[i]));
        }
        for (let i = 0; i < posArray.length; i++) {
            m = curIndex;
            while (m >= 0) {
                index++;
                let chip = chips[m];
                let chipX = chip.x
                let chipY = chip.y

                let endPos = posArray[i]
                let time = endPos.sub(cc.v2(chipX, chipY)).mag() / speed
                let delay_time = delayGap * index
                if (delay_time > 0.6 && index != len) {
                    delay_time = (Math.random() * (50 - 10) + 10) / 100
                } else if (delay_time > 0.6 && index == len) {
                    delay_time = 0.6
                }
                chip.active = true;

                cc.tween(chip)
                    .delay(delay_time)
                    // .by(0.2, { position: cc.v3((chipX - endPos.x) / 15, (chipY - endPos.y) / 10) }, { easing: (dt: number) => cc.easeIn(0.4).easing(dt) })
                    // .to(time, { position: cc.v3(endPos.x, endPos.y) }, { easing: (dt: number) => cc.easeOut(0.8).easing(dt) })
                    .to(time, { position: cc.v3(endPos.x, endPos.y), scale: 0.7 }, { easing: "quadOut" })
                    .call(function () {
                        chip.active = false;
                        curNum = curNum - 1;
                        if (curNum == 0) {
                            // self.resetChips();
                            self.stopPlayFlyGoldSound();
                            if (callBack) {
                                callBack(Common.getCTime() - beforeTime)
                            }
                        }
                    }).start();

                if (i == 0 && m < gapNum) {
                    m--;
                } else if (m < gapNum) {
                    m = -1;
                } else {
                    m -= gapNum;
                }
            }
            curIndex = curIndex - 1;
        }
    }

    // 播放金币飞行声音1
    private goldSoundEffect1() {
        this._audioMng.playWinBet();
    }

    // 播放金币飞行声音2
    private goldSoundEffect2() {
        this._audioMng.playWinBet();
    }

    // 播放金币飞行声音
    private playFlyGoldSound() {
        this.schedule(this.goldSoundEffect1, 0.2);
        this.schedule(this.goldSoundEffect2, 0.4);
    }

    // 停止金币飞行声音
    private stopPlayFlyGoldSound() {
        this.unschedule(this.goldSoundEffect1);
        this.unschedule(this.goldSoundEffect2);
    }
}