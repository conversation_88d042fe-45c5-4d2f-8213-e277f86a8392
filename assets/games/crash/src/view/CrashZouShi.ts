import PopupLayer from "../../../../script/frame/component/PopupLayer";

const { ccclass, property } = cc._decorator;

@ccclass
export default class CrashZouShi extends PopupLayer {

    /** 中心图片 */
    private _centerImage: cc.Node;
    /** 左侧进度值 */
    private _lPercentLabel: cc.Label;
    /** 右侧进度值 */
    private _rPercentLabel: cc.Label;
    /** 左侧进度图片 */
    private _lProgressBar: cc.Node;
    /** 右侧进度图片 */
    private _rProgressBar: cc.Node;
    //20局走势
    private _historyRecord: cc.Node;
    /** 红色偶数赢的总数 */
    private _wTotalLabel: cc.Label;
    /** 红色0的数量 */
    private _wOneLabel: cc.Label;
    /** 红色4的数量 */
    private _wTwoLabel: cc.Label;

    /** 红色奇数赢的总数 */
    private _lTotalLabel: cc.Label;
    /** 红色1的数量 */
    private _lOneLabel: cc.Label;
    /** 红色3的数量 */
    private _lTwoLabel: cc.Label;

    /** 路线图节点 */
    private _daluScroll: cc.ScrollView;
    private _daluContent: cc.Node;

    private _historyData: any = [];
    private _init: boolean = false;

    start() {
        this.initUI();
        if (this._historyData && this._historyData.length > 0) {
            this.updateProgress();
            this.updateRecord();
            this.updateTotal();
            this.updateDalu();
        }
        this._init = true;
        this.show();
    }

    initUI() {
        let mainBox = this.node.getChildByName("MainBg");
        let topBox = mainBox.getChildByName("TopBox");
        let bottomBox = mainBox.getChildByName("BottomBox");

        this._centerImage = topBox.getChildByName("CenterBg")
        this._lPercentLabel = topBox.getChildByName("LeftBox").getChildByName("PercentLabel").getComponent(cc.Label);
        this._rPercentLabel = topBox.getChildByName("RightBox").getChildByName("PercentLabel").getComponent(cc.Label);

        this._lProgressBar = topBox.getChildByName("LeftBox").getChildByName("PercentBg")
        this._rProgressBar = topBox.getChildByName("RightBox").getChildByName("PercentBg")

        this._historyRecord = mainBox.getChildByName("HistoryBox");

        this._wTotalLabel = bottomBox.getChildByName("LeftBox").getChildByName("TotalLabel").getComponent(cc.Label);
        this._wOneLabel = bottomBox.getChildByName("LeftBox").getChildByName("OneLabel").getComponent(cc.Label);
        this._wTwoLabel = bottomBox.getChildByName("LeftBox").getChildByName("TwoLabel").getComponent(cc.Label);

        this._lTotalLabel = bottomBox.getChildByName("RightBox").getChildByName("TotalLabel").getComponent(cc.Label);
        this._lOneLabel = bottomBox.getChildByName("RightBox").getChildByName("OneLabel").getComponent(cc.Label);
        this._lTwoLabel = bottomBox.getChildByName("RightBox").getChildByName("TwoLabel").getComponent(cc.Label);

        this._daluScroll = mainBox.getChildByName("ScrollView").getComponent(cc.ScrollView);
        this._daluContent = this._daluScroll.node.getChildByName("view").getChildByName("content");
        this._daluContent.removeAllChildren();

    }

    private updateProgress() {
        let historyData = this._historyData;
        let length = historyData.length;
        if (length <= 0) return;
        let shuangWinCount = 0;
        let danWinCount = 0;
        let startIndex = length < 20 ? 0 : length - 20;
        for (let i = startIndex; i < length; i++) {
            let redpoint = historyData[i].redpoint;
            if (redpoint % 2 == 0) {
                shuangWinCount++;
            } else {
                danWinCount++;
            }
        }
        let shuangProgress = shuangWinCount / (shuangWinCount + danWinCount);
        let danProgress = 1-shuangProgress;
        let maxWidth = 470;
        let minWidth = 180;
        let shuangWidth = shuangProgress * (maxWidth - minWidth) + 180;
        let danWidth = danProgress * (maxWidth - minWidth) + 180;

        this._lPercentLabel.string = Math.floor(shuangProgress * 100) + "%";
        this._rPercentLabel.string = (100-Math.floor(shuangProgress * 100)) + "%";
        this._lPercentLabel.node.x = shuangWidth - 15;
        this._rPercentLabel.node.x = -danWidth + 15;
        this._lProgressBar.width = shuangWidth;
        this._rProgressBar.width = danWidth;
        this._centerImage.x = this._lProgressBar.parent.x + this._lProgressBar.width + 104;
    }

    private updateRecord() {
        let historyData = this._historyData;
        let length = historyData.length;
        if (length <= 0) return;
        let startIndex = length < 20 ? 0 : length - 20;
        let index = 0;
        this._historyRecord.removeAllChildren();
        for (let i = startIndex; i < length; i++) {
            let redpoint = historyData[i].redpoint;
            let res = "res/images/history/smallnum" + redpoint;
            let offsetX = 25;
            let posX = index * offsetX + 30;
            this.createSprite(res, this._historyRecord, cc.v2(posX, 0));
            index++;
        }
    }

    private updateTotal() {
        let historyData = this._historyData;
        let length = historyData.length;
        if (length <= 0) return;
        let shuangWinCount = 0;
        let danWinCount = 0;
        let count0 = 0;
        let count1 = 0;
        let count3 = 0;
        let count4 = 0;
        for (let i = 0; i < length; i++) {
            let redpoint = historyData[i].redpoint;
            if (redpoint % 2 == 0) {
                shuangWinCount++;
            } else {
                danWinCount++;
            }
            if (redpoint == 0) count0++;
            if (redpoint == 1) count1++;
            if (redpoint == 3) count3++;
            if (redpoint == 4) count4++;
        }

        this._wTotalLabel.string = shuangWinCount + "";
        this._wOneLabel.string = count0 + "";
        this._wTwoLabel.string = count4 + "";

        this._lTotalLabel.string = danWinCount + "";
        this._lOneLabel.string = count1 + "";
        this._lTwoLabel.string = count3 + "";
    }

    private updateDalu(lastAni: boolean = false) {
        let historyData = this._historyData;
        let length = historyData.length;
        if (length <= 0) return;

        this._daluContent.removeAllChildren();
        let itemBgCount = 0;
        let offsetX = 44;
        let offsetY = 44;
        let preWin = 0;
        let rowLength = 0;
        let colLength = 0;
        let rowcolLength = 0;
        let maxColLength = 0;
        for (let i = 0; i < length; i++) {
            let redpoint = historyData[i].redpoint;
            let redwin = redpoint % 2 == 0;
            let win = redwin ? 1 : 2;
            let res = "res/images/history/num" + redpoint;

            if (win != preWin) {
                colLength++;
                rowcolLength = colLength;
                rowLength = 1;
            } else {
                if (rowLength >= 6) {
                    rowcolLength++;
                } else {
                    rowLength++;
                }
            }
            if (rowcolLength > maxColLength) {
                maxColLength = rowcolLength;
            }
            preWin = win;

            let posX = rowcolLength * offsetX - offsetX * 0.5;
            let posY = 107 - (rowLength - 1) * offsetY;
            let sprite = this.createSprite(res, this._daluContent, cc.v2(posX, posY));
            sprite.node.zIndex = 10;

            if (lastAni && (length - 1) == i) {
                sprite.node.runAction(cc.blink(3, 3));
            }
        }
        maxColLength++;
        itemBgCount = maxColLength > 20 ? maxColLength : 20;
        for (let i = 0; i < itemBgCount; i++) {
            let res = "res/images/history/zpl_bg_0" + (i % 2 + 1);
            let posX = i * offsetX + offsetX * 0.5;
            let sprite = this.createSprite(res, this._daluContent, cc.v2(posX, 0));
            sprite.node.zIndex = 1;
        }
        this._daluContent.width = itemBgCount * offsetX;
        this._daluScroll.scrollToRight();
    }

    public updateHistory(historyData: any, lastAni: boolean = false) {
        this._historyData = historyData;
        if (this._init) {
            this.updateProgress();
            this.updateRecord();
            this.updateTotal();
            this.updateDalu(lastAni);
        }
    }

    // 点击关闭按钮
    private onClickClose() {
        this.close();
    }
}
