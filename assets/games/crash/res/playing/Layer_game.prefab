[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_rawFiles": null, "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "Layer", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 24}, {"__id__": 186}, {"__id__": 206}, {"__id__": 209}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 221}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_top", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 6}], "_tag": -1, "_active": true, "_components": [{"__id__": 22}], "_prefab": {"__id__": 23}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 720, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_4", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 4}], "_prefab": {"__id__": 5}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 1700, "height": 101}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_spriteFrame": {"__uuid__": "a2e924e1-0404-4faf-bf9f-4396b8e36169"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdCP8CzElPQ6TMguWixORI", "sync": false}, {"__type__": "cc.Node", "_name": "Image_jiangchi", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 7}], "_tag": -1, "_active": true, "_components": [{"__id__": 20}], "_prefab": {"__id__": 21}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 556.2, "height": 80.1}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4.1088, -52.4106, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ListView_jiangchi", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 8}, {"__id__": 11}, {"__id__": 14}], "_tag": -1, "_active": true, "_components": [{"__id__": 17}, {"__id__": 18}], "_prefab": {"__id__": 19}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 438, "height": 68}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -6.407999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_17", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 197, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-120.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_spriteFrame": {"__uuid__": "8044c1c7-9556-4de1-9760-5ab62794fac6"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5ed8RrN35GH5CAzPoOo2ho", "sync": false}, {"__type__": "cc.Node", "_name": "jiangchi_num", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 12}], "_prefab": {"__id__": 13}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 237, "height": 40}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-18, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "0e2edcf2-2935-4326-b774-fb135531c462"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "1,000,000", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eiw+awblDWZyjnQ2Oi1G/", "sync": false}, {"__type__": "cc.Node", "_name": "Image_1", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 15}], "_prefab": {"__id__": 16}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 36, "height": 37}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [241, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_spriteFrame": {"__uuid__": "6a68cbf7-3239-4e6c-b3a6-1393ba2db12d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3r8oVRXhNa7S1LdB98V7X", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.10625000000000001, "_right": 0, "_top": 0, "_bottom": -0.0044500000000000095, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.StudioComponent", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_type": 4, "_checkNormalBackFrame": null, "_checkPressedBackFrame": null, "_checkDisableBackFrame": null, "_checkNormalFrame": null, "_checkDisableFrame": null, "checkInteractable": true, "isChecked": true, "_atlasFrame": null, "firstChar": ".", "charWidth": 0, "charHeight": 0, "string": "", "_sliderBackFrame": null, "_sliderBarFrame": null, "_sliderBtnNormalFrame": null, "_sliderBtnPressedFrame": null, "_sliderBtnDisabledFrame": null, "sliderInteractable": true, "sliderProgress": 0.5, "listInertia": false, "listDirection": 1, "listHorizontalAlign": 0, "listVerticalAlign": 1, "listPadding": 4}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7eY9fnCfVAoqalGRyKXHDK", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_spriteFrame": {"__uuid__": "4a0adf44-e7d5-4459-aa06-a66d9361847d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecTvMaDqRHwpu+4yYBfYIX", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 41, "_left": 780, "_right": 780, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b45/Diuc5Ibrvj6hpfZYBX", "sync": false}, {"__type__": "cc.Node", "_name": "Node_center", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 25}, {"__id__": 46}, {"__id__": 52}, {"__id__": 65}, {"__id__": 77}, {"__id__": 100}], "_tag": -1, "_active": true, "_components": [{"__id__": 184}], "_prefab": {"__id__": 185}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 360, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_bet", "_objFlags": 0, "_parent": {"__id__": 24}, "_children": [{"__id__": 26}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 45}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_bet_1", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 27}, {"__id__": 33}, {"__id__": 35}, {"__id__": 38}, {"__id__": 41}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 44}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [415.1293, -11.2319, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_bet_bg", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [{"__id__": 28}], "_tag": -1, "_active": true, "_components": [{"__id__": 31}], "_prefab": {"__id__": 32}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 465, "height": 371}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_touch", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 29}], "_prefab": {"__id__": 30}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 465, "height": 371}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 44, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 465, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59pEA2RXZBXZMitZbRu/qC", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_spriteFrame": {"__uuid__": "7973941c-c41e-4751-b2b9-e8aefc9b9fa6"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dMvdfNe1GYYM6JMqcfwV8", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_bet_area", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_tag": -1, "_active": false, "_components": [], "_prefab": {"__id__": 34}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 346, "height": 195}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 33, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4e0G7ghOxM1blHTqniBNE0", "sync": false}, {"__type__": "cc.Node", "_name": "Text_b", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 36}], "_prefab": {"__id__": 37}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 225, "b": 120, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 34}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10.9447, -130.8295, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 34, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "12345", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9kCTDy0xCBLyn5iI1s1Ik", "sync": false}, {"__type__": "cc.Node", "_name": "Text_s_b", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 39}], "_prefab": {"__id__": 40}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 169, "g": 213, "b": 231, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 34}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4.8943, -130.8297, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 34, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "123", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e82hJ487ZFjL6CJBzm0jjQ", "sync": false}, {"__type__": "cc.Node", "_name": "Text_b_dec", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 169, "g": 213, "b": 231, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 16, "height": 34}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [4.6412, -130.8313, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 34, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "/", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86vw7uAKpLraEdWcZnDj/h", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29EYyx5vhMh5UCuwKioubO", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6Y0qELGNESrR37CTumXJv", "sync": false}, {"__type__": "cc.Node", "_name": "alarm_clock", "_objFlags": 0, "_parent": {"__id__": 24}, "_children": [{"__id__": 47}], "_tag": -1, "_active": true, "_components": [{"__id__": 50}], "_prefab": {"__id__": 51}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 127, "height": 123}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-18.1916, -112.58, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "countdown", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 48}], "_prefab": {"__id__": 49}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 45}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [7, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "33c2b6d2-cc30-4386-992f-8b13cba3c4d4"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "2", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69ahUUs+1FqrH0nNHXkL2J", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "_spriteFrame": {"__uuid__": "54ea3fcb-d46b-4eb5-a7ab-3e6fba4e92c7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3xz+ly+pP6p1qoFtNEFCt", "sync": false}, {"__type__": "cc.Node", "_name": "Node_start_process", "_objFlags": 0, "_parent": {"__id__": 24}, "_children": [{"__id__": 53}, {"__id__": 61}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 64}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 21, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_18", "_objFlags": 0, "_parent": {"__id__": 52}, "_children": [{"__id__": 54}], "_tag": -1, "_active": true, "_components": [{"__id__": 59}], "_prefab": {"__id__": 60}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 358, "height": 45}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-16.8567, -40.5068, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "LoadingBar_start_process", "_objFlags": 0, "_parent": {"__id__": 53}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 55}, {"__id__": 56}, {"__id__": 57}], "_prefab": {"__id__": 58}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 358, "height": 45}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "_spriteFrame": {"__uuid__": "057b97d2-a005-4280-b9f5-3d1aaa03b280"}, "_type": 3, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0.8, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "_N$totalLength": 1, "_N$barSprite": {"__id__": 56}, "_N$mode": 2, "_N$progress": 0.8, "_N$reverse": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d7ObpmevNAM4QyUTMgPGfF", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "_spriteFrame": {"__uuid__": "225e2605-e176-4ac9-98fd-b0fc343d7cf5"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdE1zJautDlapU+l4b/2+7", "sync": false}, {"__type__": "cc.Node", "_name": "Text_2", "_objFlags": 0, "_parent": {"__id__": 52}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 62}], "_prefab": {"__id__": 63}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 177, "height": 50}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-16.595, 13.1023, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 50, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "starts in", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbhdefdMlEyJ+QgK+KsBUB", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4b1PtH285CqbdmbBxKB6De", "sync": false}, {"__type__": "cc.Node", "_name": "Node_crash", "_objFlags": 0, "_parent": {"__id__": 24}, "_children": [{"__id__": 66}, {"__id__": 69}, {"__id__": 73}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 76}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-380.1254, -8.7896, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_19", "_objFlags": 0, "_parent": {"__id__": 65}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 67}], "_prefab": {"__id__": 68}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 318, "height": 318}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "_spriteFrame": {"__uuid__": "2b47e3cb-e645-470b-a0db-e7399409088c"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "08Lge4zcRKxLy4abZMCEuX", "sync": false}, {"__type__": "cc.Node", "_name": "Node_11", "_objFlags": 0, "_parent": {"__id__": 65}, "_children": [{"__id__": 70}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 72}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_crash_anim", "_objFlags": 0, "_parent": {"__id__": 69}, "_children": [], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 71}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 316, "height": 316}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90tVAn+WxGxo+7+K7JdgPp", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aa6HqZ1CNFM6OOfv5Lofih", "sync": false}, {"__type__": "cc.Node", "_name": "Image_27", "_objFlags": 0, "_parent": {"__id__": 65}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 74}], "_prefab": {"__id__": 75}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 312, "height": 310}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "_spriteFrame": {"__uuid__": "7c862e0b-792e-4323-a700-f56f47900720"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ead5ELQMhJlq/OPnc+wDMj", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7fCPUFM9ZBubT91i/fMc7V", "sync": false}, {"__type__": "cc.Node", "_name": "Node_trend", "_objFlags": 0, "_parent": {"__id__": 24}, "_children": [{"__id__": 78}, {"__id__": 81}, {"__id__": 93}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 99}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 214, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_21", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 79}], "_prefab": {"__id__": 80}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1650, "height": 71}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_spriteFrame": {"__uuid__": "49ea0ed1-a06b-4dec-a617-4570c8ba8716"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "88HIuxyNVNIJ8O3n6Jqx6z", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_trend", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [{"__id__": 82}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 92}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 1294, "height": 70}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2.0349, -36.49, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_trend_item", "_objFlags": 0, "_parent": {"__id__": 81}, "_children": [{"__id__": 83}, {"__id__": 87}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 91}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 131, "height": 75}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-581.5, 37.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_item_bg", "_objFlags": 0, "_parent": {"__id__": 82}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 84}, {"__id__": 85}], "_prefab": {"__id__": 86}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 131, "height": 75}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_spriteFrame": {"__uuid__": "26c08548-6cf5-4a83-b6a5-103511ed3e72"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62juzOniJBW7k9Qc3EsMDZ", "sync": false}, {"__type__": "cc.Node", "_name": "num", "_objFlags": 0, "_parent": {"__id__": 82}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 88}, {"__id__": 89}], "_prefab": {"__id__": 90}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 94, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.1412, "_right": 0, "_top": 0, "_bottom": 0.3, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "8901bb4d-015e-4702-b380-************"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "20.00x", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "874CM7Kx9MXp03BKMREtAx", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f6fngiwmpH5ZUZ1xCkmymA", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f0HsBUlvNIEJ6mMeK9kTGq", "sync": false}, {"__type__": "cc.Node", "_name": "Image_trend", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [{"__id__": 94}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 98}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [684.73, -0.82, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_icon", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 95}, {"__id__": 96}], "_prefab": {"__id__": 97}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 51, "height": 51}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.245, "_right": 0, "_top": 0, "_bottom": 0.245, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_spriteFrame": {"__uuid__": "5d116962-7bcd-4868-b10e-68e9657fdf3b"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecEaFjnYxDnYLqL/zEa+f9", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dJTnrC+FEWqQxts+oEdJe", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66cio8eHtMrrYpzvacZMAX", "sync": false}, {"__type__": "cc.Node", "_name": "Node_crash_out", "_objFlags": 0, "_parent": {"__id__": 24}, "_children": [{"__id__": 101}, {"__id__": 115}, {"__id__": 130}, {"__id__": 133}, {"__id__": 135}, {"__id__": 142}, {"__id__": 163}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 183}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_time", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 102}], "_tag": -1, "_active": true, "_components": [{"__id__": 113}], "_prefab": {"__id__": 114}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 1299, "height": 37}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-626, -203, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_move", "_objFlags": 0, "_parent": {"__id__": 101}, "_children": [{"__id__": 103}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 112}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -37, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_time_item", "_objFlags": 0, "_parent": {"__id__": 102}, "_children": [{"__id__": 104}, {"__id__": 108}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 111}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 236, "height": 37}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_23", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 105}, {"__id__": 106}], "_prefab": {"__id__": 107}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 236, "height": 12}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [118, 37, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.6757, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "_spriteFrame": {"__uuid__": "c2b74abf-44bf-441b-a44e-acb814bdc5a0"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "843CVHMsVICJtMR97v9oO0", "sync": false}, {"__type__": "cc.Node", "_name": "txt", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 109}], "_prefab": {"__id__": 110}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2.2343, 12.7157, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "295744ec-70b6-4b1e-b9de-d55f7327eb46"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "0s", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38DBGt0s5HVZS/+2iBdG2p", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eaMdqn+xJOg6Nt/+n3LShf", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85eoXGt7xIxYsarQKXlm9X", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "_type": 0, "_segements": 64, "_N$spriteFrame": null, "_N$alphaThreshold": 1, "_N$inverted": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0iINHyCdO0oF9xgelJjzp", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_multi", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 116}], "_tag": -1, "_active": true, "_components": [{"__id__": 128}], "_prefab": {"__id__": 129}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 373}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-626, -211, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_move", "_objFlags": 0, "_parent": {"__id__": 115}, "_children": [{"__id__": 117}], "_tag": -1, "_active": true, "_components": [{"__id__": 126}], "_prefab": {"__id__": 127}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 10, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_multi_item", "_objFlags": 0, "_parent": {"__id__": 116}, "_children": [{"__id__": 118}, {"__id__": 122}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 125}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 16, "height": 77}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_23", "_objFlags": 0, "_parent": {"__id__": 117}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 119}, {"__id__": 120}], "_prefab": {"__id__": 121}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 16, "height": 77}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 118}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 118}, "_enabled": true, "_spriteFrame": {"__uuid__": "aec9c72f-dd5c-4488-9585-129810a43c6d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3eW9oPhnNGuKkQ3z6BGply", "sync": false}, {"__type__": "cc.Node", "_name": "txt", "_objFlags": 0, "_parent": {"__id__": 117}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 123}], "_prefab": {"__id__": 124}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-17.5834, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 122}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "295744ec-70b6-4b1e-b9de-d55f7327eb46"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "1.00x", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "360+XpAypDm63U33ScyEfv", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f5X2/BWJhML5Mu2wQ1BZT+", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 1, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d7sBVOGdNPl4NXaaBa4Jx9", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "_type": 0, "_segements": 64, "_N$spriteFrame": null, "_N$alphaThreshold": 1, "_N$inverted": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "efchwuHg5Og6z1tg41JuBy", "sync": false}, {"__type__": "cc.Node", "_name": "crash_out", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 131}], "_prefab": {"__id__": 132}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-626, -203, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.StudioComponent.PlaceHolder", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "_baseUrl": "db://assets/iwby_Crash/ccbResources/CrashRes/playing/prefabs/CrashOut.prefab", "nestedPrefab": {"__uuid__": "10a71cd5-b504-4e0c-932d-c9e920ec31c9"}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90OlSBq+tIwbNJHJ2QLDn6", "sync": false}, {"__type__": "cc.Node", "_name": "Node_stop_info", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 134}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7wJNoIMlLrIZJLFX7GlpX", "sync": false}, {"__type__": "cc.Node", "_name": "Image_multi", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 136}], "_tag": -1, "_active": true, "_components": [{"__id__": 140}], "_prefab": {"__id__": 141}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 100}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-22.699, 86.0481, 0, 0, 0, 0, 1, 1.2, 1.2, 1]}}, {"__type__": "cc.Node", "_name": "multi_txt", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 137}, {"__id__": 138}], "_prefab": {"__id__": 139}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 205, "height": 50}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.09000000000000002, "_right": 0, "_top": 0, "_bottom": 0.25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "7c9610a4-f99d-4f0b-b046-473a5440fe00"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "20.00x", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "184y7w5r9MJbMOttNFbQQg", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 135}, "_enabled": true, "_spriteFrame": {"__uuid__": "b8c57d31-0df0-45dc-84f2-634614aeeff8"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "666e9hQ8lD9ZMo/apjihdw", "sync": false}, {"__type__": "cc.Node", "_name": "Node_stop_crash", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 143}, {"__id__": 159}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 162}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [421.8123, -130.1832, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_info", "_objFlags": 0, "_parent": {"__id__": 142}, "_children": [{"__id__": 144}], "_tag": -1, "_active": true, "_components": [{"__id__": 157}], "_prefab": {"__id__": 158}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 189, "height": 59}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.5747, 92.3306, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ListView_info", "_objFlags": 0, "_parent": {"__id__": 143}, "_children": [{"__id__": 145}, {"__id__": 148}, {"__id__": 151}], "_tag": -1, "_active": true, "_components": [{"__id__": 154}, {"__id__": 155}], "_prefab": {"__id__": 156}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 133, "height": 46}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_s_b", "_objFlags": 0, "_parent": {"__id__": 144}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 146}], "_prefab": {"__id__": 147}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 169, "g": 213, "b": 231, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 59, "height": 34}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 145}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 34, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "200", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0dmvmFDDVGG7TmpIgJYnZz", "sync": false}, {"__type__": "cc.Node", "_name": "Text_b_dec", "_objFlags": 0, "_parent": {"__id__": 144}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 149}], "_prefab": {"__id__": 150}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 169, "g": 213, "b": 231, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 16, "height": 34}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 34, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "/", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f9y89JhQlBBpKi92ce3kbg", "sync": false}, {"__type__": "cc.Node", "_name": "Text_b", "_objFlags": 0, "_parent": {"__id__": 144}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 152}], "_prefab": {"__id__": 153}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 225, "b": 120, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 59, "height": 34}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [8.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 151}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 34, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "200", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a05x7Q7MFBAbwuXzSnHrEP", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 144}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.14815, "_right": 0, "_top": 0, "_bottom": 0.11015000000000003, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.StudioComponent", "_name": "", "_objFlags": 0, "node": {"__id__": 144}, "_enabled": true, "_type": 4, "_checkNormalBackFrame": null, "_checkPressedBackFrame": null, "_checkDisableBackFrame": null, "_checkNormalFrame": null, "_checkDisableFrame": null, "checkInteractable": true, "isChecked": true, "_atlasFrame": null, "firstChar": ".", "charWidth": 0, "charHeight": 0, "string": "", "_sliderBackFrame": null, "_sliderBarFrame": null, "_sliderBtnNormalFrame": null, "_sliderBtnPressedFrame": null, "_sliderBtnDisabledFrame": null, "sliderInteractable": true, "sliderProgress": 0.5, "listInertia": false, "listDirection": 1, "listHorizontalAlign": 0, "listVerticalAlign": 1, "listPadding": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "acQ4QGVSlLB6NlEaIWdBeF", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 143}, "_enabled": true, "_spriteFrame": {"__uuid__": "28ea9c22-7cc4-48cf-8462-68c9b7f45ac8"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f9/Swx4ZNCNZk638j1SNz5", "sync": false}, {"__type__": "cc.Node", "_name": "Image_stop_crash", "_objFlags": 0, "_parent": {"__id__": 142}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 160}], "_prefab": {"__id__": 161}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 385, "height": 120}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "_spriteFrame": {"__uuid__": "673edc07-b8b0-4b8d-968c-377f2bc5d5ba"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c28+bxAaFPhYmeDz9hhfIG", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4meBBy7dG7KR6D1UaIp//", "sync": false}, {"__type__": "cc.Node", "_name": "Node_win_info", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 164}, {"__id__": 167}, {"__id__": 170}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 182}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [419.1964, -136.35, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_39", "_objFlags": 0, "_parent": {"__id__": 163}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 165}], "_prefab": {"__id__": 166}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1332, "height": 195}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.45, 0.54, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "_spriteFrame": {"__uuid__": "a094fc72-957b-4a95-b117-838bc602fae0"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f76bbhlr9FBbx1hAtAJWIy", "sync": false}, {"__type__": "cc.Node", "_name": "win_score", "_objFlags": 0, "_parent": {"__id__": 163}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 168}], "_prefab": {"__id__": 169}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 678, "height": 115}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.52, 0.52, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "7f3f3029-b8b9-4e7b-ab5d-61f42f695137"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "₹23012.58", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eae04zzjdJ5KknWwNitRBN", "sync": false}, {"__type__": "cc.Node", "_name": "ListView_mutli", "_objFlags": 0, "_parent": {"__id__": 163}, "_children": [{"__id__": 171}, {"__id__": 177}], "_tag": -1, "_active": true, "_components": [{"__id__": 180}], "_prefab": {"__id__": 181}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 348, "height": 70}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.3546, 82.11, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_15", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [{"__id__": 172}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 176}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 86}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-174, -43, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_40", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 173}, {"__id__": 174}], "_prefab": {"__id__": 175}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 65}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [71, 40, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "_spriteFrame": {"__uuid__": "a75197c4-9944-4009-90b2-3e3bb4f2e7c2"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58K5KJzD9Azrsq+ybp8WS2", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "47DfpUC2BCvJq/fovpnX1R", "sync": false}, {"__type__": "cc.Node", "_name": "win_mutli", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 178}], "_prefab": {"__id__": 179}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 205, "height": 50}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [70.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "7c9610a4-f99d-4f0b-b046-473a5440fe00"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "x20.00", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05xWso0gtJ66X9plJAXhHD", "sync": false}, {"__type__": "cc.StudioComponent", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "_type": 4, "_checkNormalBackFrame": null, "_checkPressedBackFrame": null, "_checkDisableBackFrame": null, "_checkNormalFrame": null, "_checkDisableFrame": null, "checkInteractable": true, "isChecked": true, "_atlasFrame": null, "firstChar": ".", "charWidth": 0, "charHeight": 0, "string": "", "_sliderBackFrame": null, "_sliderBarFrame": null, "_sliderBtnNormalFrame": null, "_sliderBtnPressedFrame": null, "_sliderBtnDisabledFrame": null, "sliderInteractable": true, "sliderProgress": 0.5, "listInertia": false, "listDirection": 1, "listHorizontalAlign": 0, "listVerticalAlign": 1, "listPadding": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2fkOuDRWtPKoIDgNqTJoVo", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96/KVAdzZJ6JntxQmFzAnA", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72YXFmfCNMYICpz1XsGWaI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 45, "_left": 780, "_right": 780, "_top": 360, "_bottom": 360, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53IFY3QVlLqp/JfPEN68mL", "sync": false}, {"__type__": "cc.Node", "_name": "Image_bottom_bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 187}, {"__id__": 190}, {"__id__": 193}, {"__id__": 196}, {"__id__": 199}, {"__id__": 202}], "_tag": -1, "_active": true, "_components": [{"__id__": 204}], "_prefab": {"__id__": 205}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_3", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 188}], "_prefab": {"__id__": 189}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 1293, "height": 139}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3, -25, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "_spriteFrame": {"__uuid__": "88aa7f89-dfb2-4d42-84e5-2198cd39c884"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cVszNuYtNaqsbyfu9nMEK", "sync": false}, {"__type__": "cc.Node", "_name": "Image_91", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 191}], "_prefab": {"__id__": 192}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 184, "height": 92}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-323.0379, 62.6727, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "_spriteFrame": {"__uuid__": "7223e477-**************-d29a1f1be763"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "65jnjgpBRDJL42H77d1fVj", "sync": false}, {"__type__": "cc.Node", "_name": "FileNode_self_seat", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 194}], "_prefab": {"__id__": 195}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-429.7899, 62.7019, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.StudioComponent.PlaceHolder", "_name": "", "_objFlags": 0, "node": {"__id__": 193}, "_enabled": true, "_baseUrl": "db://assets/iwby_Crash/ccbResources/CrashRes/playing/prefabs/mySeat.prefab", "nestedPrefab": {"__uuid__": "8cf5c6b9-349e-4bda-9ef3-7c09482daff2"}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67Hh8FMhNK+7HH+Y/TkRj3", "sync": false}, {"__type__": "cc.Node", "_name": "Text_name", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 197}], "_prefab": {"__id__": 198}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 24}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-301.6837, 76.17, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 24, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "gooddluckly", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4QqczbrZBX5qDtc97Augx", "sync": false}, {"__type__": "cc.Node", "_name": "Text_chips", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 200}], "_prefab": {"__id__": 201}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 80, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-368.0104, 39.39, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 26, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "34ab60c6-84f8-478d-bf56-a79250c5cdd3"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "6523k", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0mMIZSAxFKLdgzGYubEg4", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_chips", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 203}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 728, "height": 100}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [141.82, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "79QB/NadlGVrvUAo15R6YI", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 186}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 44, "_left": 780, "_right": 780, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fasKcR44tD/ZAR+xmr0hks", "sync": false}, {"__type__": "cc.Node", "_name": "Node_table_anim", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 207}], "_prefab": {"__id__": 208}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 360, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 45, "_left": 780, "_right": 780, "_top": 360, "_bottom": 360, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90duZlGdhGk6zLjnbaZ0VR", "sync": false}, {"__type__": "cc.Node", "_name": "img_hornBg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 210}, {"__id__": 214}], "_tag": -1, "_active": false, "_components": [{"__id__": 218}, {"__id__": 219}], "_prefab": {"__id__": 220}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 866, "height": 71}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 684.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "img_hornBg", "_objFlags": 0, "_parent": {"__id__": 209}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 211}, {"__id__": 212}], "_prefab": {"__id__": 213}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 34}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-291.28999999999996, 11.999000000000002, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.42955000000000004, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "_spriteFrame": {"__uuid__": "0fa555e5-998f-462e-a8d0-c554fa7a2237"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4cD3+QFblEn6zEwutqgl++", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_horn_clip", "_objFlags": 0, "_parent": {"__id__": 209}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 215}, {"__id__": 216}], "_prefab": {"__id__": 217}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 630, "height": 36}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-268, -3.997299999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 214}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.4437, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 214}, "_enabled": true, "_type": 0, "_segements": 64, "_N$spriteFrame": null, "_N$alphaThreshold": 1, "_N$inverted": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48yJGZ7KdECp9CBB697Ogl", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 209}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 9, "_left": 0.22244999999999998, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 209}, "_enabled": true, "_spriteFrame": {"__uuid__": "ed1a1658-8e11-4de8-b069-df0b4dbd54bc"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5f6wZdmYlE7bpQmaWpgnZC", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8bpe6X4UFPhZBE26djX4yH", "sync": false}]