[{"__type__": "cc.AnimationClip", "_name": "crash_move", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 60, "speed": 1, "wrapMode": 1, "enableTrsBlending": false, "_duration": 10, "_hash": 500763545, "_tracks": [{"__id__": 1}, {"__id__": 9}, {"__id__": 20}], "_exoticAnimation": null, "_events": [], "_embeddedPlayers": []}, {"__type__": "cc.animation.SizeTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}}, "_channels": [{"__id__": 5}, {"__id__": 7}]}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, {"__id__": 4}, "contentSize"]}, {"__type__": "cc.animation.HierarchyPath", "path": "Panel_crash"}, {"__type__": "cc.animation.ComponentPath", "component": "cc.UITransform"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 6}}, {"__type__": "cc.RealCurve", "_times": [0, 10], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1180, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 8}}, {"__type__": "cc.RealCurve", "_times": [0, 10], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 318, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 318, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 10}}, "_channels": [{"__id__": 12}, {"__id__": 14}, {"__id__": 16}, {"__id__": 18}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 11}, "position"]}, {"__type__": "cc.animation.HierarchyPath", "path": "Node_crash"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 13}}, {"__type__": "cc.RealCurve", "_times": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 118, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 236, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 354, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 469, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 583, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 703, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 817, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 939, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1056, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1173, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 15}}, {"__type__": "cc.RealCurve", "_times": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 6, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 15, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 32, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 53, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 80, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 115, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 154, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 201, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 253, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 314, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 17}}, {"__type__": "cc.RealCurve", "_times": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 19}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 21}}, "_channels": [{"__id__": 23}, {"__id__": 25}, {"__id__": 27}, {"__id__": 29}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 22}, "eulerAngles"]}, {"__type__": "cc.animation.HierarchyPath", "path": "Node_crash"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 24}}, {"__type__": "cc.RealCurve", "_times": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 26}}, {"__type__": "cc.RealCurve", "_times": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 28}}, {"__type__": "cc.RealCurve", "_times": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 2.6670000553131104, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0.48399999737739563, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 8.02299976348877, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 11.458000183105469, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 14.204000473022461, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 14.204000473022461, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 19.211000442504883, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 19.211000442504883, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 19.211000442504883, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 24.87700080871582, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 28.180999755859375, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 30}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}]