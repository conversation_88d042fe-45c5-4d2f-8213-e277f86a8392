[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_rawFiles": null, "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "Layer", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 10}, {"__id__": 13}, {"__id__": 45}, {"__id__": 55}, {"__id__": 62}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 83}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "btn_back", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 73}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [74.3, 678.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 9, "_left": 37.8, "_right": 0, "_top": 5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_spriteFrame": {"__uuid__": "c38d5987-d11a-4ffd-9a5a-d46e18b5ae61"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5bRcYH5aFDc7kGaGnx2hhN", "sync": false}, {"__type__": "cc.Node", "_name": "btn_archive", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 7}, {"__id__": 8}], "_prefab": {"__id__": 9}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 73}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [182.7, 678.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 9, "_left": 146.2, "_right": 0, "_top": 5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_spriteFrame": {"__uuid__": "2de52d7f-60f4-4469-8a21-9ca1eacc7d38"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2eCMHZxrRG7rqH5cO+cXbN", "sync": false}, {"__type__": "cc.Node", "_name": "node_rechargeProtect", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 11}], "_prefab": {"__id__": 12}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [146.2, 669.1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 9, "_left": 146.2, "_right": 0, "_top": 50.9, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27t2jScKlHBLzzmGDU2KDX", "sync": false}, {"__type__": "cc.Node", "_name": "node_wifi", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 14}, {"__id__": 40}], "_tag": -1, "_active": true, "_components": [{"__id__": 43}], "_prefab": {"__id__": 44}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1313.5005, 691.9705, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_net", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 15}, {"__id__": 18}, {"__id__": 21}, {"__id__": 24}, {"__id__": 27}, {"__id__": 30}, {"__id__": 33}, {"__id__": 36}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 39}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "img_xinhao_1", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 16}], "_prefab": {"__id__": 17}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_spriteFrame": {"__uuid__": "d3dcdbc8-5292-4892-a166-e35034da1003"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "03Ub/sHARLrK9UJ5VbEckC", "sync": false}, {"__type__": "cc.Node", "_name": "img_xinhao_2", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 19}], "_prefab": {"__id__": 20}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_spriteFrame": {"__uuid__": "44ed48af-c896-413c-8935-209d85998c93"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7eFvd4Ui1JIaJQlMeJQMkf", "sync": false}, {"__type__": "cc.Node", "_name": "img_xinhao_3", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 22}], "_prefab": {"__id__": 23}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_spriteFrame": {"__uuid__": "1aea4eec-981a-41de-a4dc-c465719d67f3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "earHUinPdI24OtBZAfm/zF", "sync": false}, {"__type__": "cc.Node", "_name": "img_xinhao_4", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 25}], "_prefab": {"__id__": 26}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_spriteFrame": {"__uuid__": "87412705-057f-4a8b-aacc-dcc3c6d51f19"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98jPEbWAVK8pRtg5u32o+i", "sync": false}, {"__type__": "cc.Node", "_name": "img_wifi_1", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 28}], "_prefab": {"__id__": 29}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_spriteFrame": {"__uuid__": "d1b48212-ba0f-40fd-811d-17a4a1356f92"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05TZtiFFhE9blXfQFMFN6R", "sync": false}, {"__type__": "cc.Node", "_name": "img_wifi_2", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 31}], "_prefab": {"__id__": 32}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_spriteFrame": {"__uuid__": "63be1fca-47a1-419e-9b08-fbc0e9ea2842"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "42+UHy6HFKo5U/5wsXGLeZ", "sync": false}, {"__type__": "cc.Node", "_name": "img_wifi_3", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 34}], "_prefab": {"__id__": 35}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_spriteFrame": {"__uuid__": "438ff1d1-b3fc-406e-91d7-c45c3a1ea0fa"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34mUJLEghFM5O2K41a8cUU", "sync": false}, {"__type__": "cc.Node", "_name": "img_wifi_4", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 37}], "_prefab": {"__id__": 38}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "_spriteFrame": {"__uuid__": "c3f81e5e-b644-48be-81f2-65b7bc69684b"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "45gbKC6N1DjIuoPsPdErAo", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4zmC4ktJGz7F0F2lnaxi6", "sync": false}, {"__type__": "cc.Node", "_name": "Text_net", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 41}], "_prefab": {"__id__": 42}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -36, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "100ms", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4uajUdKZPTKH+eIdlfw5t", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 246.4995, "_top": 28.0295, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8fjgROJBJMNYc2IptJbq8S", "sync": false}, {"__type__": "cc.Node", "_name": "add_cash", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 46}, {"__id__": 49}], "_tag": -1, "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}], "_prefab": {"__id__": 54}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 52}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1453.3674, 676.5186, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_add_txt", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 47}], "_prefab": {"__id__": 48}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 50}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-21.714399999999998, 0.716899999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "_spriteFrame": {"__uuid__": "cabbbc51-d2cb-4a76-bf07-3eb569350850"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53fbdRJIpKi76AscHv7PZp", "sync": false}, {"__type__": "cc.Node", "_name": "Image_2", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 50}], "_prefab": {"__id__": 51}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 74}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [55.5265, -0.170300000000001, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_spriteFrame": {"__uuid__": "930304c3-8f20-43bf-aa6d-c4305e5adc0e"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1aUyQj+rRAPq1gLt9VkBao", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 31.6326, "_top": 17.4814, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_spriteFrame": {"__uuid__": "dfcb4244-aaf2-4a5b-ba39-24e12ab6f87d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4aT2g0CrFHmpzoCY7t8qQD", "sync": false}, {"__type__": "cc.Node", "_name": "Node_top", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 56}], "_tag": -1, "_active": true, "_components": [{"__id__": 60}], "_prefab": {"__id__": 61}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 720, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "btn_setting", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_tag": -1, "_active": false, "_components": [{"__id__": 57}, {"__id__": 58}], "_prefab": {"__id__": 59}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 76}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-59, -44, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 21, "_top": 6, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "_spriteFrame": {"__uuid__": "bd5ccf6e-3424-4f4a-93dc-a9486c3b44a8"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89Fz/2aqtIT5xP84vHq7jv", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 41, "_left": 780, "_right": 780, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7cDaZkFK9K+a0wr1JYP1GI", "sync": false}, {"__type__": "cc.Node", "_name": "Node_35", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 63}, {"__id__": 66}, {"__id__": 70}], "_tag": -1, "_active": true, "_components": [{"__id__": 81}], "_prefab": {"__id__": 82}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "btn_chat", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [], "_tag": -1, "_active": false, "_components": [{"__id__": 64}], "_prefab": {"__id__": 65}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 86, "height": 86}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-579.0244, 63.3891, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_spriteFrame": {"__uuid__": "f7a21bb5-7a09-4962-88d2-75d2c2282f5a"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9bF5byu9dGjKMmgi2yjhx6", "sync": false}, {"__type__": "cc.Node", "_name": "Image_trend_btn", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [], "_tag": -1, "_active": false, "_components": [{"__id__": 67}, {"__id__": 68}], "_prefab": {"__id__": 69}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 86, "height": 86}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [567.5875, 62.63, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 36, "_left": 0, "_right": -610.5875, "_top": 0, "_bottom": 19.63, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "_spriteFrame": {"__uuid__": "aceb4770-fe3a-417d-8986-9c5787807a20"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66srZuG2BOtoB9/iwE4WVc", "sync": false}, {"__type__": "cc.Node", "_name": "Image_online", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [{"__id__": 71}, {"__id__": 74}], "_tag": -1, "_active": true, "_components": [{"__id__": 78}, {"__id__": 79}], "_prefab": {"__id__": 80}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 73}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-542.73, 52.2817, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_5", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 72}], "_prefab": {"__id__": 73}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 51, "height": 19}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.21759999999999735, -27.820700000000002, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "_spriteFrame": {"__uuid__": "7d7aeac4-f946-4813-9879-aa342784a7f7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26CZ/WnDpL65QC/1h1kR2W", "sync": false}, {"__type__": "cc.Node", "_name": "Text_online", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 75}, {"__id__": 76}], "_prefab": {"__id__": 77}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 18}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -27.64, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0.31505, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 18, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "200", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cuKIFB8tLX7T4o0m+DEop", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 70}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 9, "_left": -579.23, "_right": 0, "_top": -88.7817, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 70}, "_enabled": true, "_spriteFrame": {"__uuid__": "e940ce95-3676-4697-949f-ebd5fb10a353"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "12yn0DYGlKDo2UQyedCqyQ", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 44, "_left": 780, "_right": 780, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c01x1fRzdJc78wZq1ufvE9", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbCj3xtOtC0IuLyMieqvDY", "sync": false}]