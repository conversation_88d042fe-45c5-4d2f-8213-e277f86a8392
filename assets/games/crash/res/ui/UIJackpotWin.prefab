[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_rawFiles": null, "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "Layer", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 9}, {"__id__": 69}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 73}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_mask", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_tag": -1, "_active": true, "_components": [{"__id__": 7}], "_prefab": {"__id__": 8}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 360, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_id": "", "_opacity": 204, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aeIxLlcipD44zB6wp5lN1C", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1560, "_originalHeight": 720}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46RZBnZJPULqzTSGHfK9x", "sync": false}, {"__type__": "cc.Node", "_name": "FileNode_2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 10}, {"__id__": 13}, {"__id__": 36}, {"__id__": 59}], "_tag": -1, "_active": true, "_components": [{"__id__": 66}, {"__id__": 67}], "_prefab": {"__id__": 68}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [793, 343, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_6", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 11}], "_prefab": {"__id__": 12}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 612, "height": 144}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [18.6927, 275.2644, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_spriteFrame": {"__uuid__": "*************-4c44-aa72-470c32d71be6"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10p4iVdnhEQK0pcELZnR5Z", "sync": false}, {"__type__": "cc.Node", "_name": "Image_player_win", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 14}, {"__id__": 18}, {"__id__": 28}, {"__id__": 31}], "_tag": -1, "_active": true, "_components": [{"__id__": 34}], "_prefab": {"__id__": 35}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 179}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-244.4499, -42.5493, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_name", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}], "_prefab": {"__id__": 17}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [75, -93.4103, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.39440000000000003, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 30, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c0kiA0MfRHTIImJYCk7rPa", "sync": false}, {"__type__": "cc.Node", "_name": "Node_head", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 19}, {"__id__": 24}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 27}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-146.5128, -88.7183, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_2", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [{"__id__": 20}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 23}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_head", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 21}], "_prefab": {"__id__": 22}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 105}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "_spriteFrame": {"__uuid__": "f97d44bf-e37e-4498-b06b-d990791dbeaf"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "63j9fPW+hAv79kQ+5URFcV", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9kLhaTIVMrpt9otcIf/0b", "sync": false}, {"__type__": "cc.Node", "_name": "Image_head_bg", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 25}], "_prefab": {"__id__": 26}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 113}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_spriteFrame": {"__uuid__": "ed237aaf-f869-4e58-8f63-ed06dda57402"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "869QMHgARHX6j4bTuET0Lk", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "45T5FpojxANa1baiS3YRMj", "sync": false}, {"__type__": "cc.Node", "_name": "Image_mark_1", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 29}], "_prefab": {"__id__": 30}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 95, "height": 73}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [75, -39.11859999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_spriteFrame": {"__uuid__": "441990d9-cfb1-4f05-800e-6b8fd805ba5d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "db4wq//iFPhLEJypMOTleg", "sync": false}, {"__type__": "cc.Node", "_name": "win_num", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 32}], "_prefab": {"__id__": 33}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 193, "height": 40}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [75, -139.06, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "0e2edcf2-2935-4326-b774-fb135531c462"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "₹9999.99", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dyzv2wfFE/aTrndh5wd0J", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_spriteFrame": {"__uuid__": "002455f3-8b31-4d01-a021-eacf42b24851"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70kFUfxw9DQJJM2kdBeo+y", "sync": false}, {"__type__": "cc.Node", "_name": "Image_player_you", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 37}, {"__id__": 41}, {"__id__": 51}, {"__id__": 54}], "_tag": -1, "_active": true, "_components": [{"__id__": 57}], "_prefab": {"__id__": 58}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 179}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [242.4314, -40.3035, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_name", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 38}, {"__id__": 39}], "_prefab": {"__id__": 40}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [75, -93.4103, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.39440000000000003, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 30, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8dX1YEG9Obbq5ciklF23S", "sync": false}, {"__type__": "cc.Node", "_name": "Node_head", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [{"__id__": 42}, {"__id__": 47}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 50}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-146.5128, -88.7183, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_2", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [{"__id__": 43}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 46}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_head", "_objFlags": 0, "_parent": {"__id__": 42}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 44}], "_prefab": {"__id__": 45}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 105}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "_spriteFrame": {"__uuid__": "f97d44bf-e37e-4498-b06b-d990791dbeaf"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26OPA/CR1PaJfpOyKYzk+g", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0dz5nWK7RDNoL16wfthWYL", "sync": false}, {"__type__": "cc.Node", "_name": "Image_head_bg", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 48}], "_prefab": {"__id__": 49}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 113}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "_spriteFrame": {"__uuid__": "ed237aaf-f869-4e58-8f63-ed06dda57402"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5bMsUci6VMNJxxQOYGQaAf", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f46jonfkJC34zg+NSWkelb", "sync": false}, {"__type__": "cc.Node", "_name": "Image_mark_1", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 52}], "_prefab": {"__id__": 53}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 61}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [75, -48.11850000000001, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_spriteFrame": {"__uuid__": "8d386d12-b064-46af-bb8b-cfdefd7e57cd"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cd3SYJutxGFblnYpUtBhLz", "sync": false}, {"__type__": "cc.Node", "_name": "win_num", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 55}], "_prefab": {"__id__": 56}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 193, "height": 40}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [75, -139.06, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "0e2edcf2-2935-4326-b774-fb135531c462"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "₹9999.99", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98b80OUZNC5rR+gpY0kKVf", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "_spriteFrame": {"__uuid__": "002455f3-8b31-4d01-a021-eacf42b24851"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37EqBzPu9LEogVdHlHzD/w", "sync": false}, {"__type__": "cc.Node", "_name": "Image_7", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 60}], "_tag": -1, "_active": true, "_components": [{"__id__": 64}], "_prefab": {"__id__": 65}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1332, "height": 195}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 95, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "jackpot_num", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 61}, {"__id__": 62}], "_prefab": {"__id__": 63}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 501, "height": 115}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.31195, "_right": 0, "_top": 0, "_bottom": 0.20515, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "7f3f3029-b8b9-4e7b-ab5d-61f42f695137"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "100000", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69V4JLQ7ZC2aYVPUOY1+NB", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_spriteFrame": {"__uuid__": "a094fc72-957b-4a95-b117-838bc602fae0"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78ZZWICcNAXrzR4kxTTe/h", "sync": false}, {"__type__": "cc.StudioComponent.PlaceHolder", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_baseUrl": "db://assets/iwby_Crash/ccbResources/CrashRes/public/dialog/dialog_da_2.prefab", "nestedPrefab": {"__uuid__": "0b8de90a-96be-4862-8c4e-b845c12329c1"}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 45, "_left": 793, "_right": 767, "_top": 377, "_bottom": 343, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64+0ZSRihKjKYgEG5sxbfe", "sync": false}, {"__type__": "cc.Node", "_name": "FileNode_1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}], "_prefab": {"__id__": 72}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 360, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.StudioComponent.PlaceHolder", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_baseUrl": "db://assets/iwby_Crash/ccbResources/CrashRes/playing/effectsnew/jackpot/tx_haiwai_ma<PERSON><PERSON><PERSON>_huojian_2.prefab", "nestedPrefab": {"__uuid__": "083d0f8d-4a06-443f-a68a-946a68bdb645"}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 45, "_left": 780, "_right": 780, "_top": 360, "_bottom": 360, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfhOPYFqhFwKS+SI6WkhHU", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1vFSuNQFDJ7EsdS0+HHqO", "sync": false}]