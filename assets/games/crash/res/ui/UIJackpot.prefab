[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_rawFiles": null, "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 113}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_mask", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 7}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.0002, 1.1719, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_id": "", "_opacity": 204, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bTpli2tNCDoxgH+qtZRCw", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3dwGLv2IBDVrXTQ8987Oyt", "sync": false}, {"__type__": "cc.Node", "_name": "FileNode_1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 9}, {"__id__": 22}, {"__id__": 40}, {"__id__": 44}], "_tag": -1, "_active": true, "_components": [{"__id__": 111}], "_prefab": {"__id__": 112}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-23, -23, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_jiangchi", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [{"__id__": 10}], "_tag": -1, "_active": true, "_components": [{"__id__": 20}], "_prefab": {"__id__": 21}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 618, "height": 89}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2.11, 309.59, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ListView_jiangchi", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 11}, {"__id__": 14}], "_tag": -1, "_active": true, "_components": [{"__id__": 17}, {"__id__": 18}], "_prefab": {"__id__": 19}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 438, "height": 68}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -7.119999999999997, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_17", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 12}], "_prefab": {"__id__": 13}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 197, "height": 42}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-120.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_spriteFrame": {"__uuid__": "8044c1c7-9556-4de1-9760-5ab62794fac6"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "699RYa+B1L87voaYCPx7We", "sync": false}, {"__type__": "cc.Node", "_name": "jiangchi_num", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 15}], "_prefab": {"__id__": 16}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 237, "height": 40}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-18, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "0e2edcf2-2935-4326-b774-fb135531c462"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "1,000,000", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6pxnd96lBEacNH9TPnhIY", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.14565, "_right": 0, "_top": 0, "_bottom": 0.03799999999999998, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.StudioComponent", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_type": 4, "_checkNormalBackFrame": null, "_checkPressedBackFrame": null, "_checkDisableBackFrame": null, "_checkNormalFrame": null, "_checkDisableFrame": null, "checkInteractable": true, "isChecked": true, "_atlasFrame": null, "firstChar": ".", "charWidth": 0, "charHeight": 0, "string": "", "_sliderBackFrame": null, "_sliderBarFrame": null, "_sliderBtnNormalFrame": null, "_sliderBtnPressedFrame": null, "_sliderBtnDisabledFrame": null, "sliderInteractable": true, "sliderProgress": 0.5, "listInertia": false, "listDirection": 1, "listHorizontalAlign": 0, "listVerticalAlign": 1, "listPadding": 4}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5ciHYIOClMfoLIxvQbuumn", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_spriteFrame": {"__uuid__": "4a0adf44-e7d5-4459-aa06-a66d9361847d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29FjC/G4FMYL9yOJM0GhKO", "sync": false}, {"__type__": "cc.Node", "_name": "Image_10", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [{"__id__": 23}, {"__id__": 26}, {"__id__": 29}, {"__id__": 32}, {"__id__": 35}], "_tag": -1, "_active": true, "_components": [{"__id__": 38}], "_prefab": {"__id__": 39}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 953, "height": 40}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 215, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_2", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 24}], "_prefab": {"__id__": 25}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 240, "g": 243, "b": 9, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 26}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-410.2149, 0.009899999999998244, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 26, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Time", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dDNoeRZ1NH7bfy002o+wl", "sync": false}, {"__type__": "cc.Node", "_name": "Text_2_0", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 27}], "_prefab": {"__id__": 28}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 240, "g": 243, "b": 9, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 26}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-258.8126, -1.0051999999999985, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 26, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Rewards", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8Vb+Z0tFCQ7f+ZmUaE4Bc", "sync": false}, {"__type__": "cc.Node", "_name": "Text_2_1", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 30}], "_prefab": {"__id__": 31}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 240, "g": 243, "b": 9, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 117, "height": 26}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.04739999999998, -0.4359000000000002, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 26, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Multiples", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "115yAfCRRFh5nspS+HDaLh", "sync": false}, {"__type__": "cc.Node", "_name": "Text_2_2", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 33}], "_prefab": {"__id__": 34}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 240, "g": 243, "b": 9, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 26}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160.3999, 0.7560000000000002, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 26, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Big winner", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02UADwAX1POr7tVFbjXVb/", "sync": false}, {"__type__": "cc.Node", "_name": "Text_2_3", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 36}], "_prefab": {"__id__": 37}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 240, "g": 243, "b": 9, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 26}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [406.6984, 0.3481999999999985, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 26, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Winners", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5iDNDYsVP+4th2WYF6Nwx", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_spriteFrame": {"__uuid__": "d21df45f-4c00-4033-871d-b4bb207f716c"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4ODsY+wJPO61Fw6BCyhBU", "sync": false}, {"__type__": "cc.Node", "_name": "ListView", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 41}, {"__id__": 42}], "_prefab": {"__id__": 43}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 968, "height": 439}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 190.75, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.StudioComponent", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_type": 4, "_checkNormalBackFrame": null, "_checkPressedBackFrame": null, "_checkDisableBackFrame": null, "_checkNormalFrame": null, "_checkDisableFrame": null, "checkInteractable": true, "isChecked": true, "_atlasFrame": null, "firstChar": ".", "charWidth": 0, "charHeight": 0, "string": "", "_sliderBackFrame": null, "_sliderBarFrame": null, "_sliderBtnNormalFrame": null, "_sliderBtnPressedFrame": null, "_sliderBtnDisabledFrame": null, "sliderInteractable": true, "sliderProgress": 0.5, "listInertia": true, "listDirection": 0, "listHorizontalAlign": 1, "listVerticalAlign": 0, "listPadding": 0}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_type": 0, "_segements": 64, "_N$spriteFrame": null, "_N$alphaThreshold": 1, "_N$inverted": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cb0Ee6H1NEIYkFrjENWaBk", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_item", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [{"__id__": 45}, {"__id__": 49}, {"__id__": 53}, {"__id__": 57}, {"__id__": 61}, {"__id__": 65}, {"__id__": 69}, {"__id__": 73}, {"__id__": 84}, {"__id__": 98}, {"__id__": 102}, {"__id__": 106}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 110}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 968, "height": 87}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-483.4074, 104.0964, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_11", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}], "_prefab": {"__id__": 48}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 951, "height": 80}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [484, 43.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.008799999999999975, "_right": 0, "_top": 0, "_bottom": 0.04025000000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_spriteFrame": {"__uuid__": "d21df45f-4c00-4033-871d-b4bb207f716c"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f59mPzgcdHHK4GP9Z6r4FF", "sync": false}, {"__type__": "cc.Node", "_name": "Image_12", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 50}, {"__id__": 51}], "_prefab": {"__id__": 52}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 55}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [139, 43.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.1839, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_spriteFrame": {"__uuid__": "4db4d25a-f7e4-4e5c-b603-c91d01718ed4"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cb/XQDhrJPw634tLHkW8/G", "sync": false}, {"__type__": "cc.Node", "_name": "Image_12", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 54}, {"__id__": 55}], "_prefab": {"__id__": 56}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 55}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [309, 43.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.1839, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "_spriteFrame": {"__uuid__": "4db4d25a-f7e4-4e5c-b603-c91d01718ed4"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aKLwRcWdHkbNdGOjvRRyg", "sync": false}, {"__type__": "cc.Node", "_name": "Image_12", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 58}, {"__id__": 59}], "_prefab": {"__id__": 60}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 55}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [484, 43.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.1839, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_spriteFrame": {"__uuid__": "4db4d25a-f7e4-4e5c-b603-c91d01718ed4"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "77tJBpeqhITou0lrJJ8fNB", "sync": false}, {"__type__": "cc.Node", "_name": "Image_12", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 62}, {"__id__": 63}], "_prefab": {"__id__": 64}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 55}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [824, 43.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.1839, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_spriteFrame": {"__uuid__": "4db4d25a-f7e4-4e5c-b603-c91d01718ed4"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eahFR1nCxPNIQ+BUl8AZp0", "sync": false}, {"__type__": "cc.Node", "_name": "Text_time", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 66}, {"__id__": 67}], "_prefab": {"__id__": 68}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 70}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [71.66, 43.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.09770000000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "_useOriginalSize": false, "_actualFontSize": 40, "_fontSize": 26, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "10:23AM 27MAY", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81nvb8khtAxLsra6EKIaCk", "sync": false}, {"__type__": "cc.Node", "_name": "Text_rewards", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}], "_prefab": {"__id__": 72}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 240, "g": 243, "b": 9, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5111, "y": 0.5503}, "_contentSize": {"__type__": "cc.Size", "width": 109, "height": 28}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [219.875, 44.9087, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.33911346, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 28, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "₹112977", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "486/oMVvJJ2bn8+8ZDCYjm", "sync": false}, {"__type__": "cc.Node", "_name": "mutli_item", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [{"__id__": 74}, {"__id__": 78}], "_tag": -1, "_active": true, "_components": [{"__id__": 82}], "_prefab": {"__id__": 83}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 131, "height": 75}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [396, 43.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_item_bg", "_objFlags": 0, "_parent": {"__id__": 73}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 75}, {"__id__": 76}], "_prefab": {"__id__": 77}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 131, "height": 75}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "_spriteFrame": {"__uuid__": "26c08548-6cf5-4a83-b6a5-103511ed3e72"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cTI1cuuBHV5hrxJwGhHo5", "sync": false}, {"__type__": "cc.Node", "_name": "num", "_objFlags": 0, "_parent": {"__id__": 73}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 79}, {"__id__": 80}], "_prefab": {"__id__": 81}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 94, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.1412, "_right": 0, "_top": 0, "_bottom": 0.3, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "8901bb4d-015e-4702-b380-************"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "20.00x", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c85BnGqv1JZINl3XzfFrPD", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.06895000000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5boS6sKSpKGphXfKehGqy3", "sync": false}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [{"__id__": 85}, {"__id__": 92}], "_tag": -1, "_active": true, "_components": [{"__id__": 96}], "_prefab": {"__id__": 97}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [532, 43.5, 0, 0, 0, 0, 1, 0.68, 0.68, 1]}}, {"__type__": "cc.Node", "_name": "Panel_head", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [{"__id__": 86}], "_tag": -1, "_active": true, "_components": [{"__id__": 90}], "_prefab": {"__id__": 91}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "head_pos", "_objFlags": 0, "_parent": {"__id__": 85}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 87}, {"__id__": 88}], "_prefab": {"__id__": 89}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 90}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": -0.011349999999999971, "_right": 0, "_top": 0, "_bottom": -0.011349999999999971, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_spriteFrame": {"__uuid__": "4a4b54d8-9986-4ad3-8c2c-858df87f5c51"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8YdTNOGlGN6prHMjokT9h", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 44, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 88, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1evU9sSJJNBYIeNrCp6ScS", "sync": false}, {"__type__": "cc.Node", "_name": "Image_25", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 93}, {"__id__": 94}], "_prefab": {"__id__": 95}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 94, "height": 94}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": -0.03410000000000002, "_right": 0, "_top": 0, "_bottom": -0.03410000000000002, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "_spriteFrame": {"__uuid__": "28f1a762-52ae-46b1-ac3f-7a8bb21f6b27"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64JvDK/+VI6q242hNTOho1", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": -0.005750000000000033, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbKejinI5GUaFBCCEE0GbG", "sync": false}, {"__type__": "cc.Node", "_name": "Text_name", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 99}, {"__id__": 100}], "_prefab": {"__id__": 101}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 207, "g": 181, "b": 250, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 26}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [574, 59.16, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.5305500000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 26, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "<PERSON><PERSON>", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5mRRerR5IcpsGKNfZujRr", "sync": false}, {"__type__": "cc.Node", "_name": "Text_bet_info", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 103}, {"__id__": 104}], "_prefab": {"__id__": 105}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 240, "g": 243, "b": 9, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 214, "height": 22}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [574, 26.97, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.18355, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 22, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "BET: 740 GET: 22904", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffp2IcoB5E0pdnkCu2JJ2+", "sync": false}, {"__type__": "cc.Node", "_name": "Text_winners", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}], "_prefab": {"__id__": 109}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 31, "height": 26}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [889, 43.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.35055000000000003, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 26, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "70", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "695LZe05VHT6HOJ3EKutwH", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3aq9gRtnNDRqSk6MkiN6Q5", "sync": false}, {"__type__": "cc.StudioComponent.PlaceHolder", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_baseUrl": "db://assets/iwby_Crash/ccbResources/CrashRes/public/dialog/dialog_da_2.prefab", "nestedPrefab": {"__uuid__": "0b8de90a-96be-4862-8c4e-b845c12329c1"}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2KJwjeNVEe7wY180RGeoA", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "062aMS6dVLNZysPJd6elIs", "sync": false}]