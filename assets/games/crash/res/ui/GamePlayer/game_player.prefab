[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_rawFiles": null, "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}, {"__id__": 13}, {"__id__": 27}, {"__id__": 41}, {"__id__": 47}, {"__id__": 190}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 266}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_mask", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 7}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_id": "", "_opacity": 204, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5c+1syAvJD5rlEC6lXa/A1", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4IIxyngpJj74iVBIUR58Y", "sync": false}, {"__type__": "cc.Node", "_name": "Node_bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 9}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 12}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -33, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_170_0", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1160, "height": 631}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_spriteFrame": {"__uuid__": "8d1bc7e4-5485-43d1-8049-c49abc7fa5f0"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0a1d4IM5RFBZbBFPo3VPsI", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64TMKnZgtKGbhknXbJ7zMj", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_big_win", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 14}, {"__id__": 18}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 26}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 398, "height": 58}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-196.96, 237.01, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_btn_normal", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}], "_prefab": {"__id__": 17}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 395, "height": 60}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.0037499999999999756, "_right": 0, "_top": 0, "_bottom": -0.017249999999999988, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_spriteFrame": {"__uuid__": "bcdf32a7-de8c-46cb-b694-5c29d4ea960e"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16op4SayRGEZBdIJ3OK2k/", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_btn_select", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 19}], "_tag": -1, "_active": true, "_components": [{"__id__": 23}, {"__id__": 24}], "_prefab": {"__id__": 25}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 394, "height": 58}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_title", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 20}, {"__id__": 21}], "_prefab": {"__id__": 22}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 395, "height": 60}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": -0.0012499999999999734, "_right": 0, "_top": 0, "_bottom": -0.017249999999999988, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_spriteFrame": {"__uuid__": "75824256-2ba9-4fe2-91dc-5ffc601c3bea"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90jRjkmEdFcoZ3Gr43ZF6N", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.005049999999999999, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_spriteFrame": {"__uuid__": "9ea7f4d3-8baf-4189-8eab-33d35ef33962"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1ffEurYL5GnLsx/mzsF6no", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f5sc/6laJP7qMRe8OnFdxe", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_game_players", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 28}, {"__id__": 32}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 40}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 398, "height": 58}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [197.36, 237.01, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_btn_normal", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}], "_prefab": {"__id__": 31}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 395, "height": 60}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 5.219999999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.0037499999999999756, "_right": 0, "_top": 0, "_bottom": 0.07274999999999998, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_spriteFrame": {"__uuid__": "416be850-5fdc-42fe-8191-053541b466f1"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "73aArhAmBOE7VhUjVL/2R9", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_btn_select", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [{"__id__": 33}], "_tag": -1, "_active": true, "_components": [{"__id__": 37}, {"__id__": 38}], "_prefab": {"__id__": 39}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 394, "height": 58}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_title", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 34}, {"__id__": 35}], "_prefab": {"__id__": 36}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 395, "height": 60}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 5.219999999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": -0.0012499999999999734, "_right": 0, "_top": 0, "_bottom": 0.07274999999999998, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_spriteFrame": {"__uuid__": "dffc42f9-e097-410c-8183-f8c50aa17f34"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23rSyxOTdCZKCupngnpum6", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.005049999999999999, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_spriteFrame": {"__uuid__": "9ea7f4d3-8baf-4189-8eab-33d35ef33962"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4qpghchxLjrJGFBxjFbkA", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2f5geEPnpHELcQVqvKdZSo", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_close", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 42}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 46}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [565.7343, 256.493, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 43}, {"__id__": 44}], "_prefab": {"__id__": 45}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "_spriteFrame": {"__uuid__": "6ecc0be9-6672-4ca6-99a7-9cf6d73cf6ff"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3ti0EXMJGAIIm0LgDmKf/", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1eKjgz9CJHOKqv62Kr1Okj", "sync": false}, {"__type__": "cc.Node", "_name": "Node_content_game_players", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 48}, {"__id__": 136}, {"__id__": 159}, {"__id__": 172}, {"__id__": 178}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 189}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_top_players", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [{"__id__": 49}, {"__id__": 92}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 135}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [7.8821, 19.8542, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_player_1", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [{"__id__": 50}, {"__id__": 57}, {"__id__": 67}, {"__id__": 70}, {"__id__": 73}, {"__id__": 76}, {"__id__": 83}], "_tag": -1, "_active": true, "_components": [{"__id__": 90}], "_prefab": {"__id__": 91}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 179}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-245.0574, 180.8536, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_name", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [{"__id__": 51}], "_tag": -1, "_active": true, "_components": [{"__id__": 55}], "_prefab": {"__id__": 56}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 46}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-154.5735, -138.13, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_name", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}], "_prefab": {"__id__": 54}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 80, "g": 239, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -7.001200000000001, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.23914999999999997, "_right": 0, "_top": 0, "_bottom": 0.1304, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "230l3doTdHtYsa5NzYIzfw", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_spriteFrame": {"__uuid__": "42a7ea32-8e51-4892-9c9a-679ff607c0c1"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "00bHm5IIlF/p/6S/Y1spb8", "sync": false}, {"__type__": "cc.Node", "_name": "Node_head", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [{"__id__": 58}, {"__id__": 63}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 66}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-153, -73, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_2", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [{"__id__": 59}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 62}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_head", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 60}], "_prefab": {"__id__": 61}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 105}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_spriteFrame": {"__uuid__": "f97d44bf-e37e-4498-b06b-d990791dbeaf"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "022vjf4TNPI7SjrmWJXlvk", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5g8uedBdO6KHPHhWIqsf7", "sync": false}, {"__type__": "cc.Node", "_name": "Image_head_bg", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 64}], "_prefab": {"__id__": 65}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 113}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_spriteFrame": {"__uuid__": "ed237aaf-f869-4e58-8f63-ed06dda57402"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7al7uMP85BLJgTZd6pVXeU", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "119FTqZb9AZLEN090tWLxc", "sync": false}, {"__type__": "cc.Node", "_name": "Image_mark_1", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 68}], "_prefab": {"__id__": 69}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 60}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [65.33010000000002, -42.11859999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "_spriteFrame": {"__uuid__": "b0893206-6c94-42a6-a017-af86f2e15a2d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94n3ClNANE/IY8AkoZ0Akg", "sync": false}, {"__type__": "cc.Node", "_name": "Image_mark_2", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 71}], "_prefab": {"__id__": 72}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 23}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15.481500000000011, -99.8304, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 70}, "_enabled": true, "_spriteFrame": {"__uuid__": "*************-499e-84b0-f438c169699a"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dWjy6zUZO8IKaHvO2fhOa", "sync": false}, {"__type__": "cc.Node", "_name": "Text_mark", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 74}], "_prefab": {"__id__": 75}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 16}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-14.438799999999986, -145.73579999999998, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "_spriteFrame": {"__uuid__": "7374ad26-9873-4749-a0a4-2a85f1950ff7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "180WRJ7BxA1ovlJwkRvk7/", "sync": false}, {"__type__": "cc.Node", "_name": "Image_round", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [{"__id__": 77}], "_tag": -1, "_active": true, "_components": [{"__id__": 81}], "_prefab": {"__id__": 82}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 145, "height": 38}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [136.5684, -142.0687, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_round", "_objFlags": 0, "_parent": {"__id__": 76}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 78}, {"__id__": 79}], "_prefab": {"__id__": 80}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 221, "g": 221, "b": 221, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 99, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.15860000000000002, "_right": 0, "_top": 0, "_bottom": 0.10525000000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 30, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0bvxbxPPVKZZFvxOIKX+0Y", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_spriteFrame": {"__uuid__": "fd9d0339-409f-4fb2-89aa-5f0ff2bb81ba"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7dKnH5RDRLRoUhNO16GjEu", "sync": false}, {"__type__": "cc.Node", "_name": "Image_win", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [{"__id__": 84}], "_tag": -1, "_active": true, "_components": [{"__id__": 88}], "_prefab": {"__id__": 89}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 145, "height": 38}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [135.8902, -96.1777, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_win", "_objFlags": 0, "_parent": {"__id__": 83}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 85}, {"__id__": 86}], "_prefab": {"__id__": 87}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 221, "g": 221, "b": 221, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 99, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.15860000000000002, "_right": 0, "_top": 0, "_bottom": 0.10525000000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 30, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "12+MmXWadC8rLQ10kRF9Hb", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_spriteFrame": {"__uuid__": "fd9d0339-409f-4fb2-89aa-5f0ff2bb81ba"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1avNZPYVCmKZiQLU3Jtlp", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_spriteFrame": {"__uuid__": "002455f3-8b31-4d01-a021-eacf42b24851"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4kWYu8TdE0bX8gRaR57r0", "sync": false}, {"__type__": "cc.Node", "_name": "Image_player_2", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [{"__id__": 93}, {"__id__": 100}, {"__id__": 110}, {"__id__": 113}, {"__id__": 116}, {"__id__": 119}, {"__id__": 126}], "_tag": -1, "_active": true, "_components": [{"__id__": 133}], "_prefab": {"__id__": 134}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 179}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [227.61, 181.8383, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_name", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [{"__id__": 94}], "_tag": -1, "_active": true, "_components": [{"__id__": 98}], "_prefab": {"__id__": 99}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 46}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-154.5735, -138.13, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_name", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 95}, {"__id__": 96}], "_prefab": {"__id__": 97}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 80, "g": 239, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -7.001200000000001, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.23914999999999997, "_right": 0, "_top": 0, "_bottom": 0.1304, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "87ZMKmAvpKIrm+8+d752nL", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_spriteFrame": {"__uuid__": "42a7ea32-8e51-4892-9c9a-679ff607c0c1"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4BPrxVLlINp8291WIa1rq", "sync": false}, {"__type__": "cc.Node", "_name": "Node_head", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [{"__id__": 101}, {"__id__": 106}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 109}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-153, -73, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_2", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 102}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 105}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_head", "_objFlags": 0, "_parent": {"__id__": 101}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 103}], "_prefab": {"__id__": 104}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 105}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_spriteFrame": {"__uuid__": "f97d44bf-e37e-4498-b06b-d990791dbeaf"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcaBXf0gNO9KC3bFVBJ3No", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0fj6qcdtRJ+IYC/DS9T19x", "sync": false}, {"__type__": "cc.Node", "_name": "Image_head_bg", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 107}], "_prefab": {"__id__": 108}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 113}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "_spriteFrame": {"__uuid__": "ed237aaf-f869-4e58-8f63-ed06dda57402"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aaISIDeIBEI4OO13NFcfO7", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a9ztxmejNHW4hrR6lANnjg", "sync": false}, {"__type__": "cc.Node", "_name": "Image_mark_1", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 111}], "_prefab": {"__id__": 112}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 61}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [65.33010000000002, -42.11859999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "_spriteFrame": {"__uuid__": "a08b275a-411e-4506-9929-13d292b2996c"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ad7lsi/StIirBqbXpTXkGh", "sync": false}, {"__type__": "cc.Node", "_name": "Image_mark_2", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 114}], "_prefab": {"__id__": 115}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 23}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15.481500000000011, -99.8304, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "_spriteFrame": {"__uuid__": "*************-499e-84b0-f438c169699a"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10wPrrsndBhKxujDBUsyMA", "sync": false}, {"__type__": "cc.Node", "_name": "Text_mark", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 117}], "_prefab": {"__id__": 118}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 16}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-14.438799999999986, -145.73579999999998, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "_spriteFrame": {"__uuid__": "7374ad26-9873-4749-a0a4-2a85f1950ff7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59uqTNphJA8qji10+Wtto0", "sync": false}, {"__type__": "cc.Node", "_name": "Image_round", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [{"__id__": 120}], "_tag": -1, "_active": true, "_components": [{"__id__": 124}], "_prefab": {"__id__": 125}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 145, "height": 38}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [136.5684, -142.0687, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_round", "_objFlags": 0, "_parent": {"__id__": 119}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 121}, {"__id__": 122}], "_prefab": {"__id__": 123}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 221, "g": 221, "b": 221, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 99, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.15860000000000002, "_right": 0, "_top": 0, "_bottom": 0.10525000000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 30, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4U5BiKYdLpJIDzjj7Sa5I", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "_spriteFrame": {"__uuid__": "fd9d0339-409f-4fb2-89aa-5f0ff2bb81ba"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5daNCHXNPhIRgC9SniRU5", "sync": false}, {"__type__": "cc.Node", "_name": "Image_win", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [{"__id__": 127}], "_tag": -1, "_active": true, "_components": [{"__id__": 131}], "_prefab": {"__id__": 132}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 145, "height": 38}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [135.8902, -96.1777, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_win", "_objFlags": 0, "_parent": {"__id__": 126}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 128}, {"__id__": 129}], "_prefab": {"__id__": 130}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 221, "g": 221, "b": 221, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 99, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.15860000000000002, "_right": 0, "_top": 0, "_bottom": 0.10525000000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 30, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "052Cd8SrhMOIM/SWSCguwi", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "_spriteFrame": {"__uuid__": "fd9d0339-409f-4fb2-89aa-5f0ff2bb81ba"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6dXvA1SYVEXraWh3q8yHUi", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "_spriteFrame": {"__uuid__": "002455f3-8b31-4d01-a021-eacf42b24851"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f7s5yOLN5BRKWJL56KeGtn", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccg1mUGHlBXI/kGO/MwnnN", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_item", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [{"__id__": 137}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 158}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 236, "height": 120}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-462.8073, 12.7975, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_item_bg", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [{"__id__": 138}, {"__id__": 144}, {"__id__": 147}, {"__id__": 151}], "_tag": -1, "_active": true, "_components": [{"__id__": 155}, {"__id__": 156}], "_prefab": {"__id__": 157}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 212, "height": 108}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.298, -0.8520000000000039, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_head", "_objFlags": 0, "_parent": {"__id__": 137}, "_children": [{"__id__": 139}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 143}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 113}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [52.03, -54.02, 0, 0, 0, 0, 1, 0.63, 0.63, 1]}}, {"__type__": "cc.Node", "_name": "Image_head", "_objFlags": 0, "_parent": {"__id__": 138}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 140}, {"__id__": 141}], "_prefab": {"__id__": 142}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 105}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.03125, "_right": 0, "_top": 0, "_bottom": 0.03539999999999999, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "_spriteFrame": {"__uuid__": "f97d44bf-e37e-4498-b06b-d990791dbeaf"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "feDBHqI1xENqRHkUkfKhri", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1ljRawYBIKYFUNhFhhvVk", "sync": false}, {"__type__": "cc.Node", "_name": "Image_head_bg", "_objFlags": 0, "_parent": {"__id__": 137}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 145}], "_prefab": {"__id__": 146}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 113}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [52.03, -54.02, 0, 0, 0, 0, 1, 0.63, 0.63, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 144}, "_enabled": true, "_spriteFrame": {"__uuid__": "ed237aaf-f869-4e58-8f63-ed06dda57402"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcR2dtQdFKMauelxdu0guX", "sync": false}, {"__type__": "cc.Node", "_name": "Text_name", "_objFlags": 0, "_parent": {"__id__": 137}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 148}, {"__id__": 149}], "_prefab": {"__id__": 150}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 80, "g": 239, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [153.17, -38.1495, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 147}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0.5810000000000001, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 147}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0GSUC1vFNJaJ/6tDQRzcn", "sync": false}, {"__type__": "cc.Node", "_name": "Text_money", "_objFlags": 0, "_parent": {"__id__": 137}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 152}, {"__id__": 153}], "_prefab": {"__id__": 154}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [153.17, -71.14920000000001, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 151}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0.55975, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 151}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "123456", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "195UWlM3ZK7ZSUQgskKmGX", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.0055, "_right": 0, "_top": 0, "_bottom": 0.09289999999999998, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "_spriteFrame": {"__uuid__": "002455f3-8b31-4d01-a021-eacf42b24851"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1bu/pGlzpOGryskU5hDqgp", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f0ZYeyP6FJ35OS6WJzsIk6", "sync": false}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [{"__id__": 160}, {"__id__": 162}], "_tag": -1, "_active": true, "_components": [{"__id__": 167}, {"__id__": 170}], "_prefab": {"__id__": 171}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 944, "height": 251}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-461.62, 13.86, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 161}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 952, "height": 310}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "47ulZSLI5DWrebFlczUoQD", "sync": false}, {"__type__": "cc.Node", "_name": "vScrollBar", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [{"__id__": 163}], "_tag": -1, "_active": true, "_components": [{"__id__": 166}, {"__id__": 168}], "_prefab": {"__id__": 169}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 15, "height": 251}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 162}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 164}], "_prefab": {"__id__": 165}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 15, "height": 203.2290322580645}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7.5, -77.7290322580645, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 163}, "_enabled": true, "_spriteFrame": {"__uuid__": "5c3bb932-6c3c-468f-88a9-c8c61d458641"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44P1VufqRP15AjHmYLLgMi", "sync": false}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "_scrollView": {"__id__": 167}, "_touching": false, "_opacity": 255, "enableAutoHide": true, "autoHideTime": 1, "_N$handle": {"__id__": 164}, "_N$direction": 1}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "content": {"__id__": 160}, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.5, "elastic": true, "bounceDuration": 1, "scrollEvents": [], "cancelInnerEvents": true, "_N$verticalScrollBar": {"__id__": 166}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 37, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eLWhHll1P2aF6avKEb/9J", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "_type": 0, "_segements": 64, "_N$spriteFrame": null, "_N$alphaThreshold": 1, "_N$inverted": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25MtOsevdEoY0b3wH72Ec7", "sync": false}, {"__type__": "cc.Node", "_name": "Text_total_people_title", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [{"__id__": 173}], "_tag": -1, "_active": true, "_components": [{"__id__": 176}], "_prefab": {"__id__": 177}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 34, "g": 250, "b": 110, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 227, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-467.4504, -261.8906, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_total_people", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 174}], "_prefab": {"__id__": 175}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 34, "g": 250, "b": 110, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 36, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [237.1974, 1.205, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "308", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33CAdPiHpNW59iYSwum1s9", "sync": false}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "TOTAL ONLINE PLAYERS:", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66TcAV3+JMaJeIbJtv9Rbz", "sync": false}, {"__type__": "cc.Node", "_name": "Node_page", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [{"__id__": 179}, {"__id__": 182}, {"__id__": 185}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 188}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [347.8798, -260.1415, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_prev", "_objFlags": 0, "_parent": {"__id__": 178}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 180}], "_prefab": {"__id__": 181}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 34, "g": 250, "b": 110, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-49.65, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "<PREV", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49XKYnhBdN/5rIZJM0UPbe", "sync": false}, {"__type__": "cc.Node", "_name": "Text_page", "_objFlags": 0, "_parent": {"__id__": 178}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 183}], "_prefab": {"__id__": 184}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 34, "g": 250, "b": 110, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.0002, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 182}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "5/5", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72qnq22yRNA7YMWjhwEX2l", "sync": false}, {"__type__": "cc.Node", "_name": "Text_next", "_objFlags": 0, "_parent": {"__id__": 178}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 186}], "_prefab": {"__id__": 187}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 34, "g": 250, "b": 110, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 20}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [54, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 185}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 20, "_lineHeight": 0, "_enableWrapText": true, "_N$file": {"__uuid__": "f5591d18-feff-4b0d-9afe-50bc41081670"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "NEXT>", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7d6C3gWHVCEKxNQJ8PcjBd", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dare6szhKbI/m+s6HXM8V", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6c+p1SEtlOB5l1qgBeie+n", "sync": false}, {"__type__": "cc.Node", "_name": "Node_content_big_win", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 191}, {"__id__": 210}, {"__id__": 215}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 265}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1004, "height": 568}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_titleBg", "_objFlags": 0, "_parent": {"__id__": 190}, "_children": [{"__id__": 192}, {"__id__": 195}, {"__id__": 198}, {"__id__": 201}, {"__id__": 204}], "_tag": -1, "_active": true, "_components": [{"__id__": 207}, {"__id__": 208}], "_prefab": {"__id__": 209}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 934, "height": 40}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 191, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_title_1", "_objFlags": 0, "_parent": {"__id__": 191}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 193}], "_prefab": {"__id__": 194}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 252, "b": 36, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 24}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-406.0002, 1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 24, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Time", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1ccuGpZPVNka+IvHz0edYj", "sync": false}, {"__type__": "cc.Node", "_name": "Text_title_2", "_objFlags": 0, "_parent": {"__id__": 191}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 196}], "_prefab": {"__id__": 197}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 252, "b": 36, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 24}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-259.9971, 1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 24, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Rewards", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beRNJXp59LVa4MuNo5Zv37", "sync": false}, {"__type__": "cc.Node", "_name": "Text_title_3", "_objFlags": 0, "_parent": {"__id__": 191}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 199}], "_prefab": {"__id__": 200}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 252, "b": 36, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 36, "height": 24}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-102.00229999999999, 1.0000999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 198}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 24, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Bet", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35fHDA2vZOVIukU7TQez1f", "sync": false}, {"__type__": "cc.Node", "_name": "Text_title_4", "_objFlags": 0, "_parent": {"__id__": 191}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 202}], "_prefab": {"__id__": 203}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 252, "b": 36, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 24}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [73.00160000000005, 1.0001999999999995, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 24, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Type", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fkRoX+oFLi61FyM52M869", "sync": false}, {"__type__": "cc.Node", "_name": "Text_title_5", "_objFlags": 0, "_parent": {"__id__": 191}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 205}], "_prefab": {"__id__": 206}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 252, "b": 36, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 24}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [324.3849, 1.2453000000000003, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 24, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "Big Winner", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61JHN68RhMdqIOcFmJH52C", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0.03484999999999999, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "_spriteFrame": {"__uuid__": "49145e73-92c6-49d6-a023-7a8558cc6d0d"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "60bxkkwQdNEqvs2FLEttDg", "sync": false}, {"__type__": "cc.Node", "_name": "List_rewards", "_objFlags": 0, "_parent": {"__id__": 190}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 211}, {"__id__": 212}, {"__id__": 213}], "_prefab": {"__id__": 214}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 945, "height": 421}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 160, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0.02939999999999998, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.StudioComponent", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "_type": 4, "_checkNormalBackFrame": null, "_checkPressedBackFrame": null, "_checkDisableBackFrame": null, "_checkNormalFrame": null, "_checkDisableFrame": null, "checkInteractable": true, "isChecked": true, "_atlasFrame": null, "firstChar": ".", "charWidth": 0, "charHeight": 0, "string": "", "_sliderBackFrame": null, "_sliderBarFrame": null, "_sliderBtnNormalFrame": null, "_sliderBtnPressedFrame": null, "_sliderBtnDisabledFrame": null, "sliderInteractable": true, "sliderProgress": 0.5, "listInertia": true, "listDirection": 0, "listHorizontalAlign": 0, "listVerticalAlign": 0, "listPadding": 0}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "_type": 0, "_segements": 64, "_N$spriteFrame": null, "_N$alphaThreshold": 1, "_N$inverted": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdc9sOMKdDGYnE2Redk2iv", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_rewardsItem", "_objFlags": 0, "_parent": {"__id__": 190}, "_children": [{"__id__": 216}, {"__id__": 220}, {"__id__": 223}, {"__id__": 226}, {"__id__": 229}, {"__id__": 232}, {"__id__": 235}, {"__id__": 238}, {"__id__": 248}, {"__id__": 251}, {"__id__": 254}, {"__id__": 260}], "_tag": -1, "_active": true, "_components": [{"__id__": 263}], "_prefab": {"__id__": 264}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 945, "height": 87}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.1043999999999983, 93.07999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_item_bg", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 217}, {"__id__": 218}], "_prefab": {"__id__": 219}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 938, "height": 80}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.003699999999999981, "_right": 0, "_top": 0, "_bottom": 0.04025000000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "_spriteFrame": {"__uuid__": "6f5203e1-daf0-4334-8ec9-b5b94f2d2b1e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dyMDmSVdNirdcIce3l/EI", "sync": false}, {"__type__": "cc.Node", "_name": "Text_time", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 221}], "_prefab": {"__id__": 222}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 48}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-401.4994, 0.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 220}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 24, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "02:48PM\n17Aug", "_N$horizontalAlign": 1, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eda78HETFA4KQ9MRM2TsA7", "sync": false}, {"__type__": "cc.Node", "_name": "Image_line", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 224}], "_prefab": {"__id__": 225}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 64}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-332.5004, -1.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "_spriteFrame": {"__uuid__": "d8290ffa-43a6-41a7-bfae-eb9963e0d077"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "20nGI3Xb5Jj7/lS17hfQze", "sync": false}, {"__type__": "cc.Node", "_name": "Text_rewards", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 227}], "_prefab": {"__id__": 228}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 4, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-258.5, 0.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 30, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "19999", "_N$horizontalAlign": 1, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "30vEUc141GrKuB+kfW17aV", "sync": false}, {"__type__": "cc.Node", "_name": "Image_line", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 230}], "_prefab": {"__id__": 231}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 64}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-175.497, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 229}, "_enabled": true, "_spriteFrame": {"__uuid__": "d8290ffa-43a6-41a7-bfae-eb9963e0d077"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eaTcEy8MZN04t06O1Od5UJ", "sync": false}, {"__type__": "cc.Node", "_name": "Text_bet", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 233}], "_prefab": {"__id__": 234}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-105.69069999999999, 0.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 30, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "19999", "_N$horizontalAlign": 1, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72Gu454IdFlqa5pLXxf62G", "sync": false}, {"__type__": "cc.Node", "_name": "Image_line", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 236}], "_prefab": {"__id__": 237}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 64}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-26.5, -1.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 235}, "_enabled": true, "_spriteFrame": {"__uuid__": "d8290ffa-43a6-41a7-bfae-eb9963e0d077"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40dXxrr+RLb5PjhhH2ku4Z", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_trend_item", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [{"__id__": 239}, {"__id__": 243}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 247}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 131, "height": 75}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [70.39070000000004, -1.6983999999999995, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_item_bg", "_objFlags": 0, "_parent": {"__id__": 238}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 240}, {"__id__": 241}], "_prefab": {"__id__": 242}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 131, "height": 75}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.703000000000003, 1.6950000000000003, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": -0.013000000000000012, "_right": 0, "_top": 0, "_bottom": 0.022599999999999953, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "_spriteFrame": {"__uuid__": "26c08548-6cf5-4a83-b6a5-103511ed3e72"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eofn/wTdC4L1so1HCUQTh", "sync": false}, {"__type__": "cc.Node", "_name": "Text_luck_num", "_objFlags": 0, "_parent": {"__id__": 238}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 244}, {"__id__": 245}], "_prefab": {"__id__": 246}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 94, "height": 30}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 243}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.1412, "_right": 0, "_top": 0, "_bottom": 0.3, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 243}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "8901bb4d-015e-4702-b380-************"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "20.00x", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91fyG3iiZHpLz7+/m6m3oE", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96MiYChxFFLrjOvV6VLSIB", "sync": false}, {"__type__": "cc.Node", "_name": "Image_line", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 249}], "_prefab": {"__id__": 250}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 64}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [173.5, -1.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "_spriteFrame": {"__uuid__": "d8290ffa-43a6-41a7-bfae-eb9963e0d077"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0fzb6xs+9L0qpOysoeqAlk", "sync": false}, {"__type__": "cc.Node", "_name": "Text_name", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 252}], "_prefab": {"__id__": 253}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 24}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [276.6245, 0.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 251}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 24, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "B", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "71F4prytNFG4oVjiHJKeaS", "sync": false}, {"__type__": "cc.Node", "_name": "Node_head", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [{"__id__": 255}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 259}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 113}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [233.29539999999997, -0.6531999999999982, 0, 0, 0, 0, 1, 0.55, 0.55, 1]}}, {"__type__": "cc.Node", "_name": "Image_head", "_objFlags": 0, "_parent": {"__id__": 254}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 256}, {"__id__": 257}], "_prefab": {"__id__": 258}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 105}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 255}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.03125, "_right": 0, "_top": 0, "_bottom": 0.03539999999999999, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 255}, "_enabled": true, "_spriteFrame": {"__uuid__": "f97d44bf-e37e-4498-b06b-d990791dbeaf"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4aB8Lnt1RCHY/QVY90EFJd", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81lSTcsQFPCLXK/wVBnNYk", "sync": false}, {"__type__": "cc.Node", "_name": "Image_headBg", "_objFlags": 0, "_parent": {"__id__": 215}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 261}], "_prefab": {"__id__": 262}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 113}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [233.29539999999997, -0.6531999999999982, 0, 0, 0, 0, 1, 0.55, 0.55, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 260}, "_enabled": true, "_spriteFrame": {"__uuid__": "ed237aaf-f869-4e58-8f63-ed06dda57402"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3KQw4FhVCpI1s2em/tgNJ", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0.03049999999999997, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9e3LzCOvhL2JowS1Y3STPQ", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72/NzmWe1CMYAptNlh/lpN", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55J9/KttNNA6JaCCb14f+F", "sync": false}]