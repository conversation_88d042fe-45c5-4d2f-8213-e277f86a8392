{"skeleton": {"hash": "CYntg5nQyQ4HQsiaEyJJb8ydus0", "spine": "3.7.93", "width": 1560, "height": 918.96, "images": "./images/", "audio": "E:/工作文件/印度/马甲包/8.0.2/SLOT新玩法【牛】/WILD/images"}, "bones": [{"name": "root"}, {"name": "zong", "parent": "root"}, {"name": "niu", "parent": "zong", "x": 452, "y": -48.14}, {"name": "<PERSON><PERSON>en", "parent": "niu", "length": 164.77, "rotation": 90, "y": 11.59}, {"name": "niushen2", "parent": "niu", "length": 199.63, "rotation": -88.34, "y": -20.7}, {"name": "ni<PERSON>wei", "parent": "niushen2", "length": 14.68, "rotation": 4.99, "x": 188.43, "y": -1.68}, {"name": "niuwei2", "parent": "ni<PERSON>wei", "length": 15.31, "rotation": -6.65, "x": 16.04, "y": -0.89}, {"name": "niuwei3", "parent": "niuwei2", "length": 18.01, "rotation": -3.09, "x": 15.31}, {"name": "niuwei4", "parent": "niuwei3", "length": 15.31, "rotation": 3.09, "x": 18.01}, {"name": "niuwei5", "parent": "niuwei4", "length": 17.18, "rotation": 8.13, "x": 15.31}, {"name": "niuwei6", "parent": "niuwei5", "length": 13.05, "rotation": 6.67, "x": 17.68, "y": 0.05}, {"name": "niuwei7", "parent": "niuwei6", "length": 14.23, "rotation": 5.76, "x": 13.05}, {"name": "niuwei8", "parent": "niuwei7", "length": 14.23, "x": 14.23}, {"name": "niuwei9", "parent": "niuwei8", "length": 11.44, "rotation": -3.63, "x": 14.9, "y": -0.25}, {"name": "niuwei10", "parent": "niuwei9", "length": 9.35, "rotation": -2.18, "x": 11.44}, {"name": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON>en", "length": 97.2, "x": 135.73, "y": 1.07}, {"name": "niutou2", "parent": "<PERSON><PERSON><PERSON>", "x": 138.33, "y": -0.83}, {"name": "W", "parent": "zong", "x": -0.68, "y": 152.98}, {"name": "i", "parent": "zong", "x": -7.87, "y": 41.44}, {"name": "L", "parent": "zong", "x": -3.28, "y": -59.82}, {"name": "D", "parent": "zong", "x": -3.28, "y": -163.7}, {"name": "jiesuo02_000", "parent": "root", "x": 394.22, "y": -497.37}, {"name": "L2", "parent": "i", "x": 2.59, "y": -2.88, "scaleX": 0.838, "scaleY": 0.664}, {"name": "i3", "parent": "i", "x": 0.88, "y": -9.52}, {"name": "i4", "parent": "i", "x": 1.77, "y": 42.05}, {"name": "guangzhao", "parent": "niu", "x": -0.95, "y": 294.91}], "slots": [{"name": "beijing", "bone": "root"}, {"name": "di", "bone": "zong", "attachment": "di"}, {"name": "lg5", "bone": "zong", "attachment": "lg0001", "blend": "additive"}, {"name": "lg4", "bone": "zong", "attachment": "lg0001", "blend": "additive"}, {"name": "lg3", "bone": "zong", "attachment": "lg0001", "blend": "additive"}, {"name": "lg2", "bone": "zong", "attachment": "lg0001", "blend": "additive"}, {"name": "lg1", "bone": "zong", "attachment": "lg0001", "blend": "additive"}, {"name": "lg0001", "bone": "zong", "attachment": "lg0001", "blend": "additive"}, {"name": "di2", "bone": "zong", "blend": "additive"}, {"name": "jiesuo02_000", "bone": "jiesuo02_000", "attachment": "jiesuo02_004", "blend": "additive"}, {"name": "D", "bone": "D", "attachment": "D"}, {"name": "i2", "bone": "i4", "attachment": "i2"}, {"name": "i", "bone": "i3", "attachment": "i"}, {"name": "L", "bone": "L", "attachment": "L"}, {"name": "W", "bone": "W", "attachment": "W"}, {"name": "ni<PERSON>wei", "bone": "ni<PERSON>wei", "attachment": "ni<PERSON>wei"}, {"name": "<PERSON><PERSON>en", "bone": "niushen2", "attachment": "<PERSON><PERSON>en"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "wsg0001", "bone": "W", "attachment": "wsg15", "blend": "additive"}, {"name": "dsg0002", "bone": "D", "attachment": "dsg0006", "blend": "additive"}, {"name": "isg0002", "bone": "i", "attachment": "isg0008", "blend": "additive"}, {"name": "lsg0002", "bone": "L", "attachment": "lsg0008", "blend": "additive"}, {"name": "lsg2", "bone": "L2", "attachment": "lsg0005", "blend": "additive"}, {"name": "guangzhao", "bone": "guangzhao", "attachment": "guangzhao", "blend": "additive"}], "skins": {"default": {"D": {"D": {"type": "mesh", "uvs": [0.25685, 0.00463, 0.26208, 0.05679, 0.29736, 0.10184, 0.36138, 0.154, 0.44893, 0.20379, 0.56261, 0.26228, 0.69066, 0.3255, 0.74885, 0.35924, 0.86149, 0.43511, 0.94101, 0.51515, 0.97797, 0.58144, 0.98543, 0.65583, 0.94908, 0.74831, 0.86872, 0.83457, 0.71331, 0.91457, 0.56286, 0.96357, 0.44713, 0.98157, 0.33139, 0.98157, 0.17433, 0.97857, 0.1115, 0.98457, 0.05859, 1, 0.00734, 0.95757, 0.00734, 0.91157, 0.07017, 0.86257, 0.13961, 0.83457, 0.22062, 0.82457, 0.21685, 0.28519, 0.18461, 0.25869, 0.15568, 0.23069, 0.14162, 0.18169, 0.13584, 0.13069, 0.14964, 0.07837, 0.19032, 0.03458, 0.24324, 0.00415, 0.3137, 0.33439, 0.42253, 0.39337, 0.5469, 0.44723, 0.64583, 0.50108, 0.72922, 0.56178, 0.77445, 0.6182, 0.77869, 0.67462, 0.74053, 0.72334, 0.65149, 0.77036, 0.54266, 0.79771, 0.46351, 0.80712, 0.46915, 0.41356, 0.42394, 0.81225, 0.30522, 0.82165, 0.22757, 0.04863, 0.23307, 0.10013, 0.26191, 0.15246, 0.31959, 0.20479, 0.40611, 0.26875, 0.50224, 0.3269, 0.61348, 0.37674, 0.70824, 0.42574, 0.79476, 0.4897, 0.86205, 0.56861, 0.87304, 0.62925, 0.8442, 0.71065, 0.78356, 0.76865, 0.67179, 0.8224, 0.51557, 0.85987, 0.37417, 0.88186, 0.20449, 0.88838, 0.12948, 0.89737, 0.06974, 0.91769, 0.08024, 0.93574, 0.18824, 0.93009, 0.3189, 0.93251, 0.4629, 0.92445, 0.59357, 0.8938, 0.71623, 0.8567, 0.8269, 0.79783, 0.89174, 0.74216, 0.93889, 0.67307, 0.95884, 0.58478], "triangles": [48, 32, 33, 48, 33, 0, 48, 0, 1, 49, 48, 1, 49, 1, 2, 50, 49, 2, 50, 2, 3, 49, 29, 30, 51, 50, 3, 49, 30, 31, 32, 49, 31, 48, 49, 32, 50, 29, 49, 50, 28, 29, 27, 28, 50, 26, 27, 50, 52, 3, 4, 51, 3, 52, 51, 26, 50, 53, 4, 5, 52, 4, 53, 34, 26, 51, 34, 51, 52, 54, 5, 6, 53, 5, 54, 35, 52, 53, 34, 52, 35, 45, 35, 53, 36, 45, 53, 55, 6, 7, 54, 6, 55, 56, 55, 7, 54, 36, 53, 8, 56, 7, 37, 54, 55, 36, 54, 37, 38, 55, 56, 37, 55, 38, 57, 8, 9, 56, 8, 57, 39, 38, 56, 76, 57, 9, 10, 76, 9, 57, 39, 56, 58, 57, 76, 39, 57, 58, 76, 10, 11, 75, 58, 76, 11, 75, 76, 40, 39, 58, 59, 40, 58, 59, 58, 75, 41, 38, 39, 41, 39, 40, 74, 59, 75, 12, 75, 11, 74, 75, 12, 60, 40, 59, 41, 40, 60, 42, 37, 38, 42, 38, 41, 36, 44, 45, 43, 36, 37, 43, 37, 42, 73, 60, 59, 73, 59, 74, 45, 46, 35, 43, 44, 36, 44, 46, 45, 34, 25, 26, 47, 34, 35, 47, 35, 46, 61, 42, 41, 61, 41, 60, 47, 25, 34, 13, 73, 74, 13, 74, 12, 72, 61, 60, 72, 60, 73, 62, 44, 43, 61, 62, 43, 61, 43, 42, 63, 47, 46, 62, 63, 46, 62, 46, 44, 64, 24, 25, 64, 25, 47, 64, 47, 63, 71, 62, 61, 71, 61, 72, 65, 23, 24, 65, 24, 64, 14, 71, 72, 13, 14, 72, 13, 72, 73, 66, 22, 23, 66, 23, 65, 70, 63, 62, 70, 62, 71, 68, 65, 64, 69, 64, 63, 68, 64, 69, 69, 63, 70, 67, 66, 65, 67, 65, 68, 21, 22, 66, 21, 66, 67, 15, 70, 71, 15, 71, 14, 68, 19, 67, 16, 17, 69, 18, 68, 69, 17, 18, 69, 70, 16, 69, 16, 70, 15, 18, 19, 68, 20, 21, 67, 20, 67, 19], "vertices": [-16.3, 71.42, -15.91, 64.95, -13.26, 59.37, -8.46, 52.9, -1.9, 46.72, 6.63, 39.47, 16.23, 31.63, 20.6, 27.45, 29.05, 18.04, 35.01, 8.12, 37.78, -0.1, 38.34, -9.33, 35.61, -20.8, 29.59, -31.49, 17.93, -41.41, 6.65, -47.49, -2.03, -49.72, -10.71, -49.72, -22.49, -49.35, -27.2, -50.09, -31.17, -52.01, -35.02, -46.74, -35.02, -41.04, -30.3, -34.96, -25.1, -31.49, -19.02, -30.25, -19.3, 36.63, -21.72, 39.92, -23.89, 43.39, -24.94, 49.46, -25.38, 55.79, -24.34, 62.28, -21.29, 67.71, -17.32, 71.48, -12.04, 30.53, -3.88, 23.22, 5.45, 16.54, 12.87, 9.86, 19.12, 2.33, 22.52, -4.66, 22.83, -11.66, 19.97, -17.7, 13.29, -23.53, 5.13, -26.92, -0.8, -28.09, -0.38, 20.71, -3.77, -28.72, -12.68, -29.89, -18.5, 65.96, -18.09, 59.58, -15.92, 53.09, -11.6, 46.6, -5.11, 38.67, 2.1, 31.46, 10.44, 25.28, 17.55, 19.2, 24.04, 11.27, 29.09, 1.49, 29.91, -6.03, 27.75, -16.13, 23.2, -23.32, 14.82, -29.98, 3.1, -34.63, -7.5, -37.36, -20.23, -38.16, -25.86, -39.28, -30.34, -41.8, -29.55, -44.04, -21.45, -43.34, -11.65, -43.64, -0.85, -42.64, 8.95, -38.84, 18.15, -34.24, 26.45, -26.94, 31.31, -20.03, 34.85, -11.47, 36.35, -0.52], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 52, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 70, 90, 90, 72, 88, 90, 70, 92, 92, 88, 92, 94, 94, 68, 50, 94, 66, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 42, 42, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 18], "width": 75, "height": 124}}, "L": {"L": {"type": "mesh", "uvs": [0, 0.77108, 0, 0.07322, 0.22428, 0.0382, 0.42821, 0.00635, 0.57967, 0.06075, 0.5781, 0.78248, 0.64143, 0.78405, 0.7481, 0.77514, 0.94476, 0.74738, 0.94971, 0.79853, 0.95809, 0.88514, 0.60909, 0.97938, 0.52763, 0.99698, 0.43854, 0.99698, 0.25018, 0.95138, 0.08218, 0.89298, 0, 0.85378, 0.16409, 0.84449, 0.30076, 0.88588, 0.425, 0.90462, 0.51942, 0.90228, 0.51942, 0.76483, 0.44985, 0.74062, 0.21004, 0.73489, 0.27781, 0.79985, 0.40996, 0.84816, 0.53132, 0.8685, 0.641, 0.87296], "triangles": [23, 1, 2, 3, 22, 2, 4, 21, 22, 4, 22, 3, 23, 2, 22, 21, 4, 5, 0, 1, 23, 7, 8, 9, 24, 23, 22, 17, 0, 23, 17, 23, 24, 25, 24, 22, 15, 16, 0, 26, 21, 5, 25, 22, 21, 26, 25, 21, 27, 5, 6, 26, 5, 27, 27, 6, 7, 27, 7, 9, 27, 9, 10, 18, 24, 25, 17, 24, 18, 17, 15, 0, 26, 19, 25, 20, 26, 27, 20, 19, 26, 18, 25, 19, 14, 17, 18, 15, 17, 14, 11, 20, 27, 11, 27, 10, 13, 19, 20, 12, 13, 20, 18, 19, 13, 14, 18, 13, 11, 12, 20], "vertices": [-7.57, -29.85, -7.57, 43.43, -0.17, 47.1, 6.56, 50.45, 11.56, 44.74, 11.51, -31.05, 13.6, -31.21, 17.12, -30.28, 23.61, -27.36, 23.77, -32.73, 24.05, -41.83, 12.53, -51.72, 9.85, -53.57, 6.91, -53.57, 0.69, -48.78, -4.85, -42.65, -7.57, -38.53, -2.15, -37.56, 2.36, -41.9, 6.46, -43.87, 9.57, -43.62, 9.57, -29.19, 7.28, -26.65, -0.64, -26.05, 1.6, -32.87, 5.96, -37.94, 9.97, -40.08, 13.59, -40.55], "hull": 17, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 2, 0, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 16, 18, 18, 20, 10, 42, 42, 44, 6, 44, 2, 4, 4, 6, 4, 46, 46, 48, 48, 50, 50, 52, 18, 54, 54, 40, 52, 54], "width": 33, "height": 105}}, "W": {"W": {"type": "mesh", "uvs": [0, 0.02547, 0.01595, 0.06727, 0.04033, 0.13114, 0.09904, 0.13533, 0.16551, 0.14997, 0.21537, 0.19392, 0.21445, 0.7531, 0.24837, 0.82187, 0.28313, 0.89234, 0.34666, 0.96271, 0.45882, 0.99901, 0.57725, 0.91382, 0.64435, 0.94414, 0.68182, 0.96436, 0.72464, 0.9593, 0.97958, 0.80188, 0.99052, 0.07814, 0.94667, 0.01761, 0.88074, 0.05715, 0.77868, 0.11835, 0.78129, 0.78171, 0.75, 0.78171, 0.72131, 0.77309, 0.72131, 0.28173, 0.67567, 0.22139, 0.60076, 0.16988, 0.51268, 0.10932, 0.51138, 0.76816, 0.49443, 0.80388, 0.47096, 0.8125, 0.43445, 0.79526, 0.43054, 0.22631, 0.41458, 0.17414, 0.37425, 0.12859, 0.33015, 0.0788, 0.25191, 0.02708, 0.18411, 0.00614, 0.03807, 0, 0.0964, 0.07093, 0.16175, 0.08963, 0.20433, 0.1261, 0.22147, 0.16743, 0.3084, 0.88325, 0.37634, 0.91564, 0.43328, 0.91747, 0.55946, 0.84291, 0.62407, 0.87912, 0.6673, 0.88845, 0.70683, 0.88378, 0.9403, 0.73212, 0.6747, 0.73607, 0.69676, 0.76501, 0.38793, 0.21061, 0.39284, 0.7407, 0.41367, 0.78121, 0.2791, 0.07243, 0.31879, 0.13599, 0.33346, 0.21014, 0.33087, 0.74547, 0.3475, 0.79501, 0.38094, 0.83575, 0.41868, 0.85561, 0.45481, 0.85408, 0.50388, 0.82862, 0.5152, 0.79858, 0.60015, 0.77215, 0.61436, 0.82317, 0.64422, 0.85673, 0.88484, 0.72685, 0.86181, 0.7533, 0.81867, 0.77275], "triangles": [1, 0, 37, 38, 37, 36, 1, 37, 38, 34, 55, 35, 36, 35, 55, 39, 38, 36, 39, 36, 55, 40, 39, 55, 2, 1, 38, 3, 38, 39, 2, 38, 3, 56, 55, 34, 56, 34, 33, 41, 40, 55, 4, 39, 40, 3, 39, 4, 56, 41, 55, 4, 40, 41, 5, 4, 41, 57, 56, 33, 52, 57, 33, 41, 56, 57, 5, 41, 57, 32, 52, 33, 52, 32, 31, 17, 49, 18, 17, 16, 49, 19, 68, 20, 68, 18, 49, 16, 15, 49, 24, 23, 50, 27, 26, 25, 22, 50, 23, 53, 52, 31, 27, 25, 65, 31, 30, 53, 57, 6, 5, 53, 58, 57, 53, 57, 52, 58, 6, 57, 50, 22, 51, 65, 25, 24, 50, 65, 24, 68, 69, 70, 18, 68, 19, 30, 54, 53, 68, 70, 20, 59, 58, 53, 59, 53, 54, 64, 27, 65, 64, 28, 27, 7, 6, 58, 7, 58, 59, 51, 66, 65, 51, 65, 50, 45, 64, 65, 63, 28, 64, 60, 59, 54, 60, 54, 30, 66, 45, 65, 63, 64, 45, 62, 30, 29, 61, 60, 30, 63, 62, 29, 63, 29, 28, 62, 61, 30, 67, 66, 51, 46, 66, 67, 45, 66, 46, 42, 7, 59, 42, 59, 60, 22, 67, 51, 48, 22, 21, 48, 67, 22, 48, 21, 20, 48, 20, 70, 48, 70, 69, 69, 68, 49, 48, 69, 49, 47, 67, 48, 46, 67, 47, 8, 7, 42, 11, 45, 46, 43, 42, 60, 43, 60, 61, 44, 61, 62, 43, 61, 44, 45, 44, 62, 45, 62, 63, 12, 46, 47, 11, 46, 12, 15, 14, 48, 15, 48, 49, 9, 42, 43, 8, 42, 9, 13, 47, 48, 13, 48, 14, 12, 47, 13, 11, 10, 44, 11, 44, 45, 9, 43, 44, 10, 9, 44], "vertices": [-60.17, 50.57, -58.55, 46.05, -56.06, 39.16, -50.07, 38.7, -43.29, 37.12, -38.21, 32.38, -38.3, -28.02, -34.84, -35.44, -31.29, -43.05, -24.81, -50.65, -13.37, -54.57, -1.29, -45.37, 5.55, -48.65, 9.37, -50.83, 13.74, -50.29, 39.74, -33.28, 40.86, 44.88, 36.39, 51.42, 29.66, 47.15, 19.25, 40.54, 19.52, -31.11, 16.33, -31.11, 13.4, -30.17, 13.4, 22.89, 8.75, 29.41, 1.1, 34.97, -7.88, 41.51, -8.01, -29.64, -9.74, -33.5, -12.13, -34.43, -15.86, -32.57, -16.26, 28.88, -17.89, 34.51, -22, 39.43, -26.5, 44.81, -34.48, 50.39, -41.39, 52.66, -56.29, 53.32, -50.34, 45.66, -43.67, 43.64, -39.33, 39.7, -37.58, 35.24, -28.72, -42.07, -21.79, -45.57, -15.98, -45.77, -3.11, -37.72, 3.48, -41.63, 7.89, -42.63, 11.92, -42.13, 35.74, -25.75, 8.65, -26.18, 10.9, -29.3, -20.6, 30.57, -20.1, -26.68, -17.98, -31.05, -31.7, 45.5, -27.66, 38.63, -26.16, 30.62, -26.42, -27.19, -24.73, -32.54, -21.32, -36.94, -17.47, -39.09, -13.78, -38.92, -8.78, -36.17, -7.62, -32.93, 1.04, -30.07, 2.49, -35.58, 5.54, -39.21, 30.08, -25.18, 27.73, -28.04, 23.33, -30.14], "hull": 38, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 68, 70, 70, 72, 72, 74, 0, 74, 0, 2, 2, 4, 2, 76, 76, 78, 78, 80, 80, 82, 82, 10, 12, 14, 14, 16, 14, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 34, 48, 100, 100, 102, 102, 44, 64, 66, 66, 68, 66, 104, 104, 106, 106, 108, 108, 60, 72, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 54, 48, 50, 50, 52, 50, 130, 130, 132, 132, 134, 134, 96, 34, 36, 36, 38, 36, 136, 136, 138, 138, 140, 140, 40], "width": 102, "height": 108}}, "di": {"di": {"type": "mesh", "uvs": [1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.92857, 0, 0.85714, 0, 0.78571, 0, 0.71429, 0, 0.64286, 0, 0.57143, 0, 0.5, 0, 0.42857, 0, 0.35714, 0, 0.28571, 0, 0.21429, 0, 0.14286, 0, 0.07143, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 1, 0.07143, 1, 0.14286, 1, 0.21429, 1, 0.28571, 1, 0.35714, 1, 0.42857, 1, 0.5, 1, 0.57143, 1, 0.64286, 1, 0.71429, 1, 0.78571, 1, 0.85714, 1, 0.92857, 0.8, 0.92857, 0.6, 0.92857, 0.4, 0.92857, 0.2, 0.92857, 0.8, 0.85714, 0.6, 0.85714, 0.4, 0.85714, 0.2, 0.85714, 0.8, 0.78571, 0.6, 0.78571, 0.4, 0.78571, 0.2, 0.78571, 0.8, 0.71429, 0.6, 0.71429, 0.4, 0.71429, 0.2, 0.71429, 0.8, 0.64286, 0.6, 0.64286, 0.4, 0.64286, 0.2, 0.64286, 0.8, 0.57143, 0.6, 0.57143, 0.4, 0.57143, 0.2, 0.57143, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2, 0.5, 0.8, 0.42857, 0.6, 0.42857, 0.4, 0.42857, 0.2, 0.42857, 0.8, 0.35714, 0.6, 0.35714, 0.4, 0.35714, 0.2, 0.35714, 0.8, 0.28571, 0.6, 0.28571, 0.4, 0.28571, 0.2, 0.28571, 0.8, 0.21429, 0.6, 0.21429, 0.4, 0.21429, 0.2, 0.21429, 0.8, 0.14286, 0.6, 0.14286, 0.4, 0.14286, 0.2, 0.14286, 0.8, 0.07143, 0.6, 0.07143, 0.4, 0.07143, 0.2, 0.07143], "triangles": [0, 37, 1, 1, 37, 38, 37, 36, 38, 38, 36, 42, 36, 35, 42, 42, 35, 46, 35, 34, 46, 46, 34, 50, 34, 33, 50, 50, 33, 54, 33, 32, 54, 54, 32, 58, 32, 31, 58, 58, 31, 62, 31, 30, 62, 62, 30, 66, 30, 29, 66, 66, 29, 70, 29, 28, 70, 70, 28, 74, 28, 27, 74, 74, 27, 78, 27, 26, 78, 78, 26, 82, 26, 25, 82, 82, 25, 86, 25, 24, 86, 86, 24, 23, 1, 38, 2, 2, 38, 39, 38, 42, 39, 39, 42, 43, 42, 46, 43, 43, 46, 47, 46, 50, 47, 47, 50, 51, 50, 54, 51, 51, 54, 55, 54, 58, 55, 55, 58, 59, 58, 62, 59, 59, 62, 63, 62, 66, 63, 63, 66, 67, 66, 70, 67, 67, 70, 71, 70, 74, 71, 71, 74, 75, 74, 78, 75, 75, 78, 79, 78, 82, 79, 79, 82, 83, 82, 86, 83, 83, 86, 87, 86, 23, 87, 87, 23, 22, 2, 39, 3, 3, 39, 40, 39, 43, 40, 40, 43, 44, 43, 47, 44, 44, 47, 48, 47, 51, 48, 48, 51, 52, 51, 55, 52, 52, 55, 56, 55, 59, 56, 56, 59, 60, 59, 63, 60, 60, 63, 64, 63, 67, 64, 64, 67, 68, 67, 71, 68, 68, 71, 72, 71, 75, 72, 72, 75, 76, 75, 79, 76, 76, 79, 80, 79, 83, 80, 80, 83, 84, 83, 87, 84, 84, 87, 88, 87, 22, 88, 88, 22, 21, 3, 40, 4, 4, 40, 41, 40, 44, 41, 41, 44, 45, 44, 48, 45, 45, 48, 49, 48, 52, 49, 49, 52, 53, 52, 56, 53, 53, 56, 57, 56, 60, 57, 57, 60, 61, 60, 64, 61, 61, 64, 65, 64, 68, 65, 65, 68, 69, 68, 72, 69, 69, 72, 73, 72, 76, 73, 73, 76, 77, 76, 80, 77, 77, 80, 81, 80, 84, 81, 81, 84, 85, 84, 88, 85, 85, 88, 89, 88, 21, 89, 89, 21, 20, 4, 41, 5, 5, 41, 6, 41, 45, 6, 6, 45, 7, 45, 49, 7, 7, 49, 8, 49, 53, 8, 8, 53, 9, 53, 57, 9, 9, 57, 10, 57, 61, 10, 10, 61, 11, 61, 65, 11, 11, 65, 12, 65, 69, 12, 12, 69, 13, 69, 73, 13, 13, 73, 14, 73, 77, 14, 14, 77, 15, 77, 81, 15, 15, 81, 16, 81, 85, 16, 16, 85, 17, 85, 89, 17, 17, 89, 18, 89, 20, 18, 18, 20, 19], "vertices": [96, -241.5, 57.6, -241.5, 19.2, -241.5, -19.2, -241.5, -57.6, -241.5, -96, -241.5, -96, -207, -96, -172.5, -96, -138, -96, -103.5, -96, -69, -96, -34.5, -96, 0, -96, 34.5, -96, 69, -96, 103.5, -96, 138, -96, 172.5, -96, 207, -96, 241.5, -57.6, 241.5, -19.2, 241.5, 19.2, 241.5, 57.6, 241.5, 96, 241.5, 96, 207, 96, 172.5, 96, 138, 96, 103.5, 96, 69, 96, 34.5, 96, 0, 96, -34.5, 96, -69, 96, -103.5, 96, -138, 96, -172.5, 96, -207, 57.6, -207, 19.2, -207, -19.2, -207, -57.6, -207, 57.6, -172.5, 19.2, -172.5, -19.2, -172.5, -57.6, -172.5, 57.6, -138, 19.2, -138, -19.2, -138, -57.6, -138, 57.6, -103.5, 19.2, -103.5, -19.2, -103.5, -57.6, -103.5, 57.6, -69, 19.2, -69, -19.2, -69, -57.6, -69, 57.6, -34.5, 19.2, -34.5, -19.2, -34.5, -57.6, -34.5, 57.6, 0, 19.2, 0, -19.2, 0, -57.6, 0, 57.6, 34.5, 19.2, 34.5, -19.2, 34.5, -57.6, 34.5, 57.6, 69, 19.2, 69, -19.2, 69, -57.6, 69, 57.6, 103.5, 19.2, 103.5, -19.2, 103.5, -57.6, 103.5, 57.6, 138, 19.2, 138, -19.2, 138, -57.6, 138, 57.6, 172.5, 19.2, 172.5, -19.2, 172.5, -57.6, 172.5, 57.6, 207, 19.2, 207, -19.2, 207, -57.6, 207], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 0], "width": 192, "height": 483}}, "di2": {"di": {"type": "mesh", "uvs": [1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.92857, 0, 0.85714, 0, 0.78571, 0, 0.71429, 0, 0.64286, 0, 0.57143, 0, 0.5, 0, 0.42857, 0, 0.35714, 0, 0.28571, 0, 0.21429, 0, 0.14286, 0, 0.07143, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 1, 0.07143, 1, 0.14286, 1, 0.21429, 1, 0.28571, 1, 0.35714, 1, 0.42857, 1, 0.5, 1, 0.57143, 1, 0.64286, 1, 0.71429, 1, 0.78571, 1, 0.85714, 1, 0.92857, 0.8, 0.92857, 0.6, 0.92857, 0.4, 0.92857, 0.2, 0.92857, 0.8, 0.85714, 0.6, 0.85714, 0.4, 0.85714, 0.2, 0.85714, 0.8, 0.78571, 0.6, 0.78571, 0.4, 0.78571, 0.2, 0.78571, 0.8, 0.71429, 0.6, 0.71429, 0.4, 0.71429, 0.2, 0.71429, 0.8, 0.64286, 0.6, 0.64286, 0.4, 0.64286, 0.2, 0.64286, 0.8, 0.57143, 0.6, 0.57143, 0.4, 0.57143, 0.2, 0.57143, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2, 0.5, 0.8, 0.42857, 0.6, 0.42857, 0.4, 0.42857, 0.2, 0.42857, 0.8, 0.35714, 0.6, 0.35714, 0.4, 0.35714, 0.2, 0.35714, 0.8, 0.28571, 0.6, 0.28571, 0.4, 0.28571, 0.2, 0.28571, 0.8, 0.21429, 0.6, 0.21429, 0.4, 0.21429, 0.2, 0.21429, 0.8, 0.14286, 0.6, 0.14286, 0.4, 0.14286, 0.2, 0.14286, 0.8, 0.07143, 0.6, 0.07143, 0.4, 0.07143, 0.2, 0.07143], "triangles": [0, 37, 1, 1, 37, 38, 37, 36, 38, 38, 36, 42, 36, 35, 42, 42, 35, 46, 35, 34, 46, 46, 34, 50, 34, 33, 50, 50, 33, 54, 33, 32, 54, 54, 32, 58, 32, 31, 58, 58, 31, 62, 31, 30, 62, 62, 30, 66, 30, 29, 66, 66, 29, 70, 29, 28, 70, 70, 28, 74, 28, 27, 74, 74, 27, 78, 27, 26, 78, 78, 26, 82, 26, 25, 82, 82, 25, 86, 25, 24, 86, 86, 24, 23, 1, 38, 2, 2, 38, 39, 38, 42, 39, 39, 42, 43, 42, 46, 43, 43, 46, 47, 46, 50, 47, 47, 50, 51, 50, 54, 51, 51, 54, 55, 54, 58, 55, 55, 58, 59, 58, 62, 59, 59, 62, 63, 62, 66, 63, 63, 66, 67, 66, 70, 67, 67, 70, 71, 70, 74, 71, 71, 74, 75, 74, 78, 75, 75, 78, 79, 78, 82, 79, 79, 82, 83, 82, 86, 83, 83, 86, 87, 86, 23, 87, 87, 23, 22, 2, 39, 3, 3, 39, 40, 39, 43, 40, 40, 43, 44, 43, 47, 44, 44, 47, 48, 47, 51, 48, 48, 51, 52, 51, 55, 52, 52, 55, 56, 55, 59, 56, 56, 59, 60, 59, 63, 60, 60, 63, 64, 63, 67, 64, 64, 67, 68, 67, 71, 68, 68, 71, 72, 71, 75, 72, 72, 75, 76, 75, 79, 76, 76, 79, 80, 79, 83, 80, 80, 83, 84, 83, 87, 84, 84, 87, 88, 87, 22, 88, 88, 22, 21, 3, 40, 4, 4, 40, 41, 40, 44, 41, 41, 44, 45, 44, 48, 45, 45, 48, 49, 48, 52, 49, 49, 52, 53, 52, 56, 53, 53, 56, 57, 56, 60, 57, 57, 60, 61, 60, 64, 61, 61, 64, 65, 64, 68, 65, 65, 68, 69, 68, 72, 69, 69, 72, 73, 72, 76, 73, 73, 76, 77, 76, 80, 77, 77, 80, 81, 80, 84, 81, 81, 84, 85, 84, 88, 85, 85, 88, 89, 88, 21, 89, 89, 21, 20, 4, 41, 5, 5, 41, 6, 41, 45, 6, 6, 45, 7, 45, 49, 7, 7, 49, 8, 49, 53, 8, 8, 53, 9, 53, 57, 9, 9, 57, 10, 57, 61, 10, 10, 61, 11, 61, 65, 11, 11, 65, 12, 65, 69, 12, 12, 69, 13, 69, 73, 13, 13, 73, 14, 73, 77, 14, 14, 77, 15, 77, 81, 15, 15, 81, 16, 81, 85, 16, 16, 85, 17, 85, 89, 17, 17, 89, 18, 89, 20, 18, 18, 20, 19], "vertices": [96, -241.5, 57.6, -241.5, 19.2, -241.5, -19.2, -241.5, -57.6, -241.5, -96, -241.5, -96, -207, -96, -172.5, -96, -138, -96, -103.5, -96, -69, -96, -34.5, -96, 0, -96, 34.5, -96, 69, -96, 103.5, -96, 138, -96, 172.5, -96, 207, -96, 241.5, -57.6, 241.5, -19.2, 241.5, 19.2, 241.5, 57.6, 241.5, 96, 241.5, 96, 207, 96, 172.5, 96, 138, 96, 103.5, 96, 69, 96, 34.5, 96, 0, 96, -34.5, 96, -69, 96, -103.5, 96, -138, 96, -172.5, 96, -207, 57.6, -207, 19.2, -207, -19.2, -207, -57.6, -207, 57.6, -172.5, 19.2, -172.5, -19.2, -172.5, -57.6, -172.5, 57.6, -138, 19.2, -138, -19.2, -138, -57.6, -138, 57.6, -103.5, 19.2, -103.5, -19.2, -103.5, -57.6, -103.5, 57.6, -69, 19.2, -69, -19.2, -69, -57.6, -69, 57.6, -34.5, 19.2, -34.5, -19.2, -34.5, -57.6, -34.5, 57.6, 0, 19.2, 0, -19.2, 0, -57.6, 0, 57.6, 34.5, 19.2, 34.5, -19.2, 34.5, -57.6, 34.5, 57.6, 69, 19.2, 69, -19.2, 69, -57.6, 69, 57.6, 103.5, 19.2, 103.5, -19.2, 103.5, -57.6, 103.5, 57.6, 138, 19.2, 138, -19.2, 138, -57.6, 138, 57.6, 172.5, 19.2, 172.5, -19.2, 172.5, -57.6, 172.5, 57.6, 207, 19.2, 207, -19.2, 207, -57.6, 207], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 0], "width": 192, "height": 483}}, "dsg0002": {"dsg0002": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0003": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0004": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0005": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0006": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0007": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0008": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0009": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0010": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0011": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0012": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0013": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0014": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0015": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}, "dsg0016": {"x": 5.15, "y": 8.37, "scaleX": 2, "scaleY": 2, "width": 47, "height": 72}}, "guangzhao": {"guangzhao": {"x": 11.09, "y": -136.72, "scaleX": 2.237, "scaleY": 2.71, "rotation": 90, "width": 162, "height": 182}}, "i": {"i": {"type": "mesh", "uvs": [0.15039, 0.75738, 0.13326, 0.36762, 0.34738, 0.3234, 0.54906, 0.28174, 0.66566, 0.33875, 0.67226, 0.8, 0.77346, 0.8, 0.85706, 0.79482, 0.92478, 0.82669, 0.96501, 0.902, 0.62011, 0.99285, 0.54611, 0.99285, 0.41291, 0.96795, 0.27125, 0.92027, 0.17611, 0.86051, 0.15285, 0.81355, 0.22252, 0.8267, 0.3167, 0.87523, 0.46972, 0.91286, 0.57272, 0.92772, 0.61392, 0.78907, 0.56389, 0.75737, 0.34612, 0.76431, 0.40792, 0.83363, 0.51092, 0.87523, 0.66768, 0.90047], "triangles": [21, 3, 4, 2, 3, 21, 22, 0, 1, 2, 22, 1, 21, 22, 2, 5, 20, 21, 21, 4, 5, 16, 15, 0, 22, 16, 0, 23, 22, 21, 14, 15, 16, 17, 16, 22, 17, 22, 23, 24, 23, 21, 24, 21, 20, 25, 20, 5, 24, 20, 25, 25, 5, 6, 8, 25, 6, 8, 6, 7, 18, 23, 24, 17, 23, 18, 13, 16, 17, 14, 16, 13, 19, 24, 25, 18, 24, 19, 12, 17, 18, 13, 17, 12, 11, 18, 19, 12, 18, 11, 10, 19, 25, 11, 19, 10, 9, 10, 25, 9, 25, 8], "vertices": [-6.59, -12.39, -7.19, 28.15, 0.31, 32.75, 7.37, 37.08, 11.45, 31.15, 11.68, -16.82, 15.22, -16.82, 18.15, -16.28, 20.52, -19.6, 21.92, -27.43, 9.85, -36.88, 7.26, -36.88, 2.6, -34.29, -2.36, -29.33, -5.69, -23.11, -6.5, -18.23, -4.06, -19.6, -0.77, -24.64, 4.59, -28.56, 8.19, -30.1, 9.64, -15.68, 7.88, -12.39, 0.26, -13.11, 2.43, -20.32, 6.03, -24.64, 11.52, -27.27], "hull": 16, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 2, 0, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 10, 40, 40, 42, 42, 6, 2, 4, 4, 6, 4, 44, 44, 46, 46, 48, 16, 50, 50, 38, 48, 50], "width": 35, "height": 104}}, "i2": {"i2": {"type": "mesh", "uvs": [0.48134, 0, 0.01257, 0.39289, 0.02587, 0.56109, 0.47144, 1, 0.51669, 0.99748, 0.9789, 0.60202, 0.9589, 0.40203, 0.48141, 0.7964], "triangles": [7, 2, 1, 0, 7, 1, 6, 7, 0, 5, 7, 6, 4, 7, 5, 3, 2, 7, 3, 7, 4], "vertices": [0.46, 14.22, -13.6, 0.47, -13.2, -5.42, 0.17, -20.78, 1.53, -20.7, 15.39, -6.85, 14.79, 0.15, 0.47, -13.66], "hull": 7, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 0, 12, 2, 14, 14, 12], "width": 30, "height": 35}}, "isg0002": {"isg0002": {"x": 6.05, "y": 38.55, "scaleX": 2, "scaleY": 2, "width": 26, "height": 26}, "isg0003": {"x": 6.05, "y": 38.55, "scaleX": 2, "scaleY": 2, "width": 26, "height": 26}, "isg0004": {"x": 6.05, "y": 38.55, "scaleX": 2, "scaleY": 2, "width": 26, "height": 26}, "isg0005": {"x": 6.05, "y": 38.55, "scaleX": 2, "scaleY": 2, "width": 26, "height": 26}, "isg0006": {"x": 6.05, "y": 38.55, "scaleX": 2, "scaleY": 2, "width": 26, "height": 26}, "isg0007": {"x": 6.05, "y": 38.55, "scaleX": 2, "scaleY": 2, "width": 26, "height": 26}, "isg0008": {"x": 6.05, "y": 38.55, "scaleX": 2, "scaleY": 2, "width": 26, "height": 26}}, "jiesuo02_000": {"jiesuo02_004": {"x": 4.01, "y": 2.41, "width": 128, "height": 128}}, "lg0001": {"lg0001": {"type": "mesh", "uvs": [0.18129, 0.01959, 0.11283, 0.16364, 0.08433, 0.27362, 0.11158, 0.39105, 0.11245, 0.46968, 0.19075, 0.52066, 0.20168, 0.53682, 0.13455, 0.53204, 0.04528, 0.63813, 0.09736, 0.75074, 0.10233, 0.84837, 0.18947, 0.96715, 0.65796, 0.97649, 0.74203, 0.85642, 0.657, 0.74993, 0.57767, 0.62826, 0.53901, 0.55781, 0.57789, 0.50817, 0.63505, 0.46706, 0.70948, 0.38988, 0.85052, 0.27418, 0.89044, 0.16622, 0.65839, 0.01935, 0.38463, 0.58902], "triangles": [20, 22, 21, 19, 22, 20, 3, 2, 1, 22, 1, 0, 18, 22, 19, 18, 1, 22, 17, 3, 1, 3, 5, 4, 1, 18, 17, 3, 23, 5, 5, 23, 6, 23, 3, 17, 17, 16, 23, 6, 9, 8, 10, 9, 11, 7, 6, 8, 23, 9, 6, 23, 11, 9, 12, 14, 13, 23, 15, 14, 23, 16, 15, 23, 14, 11, 12, 11, 14], "vertices": [54.65, 227.57, 50.2, 191.84, 48.35, 164.57, 50.12, 135.45, 50.17, 115.94, 55.26, 103.3, 55.97, 99.29, 51.61, 100.48, 45.81, 74.17, 49.19, 46.24, 49.52, 22.03, 55.18, -7.43, 85.63, -9.75, 91.1, 20.03, 85.57, 46.44, 80.41, 76.62, 77.9, 94.09, 80.43, 106.4, 84.14, 116.59, 88.98, 135.73, 98.15, 164.43, 100.74, 191.2, 85.66, 227.62, 67.87, 86.35], "hull": 23, "edges": [22, 24, 42, 44, 0, 44, 12, 14, 12, 46, 46, 32, 0, 2, 2, 4, 40, 42, 4, 6, 6, 8, 38, 40, 36, 38, 8, 10, 10, 12, 32, 34, 34, 36, 14, 16, 30, 32, 16, 18, 28, 30, 18, 20, 20, 22, 24, 26, 26, 28], "width": 65, "height": 248}, "lg1": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0002", "parent": "lg0001", "width": 65, "height": 248}, "lg2": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0003", "parent": "lg0001", "width": 65, "height": 248}, "lg3": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0004", "parent": "lg0001", "width": 65, "height": 248}, "lg4": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0005", "parent": "lg0001", "width": 65, "height": 248}, "lg5": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0006", "parent": "lg0001", "width": 65, "height": 248}, "lg6": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0007", "parent": "lg0001", "width": 65, "height": 248}, "lg7": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0008", "parent": "lg0001", "width": 65, "height": 248}, "lg8": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0009", "parent": "lg0001", "width": 65, "height": 248}, "lg9": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0010", "parent": "lg0001", "width": 65, "height": 248}, "lg10": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0011", "parent": "lg0001", "width": 65, "height": 248}, "lg11": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0012", "parent": "lg0001", "width": 65, "height": 248}, "lg12": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0013", "parent": "lg0001", "width": 65, "height": 248}, "lg13": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0014", "parent": "lg0001", "width": 65, "height": 248}, "lg14": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0015", "parent": "lg0001", "width": 65, "height": 248}, "lg15": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0016", "parent": "lg0001", "width": 65, "height": 248}, "lg16": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0017", "parent": "lg0001", "width": 65, "height": 248}, "lg17": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0018", "parent": "lg0001", "width": 65, "height": 248}, "lg18": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0019", "parent": "lg0001", "width": 65, "height": 248}, "lg19": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0020", "parent": "lg0001", "width": 65, "height": 248}, "lg20": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0021", "parent": "lg0001", "width": 65, "height": 248}, "lg21": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0022", "parent": "lg0001", "width": 65, "height": 248}, "lg22": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0023", "parent": "lg0001", "width": 65, "height": 248}, "lg23": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0024", "parent": "lg0001", "width": 65, "height": 248}, "lg24": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0025", "parent": "lg0001", "width": 65, "height": 248}}, "lg1": {"lg0001": {"type": "mesh", "uvs": [0.18129, 0.01959, 0.11283, 0.16364, 0.08433, 0.27362, 0.11158, 0.39105, 0.11245, 0.46968, 0.19075, 0.52066, 0.20168, 0.53682, 0.13455, 0.53204, 0.04528, 0.63813, 0.09736, 0.75074, 0.10233, 0.84837, 0.18947, 0.96715, 0.65796, 0.97649, 0.74203, 0.85642, 0.657, 0.74993, 0.57767, 0.62826, 0.53901, 0.55781, 0.57789, 0.50817, 0.63505, 0.46706, 0.70948, 0.38988, 0.85052, 0.27418, 0.89044, 0.16622, 0.65839, 0.01935, 0.38463, 0.58902], "triangles": [20, 22, 21, 19, 22, 20, 3, 2, 1, 22, 1, 0, 18, 22, 19, 18, 1, 22, 17, 3, 1, 3, 5, 4, 1, 18, 17, 3, 23, 5, 5, 23, 6, 23, 3, 17, 17, 16, 23, 6, 9, 8, 10, 9, 11, 7, 6, 8, 23, 9, 6, 23, 11, 9, 12, 14, 13, 23, 15, 14, 23, 16, 15, 23, 14, 11, 12, 11, 14], "vertices": [-81.39, 247.83, -45.41, 249.1, -18.08, 248.54, 10.77, 244.2, 32.35, 241.96, 45.17, 235.85, 57.71, 228.2, 65.56, 220.77, 65.57, 220.77, 65.55, 220.71, 65.38, 220.65, 65.54, 220.82, 65.54, 220.74, 65.63, 220.75, 65.56, 220.77, 65.56, 220.69, 55.83, 217.49, 39.74, 212.13, 29.78, 209.56, 7.05, 205.51, -22.34, 198.92, -49.24, 198.7, -84.19, 216.94, 65.59, 220.75], "hull": 23, "edges": [22, 24, 42, 44, 0, 44, 12, 14, 12, 46, 46, 32, 0, 2, 2, 4, 40, 42, 4, 6, 6, 8, 38, 40, 36, 38, 8, 10, 10, 12, 32, 34, 34, 36, 14, 16, 30, 32, 16, 18, 28, 30, 18, 20, 20, 22, 24, 26, 26, 28], "width": 65, "height": 248}, "lg1": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0002", "parent": "lg0001", "width": 65, "height": 248}, "lg2": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0003", "parent": "lg0001", "width": 65, "height": 248}, "lg3": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0004", "parent": "lg0001", "width": 65, "height": 248}, "lg4": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0005", "parent": "lg0001", "width": 65, "height": 248}, "lg5": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0006", "parent": "lg0001", "width": 65, "height": 248}, "lg6": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0007", "parent": "lg0001", "width": 65, "height": 248}, "lg7": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0008", "parent": "lg0001", "width": 65, "height": 248}, "lg8": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0009", "parent": "lg0001", "width": 65, "height": 248}, "lg9": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0010", "parent": "lg0001", "width": 65, "height": 248}, "lg10": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0011", "parent": "lg0001", "width": 65, "height": 248}, "lg11": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0012", "parent": "lg0001", "width": 65, "height": 248}, "lg12": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0013", "parent": "lg0001", "width": 65, "height": 248}, "lg13": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0014", "parent": "lg0001", "width": 65, "height": 248}, "lg14": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0015", "parent": "lg0001", "width": 65, "height": 248}, "lg15": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0016", "parent": "lg0001", "width": 65, "height": 248}, "lg16": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0017", "parent": "lg0001", "width": 65, "height": 248}, "lg17": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0018", "parent": "lg0001", "width": 65, "height": 248}, "lg18": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0019", "parent": "lg0001", "width": 65, "height": 248}, "lg19": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0020", "parent": "lg0001", "width": 65, "height": 248}, "lg20": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0021", "parent": "lg0001", "width": 65, "height": 248}, "lg21": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0022", "parent": "lg0001", "width": 65, "height": 248}, "lg22": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0023", "parent": "lg0001", "width": 65, "height": 248}, "lg23": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0024", "parent": "lg0001", "width": 65, "height": 248}, "lg24": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0025", "parent": "lg0001", "width": 65, "height": 248}}, "lg2": {"lg0001": {"type": "mesh", "uvs": [0.18129, 0.01959, 0.11283, 0.16364, 0.08433, 0.27362, 0.11158, 0.39105, 0.11245, 0.46968, 0.19075, 0.52066, 0.20168, 0.53682, 0.13455, 0.53204, 0.04528, 0.63813, 0.09736, 0.75074, 0.10233, 0.84837, 0.18947, 0.96715, 0.65796, 0.97649, 0.74203, 0.85642, 0.657, 0.74993, 0.57767, 0.62826, 0.53901, 0.55781, 0.57789, 0.50817, 0.63505, 0.46706, 0.70948, 0.38988, 0.85052, 0.27418, 0.89044, 0.16622, 0.65839, 0.01935, 0.38463, 0.58902], "triangles": [20, 22, 21, 19, 22, 20, 3, 2, 1, 22, 1, 0, 18, 22, 19, 18, 1, 22, 17, 3, 1, 3, 5, 4, 1, 18, 17, 3, 23, 5, 5, 23, 6, 23, 3, 17, 17, 16, 23, 6, 9, 8, 10, 9, 11, 7, 6, 8, 23, 9, 6, 23, 11, 9, 12, 14, 13, 23, 15, 14, 23, 16, 15, 23, 14, 11, 12, 11, 14], "vertices": [54.81, -230.33, 50.36, -193.53, 48.51, -165.42, 50.28, -135.42, 50.33, -115.33, 55.42, -102.3, 56.13, -98.17, 51.77, -99.39, 45.97, -72.29, 49.35, -43.51, 49.68, -18.56, 55.34, 11.78, 85.79, 14.17, 91.26, -16.51, 85.73, -43.72, 80.57, -74.81, 78.06, -92.81, 80.59, -105.49, 84.3, -116, 89.14, -135.72, 98.31, -165.28, 100.9, -192.87, 85.82, -230.39, 68.03, -84.83], "hull": 23, "edges": [22, 24, 42, 44, 0, 44, 12, 14, 12, 46, 46, 32, 0, 2, 2, 4, 40, 42, 4, 6, 6, 8, 38, 40, 36, 38, 8, 10, 10, 12, 32, 34, 34, 36, 14, 16, 30, 32, 16, 18, 28, 30, 18, 20, 20, 22, 24, 26, 26, 28], "width": 65, "height": 248}, "lg1": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0002", "parent": "lg0001", "width": 65, "height": 248}, "lg2": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0003", "parent": "lg0001", "width": 65, "height": 248}, "lg3": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0004", "parent": "lg0001", "width": 65, "height": 248}, "lg4": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0005", "parent": "lg0001", "width": 65, "height": 248}, "lg5": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0006", "parent": "lg0001", "width": 65, "height": 248}, "lg6": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0007", "parent": "lg0001", "width": 65, "height": 248}, "lg7": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0008", "parent": "lg0001", "width": 65, "height": 248}, "lg8": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0009", "parent": "lg0001", "width": 65, "height": 248}, "lg9": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0010", "parent": "lg0001", "width": 65, "height": 248}, "lg10": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0011", "parent": "lg0001", "width": 65, "height": 248}, "lg11": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0012", "parent": "lg0001", "width": 65, "height": 248}, "lg12": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0013", "parent": "lg0001", "width": 65, "height": 248}, "lg13": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0014", "parent": "lg0001", "width": 65, "height": 248}, "lg14": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0015", "parent": "lg0001", "width": 65, "height": 248}, "lg15": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0016", "parent": "lg0001", "width": 65, "height": 248}, "lg16": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0017", "parent": "lg0001", "width": 65, "height": 248}, "lg17": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0018", "parent": "lg0001", "width": 65, "height": 248}, "lg18": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0019", "parent": "lg0001", "width": 65, "height": 248}, "lg19": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0020", "parent": "lg0001", "width": 65, "height": 248}, "lg20": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0021", "parent": "lg0001", "width": 65, "height": 248}, "lg21": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0022", "parent": "lg0001", "width": 65, "height": 248}, "lg22": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0023", "parent": "lg0001", "width": 65, "height": 248}, "lg23": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0024", "parent": "lg0001", "width": 65, "height": 248}, "lg24": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0025", "parent": "lg0001", "width": 65, "height": 248}}, "lg3": {"lg0001": {"type": "mesh", "uvs": [0.18129, 0.01959, 0.11283, 0.16364, 0.08433, 0.27362, 0.11158, 0.39105, 0.11245, 0.46968, 0.19075, 0.52066, 0.20168, 0.53682, 0.13455, 0.53204, 0.04528, 0.63813, 0.09736, 0.75074, 0.10233, 0.84837, 0.18947, 0.96715, 0.65796, 0.97649, 0.74203, 0.85642, 0.657, 0.74993, 0.57767, 0.62826, 0.53901, 0.55781, 0.57789, 0.50817, 0.63505, 0.46706, 0.70948, 0.38988, 0.85052, 0.27418, 0.89044, 0.16622, 0.65839, 0.01935, 0.38463, 0.58902], "triangles": [20, 22, 21, 19, 22, 20, 3, 2, 1, 22, 1, 0, 18, 22, 19, 18, 1, 22, 17, 3, 1, 3, 5, 4, 1, 18, 17, 3, 23, 5, 5, 23, 6, 23, 3, 17, 17, 16, 23, 6, 9, 8, 10, 9, 11, 7, 6, 8, 23, 9, 6, 23, 11, 9, 12, 14, 13, 23, 15, 14, 23, 16, 15, 23, 14, 11, 12, 11, 14], "vertices": [-57.23, 228.7, -52.78, 192.97, -50.93, 165.7, -52.7, 136.58, -52.76, 117.08, -57.85, 104.43, -58.56, 100.42, -54.19, 101.61, -48.39, 75.3, -51.78, 47.37, -52.1, 23.16, -57.76, -6.3, -88.22, -8.61, -93.68, 21.17, -88.15, 47.57, -83, 77.75, -80.48, 95.22, -83.01, 107.53, -86.73, 117.72, -91.56, 136.87, -100.73, 165.56, -103.33, 192.33, -88.24, 228.76, -70.45, 87.48], "hull": 23, "edges": [22, 24, 42, 44, 0, 44, 12, 14, 12, 46, 46, 32, 0, 2, 2, 4, 40, 42, 4, 6, 6, 8, 38, 40, 36, 38, 8, 10, 10, 12, 32, 34, 34, 36, 14, 16, 30, 32, 16, 18, 28, 30, 18, 20, 20, 22, 24, 26, 26, 28], "width": 65, "height": 248}, "lg1": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0002", "parent": "lg0001", "width": 65, "height": 248}, "lg2": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0003", "parent": "lg0001", "width": 65, "height": 248}, "lg3": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0004", "parent": "lg0001", "width": 65, "height": 248}, "lg4": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0005", "parent": "lg0001", "width": 65, "height": 248}, "lg5": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0006", "parent": "lg0001", "width": 65, "height": 248}, "lg6": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0007", "parent": "lg0001", "width": 65, "height": 248}, "lg7": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0008", "parent": "lg0001", "width": 65, "height": 248}, "lg8": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0009", "parent": "lg0001", "width": 65, "height": 248}, "lg9": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0010", "parent": "lg0001", "width": 65, "height": 248}, "lg10": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0011", "parent": "lg0001", "width": 65, "height": 248}, "lg11": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0012", "parent": "lg0001", "width": 65, "height": 248}, "lg12": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0013", "parent": "lg0001", "width": 65, "height": 248}, "lg13": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0014", "parent": "lg0001", "width": 65, "height": 248}, "lg14": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0015", "parent": "lg0001", "width": 65, "height": 248}, "lg15": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0016", "parent": "lg0001", "width": 65, "height": 248}, "lg16": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0017", "parent": "lg0001", "width": 65, "height": 248}, "lg17": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0018", "parent": "lg0001", "width": 65, "height": 248}, "lg18": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0019", "parent": "lg0001", "width": 65, "height": 248}, "lg19": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0020", "parent": "lg0001", "width": 65, "height": 248}, "lg20": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0021", "parent": "lg0001", "width": 65, "height": 248}, "lg21": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0022", "parent": "lg0001", "width": 65, "height": 248}, "lg22": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0023", "parent": "lg0001", "width": 65, "height": 248}, "lg23": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0024", "parent": "lg0001", "width": 65, "height": 248}, "lg24": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0025", "parent": "lg0001", "width": 65, "height": 248}}, "lg4": {"lg0001": {"type": "mesh", "uvs": [0.18129, 0.01959, 0.11283, 0.16364, 0.08433, 0.27362, 0.11158, 0.39105, 0.11245, 0.46968, 0.19075, 0.52066, 0.20168, 0.53682, 0.13455, 0.53204, 0.04528, 0.63813, 0.09736, 0.75074, 0.10233, 0.84837, 0.18947, 0.96715, 0.65796, 0.97649, 0.74203, 0.85642, 0.657, 0.74993, 0.57767, 0.62826, 0.53901, 0.55781, 0.57789, 0.50817, 0.63505, 0.46706, 0.70948, 0.38988, 0.85052, 0.27418, 0.89044, 0.16622, 0.65839, 0.01935, 0.38463, 0.58902], "triangles": [20, 22, 21, 19, 22, 20, 3, 2, 1, 22, 1, 0, 18, 22, 19, 18, 1, 22, 17, 3, 1, 3, 5, 4, 1, 18, 17, 3, 23, 5, 5, 23, 6, 23, 3, 17, 17, 16, 23, 6, 9, 8, 10, 9, 11, 7, 6, 8, 23, 9, 6, 23, 11, 9, 12, 14, 13, 23, 15, 14, 23, 16, 15, 23, 14, 11, 12, 11, 14], "vertices": [-58.48, -226.85, -54.03, -190.41, -52.18, -162.59, -53.95, -132.88, -54.01, -112.99, -59.1, -100.1, -59.81, -96.01, -55.45, -97.22, -49.64, -70.38, -53.03, -41.9, -53.35, -17.2, -59.02, 12.85, -89.47, 15.21, -94.93, -15.16, -89.4, -42.1, -84.25, -72.88, -81.74, -90.7, -84.26, -103.26, -87.98, -113.65, -92.82, -133.18, -101.98, -162.45, -104.58, -189.76, -89.5, -226.91, -71.7, -82.8], "hull": 23, "edges": [22, 24, 42, 44, 0, 44, 12, 14, 12, 46, 46, 32, 0, 2, 2, 4, 40, 42, 4, 6, 6, 8, 38, 40, 36, 38, 8, 10, 10, 12, 32, 34, 34, 36, 14, 16, 30, 32, 16, 18, 28, 30, 18, 20, 20, 22, 24, 26, 26, 28], "width": 65, "height": 248}, "lg1": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0002", "parent": "lg0001", "width": 65, "height": 248}, "lg2": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0003", "parent": "lg0001", "width": 65, "height": 248}, "lg3": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0004", "parent": "lg0001", "width": 65, "height": 248}, "lg4": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0005", "parent": "lg0001", "width": 65, "height": 248}, "lg5": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0006", "parent": "lg0001", "width": 65, "height": 248}, "lg6": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0007", "parent": "lg0001", "width": 65, "height": 248}, "lg7": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0008", "parent": "lg0001", "width": 65, "height": 248}, "lg8": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0009", "parent": "lg0001", "width": 65, "height": 248}, "lg9": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0010", "parent": "lg0001", "width": 65, "height": 248}, "lg10": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0011", "parent": "lg0001", "width": 65, "height": 248}, "lg11": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0012", "parent": "lg0001", "width": 65, "height": 248}, "lg12": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0013", "parent": "lg0001", "width": 65, "height": 248}, "lg13": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0014", "parent": "lg0001", "width": 65, "height": 248}, "lg14": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0015", "parent": "lg0001", "width": 65, "height": 248}, "lg15": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0016", "parent": "lg0001", "width": 65, "height": 248}, "lg16": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0017", "parent": "lg0001", "width": 65, "height": 248}, "lg17": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0018", "parent": "lg0001", "width": 65, "height": 248}, "lg18": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0019", "parent": "lg0001", "width": 65, "height": 248}, "lg19": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0020", "parent": "lg0001", "width": 65, "height": 248}, "lg20": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0021", "parent": "lg0001", "width": 65, "height": 248}, "lg21": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0022", "parent": "lg0001", "width": 65, "height": 248}, "lg22": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0023", "parent": "lg0001", "width": 65, "height": 248}, "lg23": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0024", "parent": "lg0001", "width": 65, "height": 248}, "lg24": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0025", "parent": "lg0001", "width": 65, "height": 248}}, "lg5": {"lg0001": {"type": "mesh", "uvs": [0.18129, 0.01959, 0.11283, 0.16364, 0.08433, 0.27362, 0.11158, 0.39105, 0.11245, 0.46968, 0.19075, 0.52066, 0.20168, 0.53682, 0.13455, 0.53204, 0.04528, 0.63813, 0.09736, 0.75074, 0.10233, 0.84837, 0.18947, 0.96715, 0.65796, 0.97649, 0.74203, 0.85642, 0.657, 0.74993, 0.57767, 0.62826, 0.53901, 0.55781, 0.57789, 0.50817, 0.63505, 0.46706, 0.70948, 0.38988, 0.85052, 0.27418, 0.89044, 0.16622, 0.65839, 0.01935, 0.38463, 0.58902], "triangles": [20, 22, 21, 19, 22, 20, 3, 2, 1, 22, 1, 0, 18, 22, 19, 18, 1, 22, 17, 3, 1, 3, 5, 4, 1, 18, 17, 3, 23, 5, 5, 23, 6, 23, 3, 17, 17, 16, 23, 6, 9, 8, 10, 9, 11, 7, 6, 8, 23, 9, 6, 23, 11, 9, 12, 14, 13, 23, 15, 14, 23, 16, 15, 23, 14, 11, 12, 11, 14], "vertices": [76.34, -248.25, 40.37, -249.53, 13.03, -248.96, -15.82, -244.62, -37.39, -242.38, -50.22, -236.28, -62.75, -228.63, -70.61, -221.2, -70.61, -221.19, -70.59, -221.14, -70.42, -221.07, -70.58, -221.24, -70.58, -221.17, -70.67, -221.17, -70.61, -221.19, -70.6, -221.12, -60.87, -217.91, -44.78, -212.56, -34.82, -209.99, -12.09, -205.94, 17.3, -199.34, 44.2, -199.12, 79.14, -217.37, -70.63, -221.17], "hull": 23, "edges": [22, 24, 42, 44, 0, 44, 12, 14, 12, 46, 46, 32, 0, 2, 2, 4, 40, 42, 4, 6, 6, 8, 38, 40, 36, 38, 8, 10, 10, 12, 32, 34, 34, 36, 14, 16, 30, 32, 16, 18, 28, 30, 18, 20, 20, 22, 24, 26, 26, 28], "width": 65, "height": 248}, "lg1": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0002", "parent": "lg0001", "width": 65, "height": 248}, "lg2": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0003", "parent": "lg0001", "width": 65, "height": 248}, "lg3": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0004", "parent": "lg0001", "width": 65, "height": 248}, "lg4": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0005", "parent": "lg0001", "width": 65, "height": 248}, "lg5": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0006", "parent": "lg0001", "width": 65, "height": 248}, "lg6": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0007", "parent": "lg0001", "width": 65, "height": 248}, "lg7": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0008", "parent": "lg0001", "width": 65, "height": 248}, "lg8": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0009", "parent": "lg0001", "width": 65, "height": 248}, "lg9": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0010", "parent": "lg0001", "width": 65, "height": 248}, "lg10": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0011", "parent": "lg0001", "width": 65, "height": 248}, "lg11": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0012", "parent": "lg0001", "width": 65, "height": 248}, "lg12": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0013", "parent": "lg0001", "width": 65, "height": 248}, "lg13": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0014", "parent": "lg0001", "width": 65, "height": 248}, "lg14": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0015", "parent": "lg0001", "width": 65, "height": 248}, "lg15": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0016", "parent": "lg0001", "width": 65, "height": 248}, "lg16": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0017", "parent": "lg0001", "width": 65, "height": 248}, "lg17": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0018", "parent": "lg0001", "width": 65, "height": 248}, "lg18": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0019", "parent": "lg0001", "width": 65, "height": 248}, "lg19": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0020", "parent": "lg0001", "width": 65, "height": 248}, "lg20": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0021", "parent": "lg0001", "width": 65, "height": 248}, "lg21": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0022", "parent": "lg0001", "width": 65, "height": 248}, "lg22": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0023", "parent": "lg0001", "width": 65, "height": 248}, "lg23": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0024", "parent": "lg0001", "width": 65, "height": 248}, "lg24": {"type": "<PERSON><PERSON><PERSON>", "path": "lg0025", "parent": "lg0001", "width": 65, "height": 248}}, "lsg0002": {"lsg0002": {"x": 11.36, "y": -1.69, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0003": {"x": 11.36, "y": -1.69, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0004": {"x": 11.36, "y": -1.69, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0005": {"x": 11.36, "y": -1.69, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0006": {"x": 11.36, "y": -1.69, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0007": {"x": 11.36, "y": -1.69, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0008": {"x": 11.36, "y": -1.69, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}}, "lsg2": {"lsg0002": {"x": 11.46, "y": -8.74, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0003": {"x": 11.46, "y": -8.74, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0004": {"x": 11.46, "y": -8.74, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0005": {"x": 11.46, "y": -8.74, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0006": {"x": 11.46, "y": -8.74, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0007": {"x": 11.46, "y": -8.74, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}, "lsg0008": {"x": 11.46, "y": -8.74, "scaleX": 2, "scaleY": 2, "width": 28, "height": 59}}, "niushen": {"niushen": {"type": "mesh", "uvs": [1, 0.45453, 1, 1, 0, 1, 0, 0.4669, 0, 0, 1, 0], "triangles": [1, 2, 3, 4, 5, 0, 1, 3, 0, 3, 4, 0], "vertices": [1, 2, 120.78, -4, 1, 1, 4, 208.73, 114.77, 1, 1, 4, 201.7, -127.13, 1, 1, 2, -121.22, -9.03, 1, 1, 3, 169.4, 121.22, 1, 1, 3, 169.4, -120.78, 1], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "width": 242, "height": 407}}, "niutou": {"niutou": {"type": "mesh", "uvs": [0, 0.2859, 0.12985, 0.13254, 0.224, 0.17195, 0.29075, 0.45552, 0.32356, 0.2653, 0.42199, 0, 0.59962, 0, 0.67882, 0.28291, 0.7071, 0.43791, 0.75801, 0.15433, 1, 0.22128, 1, 0.62151, 0.79626, 1, 0.21497, 1, 0, 0.65109, 0.33701, 0.34956, 0.47547, 0.27412, 0.49624, 0.21868, 0.52492, 0.26796, 0.64262, 0.3588], "triangles": [17, 5, 6, 7, 17, 6, 17, 4, 5, 7, 18, 17, 16, 4, 17, 16, 17, 18, 15, 4, 16, 19, 18, 7, 9, 10, 8, 19, 7, 8, 4, 15, 3, 8, 10, 11, 0, 3, 14, 2, 0, 1, 3, 0, 2, 13, 14, 3, 12, 8, 11, 16, 18, 19, 19, 8, 16, 16, 3, 15, 16, 8, 3, 12, 3, 8, 13, 3, 12], "vertices": [1, 15, 90.35, 135.15, 1, 1, 15, 117.34, 99.57, 1, 1, 15, 110.41, 73.77, 1, 1, 15, 60.5, 55.48, 1, 2, 15, 93.98, 46.49, 0.92686, 16, -44.35, 47.32, 0.07314, 2, 15, 140.67, 19.52, 0.16134, 16, 2.35, 20.35, 0.83866, 2, 15, 140.67, -29.15, 0.18728, 16, 2.35, -28.32, 0.81272, 2, 15, 90.88, -50.85, 0.90839, 16, -47.45, -50.02, 0.09161, 1, 15, 63.6, -58.6, 1, 1, 15, 113.51, -72.55, 1, 1, 15, 101.73, -138.85, 1, 1, 15, 31.29, -138.85, 1, 1, 15, -35.33, -83.03, 1, 1, 15, -35.33, 76.25, 1, 1, 15, 26.08, 135.15, 1, 1, 15, 79.15, 42.81, 1, 1, 15, 92.43, 4.87, 1, 1, 15, 102.18, -0.82, 1, 1, 15, 93.51, -8.68, 1, 1, 15, 77.52, -40.93, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 6, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 16], "width": 274, "height": 176}}, "niuwei": {"niuwei": {"type": "mesh", "uvs": [1, 0.69079, 0.89326, 0.65425, 0.77256, 0.63203, 0.65563, 0.56499, 0.54796, 0.48553, 0.43507, 0.56148, 0.3439, 0.70698, 0.26292, 0.97087, 0.15809, 1, 0.07549, 1, 0, 1, 0.00315, 0.70931, 0.06042, 0.47166, 0.13815, 0.26892, 0.23333, 0.05825, 0.32812, 0, 0.4319, 0, 0.55223, 0, 0.66247, 0, 0.77864, 0.145, 0.89425, 0.14651, 0.99999, 0.14651], "triangles": [12, 9, 11, 9, 10, 11, 8, 12, 13, 9, 12, 8, 13, 14, 7, 13, 7, 8, 16, 6, 15, 14, 6, 7, 6, 14, 15, 4, 16, 17, 5, 16, 4, 6, 16, 5, 18, 4, 17, 18, 3, 4, 18, 19, 3, 19, 2, 3, 19, 20, 2, 1, 2, 20, 1, 20, 21, 0, 1, 21], "vertices": [1, 5, -1.81, 7.64, 1, 2, 5, 13.82, 6, 0.97664, 6, -3, 6.59, 0.02336, 3, 5, 31.52, 4.66, 0.00219, 6, 14.74, 7.31, 0.58471, 7, -0.97, 7.26, 0.4131, 2, 7, 16.3, 7.71, 0.65266, 8, -1.29, 7.79, 0.34734, 2, 8, 14.65, 6.87, 0.50972, 9, 0.32, 6.9, 0.49028, 3, 9, 17.01, 7.72, 0.49711, 10, 0.23, 7.69, 0.50265, 11, -11.98, 8.94, 0.00024, 3, 10, 14.1, 8.94, 0.32099, 11, 1.95, 8.79, 0.67454, 12, -12.29, 8.79, 0.00448, 2, 11, 15.3, 12.01, 0.45386, 12, 1.07, 12.01, 0.54614, 2, 12, 16.06, 8.39, 0.83451, 13, 0.61, 8.69, 0.16549, 3, 12, 27.71, 4.96, 0.02868, 13, 12.45, 6.01, 0.42021, 14, 0.78, 6.04, 0.5511, 1, 14, 11.69, 4.01, 1, 1, 14, 9.85, -3.34, 1, 2, 13, 11.58, -7.88, 0.60637, 14, 0.44, -7.87, 0.39363, 3, 11, 27.75, -10.68, 0.00642, 12, 13.51, -10.68, 0.37373, 13, -0.73, -10.49, 0.61984, 3, 11, 12.78, -11.98, 0.63525, 12, -1.46, -11.98, 0.3475, 13, -15.59, -12.74, 0.01724, 2, 10, 12.99, -9.56, 0.49316, 11, -1.02, -9.5, 0.50684, 2, 9, 16.46, -6.88, 0.81997, 10, -2.01, -6.74, 0.18003, 2, 8, 14.94, -5.76, 0.64288, 9, -1.18, -5.65, 0.35712, 2, 7, 17.16, -6.99, 0.59993, 8, -1.22, -6.94, 0.40007, 2, 6, 14.76, -5.39, 0.55509, 7, -0.26, -5.41, 0.44491, 2, 5, 13.1, -7.18, 0.32259, 6, -2.19, -6.58, 0.67741, 1, 5, -2.43, -6.5, 1], "hull": 22, "edges": [20, 22, 0, 42, 40, 42, 0, 2, 40, 2, 38, 40, 2, 4, 38, 4, 36, 38, 4, 6, 36, 6, 34, 36, 6, 8, 34, 8, 32, 34, 8, 10, 32, 10, 30, 32, 10, 12, 30, 12, 28, 30, 12, 14, 28, 14, 26, 28, 14, 16, 26, 16, 22, 24, 24, 26, 16, 18, 18, 20, 24, 18], "width": 147, "height": 26}}, "wsg0001": {"wsg0001": {"type": "mesh", "uvs": [0.04977, 0.04373, 0.16817, 0.02553, 0.31617, 0.04919, 0.41977, 0.16383, 0.41977, 0.70609, 0.44567, 0.73884, 0.47712, 0.71882, 0.47712, 0.14563, 0.53632, 0.12016, 0.65287, 0.20932, 0.66582, 0.70609, 0.68694, 0.72261, 0.69728, 0.71113, 0.68835, 0.13762, 0.86608, 0.04739, 0.89762, 0.08686, 0.90908, 0.679, 0.88902, 0.74949, 0.69695, 0.88483, 0.55648, 0.86791, 0.40742, 0.92431, 0.28415, 0.86791, 0.20388, 0.74667, 0.19528, 0.19683, 0.04335, 0.14608], "triangles": [23, 1, 2, 23, 2, 3, 24, 0, 1, 23, 24, 1, 23, 3, 4, 7, 8, 9, 7, 9, 6, 13, 14, 15, 12, 13, 15, 15, 16, 12, 10, 6, 9, 22, 23, 4, 17, 12, 16, 21, 22, 4, 21, 4, 5, 19, 6, 10, 19, 10, 11, 5, 6, 19, 17, 18, 11, 17, 11, 12, 19, 11, 18, 20, 21, 5, 20, 5, 19], "vertices": [-62.94, 55.98, -48.73, 58.2, -30.97, 55.32, -18.54, 41.33, -18.54, -24.82, -15.43, -28.82, -11.66, -26.38, -11.66, 43.55, -4.56, 46.66, 9.43, 35.78, 10.98, -24.82, 13.52, -26.84, 14.76, -25.44, 13.69, 44.53, 35.02, 55.54, 38.8, 50.72, 40.18, -21.52, 37.77, -30.12, 14.72, -46.63, -2.14, -44.57, -20.02, -51.45, -34.82, -44.57, -44.45, -29.78, -45.48, 37.3, -63.71, 43.5], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 60, "height": 61}, "wsg1": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0002", "parent": "wsg0001", "width": 60, "height": 61}, "wsg2": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0003", "parent": "wsg0001", "width": 60, "height": 61}, "wsg3": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0004", "parent": "wsg0001", "width": 60, "height": 61}, "wsg4": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0005", "parent": "wsg0001", "width": 60, "height": 61}, "wsg5": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0006", "parent": "wsg0001", "width": 60, "height": 61}, "wsg6": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0007", "parent": "wsg0001", "width": 60, "height": 61}, "wsg7": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0008", "parent": "wsg0001", "width": 60, "height": 61}, "wsg8": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0009", "parent": "wsg0001", "width": 60, "height": 61}, "wsg9": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0010", "parent": "wsg0001", "width": 60, "height": 61}, "wsg10": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0011", "parent": "wsg0001", "width": 60, "height": 61}, "wsg11": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0012", "parent": "wsg0001", "width": 60, "height": 61}, "wsg12": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0013", "parent": "wsg0001", "width": 60, "height": 61}, "wsg13": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0014", "parent": "wsg0001", "width": 60, "height": 61}, "wsg14": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0015", "parent": "wsg0001", "width": 60, "height": 61}, "wsg15": {"type": "<PERSON><PERSON><PERSON>", "path": "wsg0016", "parent": "wsg0001", "width": 60, "height": 61}}}}, "animations": {"wild0": {"slots": {"D": {"attachment": [{"time": 0, "name": null}, {"time": 0.5333, "name": "D"}]}, "L": {"attachment": [{"time": 0, "name": null}, {"time": 0.6, "name": "L"}]}, "W": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "W"}]}, "di": {"attachment": [{"time": 0, "name": null}, {"time": 0.3667, "name": "di"}]}, "di2": {"color": [{"time": 0, "color": "ffffffa2", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffa2"}, {"time": 1, "color": "ffffff2f"}], "attachment": [{"time": 0.3667, "name": "di"}]}, "dsg0002": {"attachment": [{"time": 0, "name": null}]}, "guangzhao": {"color": [{"time": 0, "color": "ffffff29"}, {"time": 0.1667, "color": "ffffff74"}, {"time": 0.3333, "color": "ffffff7c"}, {"time": 0.5, "color": "ffffff74"}, {"time": 0.6667, "color": "ffffff7c"}, {"time": 0.8333, "color": "ffffff74"}, {"time": 1, "color": "ffffff7c"}]}, "i": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": "i"}]}, "i2": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": "i2"}]}, "isg0002": {"attachment": [{"time": 0, "name": null}]}, "jiesuo02_000": {"attachment": [{"time": 0, "name": null}, {"time": 0.4, "name": "jiesuo02_004"}, {"time": 0.8, "name": null}]}, "lg0001": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2, "name": "lg0001"}, {"time": 0.2333, "name": "lg1"}, {"time": 0.2667, "name": "lg2"}, {"time": 0.3, "name": "lg3"}, {"time": 0.3333, "name": "lg4"}, {"time": 0.3667, "name": "lg5"}, {"time": 0.4, "name": "lg6"}, {"time": 0.4333, "name": "lg7"}, {"time": 0.4667, "name": "lg8"}, {"time": 0.5, "name": "lg9"}, {"time": 0.5333, "name": "lg10"}, {"time": 0.5667, "name": "lg11"}, {"time": 0.6, "name": "lg12"}, {"time": 0.6333, "name": "lg13"}, {"time": 0.6667, "name": "lg14"}, {"time": 0.7, "name": "lg15"}, {"time": 0.7333, "name": "lg16"}, {"time": 0.7667, "name": "lg17"}, {"time": 0.8, "name": "lg18"}, {"time": 0.8333, "name": "lg19"}, {"time": 0.8667, "name": "lg20"}, {"time": 0.9, "name": "lg21"}, {"time": 0.9333, "name": "lg22"}, {"time": 0.9667, "name": "lg23"}, {"time": 1, "name": "lg24"}]}, "lg1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2, "name": "lg17"}, {"time": 0.2333, "name": "lg18"}, {"time": 0.2667, "name": "lg19"}, {"time": 0.3, "name": "lg20"}, {"time": 0.3333, "name": "lg21"}, {"time": 0.3667, "name": "lg22"}, {"time": 0.4, "name": "lg23"}, {"time": 0.4333, "name": "lg24"}, {"time": 0.4667, "name": "lg0001"}, {"time": 0.5, "name": "lg1"}, {"time": 0.5333, "name": "lg2"}, {"time": 0.5667, "name": "lg3"}, {"time": 0.6, "name": "lg4"}, {"time": 0.6333, "name": "lg5"}, {"time": 0.6667, "name": "lg6"}, {"time": 0.7, "name": "lg7"}, {"time": 0.7333, "name": "lg8"}, {"time": 0.7667, "name": "lg9"}, {"time": 0.8, "name": "lg10"}, {"time": 0.8333, "name": "lg11"}, {"time": 0.8667, "name": "lg12"}, {"time": 0.9, "name": "lg13"}, {"time": 0.9333, "name": "lg14"}, {"time": 0.9667, "name": "lg15"}, {"time": 1, "name": "lg16"}]}, "lg2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2, "name": "lg12"}, {"time": 0.2333, "name": "lg13"}, {"time": 0.2667, "name": "lg14"}, {"time": 0.3, "name": "lg15"}, {"time": 0.3333, "name": "lg16"}, {"time": 0.3667, "name": "lg17"}, {"time": 0.4, "name": "lg18"}, {"time": 0.4333, "name": "lg19"}, {"time": 0.4667, "name": "lg20"}, {"time": 0.5, "name": "lg21"}, {"time": 0.5333, "name": "lg22"}, {"time": 0.5667, "name": "lg23"}, {"time": 0.6, "name": "lg24"}, {"time": 0.6333, "name": "lg0001"}, {"time": 0.6667, "name": "lg1"}, {"time": 0.7, "name": "lg2"}, {"time": 0.7333, "name": "lg3"}, {"time": 0.7667, "name": "lg4"}, {"time": 0.8, "name": "lg5"}, {"time": 0.8333, "name": "lg6"}, {"time": 0.8667, "name": "lg7"}, {"time": 0.9, "name": "lg8"}, {"time": 0.9333, "name": "lg9"}, {"time": 0.9667, "name": "lg10"}, {"time": 1, "name": "lg11"}]}, "lg3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2, "name": "lg8"}, {"time": 0.2333, "name": "lg9"}, {"time": 0.2667, "name": "lg10"}, {"time": 0.3, "name": "lg11"}, {"time": 0.3333, "name": "lg12"}, {"time": 0.3667, "name": "lg13"}, {"time": 0.4, "name": "lg14"}, {"time": 0.4333, "name": "lg15"}, {"time": 0.4667, "name": "lg16"}, {"time": 0.5, "name": "lg17"}, {"time": 0.5333, "name": "lg18"}, {"time": 0.5667, "name": "lg19"}, {"time": 0.6, "name": "lg20"}, {"time": 0.6333, "name": "lg21"}, {"time": 0.6667, "name": "lg22"}, {"time": 0.7, "name": "lg23"}, {"time": 0.7333, "name": "lg24"}, {"time": 0.7667, "name": "lg0001"}, {"time": 0.8, "name": "lg1"}, {"time": 0.8333, "name": "lg2"}, {"time": 0.8667, "name": "lg3"}, {"time": 0.9, "name": "lg4"}, {"time": 0.9333, "name": "lg5"}, {"time": 0.9667, "name": "lg6"}, {"time": 1, "name": "lg7"}]}, "lg4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2, "name": "lg21"}, {"time": 0.2333, "name": "lg22"}, {"time": 0.2667, "name": "lg23"}, {"time": 0.3, "name": "lg24"}, {"time": 0.3333, "name": "lg0001"}, {"time": 0.3667, "name": "lg1"}, {"time": 0.4, "name": "lg2"}, {"time": 0.4333, "name": "lg3"}, {"time": 0.4667, "name": "lg4"}, {"time": 0.5, "name": "lg5"}, {"time": 0.5333, "name": "lg6"}, {"time": 0.5667, "name": "lg7"}, {"time": 0.6, "name": "lg8"}, {"time": 0.6333, "name": "lg9"}, {"time": 0.6667, "name": "lg10"}, {"time": 0.7, "name": "lg11"}, {"time": 0.7333, "name": "lg12"}, {"time": 0.7667, "name": "lg13"}, {"time": 0.8, "name": "lg14"}, {"time": 0.8333, "name": "lg15"}, {"time": 0.8667, "name": "lg16"}, {"time": 0.9, "name": "lg17"}, {"time": 0.9333, "name": "lg18"}, {"time": 0.9667, "name": "lg19"}, {"time": 1, "name": "lg20"}]}, "lg5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2, "name": "lg4"}, {"time": 0.2333, "name": "lg5"}, {"time": 0.2667, "name": "lg6"}, {"time": 0.3, "name": "lg7"}, {"time": 0.3333, "name": "lg8"}, {"time": 0.3667, "name": "lg9"}, {"time": 0.4, "name": "lg10"}, {"time": 0.4333, "name": "lg11"}, {"time": 0.4667, "name": "lg12"}, {"time": 0.5, "name": "lg13"}, {"time": 0.5333, "name": "lg14"}, {"time": 0.5667, "name": "lg15"}, {"time": 0.6, "name": "lg16"}, {"time": 0.6333, "name": "lg17"}, {"time": 0.6667, "name": "lg18"}, {"time": 0.7, "name": "lg19"}, {"time": 0.7333, "name": "lg20"}, {"time": 0.7667, "name": "lg21"}, {"time": 0.8, "name": "lg22"}, {"time": 0.8333, "name": "lg23"}, {"time": 0.8667, "name": "lg24"}, {"time": 0.9, "name": "lg0001"}, {"time": 0.9333, "name": "lg1"}, {"time": 0.9667, "name": "lg2"}, {"time": 1, "name": "lg3"}]}, "lsg0002": {"attachment": [{"time": 0, "name": null}]}, "lsg2": {"attachment": [{"time": 0, "name": null}]}, "wsg0001": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"niushen2": {"scale": [{"time": 0, "x": 1, "y": 0.973}, {"time": 0.1, "x": 0.871, "y": 1}, {"time": 0.2, "x": 1, "y": 0.973}, {"time": 0.3, "x": 0.871, "y": 1}, {"time": 0.4, "x": 1, "y": 0.973}, {"time": 0.5, "x": 0.871, "y": 1}, {"time": 0.6, "x": 1, "y": 0.973}, {"time": 0.7, "x": 0.871, "y": 1}, {"time": 0.8, "x": 1, "y": 0.973}, {"time": 0.9, "x": 0.871, "y": 1}, {"time": 1, "x": 1, "y": 0.973}]}, "niushen": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 0, "y": -8.24}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.3, "x": 0, "y": -8.24}, {"time": 0.4, "x": 0, "y": 0}, {"time": 0.5, "x": 0, "y": -8.24}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.7, "x": 0, "y": -8.24}, {"time": 0.8, "x": 0, "y": 0}, {"time": 0.9, "x": 0, "y": -8.24}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0.973}, {"time": 0.1, "x": 0.921, "y": 1}, {"time": 0.2, "x": 1, "y": 0.973}, {"time": 0.3, "x": 0.921, "y": 1}, {"time": 0.4, "x": 1, "y": 0.973}, {"time": 0.5, "x": 0.921, "y": 1}, {"time": 0.6, "x": 1, "y": 0.973}, {"time": 0.7, "x": 0.921, "y": 1}, {"time": 0.8, "x": 1, "y": 0.973}, {"time": 0.9, "x": 0.921, "y": 1}, {"time": 1, "x": 1, "y": 0.973}]}, "niutou": {"scale": [{"time": 0, "x": 0.997, "y": 0.997}, {"time": 0.0333, "x": 1.03, "y": 1.03}, {"time": 0.1333, "x": 0.947, "y": 0.947}, {"time": 0.2, "x": 0.997, "y": 0.997}, {"time": 0.2333, "x": 1.03, "y": 1.03}, {"time": 0.3333, "x": 0.947, "y": 0.947}, {"time": 0.4, "x": 0.997, "y": 0.997}, {"time": 0.4333, "x": 1.03, "y": 1.03}, {"time": 0.5333, "x": 0.947, "y": 0.947}, {"time": 0.6, "x": 0.997, "y": 0.997}, {"time": 0.6333, "x": 1.03, "y": 1.03}, {"time": 0.7333, "x": 0.947, "y": 0.947}, {"time": 0.8, "x": 0.997, "y": 0.997}, {"time": 0.8667, "x": 1.03, "y": 1.03}, {"time": 0.9667, "x": 0.947, "y": 0.947}, {"time": 1, "x": 0.997, "y": 0.997}]}, "niu": {"translate": [{"time": 0, "x": -456.58, "y": -605.58}, {"time": 1, "x": -456.58, "y": 777.62}], "scale": [{"time": 0, "x": 0.959, "y": 1}, {"time": 0.1667, "x": 1.028, "y": 0.944}, {"time": 0.3333, "x": 0.959, "y": 1}, {"time": 0.5, "x": 1.028, "y": 0.944}, {"time": 0.6667, "x": 0.959, "y": 1}, {"time": 0.8333, "x": 1.028, "y": 0.944}, {"time": 1, "x": 0.959, "y": 1}]}, "niuwei2": {"rotate": [{"time": 0, "angle": -11.56}, {"time": 0.0333, "angle": -16.28}, {"time": 0.2, "angle": 7.33}, {"time": 0.3333, "angle": -11.56}, {"time": 0.3667, "angle": -16.28}, {"time": 0.5333, "angle": 7.33}, {"time": 0.6667, "angle": -11.56}, {"time": 0.7, "angle": -16.28}, {"time": 0.8667, "angle": 7.33}, {"time": 1, "angle": -11.56}]}, "niuwei3": {"rotate": [{"time": 0, "angle": -6.83}, {"time": 0.0667, "angle": -16.28}, {"time": 0.2333, "angle": 7.33}, {"time": 0.3333, "angle": -6.83}, {"time": 0.4, "angle": -16.28}, {"time": 0.5667, "angle": 7.33}, {"time": 0.6667, "angle": -6.83}, {"time": 0.7333, "angle": -16.28}, {"time": 0.9, "angle": 7.33}, {"time": 1, "angle": -6.83}]}, "niuwei4": {"rotate": [{"time": 0, "angle": -2.11}, {"time": 0.1, "angle": -16.28}, {"time": 0.2667, "angle": 7.33}, {"time": 0.3333, "angle": -2.11}, {"time": 0.4333, "angle": -16.28}, {"time": 0.6, "angle": 7.33}, {"time": 0.6667, "angle": -2.11}, {"time": 0.7667, "angle": -16.28}, {"time": 0.9333, "angle": 7.33}, {"time": 1, "angle": -2.11}]}, "niuwei5": {"rotate": [{"time": 0, "angle": 2.61}, {"time": 0.1333, "angle": -16.28}, {"time": 0.3, "angle": 7.33}, {"time": 0.3333, "angle": 2.61}, {"time": 0.4667, "angle": -16.28}, {"time": 0.6333, "angle": 7.33}, {"time": 0.6667, "angle": 2.61}, {"time": 0.8, "angle": -16.28}, {"time": 0.9667, "angle": 7.33}, {"time": 1, "angle": 2.61}]}, "niuwei7": {"rotate": [{"time": 0, "angle": 2.61}, {"time": 0.0333, "angle": 7.33}, {"time": 0.2, "angle": -16.28}, {"time": 0.3333, "angle": 2.61}, {"time": 0.3667, "angle": 7.33}, {"time": 0.5333, "angle": -16.28}, {"time": 0.6667, "angle": 2.61}, {"time": 0.7, "angle": 7.33}, {"time": 0.8667, "angle": -16.28}, {"time": 1, "angle": 2.61}]}, "niuwei6": {"rotate": [{"time": 0, "angle": 7.33}, {"time": 0.1667, "angle": -16.28}, {"time": 0.3333, "angle": 7.33}, {"time": 0.5, "angle": -16.28}, {"time": 0.6667, "angle": 7.33}, {"time": 0.8333, "angle": -16.28}, {"time": 1, "angle": 7.33}]}, "niuwei9": {"rotate": [{"time": 0, "angle": -6.83}, {"time": 0.1, "angle": 7.33}, {"time": 0.2667, "angle": -16.28}, {"time": 0.3333, "angle": -6.83}, {"time": 0.4333, "angle": 7.33}, {"time": 0.6, "angle": -16.28}, {"time": 0.6667, "angle": -6.83}, {"time": 0.7667, "angle": 7.33}, {"time": 0.9333, "angle": -16.28}, {"time": 1, "angle": -6.83}]}, "niuwei8": {"rotate": [{"time": 0, "angle": -2.11}, {"time": 0.0667, "angle": 7.33}, {"time": 0.2333, "angle": -16.28}, {"time": 0.3333, "angle": -2.11}, {"time": 0.4, "angle": 7.33}, {"time": 0.5667, "angle": -16.28}, {"time": 0.6667, "angle": -2.11}, {"time": 0.7333, "angle": 7.33}, {"time": 0.9, "angle": -16.28}, {"time": 1, "angle": -2.11}]}, "niuwei10": {"rotate": [{"time": 0, "angle": -11.56}, {"time": 0.1333, "angle": 7.33}, {"time": 0.3, "angle": -16.28}, {"time": 0.3333, "angle": -11.56}, {"time": 0.4667, "angle": 7.33}, {"time": 0.6333, "angle": -16.28}, {"time": 0.6667, "angle": -11.56}, {"time": 0.8, "angle": 7.33}, {"time": 0.9667, "angle": -16.28}, {"time": 1, "angle": -11.56}]}, "niutou2": {"translate": [{"time": 0, "x": -4.14, "y": 0}, {"time": 0.0333, "x": -6.9, "y": 0}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.2, "x": -4.14, "y": 0}, {"time": 0.2333, "x": -6.9, "y": 0}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.4, "x": -4.14, "y": 0}, {"time": 0.4333, "x": -6.9, "y": 0}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6, "x": -4.14, "y": 0}, {"time": 0.6333, "x": -6.9, "y": 0}, {"time": 0.7333, "x": 0, "y": 0}, {"time": 0.8, "x": -4.14, "y": 0}, {"time": 0.8667, "x": -6.9, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1, "x": -4.14, "y": 0}], "scale": [{"time": 0, "x": 0.862, "y": 0.862}, {"time": 0.0333, "x": 0.77, "y": 0.77}, {"time": 0.1333, "x": 1, "y": 1}, {"time": 0.2, "x": 0.862, "y": 0.862}, {"time": 0.2333, "x": 0.77, "y": 0.77}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 0.4, "x": 0.862, "y": 0.862}, {"time": 0.4333, "x": 0.77, "y": 0.77}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.6, "x": 0.862, "y": 0.862}, {"time": 0.6333, "x": 0.77, "y": 0.77}, {"time": 0.7333, "x": 1, "y": 1}, {"time": 0.8, "x": 0.862, "y": 0.862}, {"time": 0.8667, "x": 0.77, "y": 0.77}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1, "x": 0.862, "y": 0.862}]}, "D": {"scale": [{"time": 0, "x": 0.101, "y": 0.101, "curve": "stepped"}, {"time": 0.5333, "x": 0.101, "y": 0.101}, {"time": 0.6333, "x": 1.187, "y": 1.187}, {"time": 0.7, "x": 0.939, "y": 0.939}, {"time": 0.8, "x": 1, "y": 1}, {"time": 1, "x": 0.936, "y": 0.936}]}, "L": {"scale": [{"time": 0, "x": 0.101, "y": 0.101, "curve": "stepped"}, {"time": 0.6, "x": 0.101, "y": 0.101}, {"time": 0.7, "x": 1.216, "y": 1.216}, {"time": 0.7667, "x": 0.939, "y": 0.939}, {"time": 0.8667, "x": 1, "y": 1}, {"time": 1, "x": 0.973, "y": 0.973}]}, "W": {"scale": [{"time": 0, "x": 0.101, "y": 0.101, "curve": "stepped"}, {"time": 0.7333, "x": 0.101, "y": 0.101}, {"time": 0.8333, "x": 1.284, "y": 1.284}, {"time": 0.9, "x": 0.939, "y": 0.939}, {"time": 1, "x": 1.116, "y": 1.116}]}, "i": {"scale": [{"time": 0, "x": 0.101, "y": 0.101, "curve": "stepped"}, {"time": 0.6667, "x": 0.101, "y": 0.101}, {"time": 0.7667, "x": 1.277, "y": 1.277}, {"time": 0.8333, "x": 0.939, "y": 0.939}, {"time": 0.9333, "x": 1, "y": 1}, {"time": 1, "x": 1.011, "y": 1.011}]}, "jiesuo02_000": {"rotate": [{"time": 0, "angle": 180}], "translate": [{"time": 0, "x": -386.75, "y": 348.31}, {"time": 0.4, "x": -386.75, "y": 269.52}, {"time": 0.6, "x": -386.75, "y": 446.29}, {"time": 0.6667, "x": -386.75, "y": 588.86}, {"time": 0.7667, "x": -386.75, "y": 706.67}], "scale": [{"time": 0, "x": 3.023, "y": 2.8, "curve": "stepped"}, {"time": 0.4, "x": 3.023, "y": 2.8}, {"time": 0.6, "x": 3.331, "y": 3.954}, {"time": 0.6667, "x": 3.208, "y": 6.043}, {"time": 0.7667, "x": 3.023, "y": 2.762}]}, "guangzhao": {"translate": [{"time": 0, "x": 0, "y": -26.87}, {"time": 0.1667, "x": 0, "y": -4.81}, {"time": 0.3333, "x": 0, "y": -26.87}, {"time": 0.5, "x": 0, "y": -4.81}, {"time": 0.6667, "x": 0, "y": -26.87}, {"time": 0.8333, "x": 0, "y": -4.81}, {"time": 1, "x": 0, "y": -26.87}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 1, "y": 1.357}, {"time": 0.3333, "x": 0.794, "y": 0.48}, {"time": 0.5, "x": 1, "y": 1.357}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.357}, {"time": 1, "x": 1, "y": 1}]}}, "deform": {"default": {"D": {"D": [{"time": 0, "offset": 2, "vertices": [-0.07353, 0, -0.25734, 0, -0.25734, -0.11029, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.01332, -0.0864, 0.89953, 0, 0.7545, 0, 0.72378, 0, 0.67644, 0.11558, 0.69632, 0.11558, 1.08157, 0, 1.0617, 0, 0.86417, 0, 0.94374, 0.31424, 0.94374, 0.26741, 1.0364, 0.31424, 0.94122, 0.3777, 0.87777, 0.26665, 0.75568, 0.28132, 0.75568, -0.52898, -0.05288, -0.05288, -0.05288, 0, -0.25809, -0.10324, -0.25381, 0, -0.25381, 0, -0.25403, 0, 0, 0, -0.96784, 0.75733, 0, 0, 0, -1.06992, 0, -0.77524, -0.19741, -0.28603, -0.96287, 0.31022, -0.68662, -0.28605, -0.09589, -0.31112, -0.24853, -0.6704, 0, -0.28731, 0.98913, -0.11986, 0.98913, -1.3277, 0, 0, -0.96784, 0.15334, -0.53297, -0.75118, -0.98338, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.0799, -0.75118, -1.37843, -0.32472, -1.0799, -0.15412, 0.01995, 0.16014, 0.78561, 0.23568, 0, 0, 0, 0, 0, 0, 0.32921, -0.82303, 0.41899, -0.41899, 1.07741, -0.41899, 0.83798, 0, 0.58659], "curve": [0.346, 0.38, 0.757, 1]}, {"time": 0.6, "offset": 2, "vertices": [-0.292, 0, -1.022, 0, -1.022, -0.43799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.96214, -0.34312, -0.644, 0, -1.21999, 0, -1.342, 0, -1.52999, 0.459, -2.601, 0.459, -1.071, 0, 0, 0, 0, 0, -0.62, 0, -0.61999, -0.186, -0.252, 0, -0.63, 0.252, -0.882, -0.189, 0, 0, 0, 0, -0.21, -0.20999, -0.21, 0, -1.02499, -0.41, -1.00799, 0, -1.008, 0, -1.00884, 2e-05, 0, 0, 1.38599, -1.59799, 0, 0, 0, 0, 0, 0.784, -0.78399, 1.56799, -1.12, 1.23199, -1.56799, 1.56799, -0.987, 0.98701, -0.987, 0, 0, 0, -1.554, -0.47601, -1.554, 1.08598, 0, 0, 1.38599, 0.60899, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 2, "vertices": [-0.146, 0, -0.511, 0, -0.511, -0.21899, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.32807, -0.17156, 0.3875, 0, 0.0995, 0, 0.0385, 0, -0.0555, 0.2295, -0.3975, 0.2295, 0.3675, 0, 0.7095, 0, 0.5775, 0, 0.425, 0.21, 0.425, 0.117, 0.609, 0.21, 0.42, 0.336, 0.294, 0.1155, 0.505, 0.188, 0.505, -0.3535, -0.105, -0.105, -0.105, 0, -0.51249, -0.205, -0.50399, 0, -0.504, 0, -0.50442, 1e-05, 0, 0, -0.187, -0.024, 0, 0, 0, -0.715, 0, -0.258, -0.392, 0.329, -1.015, 0.616, -0.979, 0.32899, -0.3915, 0.11951, -0.4935, -0.44801, 0, -0.192, 0.1455, -0.23801, 0.1455, -0.52701, 0, 0, -0.187, 0.3045, -0.047, -0.50199, -0.34799, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.61199, -0.217, -0.41249, -0.103, 0.3225, 0.10702, 0.525, 0.1575, 0, 0, 0, 0, 0, 0, 0.22, -0.55, 0.28, -0.28, 0.72, -0.28, 0.56, 0, 0.392]}]}, "L": {"L": [{"time": 0, "offset": 4, "vertices": [0.52148, 0.39247, 0, 0, -1.29585, 0, -1.29585, 0, -0.49054, -0.15082, -0.36522, -0.149, -1.81252, -0.59673, 0.26723, -0.09951, 0.20081, 0.51707, 2.0007, 0.82713, 1.27799, 0.24814, 1.3207, 0.24814, 0.57814, -0.87301, 0.62695, -1.19978, 0.59989, -0.29059, 0, 0, 0, 0, 0.83622, 0, 1.17071, 0.58535, -0.30177, -0.51156, 0, 0, 0.52148, 0, 0.5063, 0, 0.84442, 0, 0.88102, -0.03661, 1.13753, 0.88393], "curve": [0.37, 0.63, 0.708, 1]}, {"time": 0.1333, "offset": 4, "vertices": [0.721, 0.49, 0, 0, -1.59599, 0, -1.59599, 0, -0.69999, -0.19999, -0.49999, -0.2, -1.92499, -0.53899, 0.294, 0.042, 0.364, 0.637, 2.27499, 0.91, 1.547, 0.273, 1.547, 0.273, 0.6, -1.01999, 0.59999, -1.31998, 0.65999, -0.48, 0, 0, 0, 0, 0.91999, 0, 1.288, 0.644, -0.44001, -0.56001, 0, 0, 0.721, 0, 0.651, 0, 1.02299, 0, 1.02299, 0, 1.30199, 1.02299], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "offset": 4, "vertices": [-1.47, -0.581, 0, 0, 1.69999, 0, 1.69999, 0, 1.6, 0.34, 0.98, 0.36, -0.68999, -1.17299, 0, -1.51199, -1.42799, -0.68, -0.73699, 0, -1.40698, 0, -0.93799, 0, 0.35999, 0.594, 0.89599, 0, 0, 1.59998, 0, 0, 0, 0, 0, 0, 0, 0, 1.078, -0.028, 0, 0, -1.47, 0, -0.938, 0, -0.93799, 0, -0.53599, -0.40201, -0.504, -0.504], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 4, "vertices": [-1.35469, -0.52463, 0, 0, 1.52652, 0, 1.52652, 0, 1.47895, 0.31158, 0.90211, 0.33053, -0.75499, -1.13962, 0.01547, -1.4302, -1.33368, -0.61068, -0.57847, 0.04789, -1.25151, 0.01437, -0.8072, 0.01437, 0.37263, 0.50905, 0.88041, -0.06947, 0.03474, 1.49051, 0, 0, 0, 0, 0.04842, 0, 0.06779, 0.03389, 0.9981, -0.056, 0, 0, -1.35469, 0, -0.85437, 0, -0.83478, 0, -0.45394, -0.38085, -0.40895, -0.42363]}]}, "W": {"W": [{"time": 0, "vertices": [0.45071, 0, 0.22535, 0.09658, 1.26564, -0.08877, 1.03632, -0.07593, 0.26873, -0.27538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.56421, 0, 0.41518, 0, -0.35506, 0, 0.37151, 0, 1.51327, 0, 0.55342, 0, 0.55342, 0, 0.84716, 0, -0.63127, -0.29692, 0, 0, 0, 0, 0, 0, 0.53784, 0, 0.53784, 0, 0.33949, 0, -0.23275, 0.25847, 0, 0, 0, 0, 0, 0, 0, 0, 0.52227, 0, 0.52227, 0, 0.21314, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07475, 0.42002, -0.27408, 0.84716, -0.34883, 0.33949, 0, 0, 0, 0.34883, 0, 0.34883, 0, -0.1495, 0, -0.57371, 0, -0.13168, 0, -0.23936, 0, -0.23936, 0, -0.60931, 0, -0.2457, 0, 0.42447, 0, 0.68387, 0, 0, 0.07074, -0.09433, 0.16507, -0.23275, 0, -0.22757, 0, 0.18984, -0.07475, -0.63127, 0, -0.76891, 0, -0.13229], "curve": [0.378, 0.52, 0.747, 1]}, {"time": 0.3667, "vertices": [0.812, 0, 0.40599, 0.174, 2.67298, 0.38997, 2.14199, 0.25598, 1.30899, -0.339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.05698, 0, 0.748, 0, 0, 0, 1.309, 0, 3.36599, 0, 1.575, 0, 1.575, 0, 0, 0, -2.07998, -1.064, 0, 0, 0, 0, 0, 0, 1.575, 0, 1.575, 0, 0, 0, -1.75, 0.836, 0, 0, 0, 0, 0, 0, 0, 0, 1.575, 0, 1.575, 0, 0.384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.533, 0, -1.022, 0, -1.50699, 0, -1.50699, 0, -1.81998, 0, -0.90999, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.74999, 0, -1.218, 0, -0.87, 0, -2.07998, 0, -1.94398, 0, -0.56699], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "vertices": [0.21368, 0, 0.10684, 0.04579, 0.34237, -0.40284, 0.31095, -0.29368, -0.41374, -0.23363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.41511, 0, 0.19684, 0, -0.58799, 0, -0.24352, 0, 0.2978, 0, -0.11679, 0, -0.11679, 0, 1.40294, 0, 0.31916, 0.20632, 0, 0, 0, 0, 0, 0, -0.14258, 0, -0.14258, 0, 0.56221, 0, 0.76262, -0.12041, 0, 0, 0, 0, 0, 0, 0, 0, -0.16836, 0, -0.16836, 0, 0.10105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12379, 0.69557, -0.45389, 1.40294, -0.57767, 0.56221, 0, 0, 0, 0.57768, 0, 0.57768, 0, -0.24758, 0, 0.05563, 0, 0.45242, 0, 0.59226, 0, 0.59226, 0, 0.18495, 0, 0.1901, 0, 0.70294, 0, 1.13252, 0, 0, 0.11716, -0.15621, 0.27337, 0.76262, 0, 0.4222, 0, 0.88515, -0.12379, 0.31916, 0, 0.002, 0, 0.15289]}]}, "di": {"di": [{"time": 0, "offset": 13, "vertices": [-33.80292, -1e-05, -69.36188, -1e-05, -104.92081, -1e-05, -138.3168, -2e-05, -172.21878, -2e-05, -208.11475, -2e-05, -242.08777, -2e-05, -278.82373, -3e-05, -311.58368, -3e-05, -345.9817, -3e-05, -379.49768, -4e-05, -415.06573, -4e-05, -448.58173, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -448.58173, -4e-05, -415.06573, -3e-05, -379.49768, -3e-05, -345.9817, -3e-05, -311.58368, -2e-05, -278.82373, -2e-05, -242.08777, -2e-05, -208.11475, -2e-05, -172.21878, -1e-05, -138.3168, -1e-05, -104.92081, -1e-05, -69.36188, 0, -33.80292, 0, -33.80292, 0, -33.80292, 0, -33.80292, 0, -33.80292, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -138.3168, -1e-05, -138.3168, -1e-05, -138.3168, -1e-05, -138.3168, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -278.82373, -2e-05, -278.82373, -2e-05, -278.82373, -2e-05, -278.82373, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -379.49768, -3e-05, -379.49768, -3e-05, -379.49768, -3e-05, -379.49768, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -448.58173, -4e-05, -448.58173, -4e-05, -448.58173, -4e-05, -448.58173], "curve": "stepped"}, {"time": 0.3667, "offset": 13, "vertices": [-33.80292, -1e-05, -69.36188, -1e-05, -104.92081, -1e-05, -138.3168, -2e-05, -172.21878, -2e-05, -208.11475, -2e-05, -242.08777, -2e-05, -278.82373, -3e-05, -311.58368, -3e-05, -345.9817, -3e-05, -379.49768, -4e-05, -415.06573, -4e-05, -448.58173, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -448.58173, -4e-05, -415.06573, -3e-05, -379.49768, -3e-05, -345.9817, -3e-05, -311.58368, -2e-05, -278.82373, -2e-05, -242.08777, -2e-05, -208.11475, -2e-05, -172.21878, -1e-05, -138.3168, -1e-05, -104.92081, -1e-05, -69.36188, 0, -33.80292, 0, -33.80292, 0, -33.80292, 0, -33.80292, 0, -33.80292, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -138.3168, -1e-05, -138.3168, -1e-05, -138.3168, -1e-05, -138.3168, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -278.82373, -2e-05, -278.82373, -2e-05, -278.82373, -2e-05, -278.82373, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -379.49768, -3e-05, -379.49768, -3e-05, -379.49768, -3e-05, -379.49768, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -448.58173, -4e-05, -448.58173, -4e-05, -448.58173, -4e-05, -448.58173]}, {"time": 0.4, "offset": 15, "vertices": [-35.55896, -1e-05, -71.11789, -1e-05, -104.51388, -1e-05, -138.41586, -2e-05, -174.31183, -2e-05, -208.28485, -2e-05, -245.02081, -2e-05, -277.78073, -3e-05, -312.17874, -3e-05, -345.69473, -3e-05, -381.2628, -4e-05, -414.77878, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -414.77878, -3e-05, -381.2628, -3e-05, -345.69473, -3e-05, -312.17874, -2e-05, -277.78073, -2e-05, -245.02081, -2e-05, -208.28485, -2e-05, -174.31183, -1e-05, -138.41586, -1e-05, -104.51388, -1e-05, -71.11789, 0, -35.55896, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -35.55896, 0, -35.55896, 0, -35.55896, 0, -35.55896, -1e-05, -71.11789, -1e-05, -71.11789, -1e-05, -71.11789, -1e-05, -71.11789, -1e-05, -104.51388, -1e-05, -104.51388, -1e-05, -104.51388, -1e-05, -104.51388, -1e-05, -138.41586, -1e-05, -138.41586, -1e-05, -138.41586, -1e-05, -138.41586, -2e-05, -174.31183, -2e-05, -174.31183, -2e-05, -174.31183, -2e-05, -174.31183, -2e-05, -208.28485, -2e-05, -208.28485, -2e-05, -208.28485, -2e-05, -208.28485, -2e-05, -245.02081, -2e-05, -245.02081, -2e-05, -245.02081, -2e-05, -245.02081, -2e-05, -277.78073, -2e-05, -277.78073, -2e-05, -277.78073, -2e-05, -277.78073, -3e-05, -312.17874, -3e-05, -312.17874, -3e-05, -312.17874, -3e-05, -312.17874, -3e-05, -345.69473, -3e-05, -345.69473, -3e-05, -345.69473, -3e-05, -345.69473, -3e-05, -381.2628, -3e-05, -381.2628, -3e-05, -381.2628, -3e-05, -381.2628, -4e-05, -414.77878, -4e-05, -414.77878, -4e-05, -414.77878, -4e-05, -414.77878]}, {"time": 0.4333, "offset": 17, "vertices": [-35.55893, -1e-05, -68.95492, -1e-05, -102.8569, -1e-05, -138.75287, -2e-05, -172.72589, -2e-05, -209.46185, -2e-05, -242.22183, -2e-05, -276.6198, -3e-05, -310.1358, -3e-05, -345.70386, -3e-05, -379.21985, -4e-05, -412.73578, -4e-05, -412.73578, -4e-05, -412.73578, -4e-05, -412.73578, -4e-05, -412.73578, -4e-05, -412.73578, -3e-05, -379.21985, -3e-05, -345.70386, -3e-05, -310.1358, -2e-05, -276.6198, -2e-05, -242.22183, -2e-05, -209.46185, -2e-05, -172.72589, -1e-05, -138.75287, -1e-05, -102.8569, -1e-05, -68.95492, 0, -35.55893, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -35.55893, 0, -35.55893, 0, -35.55893, 0, -35.55893, -1e-05, -68.95492, -1e-05, -68.95492, -1e-05, -68.95492, -1e-05, -68.95492, -1e-05, -102.8569, -1e-05, -102.8569, -1e-05, -102.8569, -1e-05, -102.8569, -1e-05, -138.75287, -1e-05, -138.75287, -1e-05, -138.75287, -1e-05, -138.75287, -2e-05, -172.72589, -2e-05, -172.72589, -2e-05, -172.72589, -2e-05, -172.72589, -2e-05, -209.46185, -2e-05, -209.46185, -2e-05, -209.46185, -2e-05, -209.46185, -2e-05, -242.22183, -2e-05, -242.22183, -2e-05, -242.22183, -2e-05, -242.22183, -2e-05, -276.6198, -2e-05, -276.6198, -2e-05, -276.6198, -2e-05, -276.6198, -3e-05, -310.1358, -3e-05, -310.1358, -3e-05, -310.1358, -3e-05, -310.1358, -3e-05, -345.70386, -3e-05, -345.70386, -3e-05, -345.70386, -3e-05, -345.70386, -3e-05, -379.21985, -3e-05, -379.21985, -3e-05, -379.21985, -3e-05, -379.21985]}, {"time": 0.4667, "offset": 19, "vertices": [-33.39599, -1e-05, -67.29797, -1e-05, -103.19394, -1e-05, -137.16696, -2e-05, -173.90292, -2e-05, -206.6629, -2e-05, -241.06088, -2e-05, -274.57684, -3e-05, -310.1449, -3e-05, -343.6609, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -343.6609, -3e-05, -310.1449, -2e-05, -274.57684, -2e-05, -241.06088, -2e-05, -206.6629, -2e-05, -173.90292, -1e-05, -137.16696, -1e-05, -103.19394, -1e-05, -67.29797, 0, -33.39599, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.39599, 0, -33.39599, 0, -33.39599, 0, -33.39599, -1e-05, -67.29797, -1e-05, -67.29797, -1e-05, -67.29797, -1e-05, -67.29797, -1e-05, -103.19394, -1e-05, -103.19394, -1e-05, -103.19394, -1e-05, -103.19394, -1e-05, -137.16696, -1e-05, -137.16696, -1e-05, -137.16696, -1e-05, -137.16696, -2e-05, -173.90292, -2e-05, -173.90292, -2e-05, -173.90292, -2e-05, -173.90292, -2e-05, -206.6629, -2e-05, -206.6629, -2e-05, -206.6629, -2e-05, -206.6629, -2e-05, -241.06088, -2e-05, -241.06088, -2e-05, -241.06088, -2e-05, -241.06088, -2e-05, -274.57684, -2e-05, -274.57684, -2e-05, -274.57684, -2e-05, -274.57684, -3e-05, -310.1449, -3e-05, -310.1449, -3e-05, -310.1449, -3e-05, -310.1449, -3e-05, -343.6609, -3e-05, -343.6609, -3e-05, -343.6609, -3e-05, -343.6609]}, {"time": 0.5, "offset": 21, "vertices": [-33.90199, -1e-05, -69.79795, -1e-05, -103.77094, -1e-05, -140.5069, -2e-05, -173.26688, -2e-05, -207.66486, -2e-05, -241.18085, -2e-05, -276.7489, -3e-05, -310.2649, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -310.2649, -2e-05, -276.7489, -2e-05, -241.18085, -2e-05, -207.66486, -2e-05, -173.26688, -1e-05, -140.5069, -1e-05, -103.77094, -1e-05, -69.79795, 0, -33.90199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.90199, 0, -33.90199, 0, -33.90199, 0, -33.90199, -1e-05, -69.79795, -1e-05, -69.79795, -1e-05, -69.79795, -1e-05, -69.79795, -1e-05, -103.77094, -1e-05, -103.77094, -1e-05, -103.77094, -1e-05, -103.77094, -1e-05, -140.5069, -1e-05, -140.5069, -1e-05, -140.5069, -1e-05, -140.5069, -2e-05, -173.26688, -2e-05, -173.26688, -2e-05, -173.26688, -2e-05, -173.26688, -2e-05, -207.66486, -2e-05, -207.66486, -2e-05, -207.66486, -2e-05, -207.66486, -2e-05, -241.18085, -2e-05, -241.18085, -2e-05, -241.18085, -2e-05, -241.18085, -2e-05, -276.7489, -2e-05, -276.7489, -2e-05, -276.7489, -2e-05, -276.7489, -3e-05, -310.2649, -3e-05, -310.2649, -3e-05, -310.2649, -3e-05, -310.2649]}, {"time": 0.5333, "offset": 23, "vertices": [-35.89597, -1e-05, -69.86896, -1e-05, -106.60493, -1e-05, -139.3649, -2e-05, -173.76288, -2e-05, -207.27887, -2e-05, -242.84686, -2e-05, -276.36285, -3e-05, -309.87878, -3e-05, -309.87878, -3e-05, -309.87878, -3e-05, -309.87878, -3e-05, -309.87878, -3e-05, -309.87878, -2e-05, -276.36285, -2e-05, -242.84686, -2e-05, -207.27887, -2e-05, -173.76288, -1e-05, -139.3649, -1e-05, -106.60493, -1e-05, -69.86896, 0, -35.89597, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -35.89597, 0, -35.89597, 0, -35.89597, 0, -35.89597, -1e-05, -69.86896, -1e-05, -69.86896, -1e-05, -69.86896, -1e-05, -69.86896, -1e-05, -106.60493, -1e-05, -106.60493, -1e-05, -106.60493, -1e-05, -106.60493, -1e-05, -139.3649, -1e-05, -139.3649, -1e-05, -139.3649, -1e-05, -139.3649, -2e-05, -173.76288, -2e-05, -173.76288, -2e-05, -173.76288, -2e-05, -173.76288, -2e-05, -207.27887, -2e-05, -207.27887, -2e-05, -207.27887, -2e-05, -207.27887, -2e-05, -242.84686, -2e-05, -242.84686, -2e-05, -242.84686, -2e-05, -242.84686, -2e-05, -276.36285, -2e-05, -276.36285, -2e-05, -276.36285, -2e-05, -276.36285]}, {"time": 0.5667, "offset": 25, "vertices": [-33.97299, -1e-05, -70.70897, -1e-05, -103.46893, -1e-05, -137.8669, -1e-05, -171.3829, -2e-05, -206.9509, -2e-05, -240.46689, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -240.46689, -2e-05, -206.9509, -1e-05, -171.3829, -1e-05, -137.8669, -1e-05, -103.46893, -1e-05, -70.70897, 0, -33.97299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.97299, 0, -33.97299, 0, -33.97299, 0, -33.97299, -1e-05, -70.70897, -1e-05, -70.70897, -1e-05, -70.70897, -1e-05, -70.70897, -1e-05, -103.46893, -1e-05, -103.46893, -1e-05, -103.46893, -1e-05, -103.46893, -1e-05, -137.8669, -1e-05, -137.8669, -1e-05, -137.8669, -1e-05, -137.8669, -1e-05, -171.3829, -1e-05, -171.3829, -1e-05, -171.3829, -1e-05, -171.3829, -2e-05, -206.9509, -2e-05, -206.9509, -2e-05, -206.9509, -2e-05, -206.9509, -2e-05, -240.46689, -2e-05, -240.46689, -2e-05, -240.46689, -2e-05, -240.46689]}, {"time": 0.6, "offset": 27, "vertices": [-36.73598, -1e-05, -69.49594, -1e-05, -103.89391, -1e-05, -137.40991, -2e-05, -172.9779, -2e-05, -206.4939, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -206.4939, -2e-05, -172.9779, -1e-05, -137.40991, -1e-05, -103.89391, -1e-05, -69.49594, 0, -36.73598, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -36.73598, 0, -36.73598, 0, -36.73598, 0, -36.73598, -1e-05, -69.49594, -1e-05, -69.49594, -1e-05, -69.49594, -1e-05, -69.49594, -1e-05, -103.89391, -1e-05, -103.89391, -1e-05, -103.89391, -1e-05, -103.89391, -1e-05, -137.40991, -1e-05, -137.40991, -1e-05, -137.40991, -1e-05, -137.40991, -2e-05, -172.9779, -2e-05, -172.9779, -2e-05, -172.9779, -2e-05, -172.9779, -2e-05, -206.4939, -2e-05, -206.4939, -2e-05, -206.4939, -2e-05, -206.4939]}, {"time": 0.6333, "offset": 29, "vertices": [-32.75996, -1e-05, -67.15793, -1e-05, -100.67393, -1e-05, -136.24191, -1e-05, -169.7579, -2e-05, -203.27393, -2e-05, -203.27393, -2e-05, -203.27393, -2e-05, -203.27393, -2e-05, -203.27393, -2e-05, -203.27393, -1e-05, -169.7579, -1e-05, -136.24191, -1e-05, -100.67393, -1e-05, -67.15793, 0, -32.75996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -32.75996, 0, -32.75996, 0, -32.75996, 0, -32.75996, -1e-05, -67.15793, -1e-05, -67.15793, -1e-05, -67.15793, -1e-05, -67.15793, -1e-05, -100.67393, -1e-05, -100.67393, -1e-05, -100.67393, -1e-05, -100.67393, -1e-05, -136.24191, -1e-05, -136.24191, -1e-05, -136.24191, -1e-05, -136.24191, -1e-05, -169.7579, -1e-05, -169.7579, -1e-05, -169.7579, -1e-05, -169.7579]}, {"time": 0.6667, "offset": 33, "vertices": [-33.51601, -1e-05, -69.08398, -1e-05, -102.59999, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -102.59999, -1e-05, -69.08398, 0, -33.51601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601, -1e-05, -69.08398, -1e-05, -69.08398, -1e-05, -69.08398, -1e-05, -69.08398, -1e-05, -102.59999, -1e-05, -102.59999, -1e-05, -102.59999, -1e-05, -102.59999]}, {"time": 0.7, "offset": 37, "vertices": [-33.51601, -1e-05, -67.03201, -1e-05, -67.03201, -1e-05, -67.03201, -1e-05, -67.03201, -1e-05, -67.03201, -1e-05, -67.03201, 0, -33.51601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601]}, {"time": 0.7333, "offset": 39, "vertices": [-33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601]}, {"time": 0.7667}]}, "di2": {"di": [{"time": 0, "offset": 13, "vertices": [-33.80292, -1e-05, -69.36188, -1e-05, -104.92081, -1e-05, -138.3168, -2e-05, -172.21878, -2e-05, -208.11475, -2e-05, -242.08777, -2e-05, -278.82373, -3e-05, -311.58368, -3e-05, -345.9817, -3e-05, -379.49768, -4e-05, -415.06573, -4e-05, -448.58173, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -448.58173, -4e-05, -415.06573, -3e-05, -379.49768, -3e-05, -345.9817, -3e-05, -311.58368, -2e-05, -278.82373, -2e-05, -242.08777, -2e-05, -208.11475, -2e-05, -172.21878, -1e-05, -138.3168, -1e-05, -104.92081, -1e-05, -69.36188, 0, -33.80292, 0, -33.80292, 0, -33.80292, 0, -33.80292, 0, -33.80292, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -138.3168, -1e-05, -138.3168, -1e-05, -138.3168, -1e-05, -138.3168, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -278.82373, -2e-05, -278.82373, -2e-05, -278.82373, -2e-05, -278.82373, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -379.49768, -3e-05, -379.49768, -3e-05, -379.49768, -3e-05, -379.49768, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -448.58173, -4e-05, -448.58173, -4e-05, -448.58173, -4e-05, -448.58173], "curve": "stepped"}, {"time": 0.3667, "offset": 13, "vertices": [-33.80292, -1e-05, -69.36188, -1e-05, -104.92081, -1e-05, -138.3168, -2e-05, -172.21878, -2e-05, -208.11475, -2e-05, -242.08777, -2e-05, -278.82373, -3e-05, -311.58368, -3e-05, -345.9817, -3e-05, -379.49768, -4e-05, -415.06573, -4e-05, -448.58173, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -482.09766, -4e-05, -448.58173, -4e-05, -415.06573, -3e-05, -379.49768, -3e-05, -345.9817, -3e-05, -311.58368, -2e-05, -278.82373, -2e-05, -242.08777, -2e-05, -208.11475, -2e-05, -172.21878, -1e-05, -138.3168, -1e-05, -104.92081, -1e-05, -69.36188, 0, -33.80292, 0, -33.80292, 0, -33.80292, 0, -33.80292, 0, -33.80292, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -69.36188, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -104.92081, -1e-05, -138.3168, -1e-05, -138.3168, -1e-05, -138.3168, -1e-05, -138.3168, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -172.21878, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -208.11475, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -242.08777, -2e-05, -278.82373, -2e-05, -278.82373, -2e-05, -278.82373, -2e-05, -278.82373, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -311.58368, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -345.9817, -3e-05, -379.49768, -3e-05, -379.49768, -3e-05, -379.49768, -3e-05, -379.49768, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -415.06573, -4e-05, -448.58173, -4e-05, -448.58173, -4e-05, -448.58173, -4e-05, -448.58173]}, {"time": 0.4, "offset": 15, "vertices": [-35.55896, -1e-05, -71.11789, -1e-05, -104.51388, -1e-05, -138.41586, -2e-05, -174.31183, -2e-05, -208.28485, -2e-05, -245.02081, -2e-05, -277.78073, -3e-05, -312.17874, -3e-05, -345.69473, -3e-05, -381.2628, -4e-05, -414.77878, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -448.2947, -4e-05, -414.77878, -3e-05, -381.2628, -3e-05, -345.69473, -3e-05, -312.17874, -2e-05, -277.78073, -2e-05, -245.02081, -2e-05, -208.28485, -2e-05, -174.31183, -1e-05, -138.41586, -1e-05, -104.51388, -1e-05, -71.11789, 0, -35.55896, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -35.55896, 0, -35.55896, 0, -35.55896, 0, -35.55896, -1e-05, -71.11789, -1e-05, -71.11789, -1e-05, -71.11789, -1e-05, -71.11789, -1e-05, -104.51388, -1e-05, -104.51388, -1e-05, -104.51388, -1e-05, -104.51388, -1e-05, -138.41586, -1e-05, -138.41586, -1e-05, -138.41586, -1e-05, -138.41586, -2e-05, -174.31183, -2e-05, -174.31183, -2e-05, -174.31183, -2e-05, -174.31183, -2e-05, -208.28485, -2e-05, -208.28485, -2e-05, -208.28485, -2e-05, -208.28485, -2e-05, -245.02081, -2e-05, -245.02081, -2e-05, -245.02081, -2e-05, -245.02081, -2e-05, -277.78073, -2e-05, -277.78073, -2e-05, -277.78073, -2e-05, -277.78073, -3e-05, -312.17874, -3e-05, -312.17874, -3e-05, -312.17874, -3e-05, -312.17874, -3e-05, -345.69473, -3e-05, -345.69473, -3e-05, -345.69473, -3e-05, -345.69473, -3e-05, -381.2628, -3e-05, -381.2628, -3e-05, -381.2628, -3e-05, -381.2628, -4e-05, -414.77878, -4e-05, -414.77878, -4e-05, -414.77878, -4e-05, -414.77878]}, {"time": 0.4333, "offset": 17, "vertices": [-35.55893, -1e-05, -68.95492, -1e-05, -102.8569, -1e-05, -138.75287, -2e-05, -172.72589, -2e-05, -209.46185, -2e-05, -242.22183, -2e-05, -276.6198, -3e-05, -310.1358, -3e-05, -345.70386, -3e-05, -379.21985, -4e-05, -412.73578, -4e-05, -412.73578, -4e-05, -412.73578, -4e-05, -412.73578, -4e-05, -412.73578, -4e-05, -412.73578, -3e-05, -379.21985, -3e-05, -345.70386, -3e-05, -310.1358, -2e-05, -276.6198, -2e-05, -242.22183, -2e-05, -209.46185, -2e-05, -172.72589, -1e-05, -138.75287, -1e-05, -102.8569, -1e-05, -68.95492, 0, -35.55893, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -35.55893, 0, -35.55893, 0, -35.55893, 0, -35.55893, -1e-05, -68.95492, -1e-05, -68.95492, -1e-05, -68.95492, -1e-05, -68.95492, -1e-05, -102.8569, -1e-05, -102.8569, -1e-05, -102.8569, -1e-05, -102.8569, -1e-05, -138.75287, -1e-05, -138.75287, -1e-05, -138.75287, -1e-05, -138.75287, -2e-05, -172.72589, -2e-05, -172.72589, -2e-05, -172.72589, -2e-05, -172.72589, -2e-05, -209.46185, -2e-05, -209.46185, -2e-05, -209.46185, -2e-05, -209.46185, -2e-05, -242.22183, -2e-05, -242.22183, -2e-05, -242.22183, -2e-05, -242.22183, -2e-05, -276.6198, -2e-05, -276.6198, -2e-05, -276.6198, -2e-05, -276.6198, -3e-05, -310.1358, -3e-05, -310.1358, -3e-05, -310.1358, -3e-05, -310.1358, -3e-05, -345.70386, -3e-05, -345.70386, -3e-05, -345.70386, -3e-05, -345.70386, -3e-05, -379.21985, -3e-05, -379.21985, -3e-05, -379.21985, -3e-05, -379.21985]}, {"time": 0.4667, "offset": 19, "vertices": [-33.39599, -1e-05, -67.29797, -1e-05, -103.19394, -1e-05, -137.16696, -2e-05, -173.90292, -2e-05, -206.6629, -2e-05, -241.06088, -2e-05, -274.57684, -3e-05, -310.1449, -3e-05, -343.6609, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -377.17682, -3e-05, -343.6609, -3e-05, -310.1449, -2e-05, -274.57684, -2e-05, -241.06088, -2e-05, -206.6629, -2e-05, -173.90292, -1e-05, -137.16696, -1e-05, -103.19394, -1e-05, -67.29797, 0, -33.39599, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.39599, 0, -33.39599, 0, -33.39599, 0, -33.39599, -1e-05, -67.29797, -1e-05, -67.29797, -1e-05, -67.29797, -1e-05, -67.29797, -1e-05, -103.19394, -1e-05, -103.19394, -1e-05, -103.19394, -1e-05, -103.19394, -1e-05, -137.16696, -1e-05, -137.16696, -1e-05, -137.16696, -1e-05, -137.16696, -2e-05, -173.90292, -2e-05, -173.90292, -2e-05, -173.90292, -2e-05, -173.90292, -2e-05, -206.6629, -2e-05, -206.6629, -2e-05, -206.6629, -2e-05, -206.6629, -2e-05, -241.06088, -2e-05, -241.06088, -2e-05, -241.06088, -2e-05, -241.06088, -2e-05, -274.57684, -2e-05, -274.57684, -2e-05, -274.57684, -2e-05, -274.57684, -3e-05, -310.1449, -3e-05, -310.1449, -3e-05, -310.1449, -3e-05, -310.1449, -3e-05, -343.6609, -3e-05, -343.6609, -3e-05, -343.6609, -3e-05, -343.6609]}, {"time": 0.5, "offset": 21, "vertices": [-33.90199, -1e-05, -69.79795, -1e-05, -103.77094, -1e-05, -140.5069, -2e-05, -173.26688, -2e-05, -207.66486, -2e-05, -241.18085, -2e-05, -276.7489, -3e-05, -310.2649, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -343.78082, -3e-05, -310.2649, -2e-05, -276.7489, -2e-05, -241.18085, -2e-05, -207.66486, -2e-05, -173.26688, -1e-05, -140.5069, -1e-05, -103.77094, -1e-05, -69.79795, 0, -33.90199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.90199, 0, -33.90199, 0, -33.90199, 0, -33.90199, -1e-05, -69.79795, -1e-05, -69.79795, -1e-05, -69.79795, -1e-05, -69.79795, -1e-05, -103.77094, -1e-05, -103.77094, -1e-05, -103.77094, -1e-05, -103.77094, -1e-05, -140.5069, -1e-05, -140.5069, -1e-05, -140.5069, -1e-05, -140.5069, -2e-05, -173.26688, -2e-05, -173.26688, -2e-05, -173.26688, -2e-05, -173.26688, -2e-05, -207.66486, -2e-05, -207.66486, -2e-05, -207.66486, -2e-05, -207.66486, -2e-05, -241.18085, -2e-05, -241.18085, -2e-05, -241.18085, -2e-05, -241.18085, -2e-05, -276.7489, -2e-05, -276.7489, -2e-05, -276.7489, -2e-05, -276.7489, -3e-05, -310.2649, -3e-05, -310.2649, -3e-05, -310.2649, -3e-05, -310.2649]}, {"time": 0.5333, "offset": 23, "vertices": [-35.89597, -1e-05, -69.86896, -1e-05, -106.60493, -1e-05, -139.3649, -2e-05, -173.76288, -2e-05, -207.27887, -2e-05, -242.84686, -2e-05, -276.36285, -3e-05, -309.87878, -3e-05, -309.87878, -3e-05, -309.87878, -3e-05, -309.87878, -3e-05, -309.87878, -3e-05, -309.87878, -2e-05, -276.36285, -2e-05, -242.84686, -2e-05, -207.27887, -2e-05, -173.76288, -1e-05, -139.3649, -1e-05, -106.60493, -1e-05, -69.86896, 0, -35.89597, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -35.89597, 0, -35.89597, 0, -35.89597, 0, -35.89597, -1e-05, -69.86896, -1e-05, -69.86896, -1e-05, -69.86896, -1e-05, -69.86896, -1e-05, -106.60493, -1e-05, -106.60493, -1e-05, -106.60493, -1e-05, -106.60493, -1e-05, -139.3649, -1e-05, -139.3649, -1e-05, -139.3649, -1e-05, -139.3649, -2e-05, -173.76288, -2e-05, -173.76288, -2e-05, -173.76288, -2e-05, -173.76288, -2e-05, -207.27887, -2e-05, -207.27887, -2e-05, -207.27887, -2e-05, -207.27887, -2e-05, -242.84686, -2e-05, -242.84686, -2e-05, -242.84686, -2e-05, -242.84686, -2e-05, -276.36285, -2e-05, -276.36285, -2e-05, -276.36285, -2e-05, -276.36285]}, {"time": 0.5667, "offset": 25, "vertices": [-33.97299, -1e-05, -70.70897, -1e-05, -103.46893, -1e-05, -137.8669, -1e-05, -171.3829, -2e-05, -206.9509, -2e-05, -240.46689, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -273.98285, -2e-05, -240.46689, -2e-05, -206.9509, -1e-05, -171.3829, -1e-05, -137.8669, -1e-05, -103.46893, -1e-05, -70.70897, 0, -33.97299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.97299, 0, -33.97299, 0, -33.97299, 0, -33.97299, -1e-05, -70.70897, -1e-05, -70.70897, -1e-05, -70.70897, -1e-05, -70.70897, -1e-05, -103.46893, -1e-05, -103.46893, -1e-05, -103.46893, -1e-05, -103.46893, -1e-05, -137.8669, -1e-05, -137.8669, -1e-05, -137.8669, -1e-05, -137.8669, -1e-05, -171.3829, -1e-05, -171.3829, -1e-05, -171.3829, -1e-05, -171.3829, -2e-05, -206.9509, -2e-05, -206.9509, -2e-05, -206.9509, -2e-05, -206.9509, -2e-05, -240.46689, -2e-05, -240.46689, -2e-05, -240.46689, -2e-05, -240.46689]}, {"time": 0.6, "offset": 27, "vertices": [-36.73598, -1e-05, -69.49594, -1e-05, -103.89391, -1e-05, -137.40991, -2e-05, -172.9779, -2e-05, -206.4939, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -240.00992, -2e-05, -206.4939, -2e-05, -172.9779, -1e-05, -137.40991, -1e-05, -103.89391, -1e-05, -69.49594, 0, -36.73598, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -36.73598, 0, -36.73598, 0, -36.73598, 0, -36.73598, -1e-05, -69.49594, -1e-05, -69.49594, -1e-05, -69.49594, -1e-05, -69.49594, -1e-05, -103.89391, -1e-05, -103.89391, -1e-05, -103.89391, -1e-05, -103.89391, -1e-05, -137.40991, -1e-05, -137.40991, -1e-05, -137.40991, -1e-05, -137.40991, -2e-05, -172.9779, -2e-05, -172.9779, -2e-05, -172.9779, -2e-05, -172.9779, -2e-05, -206.4939, -2e-05, -206.4939, -2e-05, -206.4939, -2e-05, -206.4939]}, {"time": 0.6333, "offset": 29, "vertices": [-32.75996, -1e-05, -67.15793, -1e-05, -100.67393, -1e-05, -136.24191, -1e-05, -169.7579, -2e-05, -203.27393, -2e-05, -203.27393, -2e-05, -203.27393, -2e-05, -203.27393, -2e-05, -203.27393, -2e-05, -203.27393, -1e-05, -169.7579, -1e-05, -136.24191, -1e-05, -100.67393, -1e-05, -67.15793, 0, -32.75996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -32.75996, 0, -32.75996, 0, -32.75996, 0, -32.75996, -1e-05, -67.15793, -1e-05, -67.15793, -1e-05, -67.15793, -1e-05, -67.15793, -1e-05, -100.67393, -1e-05, -100.67393, -1e-05, -100.67393, -1e-05, -100.67393, -1e-05, -136.24191, -1e-05, -136.24191, -1e-05, -136.24191, -1e-05, -136.24191, -1e-05, -169.7579, -1e-05, -169.7579, -1e-05, -169.7579, -1e-05, -169.7579]}, {"time": 0.6667, "offset": 33, "vertices": [-33.51601, -1e-05, -69.08398, -1e-05, -102.59999, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -136.116, -1e-05, -102.59999, -1e-05, -69.08398, 0, -33.51601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601, -1e-05, -69.08398, -1e-05, -69.08398, -1e-05, -69.08398, -1e-05, -69.08398, -1e-05, -102.59999, -1e-05, -102.59999, -1e-05, -102.59999, -1e-05, -102.59999]}, {"time": 0.7, "offset": 37, "vertices": [-33.51601, -1e-05, -67.03201, -1e-05, -67.03201, -1e-05, -67.03201, -1e-05, -67.03201, -1e-05, -67.03201, -1e-05, -67.03201, 0, -33.51601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601]}, {"time": 0.7333, "offset": 39, "vertices": [-33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601, 0, -33.51601]}, {"time": 0.7667}]}, "i": {"i": [{"time": 0, "offset": 4, "vertices": [1.47408, 0.89011, 0.56815, 0, -0.97911, 0, -0.97911, 0, 0.08182, -0.50617, 0, -0.50617, -0.62841, -0.87978, -1.98208, -0.1888, -0.81222, 0, -0.81222, 0, -0.38507, 0, -0.37827, 0, 0.08572, -0.03674, 0, 0, -1.02094, 0, -0.55278, 0, -1.07983, 0, -0.6895, 0, -0.31588, 0.69755, 0.56815, 0, 1.47408, 0, -0.10476, 0.45969, -0.85425, 0.40286, -0.39164, -0.23459], "curve": [0.323, 0.29, 0.757, 1]}, {"time": 0.6333, "offset": 4, "vertices": [-0.40599, 0, 0, 0, 1.606, 0, 1.606, 0, 0.588, 0, 0, 0, 0, 0, 0.60926, 0.8702, 0.96799, 0, 0.96799, 0, 0.264, 0, 0.61599, 0, 0.61599, -0.264, 0, 0, 0, 0, -0.26701, 0, -1.246, 0, -1.60199, 0, 0.588, 0.336, 0, 0, -0.40599, 0, -0.691, 0, -0.46, 0, -1.25, -0.352], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 4, "vertices": [0.51358, 0.43537, 0.27789, 0, 0.34158, 0, 0.34158, 0, 0.34042, -0.24758, 0, -0.24758, -0.30737, -0.43032, -0.65821, 0.35222, 0.09726, 0, 0.09726, 0, -0.05347, 0, 0.12968, 0, 0.35663, -0.15284, 0, 0, -0.49936, 0, -0.40678, 0, -1.16472, 0, -1.15567, 0, 0.14589, 0.51284, 0.27789, 0, 0.51358, 0, -0.40426, 0.22484, -0.65283, 0.19705, -0.83016, -0.29457]}]}, "i2": {"i2": [{"time": 0, "vertices": [0.2585, 0, 0, 0, -0.235, 0, -0.456, 0, 0.097, 0.2765, -0.098, 0.2765, 0, 0, -0.0565], "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.4, "vertices": [-0.553, 0, 0, 0, 0.553, 0, 0.52799, 0, 1.63399, 0.55299, 1.106, 0.55299, 0, 0, -0.55299], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "vertices": [0.55747, 0, 0, 0, -0.52531, 0, -0.81853, 0, -0.46926, 0.17463, -0.54157, 0.17463, 0, 0, 0.12642]}]}, "niushen": {"niushen": [{"time": 0, "offset": 1, "vertices": [-17.6701, 0, 0, 0, 0, 0, -17.6701]}, {"time": 0.1, "offset": 1, "vertices": [20.60001, 0, 0, 0, 0, 1e-05, 20.60001]}, {"time": 0.2, "offset": 1, "vertices": [-17.6701, 0, 0, 0, 0, 0, -17.6701]}, {"time": 0.3, "offset": 1, "vertices": [20.60001, 0, 0, 0, 0, 1e-05, 20.60001]}, {"time": 0.4, "offset": 1, "vertices": [-17.6701, 0, 0, 0, 0, 0, -17.6701]}, {"time": 0.5, "offset": 1, "vertices": [20.60001, 0, 0, 0, 0, 1e-05, 20.60001]}, {"time": 0.6, "offset": 1, "vertices": [-17.6701, 0, 0, 0, 0, 0, -17.6701]}, {"time": 0.7, "offset": 1, "vertices": [20.60001, 0, 0, 0, 0, 1e-05, 20.60001]}, {"time": 0.8, "offset": 1, "vertices": [-17.6701, 0, 0, 0, 0, 0, -17.6701]}, {"time": 0.9, "offset": 1, "vertices": [20.60001, 0, 0, 0, 0, 1e-05, 20.60001]}, {"time": 1, "offset": 1, "vertices": [-17.6701, 0, 0, 0, 0, 0, -17.6701]}]}}}}, "wild1": {"slots": {"di2": {"color": [{"time": 0, "color": "ffffff2e"}, {"time": 0.6, "color": "ffffff4a"}, {"time": 1.2667, "color": "ffffff2f"}, {"time": 1.8667, "color": "ffffff49"}, {"time": 2.5, "color": "ffffff2f"}], "attachment": [{"time": 0, "name": "di"}]}, "dsg0002": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "dsg0002"}, {"time": 0.7667, "name": "dsg0003"}, {"time": 0.8, "name": "dsg0004"}, {"time": 0.8333, "name": "dsg0005"}, {"time": 0.8667, "name": "dsg0006"}, {"time": 0.9333, "name": "dsg0007"}, {"time": 1.0333, "name": "dsg0008"}, {"time": 1.1, "name": "dsg0009"}, {"time": 1.1667, "name": "dsg0010"}, {"time": 1.2667, "name": "dsg0011"}, {"time": 1.3667, "name": "dsg0012"}, {"time": 1.4333, "name": "dsg0013"}, {"time": 1.4667, "name": "dsg0014"}, {"time": 1.5, "name": "dsg0015"}, {"time": 1.5333, "name": "dsg0016"}, {"time": 1.5667, "name": null}]}, "guangzhao": {"attachment": [{"time": 0, "name": null}]}, "isg0002": {"attachment": [{"time": 0, "name": null}, {"time": 0.4, "name": "isg0002"}, {"time": 0.4667, "name": "isg0003"}, {"time": 0.5333, "name": "isg0004"}, {"time": 0.6, "name": "isg0005"}, {"time": 0.8, "name": "isg0006"}, {"time": 0.9, "name": "isg0007"}, {"time": 0.9667, "name": "isg0008"}, {"time": 1, "name": null}]}, "jiesuo02_000": {"attachment": [{"time": 0, "name": null}]}, "lg0001": {"attachment": [{"time": 0.0333, "name": "lg1"}, {"time": 0.0667, "name": "lg2"}, {"time": 0.1, "name": "lg3"}, {"time": 0.1333, "name": "lg4"}, {"time": 0.1667, "name": "lg5"}, {"time": 0.2, "name": "lg6"}, {"time": 0.2333, "name": "lg7"}, {"time": 0.2667, "name": "lg8"}, {"time": 0.3, "name": "lg9"}, {"time": 0.3333, "name": "lg10"}, {"time": 0.3667, "name": "lg11"}, {"time": 0.4, "name": "lg12"}, {"time": 0.4333, "name": "lg13"}, {"time": 0.4667, "name": "lg14"}, {"time": 0.5, "name": "lg15"}, {"time": 0.5333, "name": "lg16"}, {"time": 0.5667, "name": "lg17"}, {"time": 0.6, "name": "lg18"}, {"time": 0.6333, "name": "lg19"}, {"time": 0.6667, "name": "lg20"}, {"time": 0.7, "name": "lg21"}, {"time": 0.7333, "name": "lg22"}, {"time": 0.7667, "name": "lg23"}, {"time": 0.8, "name": "lg24"}, {"time": 0.8333, "name": "lg0001"}, {"time": 0.8667, "name": "lg1"}, {"time": 0.9, "name": "lg2"}, {"time": 0.9333, "name": "lg3"}, {"time": 0.9667, "name": "lg4"}, {"time": 1, "name": "lg5"}, {"time": 1.0333, "name": "lg6"}, {"time": 1.0667, "name": "lg7"}, {"time": 1.1, "name": "lg8"}, {"time": 1.1333, "name": "lg9"}, {"time": 1.1667, "name": "lg10"}, {"time": 1.2, "name": "lg11"}, {"time": 1.2333, "name": "lg12"}, {"time": 1.2667, "name": "lg13"}, {"time": 1.3, "name": "lg14"}, {"time": 1.3333, "name": "lg15"}, {"time": 1.3667, "name": "lg16"}, {"time": 1.4, "name": "lg17"}, {"time": 1.4333, "name": "lg18"}, {"time": 1.4667, "name": "lg19"}, {"time": 1.5, "name": "lg20"}, {"time": 1.5333, "name": "lg21"}, {"time": 1.5667, "name": "lg22"}, {"time": 1.6, "name": "lg23"}, {"time": 1.6333, "name": "lg24"}, {"time": 1.6667, "name": "lg0001"}, {"time": 1.7, "name": "lg1"}, {"time": 1.7333, "name": "lg2"}, {"time": 1.7667, "name": "lg3"}, {"time": 1.8, "name": "lg4"}, {"time": 1.8333, "name": "lg5"}, {"time": 1.8667, "name": "lg6"}, {"time": 1.9, "name": "lg7"}, {"time": 1.9333, "name": "lg8"}, {"time": 1.9667, "name": "lg9"}, {"time": 2, "name": "lg10"}, {"time": 2.0333, "name": "lg11"}, {"time": 2.0667, "name": "lg12"}, {"time": 2.1, "name": "lg13"}, {"time": 2.1333, "name": "lg14"}, {"time": 2.1667, "name": "lg15"}, {"time": 2.2, "name": "lg16"}, {"time": 2.2333, "name": "lg17"}, {"time": 2.2667, "name": "lg18"}, {"time": 2.3, "name": "lg19"}, {"time": 2.3333, "name": "lg20"}, {"time": 2.3667, "name": "lg21"}, {"time": 2.4, "name": "lg22"}, {"time": 2.4333, "name": "lg23"}, {"time": 2.4667, "name": "lg24"}]}, "lg1": {"attachment": [{"time": 0, "name": "lg17"}, {"time": 0.0333, "name": "lg18"}, {"time": 0.0667, "name": "lg19"}, {"time": 0.1, "name": "lg20"}, {"time": 0.1333, "name": "lg21"}, {"time": 0.1667, "name": "lg22"}, {"time": 0.2, "name": "lg23"}, {"time": 0.2333, "name": "lg24"}, {"time": 0.2667, "name": "lg0001"}, {"time": 0.3, "name": "lg1"}, {"time": 0.3333, "name": "lg2"}, {"time": 0.3667, "name": "lg3"}, {"time": 0.4, "name": "lg4"}, {"time": 0.4333, "name": "lg5"}, {"time": 0.4667, "name": "lg6"}, {"time": 0.5, "name": "lg7"}, {"time": 0.5333, "name": "lg8"}, {"time": 0.5667, "name": "lg9"}, {"time": 0.6, "name": "lg10"}, {"time": 0.6333, "name": "lg11"}, {"time": 0.6667, "name": "lg12"}, {"time": 0.7, "name": "lg13"}, {"time": 0.7333, "name": "lg14"}, {"time": 0.7667, "name": "lg15"}, {"time": 0.8, "name": "lg16"}, {"time": 0.8333, "name": "lg17"}, {"time": 0.8667, "name": "lg18"}, {"time": 0.9, "name": "lg19"}, {"time": 0.9333, "name": "lg20"}, {"time": 0.9667, "name": "lg21"}, {"time": 1, "name": "lg22"}, {"time": 1.0333, "name": "lg23"}, {"time": 1.0667, "name": "lg24"}, {"time": 1.1, "name": "lg0001"}, {"time": 1.1333, "name": "lg1"}, {"time": 1.1667, "name": "lg2"}, {"time": 1.2, "name": "lg3"}, {"time": 1.2333, "name": "lg4"}, {"time": 1.2667, "name": "lg5"}, {"time": 1.3, "name": "lg6"}, {"time": 1.3333, "name": "lg7"}, {"time": 1.3667, "name": "lg8"}, {"time": 1.4, "name": "lg9"}, {"time": 1.4333, "name": "lg10"}, {"time": 1.4667, "name": "lg11"}, {"time": 1.5, "name": "lg12"}, {"time": 1.5333, "name": "lg13"}, {"time": 1.5667, "name": "lg14"}, {"time": 1.6, "name": "lg15"}, {"time": 1.6333, "name": "lg16"}, {"time": 1.6667, "name": "lg17"}, {"time": 1.7, "name": "lg18"}, {"time": 1.7333, "name": "lg19"}, {"time": 1.7667, "name": "lg20"}, {"time": 1.8, "name": "lg21"}, {"time": 1.8333, "name": "lg22"}, {"time": 1.8667, "name": "lg23"}, {"time": 1.9, "name": "lg24"}, {"time": 1.9333, "name": "lg0001"}, {"time": 1.9667, "name": "lg1"}, {"time": 2, "name": "lg2"}, {"time": 2.0333, "name": "lg3"}, {"time": 2.0667, "name": "lg4"}, {"time": 2.1, "name": "lg5"}, {"time": 2.1333, "name": "lg6"}, {"time": 2.1667, "name": "lg7"}, {"time": 2.2, "name": "lg8"}, {"time": 2.2333, "name": "lg9"}, {"time": 2.2667, "name": "lg10"}, {"time": 2.3, "name": "lg11"}, {"time": 2.3333, "name": "lg12"}, {"time": 2.3667, "name": "lg13"}, {"time": 2.4, "name": "lg14"}, {"time": 2.4333, "name": "lg15"}, {"time": 2.4667, "name": "lg16"}]}, "lg2": {"attachment": [{"time": 0, "name": "lg12"}, {"time": 0.0333, "name": "lg13"}, {"time": 0.0667, "name": "lg14"}, {"time": 0.1, "name": "lg15"}, {"time": 0.1333, "name": "lg16"}, {"time": 0.1667, "name": "lg17"}, {"time": 0.2, "name": "lg18"}, {"time": 0.2333, "name": "lg19"}, {"time": 0.2667, "name": "lg20"}, {"time": 0.3, "name": "lg21"}, {"time": 0.3333, "name": "lg22"}, {"time": 0.3667, "name": "lg23"}, {"time": 0.4, "name": "lg24"}, {"time": 0.4333, "name": "lg0001"}, {"time": 0.4667, "name": "lg1"}, {"time": 0.5, "name": "lg2"}, {"time": 0.5333, "name": "lg3"}, {"time": 0.5667, "name": "lg4"}, {"time": 0.6, "name": "lg5"}, {"time": 0.6333, "name": "lg6"}, {"time": 0.6667, "name": "lg7"}, {"time": 0.7, "name": "lg8"}, {"time": 0.7333, "name": "lg9"}, {"time": 0.7667, "name": "lg10"}, {"time": 0.8, "name": "lg11"}, {"time": 0.8333, "name": "lg12"}, {"time": 0.8667, "name": "lg13"}, {"time": 0.9, "name": "lg14"}, {"time": 0.9333, "name": "lg15"}, {"time": 0.9667, "name": "lg16"}, {"time": 1, "name": "lg17"}, {"time": 1.0333, "name": "lg18"}, {"time": 1.0667, "name": "lg19"}, {"time": 1.1, "name": "lg20"}, {"time": 1.1333, "name": "lg21"}, {"time": 1.1667, "name": "lg22"}, {"time": 1.2, "name": "lg23"}, {"time": 1.2333, "name": "lg24"}, {"time": 1.2667, "name": "lg0001"}, {"time": 1.3, "name": "lg1"}, {"time": 1.3333, "name": "lg2"}, {"time": 1.3667, "name": "lg3"}, {"time": 1.4, "name": "lg4"}, {"time": 1.4333, "name": "lg5"}, {"time": 1.4667, "name": "lg6"}, {"time": 1.5, "name": "lg7"}, {"time": 1.5333, "name": "lg8"}, {"time": 1.5667, "name": "lg9"}, {"time": 1.6, "name": "lg10"}, {"time": 1.6333, "name": "lg11"}, {"time": 1.6667, "name": "lg12"}, {"time": 1.7, "name": "lg13"}, {"time": 1.7333, "name": "lg14"}, {"time": 1.7667, "name": "lg15"}, {"time": 1.8, "name": "lg16"}, {"time": 1.8333, "name": "lg17"}, {"time": 1.8667, "name": "lg18"}, {"time": 1.9, "name": "lg19"}, {"time": 1.9333, "name": "lg20"}, {"time": 1.9667, "name": "lg21"}, {"time": 2, "name": "lg22"}, {"time": 2.0333, "name": "lg23"}, {"time": 2.0667, "name": "lg24"}, {"time": 2.1, "name": "lg0001"}, {"time": 2.1333, "name": "lg1"}, {"time": 2.1667, "name": "lg2"}, {"time": 2.2, "name": "lg3"}, {"time": 2.2333, "name": "lg4"}, {"time": 2.2667, "name": "lg5"}, {"time": 2.3, "name": "lg6"}, {"time": 2.3333, "name": "lg7"}, {"time": 2.3667, "name": "lg8"}, {"time": 2.4, "name": "lg9"}, {"time": 2.4333, "name": "lg10"}, {"time": 2.4667, "name": "lg11"}]}, "lg3": {"attachment": [{"time": 0, "name": "lg8"}, {"time": 0.0333, "name": "lg9"}, {"time": 0.0667, "name": "lg10"}, {"time": 0.1, "name": "lg11"}, {"time": 0.1333, "name": "lg12"}, {"time": 0.1667, "name": "lg13"}, {"time": 0.2, "name": "lg14"}, {"time": 0.2333, "name": "lg15"}, {"time": 0.2667, "name": "lg16"}, {"time": 0.3, "name": "lg17"}, {"time": 0.3333, "name": "lg18"}, {"time": 0.3667, "name": "lg19"}, {"time": 0.4, "name": "lg20"}, {"time": 0.4333, "name": "lg21"}, {"time": 0.4667, "name": "lg22"}, {"time": 0.5, "name": "lg23"}, {"time": 0.5333, "name": "lg24"}, {"time": 0.5667, "name": "lg0001"}, {"time": 0.6, "name": "lg1"}, {"time": 0.6333, "name": "lg2"}, {"time": 0.6667, "name": "lg3"}, {"time": 0.7, "name": "lg4"}, {"time": 0.7333, "name": "lg5"}, {"time": 0.7667, "name": "lg6"}, {"time": 0.8, "name": "lg7"}, {"time": 0.8333, "name": "lg8"}, {"time": 0.8667, "name": "lg9"}, {"time": 0.9, "name": "lg10"}, {"time": 0.9333, "name": "lg11"}, {"time": 0.9667, "name": "lg12"}, {"time": 1, "name": "lg13"}, {"time": 1.0333, "name": "lg14"}, {"time": 1.0667, "name": "lg15"}, {"time": 1.1, "name": "lg16"}, {"time": 1.1333, "name": "lg17"}, {"time": 1.1667, "name": "lg18"}, {"time": 1.2, "name": "lg19"}, {"time": 1.2333, "name": "lg20"}, {"time": 1.2667, "name": "lg21"}, {"time": 1.3, "name": "lg22"}, {"time": 1.3333, "name": "lg23"}, {"time": 1.3667, "name": "lg24"}, {"time": 1.4, "name": "lg0001"}, {"time": 1.4333, "name": "lg1"}, {"time": 1.4667, "name": "lg2"}, {"time": 1.5, "name": "lg3"}, {"time": 1.5333, "name": "lg4"}, {"time": 1.5667, "name": "lg5"}, {"time": 1.6, "name": "lg6"}, {"time": 1.6333, "name": "lg7"}, {"time": 1.6667, "name": "lg8"}, {"time": 1.7, "name": "lg9"}, {"time": 1.7333, "name": "lg10"}, {"time": 1.7667, "name": "lg11"}, {"time": 1.8, "name": "lg12"}, {"time": 1.8333, "name": "lg13"}, {"time": 1.8667, "name": "lg14"}, {"time": 1.9, "name": "lg15"}, {"time": 1.9333, "name": "lg16"}, {"time": 1.9667, "name": "lg17"}, {"time": 2, "name": "lg18"}, {"time": 2.0333, "name": "lg19"}, {"time": 2.0667, "name": "lg20"}, {"time": 2.1, "name": "lg21"}, {"time": 2.1333, "name": "lg22"}, {"time": 2.1667, "name": "lg23"}, {"time": 2.2, "name": "lg24"}, {"time": 2.2333, "name": "lg0001"}, {"time": 2.2667, "name": "lg1"}, {"time": 2.3, "name": "lg2"}, {"time": 2.3333, "name": "lg3"}, {"time": 2.3667, "name": "lg4"}, {"time": 2.4, "name": "lg5"}, {"time": 2.4333, "name": "lg6"}, {"time": 2.4667, "name": "lg7"}]}, "lg4": {"attachment": [{"time": 0, "name": "lg21"}, {"time": 0.0333, "name": "lg22"}, {"time": 0.0667, "name": "lg23"}, {"time": 0.1, "name": "lg24"}, {"time": 0.1333, "name": "lg0001"}, {"time": 0.1667, "name": "lg1"}, {"time": 0.2, "name": "lg2"}, {"time": 0.2333, "name": "lg3"}, {"time": 0.2667, "name": "lg4"}, {"time": 0.3, "name": "lg5"}, {"time": 0.3333, "name": "lg6"}, {"time": 0.3667, "name": "lg7"}, {"time": 0.4, "name": "lg8"}, {"time": 0.4333, "name": "lg9"}, {"time": 0.4667, "name": "lg10"}, {"time": 0.5, "name": "lg11"}, {"time": 0.5333, "name": "lg12"}, {"time": 0.5667, "name": "lg13"}, {"time": 0.6, "name": "lg14"}, {"time": 0.6333, "name": "lg15"}, {"time": 0.6667, "name": "lg16"}, {"time": 0.7, "name": "lg17"}, {"time": 0.7333, "name": "lg18"}, {"time": 0.7667, "name": "lg19"}, {"time": 0.8, "name": "lg20"}, {"time": 0.8333, "name": "lg21"}, {"time": 0.8667, "name": "lg22"}, {"time": 0.9, "name": "lg23"}, {"time": 0.9333, "name": "lg24"}, {"time": 0.9667, "name": "lg0001"}, {"time": 1, "name": "lg1"}, {"time": 1.0333, "name": "lg2"}, {"time": 1.0667, "name": "lg3"}, {"time": 1.1, "name": "lg4"}, {"time": 1.1333, "name": "lg5"}, {"time": 1.1667, "name": "lg6"}, {"time": 1.2, "name": "lg7"}, {"time": 1.2333, "name": "lg8"}, {"time": 1.2667, "name": "lg9"}, {"time": 1.3, "name": "lg10"}, {"time": 1.3333, "name": "lg11"}, {"time": 1.3667, "name": "lg12"}, {"time": 1.4, "name": "lg13"}, {"time": 1.4333, "name": "lg14"}, {"time": 1.4667, "name": "lg15"}, {"time": 1.5, "name": "lg16"}, {"time": 1.5333, "name": "lg17"}, {"time": 1.5667, "name": "lg18"}, {"time": 1.6, "name": "lg19"}, {"time": 1.6333, "name": "lg20"}, {"time": 1.6667, "name": "lg21"}, {"time": 1.7, "name": "lg22"}, {"time": 1.7333, "name": "lg23"}, {"time": 1.7667, "name": "lg24"}, {"time": 1.8, "name": "lg0001"}, {"time": 1.8333, "name": "lg1"}, {"time": 1.8667, "name": "lg2"}, {"time": 1.9, "name": "lg3"}, {"time": 1.9333, "name": "lg4"}, {"time": 1.9667, "name": "lg5"}, {"time": 2, "name": "lg6"}, {"time": 2.0333, "name": "lg7"}, {"time": 2.0667, "name": "lg8"}, {"time": 2.1, "name": "lg9"}, {"time": 2.1333, "name": "lg10"}, {"time": 2.1667, "name": "lg11"}, {"time": 2.2, "name": "lg12"}, {"time": 2.2333, "name": "lg13"}, {"time": 2.2667, "name": "lg14"}, {"time": 2.3, "name": "lg15"}, {"time": 2.3333, "name": "lg16"}, {"time": 2.3667, "name": "lg17"}, {"time": 2.4, "name": "lg18"}, {"time": 2.4333, "name": "lg19"}, {"time": 2.4667, "name": "lg20"}]}, "lg5": {"attachment": [{"time": 0, "name": "lg4"}, {"time": 0.0333, "name": "lg5"}, {"time": 0.0667, "name": "lg6"}, {"time": 0.1, "name": "lg7"}, {"time": 0.1333, "name": "lg8"}, {"time": 0.1667, "name": "lg9"}, {"time": 0.2, "name": "lg10"}, {"time": 0.2333, "name": "lg11"}, {"time": 0.2667, "name": "lg12"}, {"time": 0.3, "name": "lg13"}, {"time": 0.3333, "name": "lg14"}, {"time": 0.3667, "name": "lg15"}, {"time": 0.4, "name": "lg16"}, {"time": 0.4333, "name": "lg17"}, {"time": 0.4667, "name": "lg18"}, {"time": 0.5, "name": "lg19"}, {"time": 0.5333, "name": "lg20"}, {"time": 0.5667, "name": "lg21"}, {"time": 0.6, "name": "lg22"}, {"time": 0.6333, "name": "lg23"}, {"time": 0.6667, "name": "lg24"}, {"time": 0.7, "name": "lg0001"}, {"time": 0.7333, "name": "lg1"}, {"time": 0.7667, "name": "lg2"}, {"time": 0.8, "name": "lg3"}, {"time": 0.8333, "name": "lg4"}, {"time": 0.8667, "name": "lg5"}, {"time": 0.9, "name": "lg6"}, {"time": 0.9333, "name": "lg7"}, {"time": 0.9667, "name": "lg8"}, {"time": 1, "name": "lg9"}, {"time": 1.0333, "name": "lg10"}, {"time": 1.0667, "name": "lg11"}, {"time": 1.1, "name": "lg12"}, {"time": 1.1333, "name": "lg13"}, {"time": 1.1667, "name": "lg14"}, {"time": 1.2, "name": "lg15"}, {"time": 1.2333, "name": "lg16"}, {"time": 1.2667, "name": "lg17"}, {"time": 1.3, "name": "lg18"}, {"time": 1.3333, "name": "lg19"}, {"time": 1.3667, "name": "lg20"}, {"time": 1.4, "name": "lg21"}, {"time": 1.4333, "name": "lg22"}, {"time": 1.4667, "name": "lg23"}, {"time": 1.5, "name": "lg24"}, {"time": 1.5333, "name": "lg0001"}, {"time": 1.5667, "name": "lg1"}, {"time": 1.6, "name": "lg2"}, {"time": 1.6333, "name": "lg3"}, {"time": 1.6667, "name": "lg4"}, {"time": 1.7, "name": "lg5"}, {"time": 1.7333, "name": "lg6"}, {"time": 1.7667, "name": "lg7"}, {"time": 1.8, "name": "lg8"}, {"time": 1.8333, "name": "lg9"}, {"time": 1.8667, "name": "lg10"}, {"time": 1.9, "name": "lg11"}, {"time": 1.9333, "name": "lg12"}, {"time": 1.9667, "name": "lg13"}, {"time": 2, "name": "lg14"}, {"time": 2.0333, "name": "lg15"}, {"time": 2.0667, "name": "lg16"}, {"time": 2.1, "name": "lg17"}, {"time": 2.1333, "name": "lg18"}, {"time": 2.1667, "name": "lg19"}, {"time": 2.2, "name": "lg20"}, {"time": 2.2333, "name": "lg21"}, {"time": 2.2667, "name": "lg22"}, {"time": 2.3, "name": "lg23"}, {"time": 2.3333, "name": "lg24"}, {"time": 2.3667, "name": "lg0001"}, {"time": 2.4, "name": "lg1"}, {"time": 2.4333, "name": "lg2"}, {"time": 2.4667, "name": "lg3"}]}, "lsg0002": {"attachment": [{"time": 0, "name": null}, {"time": 0.6333, "name": "lsg0002"}, {"time": 0.7, "name": "lsg0003"}, {"time": 0.7667, "name": "lsg0004"}, {"time": 0.8333, "name": "lsg0005"}, {"time": 1.0333, "name": "lsg0006"}, {"time": 1.1333, "name": "lsg0007"}, {"time": 1.2, "name": "lsg0008"}, {"time": 1.2667, "name": null}]}, "lsg2": {"attachment": [{"time": 0, "name": null}, {"time": 0.5, "name": "lsg0002"}, {"time": 0.6, "name": "lsg0003"}, {"time": 0.6667, "name": "lsg0004"}, {"time": 0.7667, "name": "lsg0005"}, {"time": 1, "name": "lsg0006"}, {"time": 1.0667, "name": "lsg0007"}, {"time": 1.1667, "name": "lsg0008"}, {"time": 1.2667, "name": null}]}, "niushen": {"attachment": [{"time": 0, "name": null}]}, "niutou": {"attachment": [{"time": 0, "name": null}]}, "niuwei": {"attachment": [{"time": 0, "name": null}]}, "wsg0001": {"attachment": [{"time": 0, "name": null}, {"time": 0.2667, "name": "wsg1"}, {"time": 0.3, "name": "wsg2"}, {"time": 0.3333, "name": "wsg3"}, {"time": 0.3667, "name": "wsg4"}, {"time": 0.4333, "name": "wsg5"}, {"time": 0.5333, "name": "wsg6"}, {"time": 0.6, "name": "wsg7"}, {"time": 0.7, "name": "wsg8"}, {"time": 0.8, "name": "wsg9"}, {"time": 0.8667, "name": "wsg10"}, {"time": 0.9667, "name": "wsg11"}, {"time": 1.0333, "name": "wsg12"}, {"time": 1.0667, "name": "wsg13"}, {"time": 1.1, "name": "wsg14"}, {"time": 1.1333, "name": "wsg15"}, {"time": 1.1667, "name": null}]}}, "bones": {"W": {"scale": [{"time": 0, "x": 1.116, "y": 1.116, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0.868, "y": 0.868, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1.116, "y": 1.116, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": -0.024, "y": 1.012, "curve": [0.338, 0.35, 0.758, 1]}, {"time": 1.3667, "x": 1.041, "y": 1.041, "curve": [0.273, 0, 0.62, 0.41]}, {"time": 1.4333, "x": -0.024, "y": 1.012, "curve": [0.338, 0.35, 0.758, 1]}, {"time": 1.5, "x": 1.116, "y": 1.116, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0.868, "y": 0.868, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": 1.116, "y": 1.116}]}, "i": {"scale": [{"time": 0, "x": 1.011, "y": 1.011, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 1.041, "y": 1.041, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0.902, "y": 0.902, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 1.041, "y": 1.041}, {"time": 1.4333, "x": 0.04, "y": 1.035}, {"time": 1.4667, "x": -1.065, "y": 1.041}, {"time": 1.5333, "x": 0.04, "y": 1.035}, {"time": 1.5667, "x": 1.041, "y": 1.041, "curve": [0.324, 0, 0.658, 0.34]}, {"time": 1.9333, "x": 0.902, "y": 0.902, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": 1.011, "y": 1.011}]}, "L": {"scale": [{"time": 0, "x": 0.973, "y": 0.973, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.041, "y": 1.041, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0.902, "y": 0.902, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1.041, "y": 1.041}, {"time": 1.6, "x": 0.066, "y": 1.033}, {"time": 1.6667, "x": -0.983, "y": 1.017}, {"time": 1.7, "x": 0.066, "y": 1.033}, {"time": 1.7667, "x": 1.041, "y": 1.041, "curve": [0.3, 0, 0.636, 0.36]}, {"time": 2.1667, "x": 0.902, "y": 0.902, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": 0.973, "y": 0.973}]}, "D": {"scale": [{"time": 0, "x": 0.936, "y": 0.936, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1.041, "y": 1.041, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 0.902, "y": 0.902, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 1.041, "y": 1.041, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 0.029, "y": 1.041, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": -0.897, "y": 1.041, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "x": 0.029, "y": 1.041, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1.041, "y": 1.041, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3667, "x": 0.902, "y": 0.902, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": 0.936, "y": 0.936}]}, "i4": {"translate": [{"time": 1.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 8.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "x": 0, "y": 0}], "scale": [{"time": 1.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "x": 1.315, "y": 1.315, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "x": 1, "y": 1}]}}, "deform": {"default": {"D": {"D": [{"time": 0, "offset": 2, "vertices": [-0.146, 0, -0.511, 0, -0.511, -0.21899, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.32807, -0.17156, 0.3875, 0, 0.0995, 0, 0.0385, 0, -0.0555, 0.2295, -0.3975, 0.2295, 0.3675, 0, 0.7095, 0, 0.5775, 0, 0.425, 0.21, 0.425, 0.117, 0.609, 0.21, 0.42, 0.336, 0.294, 0.1155, 0.505, 0.188, 0.505, -0.3535, -0.105, -0.105, -0.105, 0, -0.51249, -0.205, -0.50399, 0, -0.504, 0, -0.50442, 1e-05, 0, 0, -0.187, -0.024, 0, 0, 0, -0.715, 0, -0.258, -0.392, 0.329, -1.015, 0.616, -0.979, 0.32899, -0.3915, 0.11951, -0.4935, -0.44801, 0, -0.192, 0.1455, -0.23801, 0.1455, -0.52701, 0, 0, -0.187, 0.3045, -0.047, -0.50199, -0.34799, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.61199, -0.217, -0.41249, -0.103, 0.3225, 0.10702, 0.525, 0.1575, 0, 0, 0, 0, 0, 0, 0.22, -0.55, 0.28, -0.28, 0.72, -0.28, 0.56, 0, 0.392], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "offset": 22, "vertices": [0.306, 0, 1.41899, 0, 1.41899, 0, 1.41899, 0, 1.41899, 0, 1.806, 0, 1.806, 0, 1.419, 0, 1.155, 0, 1.47, 0.42, 1.47, 0.42, 1.47, 0.42, 1.47, 0.42, 1.47, 0.42, 1.01, 0.37599, 1.01, -0.707, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.75999, 1.54999, 0, 0, 0, -1.42999, 0, -1.29999, 0, -0.90999, -0.91, 0, -0.39001, -0.91, 0.204, -0.748, 0, -0.89601, 0, -0.384, 1.84499, 0, 1.84499, -2.14, 0, 0, -1.75999, 0, -1.02599, -1.00398, -1.62798, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -2.15598, -0.43401, -1.75698, -0.20599, -0.287, 0.21404, 1.05, 0.315, 0, 0, 0, 0, 0, 0, 0.44, -1.10001, 0.56, -0.56, 1.44, -0.56, 1.12, 0, 0.784], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "offset": 2, "vertices": [-0.292, 0, -1.022, 0, -1.022, -0.43799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.96214, -0.34312, -0.644, 0, -1.21999, 0, -1.342, 0, -1.52999, 0.459, -2.601, 0.459, -1.071, 0, 0, 0, 0, 0, -0.62, 0, -0.61999, -0.186, -0.252, 0, -0.63, 0.252, -0.882, -0.189, 0, 0, 0, 0, -0.21, -0.20999, -0.21, 0, -1.02499, -0.41, -1.00799, 0, -1.008, 0, -1.00884, 2e-05, 0, 0, 1.38599, -1.59799, 0, 0, 0, 0, 0, 0.784, -0.78399, 1.56799, -1.12, 1.23199, -1.56799, 1.56799, -0.987, 0.98701, -0.987, 0, 0, 0, -1.554, -0.47601, -1.554, 1.08598, 0, 0, 1.38599, 0.60899, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "offset": 22, "vertices": [0.306, 0, 1.41899, 0, 1.41899, 0, 1.41899, 0, 1.41899, 0, 1.806, 0, 1.806, 0, 1.419, 0, 1.155, 0, 1.47, 0.42, 1.47, 0.42, 1.47, 0.42, 1.47, 0.42, 1.47, 0.42, 1.01, 0.37599, 1.01, -0.707, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.75999, 1.54999, 0, 0, 0, -1.42999, 0, -1.29999, 0, -0.90999, -0.91, 0, -0.39001, -0.91, 0.204, -0.748, 0, -0.89601, 0, -0.384, 1.84499, 0, 1.84499, -2.14, 0, 0, -1.75999, 0, -1.02599, -1.00398, -1.62798, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -1.75698, -1.00398, -2.15598, -0.43401, -1.75698, -0.20599, -0.287, 0.21404, 1.05, 0.315, 0, 0, 0, 0, 0, 0, 0.44, -1.10001, 0.56, -0.56, 1.44, -0.56, 1.12, 0, 0.784], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "offset": 2, "vertices": [-0.292, 0, -1.022, 0, -1.022, -0.43799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.96214, -0.34312, -0.644, 0, -1.21999, 0, -1.342, 0, -1.52999, 0.459, -2.601, 0.459, -1.071, 0, 0, 0, 0, 0, -0.62, 0, -0.61999, -0.186, -0.252, 0, -0.63, 0.252, -0.882, -0.189, 0, 0, 0, 0, -0.21, -0.20999, -0.21, 0, -1.02499, -0.41, -1.00799, 0, -1.008, 0, -1.00884, 2e-05, 0, 0, 1.38599, -1.59799, 0, 0, 0, 0, 0, 0.784, -0.78399, 1.56799, -1.12, 1.23199, -1.56799, 1.56799, -0.987, 0.98701, -0.987, 0, 0, 0, -1.554, -0.47601, -1.554, 1.08598, 0, 0, 1.38599, 0.60899, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932, 0, 0.932], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "offset": 2, "vertices": [-0.146, 0, -0.511, 0, -0.511, -0.21899, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.32807, -0.17156, 0.3875, 0, 0.0995, 0, 0.0385, 0, -0.0555, 0.2295, -0.3975, 0.2295, 0.3675, 0, 0.7095, 0, 0.5775, 0, 0.425, 0.21, 0.425, 0.117, 0.609, 0.21, 0.42, 0.336, 0.294, 0.1155, 0.505, 0.188, 0.505, -0.3535, -0.105, -0.105, -0.105, 0, -0.51249, -0.205, -0.50399, 0, -0.504, 0, -0.50442, 1e-05, 0, 0, -0.187, -0.024, 0, 0, 0, -0.715, 0, -0.258, -0.392, 0.329, -1.015, 0.616, -0.979, 0.32899, -0.3915, 0.11951, -0.4935, -0.44801, 0, -0.192, 0.1455, -0.23801, 0.1455, -0.52701, 0, 0, -0.187, 0.3045, -0.047, -0.50199, -0.34799, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.41249, -0.50199, -0.61199, -0.217, -0.41249, -0.103, 0.3225, 0.10702, 0.525, 0.1575, 0, 0, 0, 0, 0, 0, 0.22, -0.55, 0.28, -0.28, 0.72, -0.28, 0.56, 0, 0.392]}]}, "L": {"L": [{"time": 0, "offset": 4, "vertices": [-1.35469, -0.52463, 0, 0, 1.52652, 0, 1.52652, 0, 1.47895, 0.31158, 0.90211, 0.33053, -0.75499, -1.13962, 0.01547, -1.4302, -1.33368, -0.61068, -0.57847, 0.04789, -1.25151, 0.01437, -0.8072, 0.01437, 0.37263, 0.50905, 0.88041, -0.06947, 0.03474, 1.49051, 0, 0, 0, 0, 0.04842, 0, 0.06779, 0.03389, 0.9981, -0.056, 0, 0, -1.35469, 0, -0.85437, 0, -0.83478, 0, -0.45394, -0.38085, -0.40895, -0.42363], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "offset": 4, "vertices": [0.721, 0.49, 0, 0, -1.59599, 0, -1.59599, 0, -0.69999, -0.19999, -0.49999, -0.2, -1.92499, -0.53899, 0.294, 0.042, 0.364, 0.637, 2.27499, 0.91, 1.547, 0.273, 1.547, 0.273, 0.6, -1.01999, 0.59999, -1.31998, 0.65999, -0.48, 0, 0, 0, 0, 0.91999, 0, 1.288, 0.644, -0.44001, -0.56001, 0, 0, 0.721, 0, 0.651, 0, 1.02299, 0, 1.02299, 0, 1.30199, 1.02299], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "offset": 4, "vertices": [-1.47, -0.581, 0, 0, 1.69999, 0, 1.69999, 0, 1.6, 0.34, 0.98, 0.36, -0.68999, -1.17299, 0, -1.51199, -1.42799, -0.68, -0.73699, 0, -1.40698, 0, -0.93799, 0, 0.35999, 0.594, 0.89599, 0, 0, 1.59998, 0, 0, 0, 0, 0, 0, 0, 0, 1.078, -0.028, 0, 0, -1.47, 0, -0.938, 0, -0.93799, 0, -0.53599, -0.40201, -0.504, -0.504], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "offset": 4, "vertices": [0.721, 0.49, 0, 0, -1.59599, 0, -1.59599, 0, -0.69999, -0.19999, -0.49999, -0.2, -1.92499, -0.53899, 0.294, 0.042, 0.364, 0.637, 2.27499, 0.91, 1.547, 0.273, 1.547, 0.273, 0.6, -1.01999, 0.59999, -1.31998, 0.65999, -0.48, 0, 0, 0, 0, 0.91999, 0, 1.288, 0.644, -0.44001, -0.56001, 0, 0, 0.721, 0, 0.651, 0, 1.02299, 0, 1.02299, 0, 1.30199, 1.02299], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4333, "offset": 4, "vertices": [-1.47, -0.581, 0, 0, 1.69999, 0, 1.69999, 0, 1.6, 0.34, 0.98, 0.36, -0.68999, -1.17299, 0, -1.51199, -1.42799, -0.68, -0.73699, 0, -1.40698, 0, -0.93799, 0, 0.35999, 0.594, 0.89599, 0, 0, 1.59998, 0, 0, 0, 0, 0, 0, 0, 0, 1.078, -0.028, 0, 0, -1.47, 0, -0.938, 0, -0.93799, 0, -0.53599, -0.40201, -0.504, -0.504], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "offset": 4, "vertices": [-1.35469, -0.52463, 0, 0, 1.52652, 0, 1.52652, 0, 1.47895, 0.31158, 0.90211, 0.33053, -0.75499, -1.13962, 0.01547, -1.4302, -1.33368, -0.61068, -0.57847, 0.04789, -1.25151, 0.01437, -0.8072, 0.01437, 0.37263, 0.50905, 0.88041, -0.06947, 0.03474, 1.49051, 0, 0, 0, 0, 0.04842, 0, 0.06779, 0.03389, 0.9981, -0.056, 0, 0, -1.35469, 0, -0.85437, 0, -0.83478, 0, -0.45394, -0.38085, -0.40895, -0.42363]}]}, "W": {"W": [{"time": 0, "vertices": [0.21368, 0, 0.10684, 0.04579, 0.34237, -0.40284, 0.31095, -0.29368, -0.41374, -0.23363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.41511, 0, 0.19684, 0, -0.58799, 0, -0.24352, 0, 0.2978, 0, -0.11679, 0, -0.11679, 0, 1.40294, 0, 0.31916, 0.20632, 0, 0, 0, 0, 0, 0, -0.14258, 0, -0.14258, 0, 0.56221, 0, 0.76262, -0.12041, 0, 0, 0, 0, 0, 0, 0, 0, -0.16836, 0, -0.16836, 0, 0.10105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12379, 0.69557, -0.45389, 1.40294, -0.57767, 0.56221, 0, 0, 0, 0.57768, 0, 0.57768, 0, -0.24758, 0, 0.05563, 0, 0.45242, 0, 0.59226, 0, 0.59226, 0, 0.18495, 0, 0.1901, 0, 0.70294, 0, 1.13252, 0, 0, 0.11716, -0.15621, 0.27337, 0.76262, 0, 0.4222, 0, 0.88515, -0.12379, 0.31916, 0, 0.002, 0, 0.15289], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "offset": 4, "vertices": [-0.49, -0.68599, -0.343, -0.48999, -1.029, -0.196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.298, 0, 0, 0, -0.79799, 0, -0.79799, 0, -0.79799, 0, -0.721, 0, -0.721, 0, 1.904, 0, 1.17599, 0.66, 0, 0, 0, 0, 0, 0, -0.756, 0, -0.756, 0, 0.76299, 0, 1.65999, -0.46199, 0, 0, 0, 0, 0, 0, 0, 0, -0.79099, 0, -0.79099, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.168, 0.94399, -0.616, 1.90399, -0.78398, 0.76299, 0, 0, 0, 0.78399, 0, 0.78399, 0, -0.33599, 0, 0.623, 0, 0.979, 0, 1.342, 0, 1.342, 0, 0.90099, 0, 0.58299, 0, 0.95399, 0, 1.53699, 0, 0, 0.159, -0.212, 0.371, 1.65999, 0, 1.00799, 0, 1.51199, -0.168, 1.17599, 0, 0.69699, 0, 0.41], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "vertices": [0.812, 0, 0.40599, 0.174, 2.67298, 0.38997, 2.14199, 0.25598, 1.30899, -0.339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.05698, 0, 0.748, 0, 0, 0, 1.309, 0, 3.36599, 0, 1.575, 0, 1.575, 0, 0, 0, -2.07998, -1.064, 0, 0, 0, 0, 0, 0, 1.575, 0, 1.575, 0, 0, 0, -1.75, 0.836, 0, 0, 0, 0, 0, 0, 0, 0, 1.575, 0, 1.575, 0, 0.384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.533, 0, -1.022, 0, -1.50699, 0, -1.50699, 0, -1.81998, 0, -0.90999, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.74999, 0, -1.218, 0, -0.87, 0, -2.07998, 0, -1.94398, 0, -0.56699], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "offset": 4, "vertices": [-0.49, -0.68599, -0.343, -0.48999, -1.029, -0.196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.298, 0, 0, 0, -0.79799, 0, -0.79799, 0, -0.79799, 0, -0.721, 0, -0.721, 0, 1.904, 0, 1.17599, 0.66, 0, 0, 0, 0, 0, 0, -0.756, 0, -0.756, 0, 0.76299, 0, 1.65999, -0.46199, 0, 0, 0, 0, 0, 0, 0, 0, -0.79099, 0, -0.79099, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.168, 0.94399, -0.616, 1.90399, -0.78398, 0.76299, 0, 0, 0, 0.78399, 0, 0.78399, 0, -0.33599, 0, 0.623, 0, 0.979, 0, 1.342, 0, 1.342, 0, 0.90099, 0, 0.58299, 0, 0.95399, 0, 1.53699, 0, 0, 0.159, -0.212, 0.371, 1.65999, 0, 1.00799, 0, 1.51199, -0.168, 1.17599, 0, 0.69699, 0, 0.41], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0333, "vertices": [0.812, 0, 0.40599, 0.174, 2.67298, 0.38997, 2.14199, 0.25598, 1.30899, -0.339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.05698, 0, 0.748, 0, 0, 0, 1.309, 0, 3.36599, 0, 1.575, 0, 1.575, 0, 0, 0, -2.07998, -1.064, 0, 0, 0, 0, 0, 0, 1.575, 0, 1.575, 0, 0, 0, -1.75, 0.836, 0, 0, 0, 0, 0, 0, 0, 0, 1.575, 0, 1.575, 0, 0.384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.533, 0, -1.022, 0, -1.50699, 0, -1.50699, 0, -1.81998, 0, -0.90999, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.74999, 0, -1.218, 0, -0.87, 0, -2.07998, 0, -1.94398, 0, -0.56699], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "vertices": [0.21368, 0, 0.10684, 0.04579, 0.34237, -0.40284, 0.31095, -0.29368, -0.41374, -0.23363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.41511, 0, 0.19684, 0, -0.58799, 0, -0.24352, 0, 0.2978, 0, -0.11679, 0, -0.11679, 0, 1.40294, 0, 0.31916, 0.20632, 0, 0, 0, 0, 0, 0, -0.14258, 0, -0.14258, 0, 0.56221, 0, 0.76262, -0.12041, 0, 0, 0, 0, 0, 0, 0, 0, -0.16836, 0, -0.16836, 0, 0.10105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12379, 0.69557, -0.45389, 1.40294, -0.57767, 0.56221, 0, 0, 0, 0.57768, 0, 0.57768, 0, -0.24758, 0, 0.05563, 0, 0.45242, 0, 0.59226, 0, 0.59226, 0, 0.18495, 0, 0.1901, 0, 0.70294, 0, 1.13252, 0, 0, 0.11716, -0.15621, 0.27337, 0.76262, 0, 0.4222, 0, 0.88515, -0.12379, 0.31916, 0, 0.002, 0, 0.15289]}]}, "i": {"i": [{"time": 0, "offset": 4, "vertices": [0.51358, 0.43537, 0.27789, 0, 0.34158, 0, 0.34158, 0, 0.34042, -0.24758, 0, -0.24758, -0.30737, -0.43032, -0.65821, 0.35222, 0.09726, 0, 0.09726, 0, -0.05347, 0, 0.12968, 0, 0.35663, -0.15284, 0, 0, -0.49936, 0, -0.40678, 0, -1.16472, 0, -1.15567, 0, 0.14589, 0.51284, 0.27789, 0, 0.51358, 0, -0.40426, 0.22484, -0.65283, 0.19705, -0.83016, -0.29457], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "offset": 4, "vertices": [1.778, 1.034, 0.66, 0, -1.397, 0, -1.397, 0, 0, -0.588, 0, -0.588, -0.72999, -1.022, -2.40098, -0.35999, -1.1, 0, -1.1, 0, -0.48999, 0, -0.539, 0, 0, 0, 0, 0, -1.18598, 0, -0.59898, 0, -1.05297, 0, -0.54199, 0, -0.462, 0.756, 0.66, 0, 1.778, 0, -0.01, 0.534, -0.91798, 0.46799, -0.25288, -0.21561], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "offset": 4, "vertices": [-0.40599, 0, 0, 0, 1.606, 0, 1.606, 0, 0.588, 0, 0, 0, 0, 0, 0.60926, 0.8702, 0.96799, 0, 0.96799, 0, 0.264, 0, 0.61599, 0, 0.61599, -0.264, 0, 0, 0, 0, -0.26701, 0, -1.246, 0, -1.60199, 0, 0.588, 0.336, 0, 0, -0.40599, 0, -0.691, 0, -0.46, 0, -1.25, -0.352], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "offset": 4, "vertices": [1.778, 1.034, 0.66, 0, -1.397, 0, -1.397, 0, 0, -0.588, 0, -0.588, -0.72999, -1.022, -2.40098, -0.35999, -1.1, 0, -1.1, 0, -0.48999, 0, -0.539, 0, 0, 0, 0, 0, -1.18598, 0, -0.59898, 0, -1.05297, 0, -0.54199, 0, -0.462, 0.756, 0.66, 0, 1.778, 0, -0.01, 0.534, -0.91798, 0.46799, -0.25288, -0.21561], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "offset": 4, "vertices": [-0.40599, 0, 0, 0, 1.606, 0, 1.606, 0, 0.588, 0, 0, 0, 0, 0, 0.60926, 0.8702, 0.96799, 0, 0.96799, 0, 0.264, 0, 0.61599, 0, 0.61599, -0.264, 0, 0, 0, 0, -0.26701, 0, -1.246, 0, -1.60199, 0, 0.588, 0.336, 0, 0, -0.40599, 0, -0.691, 0, -0.46, 0, -1.25, -0.352], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "offset": 4, "vertices": [0.51358, 0.43537, 0.27789, 0, 0.34158, 0, 0.34158, 0, 0.34042, -0.24758, 0, -0.24758, -0.30737, -0.43032, -0.65821, 0.35222, 0.09726, 0, 0.09726, 0, -0.05347, 0, 0.12968, 0, 0.35663, -0.15284, 0, 0, -0.49936, 0, -0.40678, 0, -1.16472, 0, -1.15567, 0, 0.14589, 0.51284, 0.27789, 0, 0.51358, 0, -0.40426, 0.22484, -0.65283, 0.19705, -0.83016, -0.29457]}]}, "i2": {"i2": [{"time": 0, "vertices": [0.55747, 0, 0, 0, -0.52531, 0, -0.81853, 0, -0.46926, 0.17463, -0.54157, 0.17463, 0, 0, 0.12642], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "vertices": [1.07, 0, 0, 0, -1.02299, 0, -1.44, 0, -1.44, 0, -1.30199, 0, 0, 0, 0.44], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "vertices": [-0.553, 0, 0, 0, 0.553, 0, 0.52799, 0, 1.63399, 0.55299, 1.106, 0.55299, 0, 0, -0.55299], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "vertices": [1.07, 0, 0, 0, -1.02299, 0, -1.44, 0, -1.44, 0, -1.30199, 0, 0, 0, 0.44], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0333, "vertices": [-0.553, 0, 0, 0, 0.553, 0, 0.52799, 0, 1.63399, 0.55299, 1.106, 0.55299, 0, 0, -0.55299], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "vertices": [0.55747, 0, 0, 0, -0.52531, 0, -0.81853, 0, -0.46926, 0.17463, -0.54157, 0.17463, 0, 0, 0.12642]}]}}}}}}