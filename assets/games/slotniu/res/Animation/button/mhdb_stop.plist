<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>btn_stop.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{332,125}}</string>
                <key>offset</key>
                <string>{0,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{332,125}}</string>
                <key>sourceSize</key>
                <string>{332,129}</string>
            </dict>
            <key>mhdb_stop1_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{336,2},{177,44}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{177,44}}</string>
                <key>sourceSize</key>
                <string>{177,44}</string>
            </dict>
            <key>mhdb_stop_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{471,52},{21,38}}</string>
                <key>offset</key>
                <string>{-82,7}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{21,38}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00001.png</key>
            <dict>
                <key>frame</key>
                <string>{{451,149},{37,46}}</string>
                <key>offset</key>
                <string>{-74,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,1},{37,46}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00002.png</key>
            <dict>
                <key>frame</key>
                <string>{{392,179},{57,48}}</string>
                <key>offset</key>
                <string>{-64,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{57,48}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00003.png</key>
            <dict>
                <key>frame</key>
                <string>{{382,102},{75,48}}</string>
                <key>offset</key>
                <string>{-55,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,48}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00004.png</key>
            <dict>
                <key>frame</key>
                <string>{{382,52},{87,48}}</string>
                <key>offset</key>
                <string>{-49,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{87,48}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00005.png</key>
            <dict>
                <key>frame</key>
                <string>{{125,129},{111,48}}</string>
                <key>offset</key>
                <string>{-37,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{111,48}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00006.png</key>
            <dict>
                <key>frame</key>
                <string>{{91,179},{115,48}}</string>
                <key>offset</key>
                <string>{-24,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{11,0},{115,48}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00007.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,129},{121,48}}</string>
                <key>offset</key>
                <string>{-8,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{24,0},{121,48}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00008.png</key>
            <dict>
                <key>frame</key>
                <string>{{382,2},{103,48}}</string>
                <key>offset</key>
                <string>{14,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{55,0},{103,48}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00009.png</key>
            <dict>
                <key>frame</key>
                <string>{{208,179},{111,48}}</string>
                <key>offset</key>
                <string>{23,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{60,0},{111,48}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00010.png</key>
            <dict>
                <key>frame</key>
                <string>{{238,129},{95,48}}</string>
                <key>offset</key>
                <string>{40,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{85,0},{95,48}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00011.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,181},{87,46}}</string>
                <key>offset</key>
                <string>{46,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{95,2},{87,46}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00012.png</key>
            <dict>
                <key>frame</key>
                <string>{{321,181},{69,46}}</string>
                <key>offset</key>
                <string>{55,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{113,1},{69,46}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00013.png</key>
            <dict>
                <key>frame</key>
                <string>{{432,102},{45,46}}</string>
                <key>offset</key>
                <string>{67,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{137,1},{45,46}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
            <key>mhdb_stop_00014.png</key>
            <dict>
                <key>frame</key>
                <string>{{451,188},{33,42}}</string>
                <key>offset</key>
                <string>{73,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{149,3},{33,42}}</string>
                <key>sourceSize</key>
                <string>{185,52}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>mhdb_stop.png</string>
            <key>size</key>
            <string>{499,229}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:a2df8282f175742d26280f87cfc1fcdf:20588748a182eb4b4c015db29a4e8c43:26196ea83e66dca00638579a9dd82402$</string>
            <key>textureFileName</key>
            <string>mhdb_stop.png</string>
        </dict>
    </dict>
</plist>
