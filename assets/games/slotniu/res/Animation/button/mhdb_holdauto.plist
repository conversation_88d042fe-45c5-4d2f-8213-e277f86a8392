<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>icon_holdauto.png</key>
            <dict>
                <key>frame</key>
                <string>{{1430,2},{251,37}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{251,37}}</string>
                <key>sourceSize</key>
                <string>{251,37}</string>
            </dict>
            <key>mhdb_holdauto1_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{1683,2},{251,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{251,36}}</string>
                <key>sourceSize</key>
                <string>{251,36}</string>
            </dict>
            <key>mhdb_holdauto_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{1413,2},{15,38}}</string>
                <key>offset</key>
                <string>{-122,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{15,38}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00001.png</key>
            <dict>
                <key>frame</key>
                <string>{{1364,2},{47,38}}</string>
                <key>offset</key>
                <string>{-106,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,1},{47,38}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00002.png</key>
            <dict>
                <key>frame</key>
                <string>{{1167,2},{75,40}}</string>
                <key>offset</key>
                <string>{-92,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00003.png</key>
            <dict>
                <key>frame</key>
                <string>{{965,2},{101,40}}</string>
                <key>offset</key>
                <string>{-79,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{101,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00004.png</key>
            <dict>
                <key>frame</key>
                <string>{{719,2},{121,40}}</string>
                <key>offset</key>
                <string>{-66,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{3,1},{121,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00005.png</key>
            <dict>
                <key>frame</key>
                <string>{{584,2},{133,40}}</string>
                <key>offset</key>
                <string>{-42,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{21,1},{133,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00006.png</key>
            <dict>
                <key>frame</key>
                <string>{{306,2},{139,40}}</string>
                <key>offset</key>
                <string>{-23,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{37,1},{139,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00007.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{155,42}}</string>
                <key>offset</key>
                <string>{-1,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{51,0},{155,42}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00008.png</key>
            <dict>
                <key>frame</key>
                <string>{{159,2},{145,42}}</string>
                <key>offset</key>
                <string>{21,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{78,0},{145,42}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00009.png</key>
            <dict>
                <key>frame</key>
                <string>{{447,2},{135,40}}</string>
                <key>offset</key>
                <string>{42,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{104,0},{135,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00010.png</key>
            <dict>
                <key>frame</key>
                <string>{{842,2},{121,40}}</string>
                <key>offset</key>
                <string>{62,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{131,0},{121,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00011.png</key>
            <dict>
                <key>frame</key>
                <string>{{1068,2},{97,40}}</string>
                <key>offset</key>
                <string>{77,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{158,0},{97,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00012.png</key>
            <dict>
                <key>frame</key>
                <string>{{1244,2},{71,40}}</string>
                <key>offset</key>
                <string>{90,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{184,0},{71,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00013.png</key>
            <dict>
                <key>frame</key>
                <string>{{1317,2},{45,40}}</string>
                <key>offset</key>
                <string>{103,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{210,0},{45,40}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
            <key>mhdb_holdauto_00014.png</key>
            <dict>
                <key>frame</key>
                <string>{{1936,2},{19,30}}</string>
                <key>offset</key>
                <string>{116,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{236,10},{19,30}}</string>
                <key>sourceSize</key>
                <string>{259,44}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>mhdb_holdauto.png</string>
            <key>size</key>
            <string>{1957,46}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:68db929838e501408e722c293a3cb2b4:6a48053913456c6017d2a6f66a8984de:d9bf3b32683a3332a0682293f72dea10$</string>
            <key>textureFileName</key>
            <string>mhdb_holdauto.png</string>
        </dict>
    </dict>
</plist>
