import { LanguageType } from "../../../../script/frame/common/Define";
import Config from "../../../../script/frame/config/Config";
let _initChid = Config.CHANNEL_ID;

//////////////////////////////////////////////////////////////////////////////////
// 基础数据
export let Constant = {
    ROW_MAX: 5, //最大显示行数
    ROW_SHOW: 3,//实际可见行数
    COL_SHOW: 5, //实际可见列数
    ELEMENT_MAX_NUM: 11,//11种图标
    MAX_LINES : 9 ,   /**最大线数 */
    SCROLL_VIEW_NUM : 5,//滑动界面尺寸的个数（用于控制速度）
    BET_RATE_NUM: 9, //下注固定倍率
    NIU_COIN_MAX_NUM : 2,//出现1个或2个牛头要收集牛金币,2个以上进入免费模式
    DESK_PLAYERS_MAX_NUM : 4,//桌上最大玩家数
    customMyselfPos: 4, //处理道具位置是自己时，自定义的一个位置   
    
    BET_NUM_CONFIG_LIST: [1.8,9,18,90,180,900,1800],//下注金额配置
    
    LOCAL_STORAGE_BET_NUM_KEY: 'FMW_BET_NUM_KEY',//下注本地缓存值
    NOT_SHOW_LU_NIU_ROW_INDEX: 4,  //第4行不随机图标 鹿及牛 超出了显示
    SPIN_LIMIT_TIME : 0.3, //按下spin时长
    SHOW_ICON_PRIZE_ANI_MIN: 6,//图标6及以上图标 才显示中奖动画 


    PLAYER_COUNT:       1,                      //玩家个数

    //数值定义
    ICON_ROW:           3,       			    //图案行数
    ICON_COL:           5,       			    //图案列数
    ICON_COUNT:         11,      				//图案总数量
    REWARD_LEAST_COL:   3,       				//获奖最少列数
    PAY_CASE_COUNT:     4,       				//返奖方案
    CHEAT_CASE_COUNT:   4,       				//血控方案

    MAX_REWARD_ICON_COUNT:  3,       			//最大获奖励图案

    SCORE_TIME:			100,		            //玩家身上金币*100 倍。为了控制精度，因此不带浮点数运算；json格式对浮点数支持不好
    REWARD_TIME:		10,		                //奖励倍数*10倍。json格式对浮点数支持不好
    BIG_REWARD_CONDITION:   15,                 //15倍为超级大奖
}

// 奖池倍率客户端写死
export const JACKPOT_PERCENT = [
	[2, 10, 20, 100, 200, 1000, 2000],                    //3头牛
	[3, 15, 30, 150, 300, 1500, 3000],
	[4, 20, 40, 200, 400, 2000, 4000],
	[5, 25, 50, 250, 500, 2500, 5000],
	[6, 30, 60, 300, 600, 3000, 6000],
	[7, 35, 70, 350, 700, 3500, 7000],
	[8, 40, 80, 400, 800, 4000, 8000],                   //9头以上
]

/**每条线上的水果对应的Y轴位置 */
export const LINE_POS = [
    [2, 2, 2, 2, 2],
    [1, 1, 1, 1, 1],
    [3, 3, 3, 3, 3],
    [1, 2, 3, 2, 1],
    [3, 2, 1, 2, 3],
    [1, 1, 2, 3, 3],
    [3, 3, 2, 1, 1],
    [2, 3, 2, 1, 2],
    [2, 1, 2, 3, 2],
    [1, 2, 2, 2, 1],
    [2, 1, 1, 1, 2],
    [2, 3, 3, 3, 2],
    [3, 2, 2, 2, 3],
    [1, 3, 1, 3, 1],
    [3, 1, 3, 1, 3],
]

/**每条中奖线上的水果对应的Y轴位置  只按中间3行5列显示的 索引按 1~15 */ 
export const LINE_POS_EX = [
    [6, 7, 8, 9, 10],
    [1, 2, 3, 4, 5],
    [11, 12, 13, 14, 15],
    [1, 7, 13, 9, 5],
    [11, 7, 3, 9, 15],
    [1, 2, 8, 14, 15],
    [11, 12, 8, 4, 5],
    [6, 12, 8, 4, 10],
    [6, 2, 8, 14, 10],
]

/**每种元素中奖的“最低个数”和“对应的倍数” */
export const TYPE_INFO = {
    1: {
        multiple: {
            1: 0,
            2: 0,
            3: 3,
            4: 10,
            5: 75,
        },
    },
    2: {
        multiple: {
            1: 0,
            2: 0,
            3: 3,
            4: 10,
            5: 90,
        },
    },
    3: {
        multiple: {
            1: 0,
            2: 0,
            3: 15,
            4: 40,
            5: 150,
        },
    },
    4: {
        multiple: {
            1: 0,
            2: 0,
            3: 25,
            4: 50,
            5: 200,
        },
    },
    5: {
        multiple: {
            1: 0,
            2: 0,
            3: 30,
            4: 70,
            5: 250,
        },
    },
    6: {
        multiple: {
            1: 0,
            2: 0,
            3: 35,
            4: 80,
            5: 300,
        },
    },
    7: {
        multiple: {
            1: 0,
            2: 0,
            3: 45,
            4: 100,
            5: 350,
        },
    },
    8: {
        multiple: {
            1: 0,
            2: 0,
            3: 70,
            4: 170,
            5: 400,
        },
    },
    9: {
        multiple: {
            1: 0,
            2: 0,
            3: 0,
            4: 0,
            5: 0,
        },
    },
    10: {
        multiple: {
            1: 0,
            2: 0,
            3: 25,
            4: 40,
            5: 400,
        },
    },
    11: {
        multiple: {
            1: 0,
            2: 0,
            3: 100,
            4: 200,
            5: 1750,
        },
    },
}


//服务器图标对应值
//  11种图案类型
export const ICON_TYPE = {
    NUM_WILD:     0,   //WILD字
    NUM_10:       1,   //10
    NUM_J:          2,   //J
    NUM_Q:          3,   //Q
    NUM_K:           4,   //K
    NUM_A:      5,   //A
    NUM_LANG:      6,   //狼头
    NUM_HU:      7,   //虎头
    NUM_YING:       8,   //鹰头
    NUM_LU:     9,  //鹿头
    NUM_NIU:        10,  //牛头
}

//  11种图案Spine 动画资源
export let SPINE_ICON_NAME = [
    "icon_cai_shen",
    "icon_tanghulu_A",
    "icon_bun",
    "icon_peach",
    "icon_ruyi",
    "icon_lucky_bag",
    "icon_goldfish",
    "icon_tudi",
    "icon_milefo",
    "icon_caishen",
    "icon_gong",
]

//免费游戏状态
export const FREE_TYPE = {
    non:-1,//非免费
    playing:1, //进行中
    end:2, //已结束
}

export const TURN_TABLE_TYPE = {
    non:0,
    free5: 1,
    free10:2,
    free15:3,
    wild_single:4,
    wild_double:5,
    jackpot1:6,
    jackpot2:7,
    jackpot3:8
}

//下注倍数
//export let BetConfig = [50,100,200,300,400,500,600,700,800,900,1000,2000,3000,4000,5000,6000,7000,8000,9000,10000,50000,100000,150000,200000,300000]

//中奖方案
export let UnitConfig = [
    [20,60,300],
    [20,60,300],
    [20,80,400],
    [20,120,400],
    [20,120,400],
    [60,200,800],
    [60,400,1000],
    [60,400,1000],
    [100,600,1200]
]


// 游戏协议
export let Protos = {
    CS_SLOTNIU_GAMESTART_P : 1000,		//游戏开始
	SC_SLOTNIU_GAMESTART_P : 1001,				//游戏开始结果
	SC_SLOTNIU_FREEGAME_P : 1002,				//免费游戏结果
	CS_SLOTNIU_SPINSTART_P : 1003,				//转盘开始
	SC_SLOTNIU_SPINSTART_P: 1004,				//转盘开始结果
	CS_SLOTNIU_JPLIST_P: 1005,				//Jackpot中奖列表
	SC_SLOTNIU_JPLIST_P: 1006,				//Jackpot中奖列表
	SC_SLOTNIU_JACKPOT_P: 1007,				//同步Jackpot分数
	SC_SLOTNIU_JPAWARD_P: 1008,				//Jackpot中奖通知
    SC_SLOTNIU_PLAYERLIST_P:1009,          //广播桌面玩家列表
    SC_SLOTNIU_GAMERESULT_P:1010,         //广播桌面玩家游戏结果
}

let GameTextEnglish = {
    GOLD_BET_ERR:"Insufficient coins, bet failed",
    QUIT_TIPS:"You have started the game. If you exit the game, the system will automatically help you manage it. It will not affect the gold coin settlement. Are you sure to exit the game?"
}

let GameTextIndia = {
    GOLD_BET_ERR:"सिक्के कम हैं, दांव असफल रहा",
    QUIT_TIPS:"आपने खेल शुरू कर दिया है. यदि आप गेम से बाहर निकलते हैं, तो सिस्टम स्वचालित रूप से इसे प्रबंधित करने में आपकी सहायता करेगा। इसका सोने के सिक्कों के निपटान पर कोई असर नहीं पड़ेगा. क्या आप निश्चित रूप से खेल से बाहर निकलेंगे?"
}

// 文本内容
export let GameTextTips = GameTextEnglish

export let updateGameTextTips = () => {
    let curLanguage = cc.sys.localStorage.getItem('curLanguage') || 1;
	if (curLanguage == LanguageType.CHINESE) {
	//   GameTextTips = GameTextCH;
	} else if (curLanguage == LanguageType.ENGLISH) {
		GameTextTips = GameTextEnglish;
	} else if (curLanguage == LanguageType.INDIA) {
		GameTextTips = GameTextIndia;
	}
}


// 背景、音效文件名
export let Sounds = {
    BG_MUSIC: "res/sounds/bg",

    Jetton:  "res/sounds/jetton",
    ScrollStart:  "res/sounds/scrollStart",
    Lianxian:  "res/sounds/lianxian",
    SPIN: "res/sounds/spin", 
    GET_FREE: "res/sounds/get_free",
    GET_WILD: "res/sounds/get_wild",
    JACKPOT: "res/sounds/jackpot",
    

    BGNomal         : "csd-bgm-1",
    BGFree          : "csd-free-bgm",
    Clouds          : "csd_clouds",
    Fmw_Freetimes   : "csd_freetime_",
    Fmw_Gong        : "csd_gong",
    Fmw_Laughing    : "csd_laughing",
    Fmw_Ling        : "csd_ling",
    Fmw_Note        : "csd_note",
    Roll            : "roll",
    Btn_Start       : "btn_start",
    Btn_Click       : "btn_click",
    Gold_Roll       : "gold_roll",
    Big_Start       : "big_start",
    Big_End         : "big_end",
    Smls            : "sml_",
    Free_Result     : "free_result",
}

//赢动画名称列表
export const WIN_SPIN_NAME = {
    BIG: "big0",             // 赢BIG动画名
    EPIC: "epic0",             // 赢EPIC动画
    MEGA: "mega0",             // 赢MEGA动画
    SUPER: "super0",             // 赢SUPER动画
    TOTAL: "total0",             // 赢TOTAL动画
}

export const GameEvent = {
    JACKPOT_RECORD_INFO:"JACKPOT_RECORD_INFO",//jackpot记录
    TURN_TABLE_INFO: "TURN_TABLE_INFO",//转盘信息
    TURN_TABLE_UPDATE_JACKPORT: "TURN_TABLE_UPDATE_JACKPORT",//转盘更新奖池信息
    HALL_SWITCH_LANGUAGE: "HALL_SWITCH_LANGUAGE",//切换多语言
    ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE: "ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE", //充值到账领取
} 

export const WildtypeInfo = {
    NON: 0,
    SINGLE: 1,//大转盘中wild时单线 
    DOUBLE: 2,//大转盘中wild时双线 
} 



//////////////////////////////////////////////////////////////////////////////////
