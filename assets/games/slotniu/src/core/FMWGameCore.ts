import { Constant, FREE_TYPE, GameEvent, GameTextTips, LINE_POS_EX, Protos, WildtypeInfo, updateGameTextTips} from "./FMWDefine";
import GameCore from "../../../../script/frame/model/GameCore";
import Common from "../../../../script/frame/common/Common";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import FMWGameView from "../view/FMWGameView";
import EventManager from "../../../../script/frame/manager/EventManager";
import FMWLotteryBox from "../view/FMWLotteryBox";
import Config from "../../../../script/frame/config/Config";
import { TextTips } from "../../../../script/frame/common/Language";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";


/**滚动数值 */
export type ScrollNumberParam = {
    /**文本节点 */
    txt: cc.Label
    /**起始值 */
    began: number
    /**结束值 */
    end: number
    /**滚动次数 */
    nCount?: number
    /**滚动时长 */
    nTime?: number
    /**滚动间隔 */
    nInterval?: number
    /**延时启动 */
    nDelay?: number
    /**不节点停止动作 */
    bNotStopAction?: boolean
    /**自己格式化 */
    format?: (nValue: number, data: ScrollNumberParam) => void
    /**每次更新 */
    update?: (data: ScrollNumberParam) => void
    /**回调 */
    callback?: (data: ScrollNumberParam) => void
}

/**水果机元素 */
export type FruitMachineBaseMachineElement = {
    /**当前节点 */
    node: cc.Node
    /**所在行[0, 4] */
    nRow?: number
    /**所在列[0, 4] */
    nCol?: number
    /**元素类型[1, 11] */
    nType?: number
    /**转圈动画 */
    animCircle?: cc.Node,
    //停止行
    nStopRow?: number,
    //是否停止好了
    isOK?: boolean,
    //移动位置
    nMoveIndex?: number,

}

/**中奖的线信息 */
export type WinLinesInfo = {
    /**线（UI界面中的线索引） */
    nLine: number,
    /**元素 */
    nType?: number,
    /**中奖元素个数 */
    nCount?: number,
    /**倍率 */
    nMultiple?: number,
    /**中奖图标 3行5列 按一维数组 1~15 区分*/ 
    prizeIconList?: number[];
}

/**水果机摇奖状态 */
export enum SpinStateFruitMachine {
	/**未初始化 */
	None = 0,
	/**正常 */
	Normal,
	/**免费 */
	Free,
	/**单线 */
	Single,
	/**双线 */
	Double,
	/**转盘 */
	TurnTable
}

/**桌上玩家信息 */
export type DeskPlayersInfo = {
    /**当前玩家节点 */
    node: cc.Node,
    /**昵称金币父节点 */
    infoBgNode?: cc.Node,
    /**玩家昵称 */
    nameNode?: cc.Node,
    /**玩家金币 */
    goldNode?: cc.Node,
    /**头像底 */
    headBgNode?: cc.Node,
    /**头像 */
    headNode?: cc.Node,
    /**头像转圈动画 */
    headSpinAniNode?: cc.Node,
    /**其它动画 */
    aniNode?: cc.Node,
    /**输赢飘分 */
    winNode?: cc.Node,
    /**飘分底图 */
    winBgNode?: cc.Node,
    /**飘分 */
    winOrLoseNumNode?: cc.Node,
    /*玩家id*/
    playerid?:number,
    /*是否在播放动画标志*/
    playingAni?:boolean,
    /*玩家身上金币*/
    curMoney?: number
}

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slotniu/FMWGameCore')
export default class FMWGameCore extends GameCore {

    //////////////////////////////////////////////////////////////////////////////
    // 游戏视图对象
    gameView: FMWGameView = null;
    //lotterBox对象
    lotteryBox: FMWLotteryBox = null;

    // 下注金额
    betMoney: number = 0;
    // 自己下注索引
    meAddBeiLv: number = 0;
    //下注实际值 = 底分 * 下注倍数 * 固定倍率值9 
    bets = [];
    //下注倍数列表
    betsOdds = [];
    // 底数
    difen: number = 0;
    //PRIZE奖池金额
    prizenum: number = 0;
    //上次使用的奖池金额值
    jackpotnum: number = 0;
    //原奖池金额
    nOldJackpotNum: number = null;
    //正常开奖结果
    resultData: any = null;
     //免费开奖结果
    resultFreeData: any = null;
	/**水果机元素 [行，从0开始][列，从0开始] 二维数组 */
	machineElement: FruitMachineBaseMachineElement[][];
	machineElementMap: Map<cc.Node, FruitMachineBaseMachineElement> = new Map();
    /**摇奖按钮状态 */
	SpinState = SpinStateFruitMachine;
    /**摇奖按钮状态 */
	nSpinState: number = SpinStateFruitMachine.Normal;
    /**自动摇是否开启 */
	bAutoSpin: boolean = false;
    /**水果机是否停止 */
	bFruitIsStop: boolean = true;
    /**当前播放的线 */
    curLineIndex: number = 0;
    /**摇奖元素信息 */
	fruitData: number[][] = [[],[],[]];
    /**wildSpin列表 */
    wildSpinList: sp.Skeleton[] = [];
    /**流光Spin列表 */
    liuGuangLightSpinList: sp.Skeleton[] = [];
    /**当前检测的是第几列*/
    mCurLightIndex: number = -1;
    /**流光动画标志 */
    bLightFlag: boolean[] = [false, false, false, false, false];
    //当前位置
    m_curIndex = 0;
    //最后一个位置
    m_nLastIndex = -1;
    //滚动值
    m_RollNum = [0,0,0,0,0]
    //回退值
    m_springback = [0,0,0,0,0]
    //移动步数
    m_nDoLen = [30, 30, 30, 30, 30];
    //移动中间间隔时间
    m_nOnceMoveLen = [0.3, 0.3, 0.3, 0.3, 0.3];
    //鹿统计
    m_nDeerCount = [0,0,0,0,0];
    //鹿位置数据
    m_nDeerPosData = [];
    //牛统计
    m_nCowPosData = [];
    //最后一次牛位置
    mLastCowCount = -1;
    //最后一次鹿位置
    mLastDeerCount = -1;
    //免费总赢金额（未扣税显示用）
    freeWinTotalNum: number = 0;
    //免费总赢金额（扣税后）
    freeChangeTotalNum: number = 0;
    //记录上一次赢的金额
    lastFreeWinNum: number = 0;
    /**本次免费摇奖次数 */
	nLastFreeTimes: number = 0;
    /**上次免费摇奖次数 */
	nLastMaxFreeTimes: number = 0;
    /**免费摇结束 */
    freeTimesEndFlag = FREE_TYPE.non;
    //上次prize金额
    lastPrizenum = 0;
    //其它玩家jackpot
    otherJackpotList = [];
    //转盘数据
    turntableData = null;

    //////////////////////////////////////////////////////////////////////////////
    //匀速摇奖=================================== 目前这个没用===========begin

    /**前奏步数（控制时间） */
    nStepNumStart: number = 30;
    /**中间步数（控制时间） */
    nStepNumMiddle: number = 30;
    /**回弹步数（控制时间） */
    nStepNumSpringback: number = 10;
    /**末尾步数（控制时间） */
    nStepNumEnd: number = this.nStepNumStart + this.nStepNumSpringback;
    /**最大步数（前奏，中段，结尾） */
    nStepNumMax: number = this.nStepNumStart + this.nStepNumMiddle + this.nStepNumEnd;
    /**当前步数 */
    nStepNumCur: { [nCol: number]: number } = {};
    /**每列延迟 */
    nStepNumDelayDiff: number = 8;
    nStepNumDelay: { [nCol: number]: number } = {
        0: this.nStepNumDelayDiff * 0,
        1: this.nStepNumDelayDiff * 1,
        2: this.nStepNumDelayDiff * 2,
        3: this.nStepNumDelayDiff * 3,
        4: this.nStepNumDelayDiff * 4,
    };
    /**初始速度 */
    nSpeedStart: number = 0;
    /**当前速度 */
    nSpeedCur: { [nCol: number]: number } = {};
    /**加速度 */
    nAcceleration: number = 0;
    /**每列是否滚动 */
    bScroll: { [nCol: number]: boolean } = {};
    /**中间显示的三列索引 */
    nShowRowMin: number = 1;
    nShowRowMax: number = 3;
    /**当前牛已累加个数 */
    niuCount: number = 0;
    /**当前鹿已累加个数 */
    luCount: number = 0;
    /**最后一列是否有鹿 */
    lastColHasLu: boolean = false;
    /**每列鹿所在当前列出现的行的位置 */
    luRowList: number[] = [-1,-1,-1,-1,-1];
     /**上次中奖 用来保存中2个时，第1个也播放中奖动画*/
     lastNiuItem: cc.Node = null;
     /**上次中奖鹿 用来保存中2个时，第1个也播放中奖动画*/
     lastLuItem: cc.Node = null;
     //统计有多少列包含鹿  每列中最多只计算1个
     countAllColLuNum: number = 0;
     //自己最终实时余额
     myselfMoney: number = 0;
    //游戏总金币
    playScoreMoney = 0;
    //桌上玩家信息
    deskPlayerInfoList: DeskPlayersInfo[] = [];
    //上次免费之前是否自动状态
    lastAutoSpinState: boolean = false;
    //断线转盘界面奖池值
    reconnectLuckyjackpot: number = 0;
    //断线转盘界面奖池值
    reconnectNiuNum: number = 0;
       
    //匀速摇奖=================================== 目前这个没用===========end
    //////////////////////////////////////////////////////////////////////////////
    
    
    
    //////////////////////////////////////////////////////////////////////////////

    onLoad() {
        this.gameView = this.node.getComponent("FMWGameView");
        this.lotteryBox = this.node.getComponent("FMWLotteryBox"); 
        this.updateGameLan();
        super.onLoad();
        
    }

    private updateGameLan() {
        updateGameTextTips();
        TextTips["GameTextTips"] = GameTextTips;
    }

    //////////////////////////////////////////////////////////////////////////////

    //转盘请求
    public sendTurnTableStart() {
        this.sendGameMessage(Protos.CS_SLOTNIU_SPINSTART_P);
    }

    // jackpot记录
    public sendJackpotRecord() {
        this.sendGameMessage(Protos.CS_SLOTNIU_JPLIST_P);
    }


    // 玩家下注
    public sendGameStart() {
        this.sendGameMessage(Protos.CS_SLOTNIU_GAMESTART_P, { odds: this.betsOdds[this.meAddBeiLv] });
    }

    //加注
    public onBetSub(){
        this.meAddBeiLv -= 1;
        if(this.meAddBeiLv < 0){
            this.meAddBeiLv = this.bets.length - 1;
        }
        this.betMoney = this.bets[`${this.meAddBeiLv}`] * Config.SCORE_RATE;;
    }

    //减注
    public onBetAdd(){
        this.meAddBeiLv += 1;
        if(this.meAddBeiLv == this.bets.length){
            this.meAddBeiLv = 0;
        }
        this.betMoney = this.bets[`${this.meAddBeiLv}`] * Config.SCORE_RATE;;
    }    

    // 玩家退出游戏
    public quitGame(info?: any) {
        this.updatePlayerMoney({coin:this.myselfMoney,playerid:this.playerid})
        if (!info && !this.bFruitIsStop) {
            AlertHelper.confirm(GameTextTips.QUIT_TIPS, super.quitGame.bind(this), null);
        }
        else {
            UIHelper.clearAll();
            super.quitGame(info);
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    public start() {
        this.bindGameMessage(Protos.SC_SLOTNIU_JPLIST_P, this.onGetJackpotRecord,this);
        this.bindGameMessage(Protos.SC_SLOTNIU_GAMESTART_P, this.onStartGame,this);
        this.bindGameMessage(Protos.SC_SLOTNIU_FREEGAME_P, this.onStartFreeGame,this);
        this.bindGameMessage(Protos.SC_SLOTNIU_JACKPOT_P, this.onUpdateJackpot,this);
        this.bindGameMessage(Protos.SC_SLOTNIU_JPAWARD_P, this.onOhterPalyerJackpot,this);
        this.bindGameMessage(Protos.SC_SLOTNIU_SPINSTART_P, this.onGetTurnTableInfo,this);
        this.bindGameMessage(Protos.SC_SLOTNIU_PLAYERLIST_P, this.onUpdateDeskPlayersInfo,this);
        this.bindGameMessage(Protos.SC_SLOTNIU_GAMERESULT_P, this.onUpdateDeskPlayersResultInfo,this);
        EventManager.instance.on(GameEvent.ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE, this.rechargeToUpdateMyselfCoid, this);
        EventManager.instance.on(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        super.start();
    }

    public exit() {
        this.unbindGameMessage(Protos.SC_SLOTNIU_JPLIST_P, this.onGetJackpotRecord,this);
        this.unbindGameMessage(Protos.SC_SLOTNIU_GAMESTART_P, this.onStartGame,this);
        this.unbindGameMessage(Protos.SC_SLOTNIU_FREEGAME_P, this.onStartFreeGame,this);
        this.unbindGameMessage(Protos.SC_SLOTNIU_JACKPOT_P, this.onUpdateJackpot,this);
        this.unbindGameMessage(Protos.SC_SLOTNIU_JPAWARD_P, this.onOhterPalyerJackpot,this);
        this.unbindGameMessage(Protos.SC_SLOTNIU_SPINSTART_P, this.onGetTurnTableInfo,this);
        this.unbindGameMessage(Protos.SC_SLOTNIU_PLAYERLIST_P, this.onUpdateDeskPlayersInfo,this);
        this.unbindGameMessage(Protos.SC_SLOTNIU_GAMERESULT_P, this.onUpdateDeskPlayersResultInfo,this);
        EventManager.instance.off(GameEvent.ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE, this.rechargeToUpdateMyselfCoid, this);
        EventManager.instance.off(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        TextTips["GameTextTips"] = {};
        super.exit();
    }

    // 玩家加入
    public onPlayerEnter(info: any) {
        super.onPlayerEnter(info);
        //Common.dump(info,"onPlayerEnter");
    }

    // 玩家离开
    public onPlayerQuit(info: any) {
        super.onPlayerQuit(info);
        //Common.dump(info,"onPlayerQuit");
    }

    // 游戏结束,你条件不满足被踢出房间,如果你在暂离状态,也会被踢出房间
    public onDeletePlayer(info: any) {
        super.onDeletePlayer(info);
    }

    // 玩家状态
    public onPlayerState(info: any) {
        super.onPlayerState(info);

        //Common.dump(info,"onPlayerState");
    }

    // 更新玩家金币
    public updatePlayerMoney(info: any) {
        super.updatePlayerMoney(info);

        if (info.playerid == this.playerid) {
            if (info["coin"]) {
                this.myselfMoney = info["coin"];
            }
        }
    }

    //充值更新余额
    rechargeToUpdateMyselfCoid(){
        console.error('充值更新余额',this.myselfMoney)
        this.playScoreMoney = this.myselfMoney;
        this.gameView.showSelfMoney();
    }

    // 进入房间，房间信息
    public onRoomInfo(info: any) {
        super.onRoomInfo(info);
        //Common.dump(info,"onRoomInfo");

        // 重置游戏界面与玩家数据
        this.gameView.resetAllUI();

        // 玩家信息
        let playerlist = info["playerlist"];
        if (playerlist && typeof(playerlist) == "object") {
            // 先找出自己的位置
            for (let key in playerlist) {
                let plyInfo = playerlist[key];
                let playerid = Common.toInt(plyInfo["playerid"]);
                if (playerid == this.playerid) {
                    this.mySeatId = Common.toInt(plyInfo["seat"]);
                    break;
                }
            }
            // 显示所有玩家
            for (let key in playerlist) {
                let plyInfo = playerlist[key];
                let playerid = Common.toInt(plyInfo["playerid"]);
                let seat = Common.toInt(plyInfo["seat"]);

                // 玩家是否在游戏中  单机游戏只显示自己
                if (playerid == this.playerid) {
                    if (plyInfo["money"]) {
                        this.playScoreMoney = plyInfo["money"];
                        this.myselfMoney = this.playScoreMoney;
                        this.gameView.showSelfMoney();
                    }
                    break;
                }
            } 
        }
    }

    //接收通用道具信息
    public onRoomChat(info: any) {
        console.log("onRoomChat", info);
        let sendPlayerid = Common.toInt(info["sendPlayerid"]);
        let receiverPlayerid = Common.toInt(info["receiverPlayerid"]);
        // let type = info["type"];
        let content = info["content"];

        let sendPlayerPos = this.gameView.getHeadPosAtPlayerId(sendPlayerid);//发送者位置
        let receiverPlayerPos = this.gameView.getHeadPosAtPlayerId(receiverPlayerid,true);//接收者位置

        if(Common.isNull(sendPlayerPos) || Common.isNull(receiverPlayerPos)){
            return;
        }
    
        this.gameView.doRoomIntertChat(sendPlayerPos, receiverPlayerPos, content);
    }

    // 房间状态
    public onRoomState(info: any) {
        super.onRoomState(info);
        //Common.dump(info,"onRoomState");
    }

    // 房间信息(断线重入)
    public onToOtherRoom(info: any) {
        super.onToOtherRoom(info);
        //Common.dump(info,"onToOtherRoom");
        this.onGameConfig(info);
        this.gameView.onToOtherRoom(info);
    }

    //////////////////////////////////////////////////////////////////////////////
    //游戏主逻辑

    //参数1："jackpot" //当前Jackpot的值
    //更新jackpot奖池
    onUpdateJackpot(info: any){
        if(!info){
            return;
        }
         //jackpot奖池
         let jackpot =Number(this.gameView.moneyFormat(Number(info["jackpot"]??0)));
         this.gameView.updateJackpotNum(jackpot);
    }   

    /*
        SC_SLOTNIU_JPAWARD_P //响应-Jackpot中奖通知
        参数1："playerid" //玩家ID
        参数2："name" //玩家昵称
        参数3："headid" //玩家系统头像id
        参数4："wxheadurl" //自定义头像url
        参数5："winscore" //玩家赢的金币	
    */
    //其它玩家中奖
    onOhterPalyerJackpot(info: any){
        if(!info){
            return;
        }
        if(info.playerid == this.playerid){ //自己
            return;
        }
        this.otherJackpotList.push(info);
        
        this.gameView.playerOhterJackpotAni();
    }

    //转盘信息
    onGetTurnTableInfo(info: any){
        // Common.dump(info,'onGetTurnTableInfo');
        this.turntableData = info;
        EventManager.instance.emit(GameEvent.TURN_TABLE_INFO,info);    
    }

    //jackpot记录
    onGetJackpotRecord(info: any){
        // Common.dump(info,'onGetJackpotRecord');
        EventManager.instance.emit(GameEvent.JACKPOT_RECORD_INFO,info);    
    }

    // 游戏消息-配置
    private onGameConfig(info: any) {
        //Common.dump(info,"onGameConfig");

        //底分
         this.difen = Number(this.gameView.moneyFormat(info["difen"],2));
        
         //prize奖池
         this.prizenum = Number(this.gameView.moneyFormat(Number(info["prize"]??0),1)); 
         this.gameView.updatePrizeNum(this.prizenum);
         
         //jackpot奖池
         let jackpot =Number(this.gameView.moneyFormat(Number(info["jackpot"]??0)));
         this.gameView.updateJackpotNum(jackpot);

        //配置筹码
        let bets = info["odds"];
        for(let i in bets){
            this.bets.push(bets[`${i}`] * Constant.BET_RATE_NUM * this.difen);
            this.betsOdds.push(bets[`${i}`]);
        }
        if(this.bets.length == 0){//给个默认筹码列表
            this.bets = Constant.BET_NUM_CONFIG_LIST;
        }

        if(info["lastodds"]){
            for(let i in bets){
                if(bets[`${i}`] == info["lastodds"]){
                    this.meAddBeiLv = Number(i) - 1;
                }
            }
        }
        
        //默认最低下注金额 本地如果有正确值,则默认本地值
        // let localBetNum = cc.sys.localStorage.getItem(Constant.LOCAL_STORAGE_BET_NUM_KEY);
        // if(!Common.isNull(localBetNum)){//有效配置值
        //     this.bets.forEach((value,index)=>{
        //         if(value == Number(localBetNum)){
        //             this.meAddBeiLv = index;
        //         }
        //     })
        // }
        //默认选择的下注金额
        this.betMoney = this.bets[this.meAddBeiLv] * Config.SCORE_RATE;
        this.gameView.showBetConfig();        
    }

    /*
        参数1：数组返回结果
        参数1："playerid" //玩家ID
        参数2："name" //玩家昵称
        参数3："headid" //玩家系统头像id
        参数4："wxheadurl" //自定义头像url
        参数5："pmoney" //玩家金币
    */
    //更新桌上玩家信息
    onUpdateDeskPlayersInfo(info){
        // console.error('===玩家信息===')
        // console.error(info)
        if(!info){
            return;
        }
        
        if(cc.winSize.width/cc.winSize.height < 2){
            return;
        }

        this.gameView.onUpdateDeskPlayersInfo(info);
    }

    /*
        参数1："playerid" //玩家ID
        参数2："winscore" //赢的分数
        参数3："mult" //赢的倍数
        参数4："pmoney" //玩家金币
    */
    //更新桌上玩家游戏结果
    onUpdateDeskPlayersResultInfo(info){
        // console.error('===游戏结果===')
        // console.error(info)
        if(!info){
            return;
        }
        
        if(cc.winSize.width/cc.winSize.height < 2){
            return;
        }

        this.gameView.onUpdateDeskPlayersResultInfo(info);
    }
    


    /*
        SC_SLOTNIU_GAMESTART_P //响应-游戏开始 免费相同结构,但是多个数组结构 
        参数1："freetimes" //免费次数
        参数2："niunum" //牛的个数
        参数3："iconresult" //游戏图标集合(3x5数组,值对应EM_SLOTNIU_ICONTYPE)
        参数4："linecount" //命中的线条个数
        参数5："lineresult" //命中的线条(数组)
                参数1："line" //线条索引(1-9号线)
                参数2："num" //命中的数量(列数)
        参数6："totalmult" //命中线条赢得总倍数 
        参数7："winmoney" //命中线条赢得的总金额
        参数8："changemoney" //玩家的输赢分
        参数9："luckyspin" //是否中幸运转盘(1-是, 0-否)
        参数10："prize" //玩家当前的prize数量
        参数11："wildtype" //幸运转盘WILD(0-无，1-EM_SLOTNIU_LUCKYSPINTYPE_WILD1，2-EM_SLOTNIU_LUCKYSPINTYPE_WILD2)
        参数12："wildnum" //幸运转盘WILD数量(5-第5列,4-第4列...0-无)
        参数13："luckyjackpot" //转盘彩金
    */

    //免费游戏开始
    onStartFreeGame(info: any){
        // console.log('免费开始..........');
        console.log(info)
        this.resultFreeData = info;

        if(!this.resultFreeData || this.resultFreeData.length == 0){
            console.error('onStartFreeGame.....info is null');
            return;
        }
    }

    // 游戏开始
    onStartGame(info: any) {
        console.log('正常开始..........');
        console.log(info)

        this.resultData = info;
        this.resultFreeData = null;
        this.freeWinTotalNum = 0;
        this.freeChangeTotalNum = 0;
        this.lastFreeWinNum = 0;

        if(!this.resultData){
            console.error('onStartGame.....info is null');
            return;
        }

        //免费次数
        this.nLastFreeTimes = this.resultData.freetimes; 
        this.nLastMaxFreeTimes = this.resultData.freetimes; 
        this.freeTimesEndFlag = FREE_TYPE.non;

        //额外处理一下 如果是wild_double双列 服务器最多只返回4次 客户端双列也按从5列开始
        if(this.resultData.wildtype == WildtypeInfo.DOUBLE && this.resultData.wildnum > 0 && this.resultData.wildnum <= 4){
            this.resultData.wildnum += 1; 
        }
        this.gameView.runNotBetToQuick();
        //开始游戏
        this.lotteryBox.startGame();
        
    }

    //设置结算相关信息
    setResultDataInfo(){
        this.fruitData = [[],[],[]];
        
        this.lotteryBox.resetData();
        //解析结果值  变成3行5列数组
        for(let key in this.resultData.iconresult){
            this.fruitData[Math.floor((Number(key) - 1) / Constant.COL_SHOW)].push(this.resultData.iconresult[key]);
        }
        
        //存到线条对应对象中
        this.resultData.winLinesInfo = [];
        if(!Common.isNull(this.resultData.linecount) && this.resultData.linecount > 0){
            for(let key in this.resultData.lineresult){
                let subWinLinesInfo: WinLinesInfo = <WinLinesInfo>{}
                let lineInfo = this.resultData.lineresult[key];
                subWinLinesInfo.nLine = lineInfo.line;//中奖线
                subWinLinesInfo.nCount = lineInfo.num;//该线中奖个数

                //从对应线中取对应中奖的索引位置
                let prizeIconList = [];
                for(let j = 0; j < LINE_POS_EX[subWinLinesInfo.nLine - 1].length;j++){
                    if(j < subWinLinesInfo.nCount){
                        let prizeIndex = LINE_POS_EX[subWinLinesInfo.nLine - 1][j];
                        prizeIconList.push(prizeIndex);
                    }
                }
                subWinLinesInfo.prizeIconList = prizeIconList.sort((a,b)=>{return a - b});
                this.resultData.winLinesInfo.push(subWinLinesInfo);
            }
        }
        this.resultData.multgoldnum = this.resultData.totalmult / 10;//倍数除以10 

        //prize奖池
        this.prizenum = Number(this.gameView.moneyFormat(Number(this.resultData["prize"]??0),1)); 

        //免费不扣
        if(this.freeTimesEndFlag == FREE_TYPE.non){
            // 扣除金币
            this.playScoreMoney -= this.betMoney;
            this.gameView.showSelfMoney();
        }
    }

    //重置数值
    resetDataValue() {
        this.niuCount = 0;
        this.luCount = 0;
        this.lastLuItem = null;
        this.lastNiuItem = null;
        this.countAllColLuNum = 0;
        this.lastColHasLu = false;
        this.m_curIndex = 0;
        this.luRowList = [-1, -1, -1, -1, -1];
        this.m_RollNum = [0, 0, 0, 0, 0];
        this.m_springback = [0, 0, 0, 0, 0];
        this.m_nDoLen = [30, 30, 30, 30, 30];
        this.m_nOnceMoveLen = [0.3, 0.3, 0.3, 0.3, 0.3]
        this.m_nDeerCount = [0, 0, 0, 0, 0];
        this.m_nDeerPosData = [];
        this.m_nCowPosData = [];
        this.mLastCowCount = -1;
        this.mLastDeerCount = -1;
        this.bLightFlag = [false, false, false, false, false];
    }
}
