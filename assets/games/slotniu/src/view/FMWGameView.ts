import { Constant, Sounds, JACKPOT_PERCENT, FREE_TYPE, GameTextTips } from "../core/FMWDefine";
import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import FMWLotteryBox from "./FMWLotteryBox";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import HallManager from "../../../../script/frame/manager/HallManager";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import FMWGameCore, { DeskPlayersInfo, ScrollNumberParam, SpinStateFruitMachine } from "../core/FMWGameCore";
import Config from "../../../../script/frame/config/Config";
import FMWGameLogic from "../core/FMWGameLogic";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import DataManager from "../../../../script/frame/manager/DataManager";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import { Direction, quickPayStyle } from "../../../../script/frame/common/Define";


//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slotniu/FMWGameView')
export default class FMWGameView extends BaseLayer {

    //规则帮助界面
    @property(cc.Prefab)
    helpPrefab: cc.Prefab = null;

    //彩金记录界面
    @property(cc.Prefab)
    jackpotRecordPrefab: cc.Prefab = null;

    //转盘界面
    @property(cc.Prefab)
    turnTablePrefab: cc.Prefab = null;

    //主资源
    @property(cc.SpriteAtlas)
    mainUiAtlas: cc.SpriteAtlas = null;

    //桌上其它玩家win动画
    @property(cc.Prefab)
    otherWin_jackpot: cc.Prefab = null;

    //桌上其它玩家win动画
    @property(cc.Prefab)
    otherWin_mega: cc.Prefab = null;

    //桌上其它玩家win动画
    @property(cc.Prefab)
    otherWin_super: cc.Prefab = null;

    //头像道具
    @property(cc.Node)
    gameEmojiNode: cc.Node = null;

    // 游戏核心对象
    _gameCore: FMWGameCore = null;
    //点击
    _bDoTouch: boolean = false;
    
    // lotterBox对象
    _lotteryBox: FMWLotteryBox = null;
    _gameLogic: FMWGameLogic = null;

    helpNode: cc.Node = null; //帮助界面
    jackpotRecordLayer : cc.Node = null;//jackpot记录
    turnTableLayer: cc.Node = null;//转盘
    //当前点击的头像
    _curClickHeadIndex: number = -1;

    onLoad() {

        //动态导入所有节点
        this.LoadAllLayerObject(this.node)

        this._gameCore = this.node.getComponent("FMWGameCore");
        this._lotteryBox = this.node.getComponent("FMWLotteryBox");
        this._gameLogic = this.node.getComponent("FMWGameLogic");
        

        //初始化按钮特效
        this.initSpinAnim();
        //初始化按钮触摸事件
        this.initSpinTouch();
        //初始化桌上玩家信息
        this.initDeskPlayersInfo();
    }

    start() {
        AudioHelper.instance.playMusic(Sounds.BG_MUSIC);
    }

    //添加计时 多少分钟不操作下注 则退出到大厅
    runNotBetToQuick() {
        let self = this;
        this.LayerItems.timeOutRunNode.stopAllActions();
        cc.tween(this.LayerItems.timeOutRunNode)
        .delay(60 * 10) //10分钟
        .call(()=>{
            self.LayerItems.timeOutRunNode.stopAllActions();
            let info = {reason:3};//超时未操作被踢
            self._gameCore.quitGame(info);
        })
        .start()
    }

    //初始化桌上玩家信息
    initDeskPlayersInfo(){
        for(let i = 0;i < Constant.DESK_PLAYERS_MAX_NUM;i++){
            let deskPlayerInfo = <DeskPlayersInfo>{};
            deskPlayerInfo.node = this.LayerItems[`FileNode_player_${i}`];
            deskPlayerInfo.infoBgNode = cc.find('Node/Image_info_bg',deskPlayerInfo.node);
            deskPlayerInfo.nameNode = deskPlayerInfo.infoBgNode.getChildByName('Text_player_name');
            deskPlayerInfo.goldNode = deskPlayerInfo.infoBgNode.getChildByName('Text_player_coin');
            deskPlayerInfo.headBgNode = cc.find('Node/Image_head_bg',deskPlayerInfo.node);
            deskPlayerInfo.headNode = deskPlayerInfo.headBgNode.getChildByName('Image_head');
            deskPlayerInfo.headSpinAniNode = deskPlayerInfo.node.getChildByName('headSpinAni');
            deskPlayerInfo.aniNode = deskPlayerInfo.node.getChildByName('aniNode');
            deskPlayerInfo.winNode = deskPlayerInfo.node.getChildByName('Node_win');
            deskPlayerInfo.winBgNode = deskPlayerInfo.winNode.getChildByName('Image_win_bg');
            deskPlayerInfo.winOrLoseNumNode = deskPlayerInfo.winNode.getChildByName('winOrLoseNum');
            deskPlayerInfo.node.active = false;
            this._gameCore.deskPlayerInfoList.push(deskPlayerInfo);
        }
        
    }
    

    /**初始化按钮特效 */
    initSpinAnim() {
        let self = this;
        let bShowSpin = true;
        this.nodeRunActionRepeatForever(this.LayerItems.btn_spin, () => {
            self.LayerItems.btn_spin.getChildByName('Image_spin_txt_1').active = bShowSpin;
            self.LayerItems.btn_spin.getChildByName('Image_spin_txt_2').active = !bShowSpin;
            if (!bShowSpin) {
                let ani = self.LayerItems.btn_spin.getChildByName('Image_spin_txt_2').getComponent(cc.Animation);
                ani.play();
            }
            bShowSpin = !bShowSpin;
        }, 3.0, true)
    }

    //对象运行动作单次
    nodeRunActionOncs(node: cc.Node, callback: Function, time: number, bImmediately?: boolean, bNotStopAction?: boolean) {
        if (!callback || !time) {
            return null;
        }

        if (Common.isNull(bNotStopAction) || !bNotStopAction) {
            node.stopAllActions();
        }
        //是否立即执行
        bImmediately && callback(bImmediately);
        if (!!node) {
            cc.tween(node)
                .delay(time)
                .call(() => {
                    !!node && (callback(false));
                })
                .start();
        }
    }

    //对象运行动作循环
    nodeRunActionRepeatForever(node: cc.Node, callback: Function, time: number, bImmediately?: boolean) {
        if (!callback || !time) {
            return null;
        }
        //是否立即执行
        bImmediately && callback(bImmediately);
        node.stopAllActions();
        if (!!node) {
            cc.tween(node)
                .repeatForever(
                    cc.tween()
                        .delay(time)
                        .call(() => {
                            !!node && (callback(false));
                        })
                )
                .start();
        }
    }

    /**Label滚动上涨 */
    scrollNumber(data: ScrollNumberParam) {
        if (data.began == data.end || data.end == 0) {
            if (data.format) {
                data.format(data.end, data);
            } else {
                data.txt.string = `${data.end}`;
            }
            data.callback && data.callback(data);
            return;
        }
        //刷新定时器
        let nCur = 0;
        let nMax = data.nCount ?? 10;
        let func = (nInterval: number) => {
            this.nodeRunActionOncs(data.txt.node, (bImmediately: boolean) => {
                let nValue = data.began + (data.end - data.began) * nCur / nMax;
                //避免精度问题
                nValue = Number((nValue + 0.000001).toFixed(2));
                if (data.format) {
                    data.format(nValue, data);
                } else {
                    data.txt.string = `${nValue}`;
                }
                if (++nCur > nMax) {
                    data.callback && data.callback(data);
                } else {
                    !bImmediately && func(nInterval);
                }
            }, nInterval, nCur == 0, data.bNotStopAction);
        }
        // data.txt.node.active = false;
        this.nodeRunActionOncs(data.txt.node, () => {
            // data.txt.node.active = true;
            if (!data.nInterval) {
                func((data.nTime ?? 1.0) / nMax);
            } else {
                func(data.nInterval);
            }
        }, data.nDelay ?? 0.001, null, data.bNotStopAction);
    }

    //检测余额是否够下注
    checkMoney() {
        if(DataManager.instance.isNeedPay(Config.GAME_LIST.slotniu.gameId)){//没有充值，提示充值
            HallManager.instance.openCharge({quickPayStyle:quickPayStyle.gameVipPay});
            this.setSpinAutoState(false);
            return;
        }

        if(this._gameCore.playScoreMoney != this._gameCore.myselfMoney){
            console.error('====当前余额不一致=======',this._gameCore.playScoreMoney,this._gameCore.myselfMoney)
            this._gameCore.playScoreMoney = this._gameCore.myselfMoney;
            this.showSelfMoney();
        }
        if (this._gameCore.playScoreMoney < this._gameCore.betMoney) {
            this.setSpinAutoState(false);
            // ToastHelper.show(GameTextTips.GOLD_BET_ERR);
            HallManager.instance.openCharge({quickPayStyle:quickPayStyle.gameQuickPay});
            return false;
        }
        return true;
    }

    /**赢金数值检测隐藏 */
    checkWinLabelHide() {
        //非free状态下才需处理
        if (this._gameCore.nSpinState != this._gameCore.SpinState.Free) {
            this.LayerItems.Node_win_normal.active = false;
            this.LayerItems.Node_win_good_luck.active = true;
            this.LayerItems.Node_win_free_total.active = false;
        }
    }

    /**设置按钮自动模式状态 */
    setSpinAutoState(nState: boolean) {
        this._gameCore.bAutoSpin = nState;
        this.LayerItems.btn_spin.active = !this._gameCore.bAutoSpin;
        this.LayerItems.btn_auto.active = this._gameCore.bAutoSpin;
        if (this._gameCore.bAutoSpin) {
            this.LayerItems.btn_auto.getChildByName('Image_auto').getComponent(cc.Animation).play();
        }
    }

    /**按钮触摸 */
    initSpinTouch() {
        let touchNode = this.LayerItems.Panel_spin_touch;
        touchNode.on(cc.Node.EventType.TOUCH_START, this.onTouchStart.bind(this));
        touchNode.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove.bind(this));
        touchNode.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd.bind(this));
        touchNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel.bind(this));
    }




    /**摇奖 */
    onClickSpin() {
        //音效
        AudioHelper.instance.playEffect(Sounds.SPIN);

        //游戏进行中
        if (!this._gameCore.bFruitIsStop) {
            return;
        }

        if (!this.checkMoney()) {
            return;
        }

        //发送开始
        this._gameCore.sendGameStart();
        // 测试用
        // let info = {
        //     freetimes:0,
        //     niunum: 2,
        //     iconresult: {
        //          '1':  0, '2': 5,  '3': 7,  '4': 0, '5':  6, 
        //          '6':  10,  '7': 1,  '8': 3,  '9': 1,  '10': 1, 
        //         '11':  0, '12': 10, '13': 8, '14': 3,  '15': 1},
        //     linecount:2,
        //     lineresult:[
        //         {line:7,num:3},
        //         {line:8,num:3},
        //     ],
        //     winmoney: 600,
        //     totalmult: 0,
        //     changemoney:0,
        //     luckyspin: 1,
        //     prize: 120,
        //     wildnum:5,
        //     wildtype:0
        // };
        
        // this._gameCore.onStartGame(info);            
        
    }

    /**触摸开始 */
    onTouchStart(touch: cc.Touch) {
        // if (!this._gameCore.bFruitIsStop) {
        //     return;
        // }
        this._bDoTouch = false;
        let touchNode = this.LayerItems.Panel_spin_touch;
        clearTimeout((<any>this.LayerItems.Panel_spin_touch).spineTouchTimer);
        (<any>touchNode).spineTouchTimer = setTimeout(() => {
            if (!this._bDoTouch && this._gameCore.nSpinState != this._gameCore.SpinState.Free) {//this._gameCore.bFruitIsStop && 
                this._bDoTouch = true;
                clearTimeout((<any>touchNode).spineTouchTimer);
                this.setSpinAutoState(!this._gameCore.bAutoSpin);
            }
        }, 500);

        touchNode.getParent().setScale(1.1);
    }
    /**触摸移动 */
    onTouchMove(touch: cc.Touch) {
        //TODO
    }
    /**触摸结束 */
    onTouchEnd(touch: cc.Touch) {
        clearTimeout((<any>this.LayerItems.Panel_spin_touch).spineTouchTimer);
        this.LayerItems.Panel_spin_touch.getParent().setScale(1);
        if (this._gameCore.nSpinState != this._gameCore.SpinState.Free) {
            if (this._bDoTouch) {
                this._bDoTouch = false
            }
            else {
                this.setSpinAutoState(false);
            }

            if (this._gameCore.bFruitIsStop) {
                this.onClickSpin();
            }
        }
    }
    /**触摸取消 */
    onTouchCancel(touch: cc.Touch) {
        clearTimeout((<any>this.LayerItems.Panel_spin_touch).spineTouchTimer);
        this._bDoTouch = false
        if (this._gameCore.bFruitIsStop) {
            this.onClickSpin();
        }
    }

    /**刷新按钮状态 */
    updateSpinState(nSpinState: number,resetFlag: boolean = false, bReplayBgMusic: boolean = true) {
        //状态相同不处理
        if (this._gameCore.nSpinState == nSpinState && !resetFlag) {
            return;
        }

        //刷新按钮状态
        this._gameCore.nSpinState = nSpinState;

        this.LayerItems.btn_spin.active = false;
        this.LayerItems.btn_free.active = false;
        this.LayerItems.btn_auto.active = false;
        this.LayerItems.Panel_free.active = false;

        let btn_spin_txt_1_sp = this.LayerItems.Image_spin_txt_1.getComponent(cc.Sprite);
        switch (this._gameCore.nSpinState) {
            case SpinStateFruitMachine.None:
                break;
            case SpinStateFruitMachine.Normal:
                this.changeAddOrSubBtnColor();
                btn_spin_txt_1_sp.spriteFrame = this.mainUiAtlas.getSpriteFrame('slotniu_btn_spin_txt_1');
                this.LayerItems.freeAni.active = false;
                this.LayerItems.btn_spin.active = true;
                this.LayerItems.Image_bg_free.active = false;
                bReplayBgMusic && AudioHelper.instance.playMusic(Sounds.BG_MUSIC);
                this.LayerItems.blockInput.active = false;
                if (this._gameCore.lastAutoSpinState) {
                    this._gameCore.lastAutoSpinState = false;
                    this.setSpinAutoState(true);
                    let self = this;
                    this.scheduleOnce(() => {
                        self.onClickSpin();
                    }, 1.8)
                }
                break;
            case SpinStateFruitMachine.Free:
                this.changeAddOrSubBtnColor();
                bReplayBgMusic && AudioHelper.instance.playMusic("res/sounds/bg_spceail");
                this.LayerItems.btn_free.active = true;
                this.LayerItems.blockInput.active = true;
                //添加免费特效
                this.addFreeAnim();
                break;
            case SpinStateFruitMachine.Single:
                bReplayBgMusic && AudioHelper.instance.playMusic("res/sounds/bg_spceail");
                this.LayerItems.btn_spin.active = true;
                btn_spin_txt_1_sp.spriteFrame = this.mainUiAtlas.getSpriteFrame('slotniu_btn_spin_txt_2');
                this.LayerItems.blockInput.active = false;
                //添加免费特效
                this.addFreeAnim();
                if (this._gameCore.lastAutoSpinState || this._gameCore.bAutoSpin) {
                    this._gameCore.lastAutoSpinState = false;
                    this.setSpinAutoState(true);
                    let self = this;
                    this.scheduleOnce(() => {
                        self.onClickSpin();
                    }, 1.8)
                }
                break
            case SpinStateFruitMachine.Double:
                bReplayBgMusic && AudioHelper.instance.playMusic("res/sounds/bg_spceail");
                this.LayerItems.btn_spin.active = true;
                this.LayerItems.blockInput.active = false;
                btn_spin_txt_1_sp.spriteFrame = this.mainUiAtlas.getSpriteFrame('slotniu_btn_spin_txt_2');
                //添加免费特效
                this.addFreeAnim();
                if (this._gameCore.lastAutoSpinState || this._gameCore.bAutoSpin) {
                    this._gameCore.lastAutoSpinState = false;
                    this.setSpinAutoState(true);
                    let self = this;
                    this.scheduleOnce(() => {
                        self.onClickSpin();
                    }, 1.8)
                }
                break
            default:
                break;
        }
    }

    /**刷新Free按钮次数 */
    updateFreeTimes() {
        this.LayerItems.free_times.getComponent(cc.Label).string = `${this._gameCore.nLastFreeTimes}`;
    }

    /**添加免费特效 */
    addFreeAnim() {
        this.LayerItems.Image_bg_free.active = true;
        this.LayerItems.freeAni.active = true;
    }

    onDestroy() {
        this.LayerItems.timeOutRunNode.stopAllActions();
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
    }

    //显示当前选定下注金额
    showBetConfig(showTips: boolean = false) {
        this.LayerItems.bet_num.getComponent(cc.Label).string = `${this._gameCore.betMoney / Config.SCORE_RATE}`;
        this.LayerItems.betNumDesc.getComponent(cc.Label).string = `${(this._gameCore.betMoney / Config.SCORE_RATE / Constant.BET_RATE_NUM).toFixed(2)}x${Constant.BET_RATE_NUM}`;
        cc.sys.localStorage.setItem(Constant.LOCAL_STORAGE_BET_NUM_KEY, this._gameCore.betMoney / Config.SCORE_RATE); //保存到本地
        showTips && (this.showJackpotTips());
    }

    //彩金提示
    showJackpotTips() {
        for (let i = 1; i <= JACKPOT_PERCENT.length; i++) {
            let txt_prize = cc.find(`Node_${i}/txt_prize`, this.LayerItems.Node_JackpotTips).getComponent(cc.Label);
            txt_prize.string = `₹${parseInt((JACKPOT_PERCENT[7 - i][this._gameCore.meAddBeiLv] * this._gameCore.jackpotnum / 10000).toString())}`;
        }

        let aniNode = this.LayerItems.Node_JackpotTips.getChildByName('Image_1');
        if (this.LayerItems.Node_JackpotTips.active) {
            this.LayerItems.Node_JackpotTips.opacity = 255;
            aniNode.stopAllActions();
        }
        else {
            this.LayerItems.Node_JackpotTips.active = true;
            this.LayerItems.Node_JackpotTips.opacity = 0;
            cc.tween(this.LayerItems.Node_JackpotTips)
                .to(0.5, { opacity: 255 })
                .start();
        }
        cc.tween(aniNode)
            .delay(2)
            .call(() => {
                this.LayerItems.Node_JackpotTips.active = false;
            })
            .start();
    }

    //更新自己身上金额
    showSelfMoney() {
        this.LayerItems.scoreMoney.getComponent(cc.Label).string = this.moneyFormat(this._gameCore.playScoreMoney,2);
    }

    // 获取金币字符串 count: 0不加K; 1-n: 超过指定位数时加上K.  decimals: 显示小数位数
    moneyFormat(money: number, decimals: number = 0): string {
        return (money / Config.SCORE_RATE).toFixed(decimals);
    }

    //更新PRIZE奖池
    updatePrizeNum(prizeNum: number) {
        this.LayerItems.font_prize.getComponent(cc.Label).string = prizeNum.toFixed(1);
    }

    //刷新当前奖池金额
    updateJackpotNum(jackpotnum?: number) {

        if (Common.isNull(jackpotnum)) {
            return;
        }

        this._gameCore.jackpotnum = jackpotnum;

        let self = this;
        let allJackpotNumLabel = self.LayerItems.allJackpotNum.getComponent(cc.Label)

        if (!this._gameCore.nOldJackpotNum) {
            allJackpotNumLabel.string = `${this.formatNumberForComma(this._gameCore.jackpotnum, true)}`;
            this._gameCore.nOldJackpotNum = this._gameCore.jackpotnum;
            return;
        }

        this.scrollNumber({
            txt: allJackpotNumLabel,
            began: self._gameCore.nOldJackpotNum,
            end: self._gameCore.jackpotnum,
            format: (nValue) => {
                allJackpotNumLabel.string = `${self.formatNumberForComma(Common.toInt(nValue), true)}`;
            }
        });

        this._gameCore.nOldJackpotNum = this._gameCore.jackpotnum;
    }

    /**金额用','隔开，例如：10000 => 10,000 千分  formatFlag 奖池指定格式 123456 => 1,23,456*/
    public formatNumberForComma(data: number | string, formatFlag: boolean = false) {
        let list = [];
        let str = `${data}`;
        for (let i = str.length - 1, j = 1; i >= 0; --i, ++j) {
            list.unshift(str[i]);
            if (formatFlag) {
                j == 3 && list.unshift(`,`);
                j == 5 && list.unshift(`,`);
            }
            else {
                j % 3 == 0 && list.unshift(`,`);
            }
        }
        if (list[0] == `,`) {
            list.shift();
        }
        return list.join(``);
    }

    //断线重连
    public onToOtherRoom(info) {
        this.LayerItems.turnTableFirstShowAni.active = false;
        this.LayerItems.blockInput_turntable.active = false;
        this._gameCore.lastAutoSpinState = false;
        (<any>this.LayerItems.runAniNode).bRunAct = false;
        this.setSpinAutoState(false);
        this.updateSpinState(SpinStateFruitMachine.Normal);
        this.LayerItems.Panel_win.active = false;
        if(this.turnTableLayer){
            this.turnTableLayer.stopAllActions();
            this.turnTableLayer.removeFromParent();
            this.turnTableLayer = null;
        }
        this.LayerItems.blockInput_turntable.active = false;
        
        if(info["wildtype"] > 0){ //断线wild列状态显示
            let wildList = [SpinStateFruitMachine.Single,SpinStateFruitMachine.Double];
            this.updateSpinState(wildList[info["wildtype"] - 1]);
        }

        if(info["playerlist"]){
            if(cc.winSize.width/cc.winSize.height >= 2){
                this.onUpdateDeskPlayersInfo(info["playerlist"]);
            } 
        }
       
        if(info["luckyspin"] > 0){
            this._gameCore.reconnectLuckyjackpot = info["luckyjackpot"]?? 0;
            this._gameCore.reconnectNiuNum = info["luckyniunum"]?? 0;
            this._lotteryBox.showTurnTable(null)
        }
        this.runNotBetToQuick();
    }

    //字符格式化
    public stringFormat(strContent: string, params: any[]): string {
        var str = strContent;
        for (var i = 0; i < params.length; i++) {
            var reg = new RegExp("\\{" + i + "\\}", "gm");
            str = str.replace(reg, params[i]);
        }
        return str;
    }

    //播放其它玩家Jackpot中奖动画
    playerOhterJackpotAni() {
        let self = this;

        if(!self || !self.LayerItems.runAniNode){
            return;
        }

        if (this._gameCore.otherJackpotList.length == 0) {
            (<any>self.LayerItems.runAniNode).bRunAct = false;
            this.LayerItems.runAniNode.stopAllActions();
            return;
        }

        if (!(<any>self.LayerItems.runAniNode).bRunAct) {
            (<any>self.LayerItems.runAniNode).bRunAct = true;
            cc.tween(self.LayerItems.runAniNode)
                .repeatForever(
                    cc.tween()
                        .call(() => {
                            self.sliceOtherJackpotList();
                        })
                        .delay(10)
                        .call(() => {
                            cc.find('Node1/Sprite1', self.LayerItems.FileNode_jackpot_3).opacity = 0;
                            let ani = self.LayerItems.FileNode_jackpot_3.getComponent(cc.Animation)
                            ani.stop();

                            self.LayerItems.allJackpotNum.active = true;
                            self.LayerItems.Node_info.active = false;

                            (<any>self.LayerItems.runAniNode).bRunAct = false;
                            self.LayerItems.runAniNode.stopAllActions();
                            if (self._gameCore.otherJackpotList.length > 0) {
                                self.playerOhterJackpotAni();
                            }
                        })
                )
                .start();
        }

    }

    //更新移除在线玩家列表
    sliceOtherJackpotList() {
        if (this._gameCore.otherJackpotList.length > 0) {
            let info = this._gameCore.otherJackpotList[0];
            if (!!info) {
                //中Jackpot的玩家在桌上
                if(this.deskPlayerPlayJackpot(info.playerid,info.winscore)){
                    return;//中桌上 不播上面中奖
                }
                let headImg = this.LayerItems.other_headImg.getComponent(cc.Sprite);
                super.setPlayerHead(headImg, info.headid, info.wxheadurl ?? "");
                this.LayerItems.otherPlayerJackpot.getComponent(cc.Label).string = this.moneyFormat(info.winscore ?? 0);
                this._gameCore.otherJackpotList.splice(0, 1);
                let ani: cc.Animation = this.LayerItems.FileNode_jackpot_3.getComponent(cc.Animation)
                let animState = ani.play('animation0');
                AudioHelper.instance.stopEffectByPath(`res/sounds/jackpot`)
                AudioHelper.instance.playEffect(`res/sounds/jackpot`);
                animState.wrapMode = cc.WrapMode.Normal;
                this.LayerItems.allJackpotNum.active = false;
                this.LayerItems.Node_info.active = true;
                ani.on('finished', (() => {
                    let animState = ani.play('animation1');
                    animState.wrapMode = cc.WrapMode.Loop;
                }), this);
            }
        }
    }

    //中jackpot的玩家在桌上
    deskPlayerPlayJackpot(playerid,winscore){
        if(cc.winSize.width/cc.winSize.height < 2){
            return false;
        }

        let bReturn = false;
        for(let i = 0; i < this._gameCore.deskPlayerInfoList.length;i++){
            let playerNodeInfo = this._gameCore.deskPlayerInfoList[i];
            playerNodeInfo.node.active = true;
            if(!Common.isNull(playerid) && playerNodeInfo.playerid == playerid){
                bReturn = true;//在桌上的不播上面中奖
                playerNodeInfo.playingAni = true;
                this.deskPlayerPlayWin(0,playerNodeInfo,{pmoney:playerNodeInfo.curMoney,winscore:winscore},true);
                break;
            }
        }
        return bReturn;
    }

     //更新桌上玩家信息
     onUpdateDeskPlayersInfo(info){
        for(let i = 0; i < this._gameCore.deskPlayerInfoList.length;i++){
            let playerNodeInfo = this._gameCore.deskPlayerInfoList[i];
            playerNodeInfo.node.active = true;
            let headSp = playerNodeInfo.headNode.getComponent(cc.Sprite);
            let player = info[(i + 1) + ''];
            if(!!player && !Common.isNull(player.playerid)){
                if(player.playerid == playerNodeInfo.playerid){//同个玩家
                    if(!!playerNodeInfo.playingAni){//是否在播动画
                        //播动画暂时不管 下次再更新
                    }
                    else{
                        playerNodeInfo.curMoney = player.pmoney;
                        playerNodeInfo.goldNode.getComponent(cc.Label).string = this.moneyFormat(player.pmoney ?? 0,2);
                    }
                }
                else{
                    playerNodeInfo.headSpinAniNode.active = false;
                    playerNodeInfo.winNode.active = false;
                    playerNodeInfo.infoBgNode.active = true;
                    this.setPlayerHead(headSp, player.headid, player.wxheadurl ?? "");
                    playerNodeInfo.curMoney = player.pmoney;
                    playerNodeInfo.nameNode.getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");
                    playerNodeInfo.goldNode.getComponent(cc.Label).string = this.moneyFormat(player.pmoney ?? 0,2);
                    playerNodeInfo.playerid = player.playerid;
                }
            }
            else{
                playerNodeInfo.infoBgNode.active = false;
                playerNodeInfo.headSpinAniNode.active = false;
                playerNodeInfo.winNode.active = false;
                headSp.spriteFrame = this.mainUiAtlas.getSpriteFrame('slotniu_player_empty');
                playerNodeInfo.playerid = null;
                playerNodeInfo.curMoney = 0;
            }
        }
    }


    //更新桌上玩家游戏结果
    onUpdateDeskPlayersResultInfo(info){
        for(let i = 0; i < this._gameCore.deskPlayerInfoList.length;i++){
            let playerNodeInfo = this._gameCore.deskPlayerInfoList[i];
            playerNodeInfo.node.active = true;
            if(!Common.isNull(info.playerid) && playerNodeInfo.playerid == info.playerid){
                this.playDeskPlayerAni(playerNodeInfo,info);
                break;
            }
        }
    }



    //播放桌上玩家动画
    playDeskPlayerAni(playerNodeInfo: DeskPlayersInfo,info){
        playerNodeInfo.playingAni = true;
        playerNodeInfo.aniNode.removeAllChildren();
        playerNodeInfo.aniNode.active = true;
        playerNodeInfo.headSpinAniNode.active = true;
        playerNodeInfo.headSpinAniNode.getComponent(cc.Animation).play();
        playerNodeInfo.headSpinAniNode.opacity = 255;
        playerNodeInfo.headSpinAniNode.stopAllActions();

        cc.tween(playerNodeInfo.headSpinAniNode)
        .delay(1.0)
        .to(0.5,{opacity:0})
        .call(()=>{
            if(info.winscore <= 0){
                playerNodeInfo.playingAni = false;
            }
            playerNodeInfo.headSpinAniNode.active = false;
        })
        .start();

        if(info.winscore <= 0){
            this.scorllToGold(playerNodeInfo,info,false);
            return;
        }

        let mult = info.mult / 10;//倍数
        if(mult < 4){//4倍以下不播弹出界面动画 只播数字滚动
            this.scorllToGold(playerNodeInfo,info,false);
            return;
        }
        this.deskPlayerPlayWin(mult,playerNodeInfo,info);
    }

    //桌上玩家赢弹窗
    deskPlayerPlayWin(mult,playerNodeInfo,info,bJackpot: boolean = false){
        let playNode = this.otherWin_mega;
        let soundName = 'res/sounds/midWin_other';
        if(bJackpot){
            playNode = this.otherWin_jackpot;
            soundName = '';
        }
        else{
            if(mult >= 8){
                playNode = this.otherWin_super;
                soundName = 'res/sounds/bigWin_other';
            }
        }
        
        if(soundName.length > 0){
            this.scheduleOnce(() => {
                AudioHelper.instance.playEffect(soundName);
            }, 0.02)
        }
                
        let ani = cc.instantiate(playNode);
        playerNodeInfo.aniNode.addChild(ani);
        ani.active = true;
        ani.getComponent(cc.Animation).play();
        let self = this;
        ani.getComponent(cc.Animation).on('finished', (() => {
            self.deskPlayerFlyScore(playerNodeInfo,info);
        }), this);
    }

    //桌上玩家飘分
    deskPlayerFlyScore(playerNodeInfo,info){
        playerNodeInfo.aniNode.removeAllChildren();
        playerNodeInfo.winOrLoseNumNode.getComponent(cc.Label).string = '+' + this.moneyFormat(info.winscore ?? 0,2);
        playerNodeInfo.winOrLoseNumNode.setContentSize(playerNodeInfo.winOrLoseNumNode.getContentSize());//重新读取size大小
        playerNodeInfo.winBgNode.setContentSize(cc.size(playerNodeInfo.winOrLoseNumNode.getContentSize().width + 60,playerNodeInfo.winBgNode.getContentSize().height));
        playerNodeInfo.winNode.stopAllActions();
        playerNodeInfo.winNode.setPosition(cc.v2(0,0));
        playerNodeInfo.winNode.opacity = 255;
        playerNodeInfo.winNode.active = true;
        cc.tween(playerNodeInfo.winNode)
        .delay(1.0)
        .by(1.0,{position:cc.v3(0,10)})
        .parallel(
            cc.tween().to(1.0,{opacity:0}),
            cc.tween().by(1.0,{position:cc.v3(0,10)})
        )
        .call(()=>{
            playerNodeInfo.winNode.opacity = 255;
            playerNodeInfo.winNode.active = false;
            playerNodeInfo.playingAni = false;
        })
        .start();

        this.scorllToGold(playerNodeInfo,info,true);
    }

    //滚动身上金币
    scorllToGold(playerNodeInfo,info,playingAni: boolean = true){
        //身上余额滚动
        let nScrollTime = 5;
        let nScrollIndex = 0;

        playerNodeInfo.goldNode.stopAllActions();
        cc.tween(playerNodeInfo.goldNode)
        .repeat(
            nScrollTime,
            cc.tween()
            .delay(1.0 / nScrollTime)
            .call(()=>{
                nScrollIndex += 1;
                let nScore = info.pmoney - info.winscore * (1 - nScrollIndex / nScrollTime);
                playerNodeInfo.goldNode.getComponent(cc.Label).string = this.moneyFormat(nScore ?? 0,2);
                if(!playingAni){
                    playerNodeInfo.playingAni = false;
                }
            })
        )
        .start();
    }

    //道具特效运行
    public doRoomIntertChat(sendPlayerPos, receiverPlayerPos, content) {

        let sendPos = this.getHeadNodePositon(sendPlayerPos);
        let receiverPos = this.getHeadNodePositon(receiverPlayerPos);

        if(Common.isNull(sendPos) || Common.isNull(receiverPos)){
            return;
        }

        let startPos = cc.v3(this.gameEmojiNode.convertToNodeSpaceAR(sendPos));
        let endPos = cc.v3(this.gameEmojiNode.convertToNodeSpaceAR(receiverPos));
        let Direction1 = Direction.LEFT;
        if(receiverPlayerPos%2 == 1){
            Direction1 = Direction.RIGHT;
        }

        UIHelper.playInteractExpression(this.gameEmojiNode, startPos, endPos, content, Direction1 == Direction.LEFT);
    }

    // 点击玩家头像
    private onClickHead(target: any, customEventData: any) {

        let self = this;
        let headPos = Common.toInt(customEventData);

        let playerid = this.getPlayerIdAtHeadPos(headPos);
        if(!playerid){//座上有效玩家 
            this._curClickHeadIndex = -1;
            return;
        } 

        let pos = this.getHeadNodePositon(headPos);
        if(Common.isNull(pos)){
            this._curClickHeadIndex = -1;
            return;
        }
        let showPos = self.gameEmojiNode.convertToNodeSpaceAR(pos);
        headPos%2 == 0 ? showPos.x += 350 : showPos.x -= 350;
       
        this._curClickHeadIndex = headPos;
        
        let playerInfo = { sendPlayerid: self._gameCore.playerid, receiverPlayerid: playerid,
            singlechatfee:this._gameCore.singlechatfee};
        UIHelper.showInteractExpression(self.gameEmojiNode, showPos, playerInfo, (info: any) => {
            self._gameCore.sendChatMessage(info);
        });
    }

    //玩家头像位置
    getHeadNodePositon(pos: number){
        let newPos: cc.Vec2 = null;
        let userHeadNode: cc.Node = null;

        if(Common.isNull(pos)){
            return newPos;
        }

        if(pos == Constant.customMyselfPos){
            userHeadNode = this.LayerItems.myselfNode;
        }
        else{
            userHeadNode = this._gameCore.deskPlayerInfoList[pos].headBgNode ;
        }
        
        if(Common.isNull(userHeadNode)){
            return newPos;
        }

        newPos = userHeadNode.convertToWorldSpaceAR(userHeadNode.getAnchorPoint())

        return newPos;
    }

    //根据玩家id获取座上玩家位置 包含自己  bReceiverPlayerid:是否道具接收者 如果是 优先从座上玩家列表取位置 （主要处理自己同时坐在桌上六个玩家位置时）
    getHeadPosAtPlayerId(playerId: number,bReceiverPlayerid: boolean = false){
        let pos = null;

        if(Common.isNull(playerId)){
            return pos;
        }
        if(!!bReceiverPlayerid){
            if(this._curClickHeadIndex == Constant.customMyselfPos && playerId == this._gameCore.playerid){//自定义自己的一个位置
                pos = Constant.customMyselfPos;
            }
            else{
                for (let i = 0; i < this._gameCore.deskPlayerInfoList.length; i++) {
                    if (!!playerId && playerId > 0 && playerId == this._gameCore.deskPlayerInfoList[i].playerid) {
                        pos = i;
                        break;
                    }
                }
    
                if(Common.isNull(pos) && playerId == this._gameCore.playerid){//自定义自己的一个位置
                    pos = Constant.customMyselfPos;
                }
            }  
        }
        else{//发送者 优先从自己位置
            if(playerId == this._gameCore.playerid){//自定义自己的一个位置
                pos = Constant.customMyselfPos;
            }
            else{
                for (let i = 0; i < this._gameCore.deskPlayerInfoList.length; i++) {
                    if (!!playerId && playerId > 0 && playerId == this._gameCore.deskPlayerInfoList[i].playerid) {
                        pos = i;
                        break;
                    }
                }    
            }
        }
        
        return pos;
    }

    //根据玩家位置获取座上玩家id 包含自己
    getPlayerIdAtHeadPos(pos: number){
        let playerid = null;
        
        for (let i = 0; i < this._gameCore.deskPlayerInfoList.length; i++) {
            if (pos != null && pos >= 0 && i == pos && this._gameCore.deskPlayerInfoList[i].playerid > 0) {
                playerid = this._gameCore.deskPlayerInfoList[i].playerid;
                break;
            }
        }
        
        if(!playerid && pos == Constant.customMyselfPos){//自定义自己的一个位置 
            playerid = this._gameCore.playerid;
        }
                       
        return playerid;
    }

    
    
    //////////////////////////////////////////////////////////////////////////////
    // 重置所有界面数据
    public resetAllUI() {
        // 重置玩家界面
    }

    //////////////////////////////////////////////////////////////////////////////
    // 返回大厅
    public onClickBack() {
        this._gameCore.quitGame();
    }

    // 点击充值
    public onClickCharge() {
        HallManager.instance.openCharge();
    }

    //加注
    onClickAdd() {
        if(this._gameCore.nSpinState == SpinStateFruitMachine.Single || this._gameCore.nSpinState == SpinStateFruitMachine.Double){ //中wild情况时，不允许加减
            return;
        }
        if(!this._gameCore.bFruitIsStop){
            return;
        }
        AudioHelper.instance.playEffect(`res/sounds/jetton`);
        this._gameCore.onBetAdd();
        this.showBetConfig(true)
    }

    //减注
    onClickSub() {
        if(this._gameCore.nSpinState == SpinStateFruitMachine.Single || this._gameCore.nSpinState == SpinStateFruitMachine.Double){ //中wild情况时，不允许加减
            return;
        }
        if(!this._gameCore.bFruitIsStop){
            return;
        }
        AudioHelper.instance.playEffect(`res/sounds/jetton`);
        this._gameCore.onBetSub();
        this.showBetConfig(true)
    }

    //按钮是否允许点击，不允许时变色
    changeAddOrSubBtnColor(){
        let bEnabled = true;
        if(this._gameCore.nSpinState == SpinStateFruitMachine.Single || this._gameCore.nSpinState == SpinStateFruitMachine.Double
            || !this._gameCore.bFruitIsStop || this._gameCore.nSpinState == SpinStateFruitMachine.Free ){
                bEnabled = false;
        }

        let color = bEnabled ? cc.color(255,255,255) : cc.color(128,128,128);
        this.LayerItems.btn_jian.color = color;
        this.LayerItems.btn_jia.color = color;
        this.LayerItems.btn_jian.getComponent(ButtonLayer).enableTouch = bEnabled
        this.LayerItems.btn_jia.getComponent(ButtonLayer).enableTouch = bEnabled
    }

    //点击开始免费游戏
    onClickStartFreeGame() {

        this.updateSpinState(SpinStateFruitMachine.Free);

        this.autoStartFreeGame();
    }

    //自动开始免费游戏
    autoStartFreeGame() {
        AudioHelper.instance.playEffect(Sounds.SPIN);
        this._gameCore.resultData = this._gameCore.resultFreeData[(this._gameCore.nLastMaxFreeTimes - this._gameCore.nLastFreeTimes + 1).toString()];
        this._gameCore.freeWinTotalNum += this._gameCore.resultData.winmoney;
        this._gameCore.freeChangeTotalNum += this._gameCore.resultData.changemoney;

        this._gameCore.nLastFreeTimes -= 1;
        this.updateFreeTimes();//刷新Free按钮次数

        this._gameCore.freeTimesEndFlag = FREE_TYPE.playing;

        if (this._gameCore.nLastFreeTimes == 0) {
            this._gameCore.freeTimesEndFlag = FREE_TYPE.end;
        }

        if (!!this._gameCore.resultData) {
            this._lotteryBox.startGame();
        }
    }


    //帮助
    public onClickHelp() {
        if(!this.helpNode){
            this.helpNode = cc.instantiate(this.helpPrefab);
            SceneManager.instance.addChildNode(this.helpNode);
        }
        
        this._gameLogic.popUpEffect(this.helpNode, true);
    }

    //jackpot记录
    public onClickJackpotRecord() {
        if(!this.jackpotRecordLayer){
            this.jackpotRecordLayer = cc.instantiate(this.jackpotRecordPrefab);
            SceneManager.instance.addChildNode(this.jackpotRecordLayer);
        }

        let self = this;
        this._gameLogic.popUpEffect(this.jackpotRecordLayer, true,(()=>{
            self.jackpotRecordLayer.getComponent("FMWJackpotRecordLayer").setData();
        }));
    }

    //////////////////////////////////////////////////////////////////////////////

}
