import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import PopupLayer from "../../../../script/frame/component/PopupLayer";
import Config from "../../../../script/frame/config/Config";
import EventManager from "../../../../script/frame/manager/EventManager";
import { Constant, GameEvent, JACKPOT_PERCENT } from "../core/FMWDefine";
import FMWGameLogic from "../core/FMWGameLogic";
import FMWGameView from "./FMWGameView";

const { ccclass, property } = cc._decorator;



@ccclass
export default class FMWHelpLayer extends BaseLayer {

    //content父节点
    @property(cc.Node)
    node_content: cc.Node = null;

    //第二页上彩金数据节点
    @property(cc.Node)
    content: cc.Node = null;

    //第二页上彩金数据添加的item节点
    @property(cc.Node)
    List_jackpot_item: cc.Node = null;
    
    //节点列表
    _nodePageList: cc.Node[] = [];

     /** 游戏主对象 */
     private _gameCore: any;
   
    //当前页
    mCurPage: number = 0;
    //最大显示页
	mMaxPage: number = 6;

    //是否初始化过第二页数据
    initTwoPageDataFlag: boolean = false;
    _gameLogic :FMWGameLogic = null;
    _gameView: FMWGameView = null;
    
    onLoad() {
       this._gameCore = cc.Canvas.instance.getComponent("FMWGameCore"); 
       this._gameLogic = cc.Canvas.instance.getComponent("FMWGameLogic"); 
       this._gameView = cc.Canvas.instance.getComponent("FMWGameLogic");
       this.node_content.children.forEach(item=>this._nodePageList.push(item));
       //动态导入所有节点
       this.LoadAllLayerObject(this.node)
    }

    start() {
        cc.tween(this.LayerItems.sp_left)
        .repeatForever(
            cc.tween().by(0.5,{position: cc.v2(0,10)})
            .by(0.5,{position: cc.v2(0,-10)})
        )
        .start();

        cc.tween(this.LayerItems.sp_right)
        .repeatForever(
            cc.tween().by(0.5,{position: cc.v2(0,10)})
            .by(0.5,{position: cc.v2(0,-10)})
        )
        .start();
    }

	/**设置jackpot数据*/
	setJackpot() {
		for (let i = JACKPOT_PERCENT.length - 1; i >= 0; --i) {
			let item = cc.instantiate(this.List_jackpot_item);
			item.active = true;
			this.content.addChild(item);

			item.getChildByName('Text_0').getComponent(cc.Label).string = `x${i + 3}`;
			for (var j = 0; j < 7; ++j) {
                item.getChildByName(`Text_${7 - j}`).getComponent(cc.Label).string = `₹${parseInt((JACKPOT_PERCENT[i][j] * this._gameCore.jackpotnum / 10000).toString())}`;
			}
		}
	}

    public onClickClose() {
        this._gameLogic.popUpEffect(this.node,false);
    }

    //点击翻页
    onClickPage(target: any, customEventData: any){
        let index = Common.toNumber(customEventData);
        if(index == 1){//右翻页
            this.mCurPage = (this.mCurPage + 1)%this.mMaxPage;
        }
        else{//左翻页
            this.mCurPage = (this.mCurPage - 1 + this.mMaxPage)%this.mMaxPage;
        }
        if(this.mCurPage == 1 && !this.initTwoPageDataFlag){//点击到第二页时,未加载数据,则加载
            this.initTwoPageDataFlag = true;
            this.setJackpot();
        }
        this._nodePageList.forEach((item,mIndex)=>item.active = mIndex == this.mCurPage)
    }

}
