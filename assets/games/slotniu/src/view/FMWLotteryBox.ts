import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import { Constant, FREE_TYPE, ICON_TYPE, Sounds, TURN_TABLE_TYPE, WIN_SPIN_NAME, WildtypeInfo } from "../core/FMWDefine";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import FMWGameCore, { FruitMachineBaseMachineElement, SpinStateFruitMachine, WinLinesInfo } from "../core/FMWGameCore";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import FMWGameView from "./FMWGameView";

const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slotniu/FMWLotteryBox')
export default class FMWLotteryBox extends BaseLayer {

    // 游戏核心对象
    private _gameCore: FMWGameCore = null;
    private _gameView: FMWGameView = null;

    onLoad() {
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)

        this._gameCore = cc.Canvas.instance.getComponent("FMWGameCore");
        this._gameView = cc.Canvas.instance.getComponent("FMWGameView");

        //初始化水果机元素
        this.initMachineElement();
        //初始化加速度
        this.initAcceleration();
    }

    start() {

    }

    /**初始化水果机元素 */
    initMachineElement() {
        //初始化水果
        this._gameCore.machineElement = [];
        let cSize = this.LayerItems.Panel_container.getContentSize();
        let count = 0;
        for (let nRow = 0, nRowMax = Constant.ROW_MAX; nRow < nRowMax; ++nRow) {
            this._gameCore.machineElement[nRow] = [];
            for (let nCol = 0, nColMax = Constant.COL_SHOW; nCol < nColMax; ++nCol) {
                count += 1;
                let node = cc.instantiate(this.LayerItems.item_icon);
                node.name = 'icon' + count;
                this.LayerItems.Panel_container.addChild(node);
                node.active = true;

                let nType = Common.random(0, Constant.ELEMENT_MAX_NUM - 1);

                let machineElement: FruitMachineBaseMachineElement = {
                    node: node,
                    nRow: nRow,
                    nCol: nCol,
                };

                this._gameCore.machineElement[nRow][nCol] = machineElement;
                //保存引用
                this._gameCore.machineElementMap.set(node, machineElement);
                //刷新显示
                this.updateMachineElement(node, nType);
                //调整位置
                node.setPosition(cc.v2(
                    - 2 * (cSize.width / nColMax) + (nCol * (cSize.width / nColMax)),
                    cSize.height - (nRow * (cSize.height / Constant.ROW_SHOW))
                ));
                
                if ( (nType == ICON_TYPE.NUM_LU || nType == ICON_TYPE.NUM_NIU)) {//鹿和牛默认显示待机动画
                    node.getChildByName('icon').active = false;
                    let aniNode = node.getChildByName('prizeSpin');
                    aniNode.active = true;
                    aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${nType + 1}_idle`, true);
                }
            }
        }

        //wildSpin节点列表
        for (let i = 0; i < Constant.COL_SHOW; i++) {
            let spin = this.LayerItems.Panel_container.parent.getChildByName(`Node_wild_${i + 1}`).getChildByName('wild_spin').getComponent(sp.Skeleton);
            this._gameCore.wildSpinList.push(spin);
        }

        //流光动画列表
        for (let i = 0; i < Constant.COL_SHOW + 1; i++) {
            let spin = this.LayerItems.Node_lightAni.getChildByName(`liuGuangLightSpin${i + 1}`).getComponent(sp.Skeleton);
            this._gameCore.liuGuangLightSpinList.push(spin);
        }
    }

    /**次数 + 初始速度 + 加速度 需要满足一定条件才能正常显示 */
    initAcceleration() {
        //移动固定距离
        let cSize = this.LayerItems.Panel_container.getContentSize();
        let nOneHeight = cSize.height / Constant.ROW_SHOW * Constant.ROW_MAX;
        let nTotalDistance = nOneHeight * Constant.SCROLL_VIEW_NUM;
        //固定速度移动的距离
        let g = this._gameCore.nSpeedStart * this._gameCore.nStepNumMax;
        //前段
        // let s = this._gameCore.nStepNumStart * (this._gameCore.nStepNumStart + 1) / 2 * this._gameCore.nAcceleration;
        // //中段
        // let m = this._gameCore.nStepNumMiddle * this._gameCore.nStepNumStart * this._gameCore.nAcceleration;
        // //尾段
        // let e = s - (this._gameCore.nStepNumStart - this._gameCore.nStepNumEnd) * (this._gameCore.nStepNumStart - this._gameCore.nStepNumEnd + 1) / 2 * this._gameCore.nAcceleration;
        //加速度
        this._gameCore.nAcceleration = (nTotalDistance - g) /
            ((this._gameCore.nStepNumStart * (this._gameCore.nStepNumStart + 1) +
                this._gameCore.nStepNumMiddle * this._gameCore.nStepNumStart -
                (this._gameCore.nStepNumStart - this._gameCore.nStepNumEnd) * (this._gameCore.nStepNumStart - this._gameCore.nStepNumEnd + 1) / 2));
    }

    /**刷新水果机元素 */
    updateMachineElement(node: cc.Node, nType: number) {
        let info = this._gameCore.machineElementMap.get(node);
        info && (this._gameCore.machineElement[info.nRow][info.nCol].nType = nType);
        !!node && (node.getChildByName('icon').getComponent(cc.Sprite).spriteFrame = this._gameView.mainUiAtlas.getSpriteFrame(`slotniu_icon_${nType}`));
    }

    //开始游戏 
    startGame() {
        this._gameCore.setResultDataInfo();
        this.getGrandPrizeForecastData();
        this.doDrawLottery();
        this.playWildAni();
    }

    //重置初始数据
    resetData() {
        this.hideAllIdleSpin();
        this.LayerItems.turnTableFirstShowAni.active = false;
        this.LayerItems.blockInput_turntable.active = false;
        this._gameView.checkWinLabelHide();
        this._gameCore.resetDataValue();

        for (let i = 0; i < this._gameCore.machineElement.length; i++) {
            for (let j = 0; j < this._gameCore.machineElement[i].length; j++) {
                let machineElement = this._gameCore.machineElement[i][j];
                machineElement.nStopRow = null;
                machineElement.nMoveIndex = null;
                machineElement.isOK = false;
                (<any>machineElement.node).bPlay = false;
            }
        }
    }

    //是否播放wild动画
    playWildAni() {
        let self = this;
        //当前已经播放到了第几列
        if (this._gameCore.resultData?.wildtype > 0) { //wildtype: 0 没有wild动画  1:播放一行wild动画 2:播放2行wild动画
            if (this._gameCore.resultData.wildnum > 0) {
                this.playWildAnim(this._gameCore.resultData.wildnum - 1);
            }
        }
        else {
            this._gameCore.wildSpinList.forEach((item, index) => self.stopWildAnim(index));
        }
    }

    /**水果机滑动 匀速 调用把函数update1 改成update*/
    update1(dt: number) {
        if (this._gameCore.bFruitIsStop) {
            return;
        }

        //水果机滑动界面大小
        let cSize = this.LayerItems.Panel_container.getContentSize();
        //顶部坐标
        let t = (cSize.height / Constant.ROW_SHOW) * (Constant.ROW_MAX / 2 - 0 - 0.5);
        //底部坐标
        let d = (cSize.height / Constant.ROW_SHOW) * (Constant.ROW_MAX / 2 - (Constant.ROW_MAX) - 0.5);
        for (let nCol = 0, nColMax = Constant.COL_SHOW; nCol < nColMax; ++nCol) {
            //可滑动时处理
            if (this._gameCore.bScroll[nCol]) {
                //是否处于延时
                let tempStepNumCur = this._gameCore.nStepNumCur[nCol]++ - this._gameCore.nStepNumDelay[nCol];
                if (tempStepNumCur >= 0) {
                    //调整速度
                    let bDecelerate = false;
                    let bSpringback = false;
                    if (tempStepNumCur < this._gameCore.nStepNumStart) {
                        //加速阶段
                        this._gameCore.nSpeedCur[nCol] += this._gameCore.nAcceleration;
                    } else if (tempStepNumCur > this._gameCore.nStepNumMax - this._gameCore.nStepNumEnd) {
                        //减速阶段
                        bDecelerate = true;
                        this._gameCore.nSpeedCur[nCol] -= this._gameCore.nAcceleration;
                        //回弹阶段
                        bSpringback = tempStepNumCur > this._gameCore.nStepNumMax - this._gameCore.nStepNumSpringback;
                    } else {
                        //TODO
                    }
                    for (let nRow = 0, nRowMax = Constant.COL_SHOW; nRow < nRowMax; ++nRow) {
                        let machineElement = this._gameCore.machineElement[nRow][nCol];
                        // wild列元素不显示
                        machineElement.node.active = !this.isWildCol(nCol);
                        let pos = cc.v3(machineElement.node.getPosition());
                        pos.y -= this._gameCore.nSpeedCur[nCol];
                        //非回弹阶段
                        if (!bSpringback) {
                            //超过底部
                            if (pos.y < d) {
                                //置顶
                                pos.y = t + pos.y - d;
                                //刷新水果样式
                                do {
                                    //是否为最后一次
                                    if (bDecelerate) {
                                        let nLeftStep = this._gameCore.nStepNumMax - tempStepNumCur;
                                        let nDistance = nLeftStep * this._gameCore.nSpeedCur[nCol] - nLeftStep * (nLeftStep + 1) / 2 * this._gameCore.nAcceleration;
                                        let nOneHeight = cSize.height / Constant.ROW_SHOW * Constant.ROW_MAX;
                                        let bEnd = nDistance <= nOneHeight;
                                        if (bEnd && nRow >= this._gameCore.nShowRowMin && nRow <= this._gameCore.nShowRowMax) {
                                            let nType = this._gameCore.fruitData[nRow - this._gameCore.nShowRowMin][nCol];
                                            this.updateMachineElement(
                                                machineElement.node,
                                                nType,
                                            );
                                            if (nType == ICON_TYPE.NUM_LU || nType == ICON_TYPE.NUM_NIU) {//鹿和牛默认显示待机动画
                                                machineElement.node.getChildByName('icon').active = false;
                                                let aniNode = machineElement.node.getChildByName('prizeSpin');
                                                aniNode.active = true;
                                                aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${nType + 1}_idle`, true);
                                            }
                                            break;
                                        }
                                    }
                                   
                                    this.updateMachineElement(machineElement.node, Common.random(0, Constant.ELEMENT_MAX_NUM - 1));
                                } while (0);
                            }
                        }

                        //调整位置
                        machineElement.node.setPosition(pos);
                    }
                }
                //步数满了
                if (tempStepNumCur >= this._gameCore.nStepNumMax - 1) {

                    //显示牛动画 2个及以上
                    this.showNiuAni(nCol);

                    //显示鹿动画 相邻的2个及以上
                    this.showLuAni(nCol);

                    this.checkGrandPrizeForecast(nCol);

                    //音效
                    this.playColScrollEndAudio(nCol);
                    //停止该列滑动
                    this._gameCore.bScroll[nCol] = false;

                    //最后一列，停止所有滑动
                    if (nCol == Constant.COL_SHOW - 1) {
                        //收集牛动画
                        this.playCollectCowAnim();

                        //显示线
                        this.showWinLine();
                        // 隐藏大奖预测
                        this.hideGrandPrizeForecast();
                    }
                }
            }
        }
    }

    update(dt: number) {
        if (this._gameCore.bFruitIsStop) {
            return;
        }
        //水果机滑动界面大小
        let cSize = this.LayerItems.Panel_container.getContentSize();
        let nMaxRollNum = 30;
        for (let nCol = 0; nCol < Constant.COL_SHOW; nCol++) {
            this._gameCore.m_RollNum[nCol] += 1
            let nRollNum = this._gameCore.m_RollNum[nCol]
            nMaxRollNum = nMaxRollNum +  this._gameCore.m_nDoLen[nCol]

            if (nRollNum <= nMaxRollNum) {
                // if (!this.isWildCol(nCol, true)) {//不用判断是否wild
                let nImageIndex = null
                for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                    let rollItem = this._gameCore.machineElement[nRow][nCol];
                    rollItem.nStopRow = rollItem.nStopRow || rollItem.nRow
                    rollItem.nStopRow = rollItem.nStopRow + this._gameCore.m_nOnceMoveLen[nCol]
                    //超出底部时
                    rollItem.nMoveIndex = (rollItem.nMoveIndex || 0) + 1
                    if (rollItem.nStopRow >= Constant.ROW_MAX) {
                        //回到顶部F
                        rollItem.nStopRow = rollItem.nStopRow - Constant.ROW_MAX; 
                        // rollItem.nStopRow = rollItem.nStopRow +  0.01
                        //重置图标，只有2-4的是服务器数据图标，修改需要调整下面的赋值
                        if (nRow >= 2 && nRow <= 4) {
                            //更换图标
                            if (!rollItem.isOK) {
                                //当剩余步数不足一次循环时更变样式
                                if (nMaxRollNum - rollItem.nMoveIndex <= Constant.ROW_MAX / this._gameCore.m_nOnceMoveLen[nCol]) {
                                    
                                    //重置图标，只有2-4的是服务器数据图标，修改需要调整下面的赋值
                                    nImageIndex = this._gameCore.fruitData[nRow - 1 - 1][nCol]

                                    //self.m_RewardGoodsId[i - 1 - 1][j] = nImageIndex
                                    rollItem.isOK = true;
                                    rollItem.nType = nImageIndex;
                                    this.updateMachineElement(
                                        rollItem.node,
                                        nImageIndex,
                                    );
                                    if (nImageIndex == ICON_TYPE.NUM_LU || nImageIndex == ICON_TYPE.NUM_NIU) {//鹿和牛默认显示待机动画
                                        rollItem.node.getChildByName('icon').active = false;
                                        let aniNode = rollItem.node.getChildByName('prizeSpin');
                                        aniNode.active = true;
                                        aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${nImageIndex + 1}_idle`, true);
                                    }
                                } else {
                                    
                                    let rand = Common.random(0, Constant.ELEMENT_MAX_NUM - 1)
                                    this.updateMachineElement(rollItem.node, rand);
                                }
                            }
                        } else {
                            let rand = Common.random(0, Constant.ELEMENT_MAX_NUM - 1)
                            this.updateMachineElement(rollItem.node, rand);
                        }
                    }
                    
                    rollItem.node.y = cSize.height - (rollItem.nStopRow * (cSize.height / Constant.ROW_SHOW));                    
                }

                //滚动结束
                if (nRollNum == nMaxRollNum) {
                    this.playColScrollEndAudio(nCol);
                    this._gameCore.m_curIndex = nCol;
                }
                // }
            }
            else {
                if (4 > this._gameCore.m_curIndex) {

                    if (!this.isWildCol(this._gameCore.m_curIndex + 1, true)) {
                        this.showLightAni(this._gameCore.m_curIndex)
                    }
                    else {
                        this.hideStreamerLightAnim();
                    }
                }

                // if (!this.isWildCol(nCol, true)) {//不用判断是否wild
                let springback = this._gameCore.m_springback[nCol];
                if (springback < Constant.ROW_MAX * Constant.ROW_SHOW) {//界面3*5 最后一个显示的元素
                    for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                        let posY = cSize.height - (nRow * (cSize.height / Constant.ROW_SHOW));
                        let rollItem = this._gameCore.machineElement[nRow][nCol];
                        rollItem.nMoveIndex = 0;
                        rollItem.isOK = false;
                        // 反弹
                        if (nRow >= 2 && nRow <= 4) {
                            if (springback < 7) {
                                rollItem.node.y = (posY - (springback + 1) * 5);
                            } else {
                                rollItem.node.y = (posY - (70 - springback * 5));
                            }
                            //特效
                            if (springback == Constant.ROW_MAX * Constant.ROW_SHOW - 2) { //倒数第二个元素

                                this.checkGrandPrizeForecast(this._gameCore.m_curIndex);

                                //播放中牛或鹿的声音
                                this.playNiuOrLuPrizeSound();
                            }
                        } else {
                            //校准位置
                            rollItem.node.y = (posY)
                        }
                    }
                    springback = springback + 1
                    this._gameCore.m_springback[nCol] = springback
                    if (springback == Constant.ROW_MAX * Constant.ROW_SHOW && nCol == Constant.COL_SHOW - 1) {
                        //隐藏大奖预测
                        this.hideGrandPrizeForecast()

                        //收集牛动画
                        this.playCollectCowAnim();

                        //显示线
                        this.showWinLine();
                    }
                }
                // }
                // else{//不用判断是否wild
                //     if(!this._gameCore.isWildToEnd && nCol == 4 && this._gameCore.nSpinState > SpinStateFruitMachine.None){//wild列，是最后1列
                //         this._gameCore.isWildToEnd = true;
                //         //隐藏大奖预测
                //         this.hideGrandPrizeForecast()

                //         //收集牛动画
                //         this.playCollectCowAnim();

                //         //显示线
                //         this.showWinLine();
                //     }
                // }
            }
        }
    }

    //播放中牛或鹿中奖声音
    playNiuOrLuPrizeSound() {
        if (this._gameCore.m_nLastIndex != this._gameCore.m_curIndex) {
            this._gameCore.m_nLastIndex = this._gameCore.m_curIndex;
            let tcowData = this._gameCore.m_nCowPosData[this._gameCore.m_curIndex] || [];

            if (2 <= tcowData.length && this._gameCore.mLastCowCount != tcowData.length) {
                this._gameCore.mLastCowCount = tcowData.length;
                if (2 == this._gameCore.mLastCowCount && 4 != this._gameCore.m_curIndex) {
                    AudioHelper.instance.playEffect(`res/sounds/surprise1_${Common.random(1, 2)}`);
                } else if (3 == this._gameCore.mLastCowCount) {
                    AudioHelper.instance.playEffect(`res/sounds/surprise2_${Common.random(1, 2)}`);
                } else if (4 == this._gameCore.mLastCowCount) {
                    AudioHelper.instance.playEffect(`res/sounds/surprise3_${Common.random(1, 2)}`);
                } else if (5 <= this._gameCore.mLastCowCount) {
                    AudioHelper.instance.playEffect(`res/sounds/surprise4`);
                }
            }

            let tdeerData = this._gameCore.m_nDeerPosData[this._gameCore.m_curIndex] || [];
            if (2 <= tdeerData.length && this._gameCore.mLastDeerCount != tdeerData.length) {
                this._gameCore.mLastDeerCount = this._gameCore.m_nDeerCount[this._gameCore.m_curIndex]
                if (2 == this._gameCore.mLastDeerCount && 4 != this._gameCore.m_curIndex) {
                    AudioHelper.instance.playEffect(`res/sounds/surprise1_${Common.random(1, 2)}`);
                } else if (3 == this._gameCore.mLastDeerCount) {
                    AudioHelper.instance.playEffect(`res/sounds/surprise2_${Common.random(1, 2)}`);
                } else if (4 == this._gameCore.mLastDeerCount) {
                    AudioHelper.instance.playEffect(`res/sounds/surprise3_${Common.random(1, 2)}`);
                } else if (5 <= this._gameCore.mLastDeerCount) {
                    AudioHelper.instance.playEffect(`res/sounds/surprise4`);
                }
            }
        }
    }


    //显示鹿动画 2个及以上相邻才播放
    showLuAni(curCol: number) {
        if (this._gameCore.countAllColLuNum <= 1) {
            return;
        }

        //只有2列鹿且有1列是最后一列是鹿也不展示动画
        if (this._gameCore.countAllColLuNum == 2 && this._gameCore.lastColHasLu) {
            return;
        }

        for (let nRow = 0; nRow < Constant.ROW_SHOW; nRow++) {
            if (this._gameCore.fruitData[nRow][curCol] == ICON_TYPE.NUM_LU) {
                this._gameCore.luCount += 1;
                if (this._gameCore.luCount > 1) {//至少有2列
                    if (this._gameCore.luRowList[curCol - 1] != -1) {//前一列有鹿的数据
                        let lastRow = this._gameCore.luRowList[curCol - 1];
                        let lastNode = this._gameCore.machineElement[lastRow][curCol - 1].node;
                        if (!(<any>lastNode).bPlay) {
                            lastNode.getChildByName('icon').active = false;
                            let aniNode = lastNode.getChildByName('prizeSpin');
                            aniNode.active = true;
                            aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${ICON_TYPE.NUM_LU + 1}`, true);
                            (<any>lastNode).bPlay = true;
                        }

                        let machineElement = this._gameCore.machineElement[nRow + 1][curCol].node;
                        if (!(<any>machineElement).bPlay) {
                            machineElement.getChildByName('icon').active = false;
                            let aniNode = machineElement.getChildByName('prizeSpin');
                            aniNode.active = true;
                            aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${ICON_TYPE.NUM_LU + 1}`, true);

                            (<any>machineElement).bPlay = true;
                        }
                    }
                    else {
                        this._gameCore.luCount = 0;
                    }
                }
                break;
            }
        }

    }


    //显示牛动画 2个及以上才播放
    showNiuAni(curCol: number) {
        if (this._gameCore.resultData.niunum < Constant.NIU_COIN_MAX_NUM) {
            return;
        }

        for (let nCol = 0; nCol < Constant.COL_SHOW; nCol++) {
            if (nCol != curCol) {
                continue;
            }
            for (let nRow = 0; nRow < Constant.ROW_SHOW; nRow++) {
                if (this._gameCore.fruitData[nRow][nCol] == ICON_TYPE.NUM_NIU) {
                    this._gameCore.niuCount += 1;
                    if (this._gameCore.niuCount == 1) {//第1个时不播动画
                        this._gameCore.lastNiuItem = this._gameCore.machineElement[nRow][nCol].node;
                    }
                    if (!(<any>this._gameCore.machineElement[nRow][nCol].node).bPlay && this._gameCore.niuCount > 1) {
                        let machineElement = this._gameCore.machineElement[nRow][nCol];
                        machineElement.node.getChildByName('icon').active = false;
                        let aniNode = machineElement.node.getChildByName('prizeSpin');
                        aniNode.active = true;
                        aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${ICON_TYPE.NUM_NIU + 1}`, true);

                        (<any>this._gameCore.machineElement[nRow][nCol].node).bPlay = true;
                    }

                    if (!!this._gameCore.lastNiuItem && !(<any>this._gameCore.lastNiuItem).bPlay && this._gameCore.niuCount > 1) {
                        let machineElement = this._gameCore.lastNiuItem;
                        machineElement.getChildByName('icon').active = false;
                        let aniNode = machineElement.getChildByName('prizeSpin');
                        aniNode.active = true;
                        aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${ICON_TYPE.NUM_NIU + 1}`, true);

                        (<any>this._gameCore.lastNiuItem).bPlay = true;
                    }
                }
            }
        }

    }

    //统计有多少列包含有鹿
    countColLuNum() {
        this._gameCore.countAllColLuNum = 0;
        for (let j = 0; j < Constant.COL_SHOW; j++) {
            let count = 0;
            for (let i = 0; i < Constant.ROW_SHOW; i++) {
                if (this._gameCore.fruitData[i][j] == ICON_TYPE.NUM_LU && count == 0) {
                    count += 1;
                    this._gameCore.luRowList[j] = (i + 1);
                    this._gameCore.countAllColLuNum += 1;
                    if (j == Constant.COL_SHOW - 1) {
                        this._gameCore.lastColHasLu = true;
                    }
                }
            }
        }
    }

    /**开始摇奖 */
    doDrawLottery() {
        //隐藏所有线
        this.hideAllLine();
        //调整每列数据
        for (let nCol = 0, nColMax = Constant.COL_SHOW; nCol < nColMax; ++nCol) {
            this._gameCore.nSpeedCur[nCol] = this._gameCore.nSpeedStart;
            this._gameCore.nStepNumCur[nCol] = 0;
            this._gameCore.bScroll[nCol] = true;
        }
        this._gameCore.bFruitIsStop = false;
        this._gameView.changeAddOrSubBtnColor();
        //音效
        AudioHelper.instance.playEffect(Sounds.ScrollStart);
    }

    /**显示中奖线 */
    showWinLine() {
        let resultData = this._gameCore.resultData;
        let winLinesInfo = resultData.winLinesInfo;

        if (winLinesInfo && winLinesInfo.length > 0) {
            //先播所有中奖线及图标动画，然后轮播单条线
            this.repeatPlayMoreLineAni(winLinesInfo);
            if (this._gameCore.nLastFreeTimes <= 0 && this._gameCore.nSpinState == SpinStateFruitMachine.Free) {//免费最后一次 //先更新赢金再弹界面
                this.updateWinNum(() => { this.playWinAnim(() => { this.doFruitMachineEnd() }) }); //刷新赢金
            }
            else {
                this.playWinAnim(() => { this.updateWinNum(() => { this.doFruitMachineEnd() }) }); //刷新赢金
            }
        }
        else {
            if (this._gameCore.nLastFreeTimes <= 0 && this._gameCore.nSpinState == SpinStateFruitMachine.Free) {
                this.updateWinNum(() => { this.playWinAnim(() => { this.doFruitMachineEnd() }) }); //刷新赢金
            }
            else {
                if (resultData.multgoldnum >= 2 || resultData.luckyspin > 0) {
                    this.playWinAnim(() => { this.updateWinNum(() => { this.doFruitMachineEnd() }) }); //刷新赢金
                }
                else {
                    // //刷新赢金
                    this.updateWinNum();

                    this.doFruitMachineEnd();
                }
            }
        }
    }

    /**收集牛动画*/
    playCollectCowAnim() {
        let self = this;

        if (this._gameCore.resultData.luckyspin > 0) {
            this.LayerItems.blockInput_turntable.active = true;
            this._gameCore.lastAutoSpinState = this._gameCore.bAutoSpin;
            this._gameCore.bAutoSpin && this._gameView.setSpinAutoState(false);
        }
        

        if (this._gameCore.resultData.niunum > 0 && this._gameCore.resultData.niunum <= Constant.NIU_COIN_MAX_NUM) {//只有1个,2个牛才会收集金币
            let tsize = this.LayerItems.Panel_container.getContentSize();
            let toffsetX = tsize.width / 5;
            let toffsetY = tsize.height / 3;
            let tstartX = -tsize.width / 2 + toffsetX / 2;
            let tstartY = tsize.height / 2 - toffsetY / 2;
            let tendPos = this.LayerItems.Node_prize.getPosition();

            let tcoinNumArr = [1, 2, 3, 4, 6, 8, 10];//随机飞金币数量
            for (let nRow = 2; nRow < this._gameCore.machineElement.length; ++nRow) {//最后三组是最终结果
                for (let nCol = 0; nCol < this._gameCore.machineElement[nRow].length; ++nCol) {
                    let machineElement = this._gameCore.machineElement[nRow][nCol];
                    if (ICON_TYPE.NUM_NIU == machineElement.nType) {
                        let x = tstartX + nCol * toffsetX;
                        let y = tstartY - (nRow - 2) * toffsetY;
                        let tcoinsNum = tcoinNumArr[this._gameCore.meAddBeiLv];
                        for (let tcoinsIndex = 0; tcoinsIndex < tcoinsNum; tcoinsIndex++) {
                            let tcoins = cc.instantiate(self.LayerItems.item_coins);
                            self.LayerItems.Node_coins.addChild(tcoins);
                            tcoins.setPosition(x, y);
                            tcoins.active = false;
                            cc.tween(tcoins)
                                .delay(tcoinsIndex * 0.05)
                                .call(() => {
                                    tcoins.active = true;
                                })
                                .to(0.35, { position: cc.v3(tendPos.x + 100, tendPos.y)})
                                .to(0.5,{scale:0.5})
                                .call(() => {
                                    if (!!tcoins) {
                                        tcoins.removeFromParent();
                                    }
                                })
                                .start()
                        }
                    }
                }
            }
        }

        let txt = self.LayerItems.font_prize.getComponent(cc.Label)
        if (Common.isNull(self._gameCore.prizenum)) {
            txt.string = `$${this._gameCore.lastPrizenum}`;
            return;
        }

        self._gameView.scrollNumber({
            txt: txt,
            began: this._gameCore.lastPrizenum,
            end: self._gameCore.prizenum,
            bNotStopAction: true,
            format: (nValue) => {
                txt.string = nValue.toFixed(1);
            },
        });

        this._gameCore.lastPrizenum = self._gameCore.prizenum;

    }


    //repeat播放多种线
    repeatPlayMoreLineAni(winLinesInfo: WinLinesInfo[]) {

        if (!winLinesInfo || winLinesInfo?.length == 0) {
            return;
        }
        let self = this;
        this.hideAllIdleSpin(true);
        AudioHelper.instance.playEffect(`res/sounds/lianxian`);
        //显示所有线
        winLinesInfo.forEach(element => {
            self.setOneLineVisile(element, true, false, true);
        });

        //所有线显示一段时间后，切换至每隔1.5秒轮换每条线
        this._gameCore.curLineIndex = 0;

        (<any>this.LayerItems.Node_Lines_ex).playLineSoundList = [];

        let func = () => {
            self.hideAllLine(false,false);
            if (self._gameCore.curLineIndex >= 0) {
                if (!(<any>this.LayerItems.Node_Lines_ex).playLineSoundList[self._gameCore.curLineIndex]) {
                    self.playLineSound();
                }

                self.setOneLineVisile(winLinesInfo[self._gameCore.curLineIndex], true, false);
            }
            if (++self._gameCore.curLineIndex >= winLinesInfo.length) {
                self._gameCore.curLineIndex = 0;
            }
        }
        //轮流播放每条线
        this.LayerItems.Node_Lines_ex.stopAllActions();
        if (winLinesInfo.length == 1) {
            func();
        }
        else {
            this._gameView.nodeRunActionRepeatForever(self.LayerItems.Node_Lines_ex, func, 1.5);
        }
    }

    //播放连线声音
    playLineSound() {
        let multipleMusic = [4, 15, 25, 35];
        for (let i = 0; i < multipleMusic.length; i++) {//根据倍率播放
            if (this._gameCore.resultData.totalmult < multipleMusic[i]) {
                AudioHelper.instance.playEffect(`res/sounds/line${i + 1}`);
                (<any>this.LayerItems.Node_Lines_ex).playLineSoundList[this._gameCore.curLineIndex] = true;
                break;
            }
            if (i == multipleMusic.length - 1) {
                AudioHelper.instance.playEffect(`res/sounds/lineMore`);
                (<any>this.LayerItems.Node_Lines_ex).playLineSoundList[this._gameCore.curLineIndex] = true;
            }
        }
    }

    /**隐藏所有线 */
    hideAllLine(bStop: boolean = true,bStopOld: boolean = true) {
        this.hideAllPrizeIconAni();
        for (let i = 1; i <= Constant.MAX_LINES; ++i) {
            let winLinesInfo = <WinLinesInfo>{};
            winLinesInfo.nLine = i;
            this.setOneLineVisile(winLinesInfo, false, true,false,bStopOld);
        }
        if (bStop) {
            this.LayerItems.Node_Lines_ex.stopAllActions();
        }
    }

    /**设置单线显隐 */
    setOneLineVisile(data: WinLinesInfo, bVisible: boolean, hideAllPrizeIconAni: boolean = true, playAllPrizeAni: boolean = false,bStopOld: boolean = true) {
        let nViewLine = data.nLine;

        if (nViewLine && nViewLine <= Constant.MAX_LINES) {
            let lineNumNode = this.LayerItems.Node_Lines_ex.getChildByName(`Reward_Line_${nViewLine}`);
            if (lineNumNode) {
                lineNumNode.active = bVisible;
            }
        }

        //开奖结果列表
        // let fruitData = this._gameCore.fruitData;

        //切换多条线时，隐藏当前所有中奖后重新显示对应中奖图标动画
        if (hideAllPrizeIconAni) {
            this.hideAllPrizeIconAni();
        }
        if (data.prizeIconList && data.prizeIconList.length > 0) {
            let count = 0;
            let prizeIndexCount = 0;//中奖图标索引累加
            for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                for (let nCol = 0; nCol < Constant.COL_SHOW; nCol++) {
                    count += 1;
                    if (nRow > 0 && nRow < Constant.ROW_MAX - 1) {
                        let subitem = this._gameCore.machineElement[nRow + 1][nCol];
                        let aniNode = subitem.node.getChildByName('prizeSpin');
                        let iconNode = subitem.node.getChildByName('icon');
                        let bOrgVisible = subitem.node.getChildByName('prizeLightSpin').active;//之前有没有显示中奖图标
                        
                        let bWildFlag = this.isWildCol(nCol);
                        if (bWildFlag) { //wild列 隐藏图标 只显示中奖框
                            aniNode.active = false;
                            iconNode.active = false;
                        }
                        let nType = subitem.nType;//fruitData[nRow - 1][nCol];
                        //Constant.COL_SHOW + data.prizeIconList[prizeIndexCount] 第一行隐藏5个图标 中奖索引要加上之前图标
                        if (prizeIndexCount < data.prizeIconList.length && data.prizeIconList[prizeIndexCount] && count == Constant.COL_SHOW + data.prizeIconList[prizeIndexCount]) {
                            subitem.node.getChildByName('prizeLightSpin').active = true;
                            if (!bWildFlag && !Common.isNull(nType) && (nType >= Constant.SHOW_ICON_PRIZE_ANI_MIN || nType == ICON_TYPE.NUM_WILD)) {//nRow + 1  machineElement是最上面隐藏了一行，只显示中间的，所以减掉最上面一行图标值
                                iconNode.active = false;
                                aniNode.active = true;
                                let newType = nType
                                if (nType == ICON_TYPE.NUM_WILD) {
                                    newType = 9;//动画中名字是第9个
                                }
                                if (nType == ICON_TYPE.NUM_LU || nType == ICON_TYPE.NUM_NIU) {
                                    newType = nType + 1;
                                }
                                aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${newType}`, true);
                            }
                            prizeIndexCount += 1
                        }
                        else {
                            if (!bWildFlag && !playAllPrizeAni) { //playAllPrizeAni 播放多组线时，不停其它线动画
                                if (nType == ICON_TYPE.NUM_LU || nType == ICON_TYPE.NUM_NIU) {//鹿牛还是还原idle播放
                                    iconNode.active = false;
                                    aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${nType + 1}_idle`, true);
                                }
                                else {
                                    if(!bStopOld){
                                        if(bOrgVisible && aniNode.active){//多条线切换时，原未中奖图标不隐藏
                                            iconNode.active = true;
                                            aniNode.active = false;
                                        }
                                    }
                                    else{
                                        iconNode.active = true;
                                        aniNode.active = false;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    //隐藏所有图标中奖动画
    hideAllPrizeIconAni() {
        this._gameCore.machineElement.forEach((item) => {
            item.forEach((subitem) => {
                subitem.node.getChildByName('prizeLightSpin').active = false;
            })
        })
    }

    /**播放赢金动画 */
    playWinAnim(callback: Function = null) {
        let animName: string;
        let resultData = this._gameCore.resultData;
        let goldNum = resultData.winmoney;
        let soundName = '';

        if (this._gameCore.nLastFreeTimes == 0 && this._gameCore.nSpinState == SpinStateFruitMachine.Free) {
            animName = WIN_SPIN_NAME.TOTAL;
            goldNum = this._gameCore.freeWinTotalNum;
            soundName = 'res/sounds/freeTotalWin'

            if(goldNum <= 0){
                callback && callback();
                return;
            }
        }
        else {
            if ((resultData.multgoldnum < 2 || this._gameCore.nLastFreeTimes > 0 || goldNum <= 0) && (resultData.luckyspin <= 0 || (resultData.luckyspin > 0 && goldNum <= 0))) {
                callback && callback();
                return;
            }
            AudioHelper.instance.playEffect(`res/sounds/win`);
            if (resultData.multgoldnum < 5) {
                animName = WIN_SPIN_NAME.BIG;
                soundName = `res/sounds/smallWin`
            } else if (resultData.multgoldnum < 20) {
                animName = WIN_SPIN_NAME.MEGA;
                soundName = `res/sounds/midWin`
            } else if (resultData.multgoldnum < 40) {
                animName = WIN_SPIN_NAME.EPIC;
                soundName = `res/sounds/bigWin`
            } else {
                animName = WIN_SPIN_NAME.SUPER;
                soundName = `res/sounds/superWin`
            }
        }
        this.scheduleOnce(() => {
            AudioHelper.instance.playEffect(soundName);
        }, 0.02)

        if(resultData.luckyspin > 0){//中转盘时，因为prize奖池有更新，这里需要再更新一下余额
            this._gameCore.playScoreMoney = this._gameCore.myselfMoney;
            this._gameView.showSelfMoney();
        }

        this.LayerItems.Panel_win.active = true;
        let win_num = this.LayerItems.Panel_win.getChildByName('num').getComponent(cc.Label);
        win_num.string = '0.00';
        this.LayerItems.Panel_win.getChildByName('spinNode').getComponent(sp.Skeleton).setAnimation(0, animName, false);
        let spinGoldNode = this.LayerItems.spinGoldNode.getComponent(sp.Skeleton);
        spinGoldNode.setAnimation(0, 'jinbi0', false);
        spinGoldNode.setCompleteListener(()=>{
            spinGoldNode.setAnimation(0, 'jinbi1', true);
        })

        let self = this;
        this.scheduleOnce(() => {
            self._gameView.scrollNumber({
                txt: win_num,
                began: 0,
                end: goldNum,
                format: (nValue, dataEx) => {
                    win_num.string = `${self._gameView.moneyFormat(nValue, 2)}`
                },
                callback: () => {
                    win_num.node.stopAllActions();
                    self._gameView.nodeRunActionOncs(win_num.node, () => {
                        self.LayerItems.Panel_win.active = false;
                        callback && callback();
                    }, 1.5);
                },
            });
        }, 0.2);
    }

    /**刷新赢金数字 */
    updateWinNum(callback: Function = null) {
        let self = this;
        //先隐藏所有
        this.LayerItems.Node_win_free_total.active = false;
        this.LayerItems.Node_win_good_luck.active = false;
        this.LayerItems.Node_win_normal.active = false;

        //依据结算信息刷新
        let resultData = this._gameCore.resultData;
        if (!resultData) {
            this.LayerItems.Node_win_good_luck.active = true;
        } else {
            //免费
            if (this._gameCore.nSpinState == SpinStateFruitMachine.Free) {
                this.LayerItems.Node_win_free_total.active = true;
                let winNum = this.LayerItems.Node_win_free_total.getChildByName('win_num').getComponent(cc.Label)
                this._gameView.scrollNumber({
                    txt: winNum,
                    began: this._gameCore.lastFreeWinNum,
                    end: this._gameCore.freeWinTotalNum,
                    format: (nValueEx) => {
                        winNum.string = self._gameView.moneyFormat(nValueEx, 2);
                    },
                    callback: () => {
                        callback && callback();
                    }
                });
                this._gameCore.lastFreeWinNum = this._gameCore.freeWinTotalNum;
            }
            //正常
            else {
                if (resultData.winmoney > 0) {
                    this.LayerItems.Node_win_normal.active = true;
                    let winNum = this.LayerItems.Node_win_normal.getChildByName('win_num').getComponent(cc.Label);
                    winNum.string = '0.00';
                    this._gameView.scrollNumber({
                        txt: winNum,
                        nTime: resultData.multgoldnum < 2 ? 0.35 : 1.0,
                        began: 0,
                        end: resultData.winmoney,
                        format: (nValueEx) => {
                            winNum.string = self._gameView.moneyFormat(nValueEx, 2);
                        },
                        callback: () => {
                            this.LayerItems.Node_win_normal.stopAllActions();
                            callback && callback();
                            // cc.tween(this.LayerItems.Node_win_normal)
                            //     .delay(0.5)
                            //     .call(() => {
                            //         // this.checkWinLabelHide();
                            //         callback && callback();
                            //     })
                            //     .start()
                        }
                    });
                } else {
                    this.LayerItems.Node_win_good_luck.active = true;
                    if(resultData.luckyspin > 0){//没有中金币 延迟点弹牛界面
                        this.scheduleOnce(()=>{
                            callback && callback();
                        },1)
                    }
                    else{
                        callback && callback();
                    }
                }
            }
        }
    }

    //隐藏所有待机图标动画
    hideAllIdleSpin(bShowIdleAni: boolean = false) {
        this._gameCore.machineElement.forEach(item => {
            item.forEach(subItem => {
                subItem.node.getChildByName('icon').active = true;
                let aniNode = subItem.node.getChildByName('prizeSpin');
                if (!!aniNode) {
                    aniNode.active = false;
                    if (bShowIdleAni) {
                        let bShow = subItem.nType && (subItem.nType == ICON_TYPE.NUM_LU || subItem.nType == ICON_TYPE.NUM_NIU);
                        if (bShow) {
                            subItem.node.getChildByName('icon').active = false;
                            aniNode.active = true;
                            aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${subItem.nType + 1}_idle`, true);
                        }
                    }
                }
            })
        })
        this.hideAllPrizeIconAni();
    }

    /**播放wild动画*/
    playWildAnim(index_) {
        for (var i = 0; i < this._gameCore.machineElement.length; ++i) {
            for (var j = 0; j < this._gameCore.machineElement[i].length; ++j) {
                this._gameCore.machineElement[i][j].node.active = true;
            }
        }

        if (SpinStateFruitMachine.Double == this._gameCore.nSpinState) {
            if (index_ < 1) {
                this._gameCore.wildSpinList.forEach((item, index) => this.stopWildAnim(index));
                return;
            }
            this.stopWildAnim(index_ + 1);
            this.setWildSpinPlay(this._gameCore.wildSpinList[index_ - 1]);
            this.setWildSpinPlay(this._gameCore.wildSpinList[index_]);
            this.hideColInWild(index_ - 1);
            this.hideColInWild(index_);
        } else {
            if (!this._gameCore.wildSpinList[index_]) {
                // this._gameCore.wildSpinList.forEach((item, index) => this.stopWildAnim(index));
                return;
            }
            this.stopWildAnim(index_ + 1);
            this.setWildSpinPlay(this._gameCore.wildSpinList[index_]);
            this.hideColInWild(index_);
        }
    }

    //wild时隐藏指定列
    hideColInWild(nCol: number) {
        for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
            let machineElement = this._gameCore.machineElement[nRow][nCol];
            machineElement.node.getChildByName('icon').active = false;
            let aniNode = machineElement.node.getChildByName('prizeSpin');
            aniNode.active = false;
        }
    }

    //设置wildSpin播放
    setWildSpinPlay(wildSpin: sp.Skeleton) {
        if (!wildSpin) {
            return;
        }
        wildSpin.node.active = true;
        wildSpin.paused = false;
        wildSpin.setAnimation(0, `wild0`, false);
        wildSpin.setCompleteListener((x: sp.spine.TrackEntry) => {
            wildSpin.setAnimation(0, `wild1`, true);
            // wildSpin.removeAllEvent();
        });
    }

    //动画播放结束
    doFruitMachineEnd() {
        //更新自己余额
        if (this._gameCore.freeTimesEndFlag == FREE_TYPE.end) {//免费完成时累加的总赢金
            // this._gameCore.playScoreMoney += this._gameCore.freeChangeTotalNum;
            // if(this._gameCore.playScoreMoney != this._gameCore.myselfMoney){
            //     console.error('===================金币免费游戏累计错误===========',this._gameCore.playScoreMoney,this._gameCore.myselfMoney)
                this._gameCore.playScoreMoney = this._gameCore.myselfMoney;
            // }
            this._gameView.showSelfMoney();
            this._gameCore.freeTimesEndFlag = FREE_TYPE.non;
        }
        else {
            if (this._gameCore.nLastFreeTimes <= 0) {//免费播放时，不直接更新余额
                this._gameCore.playScoreMoney = this._gameCore.myselfMoney;
                this._gameView.showSelfMoney();
            }
        }

        //水果机停止
        if(this._gameCore.resultData.luckyspin <= 0){
            this._gameCore.bFruitIsStop = true;
            this._gameView.changeAddOrSubBtnColor();
        }
        
        this.onFruitMachineEnd();
    }

    /**水果机动画完成后回调 */
    onFruitMachineEnd() {
        let resultData = this._gameCore.resultData;

        let self = this;
        let onMachineEnd = () => {
            // 状态优先级  double single free normal
            switch (this._gameCore.nSpinState) {
                // double 状态下
                case SpinStateFruitMachine.Double: {
                    if (self._gameCore.resultData.wildnum <= 2 || self._gameCore.resultData.wildtype <= WildtypeInfo.NON) {
                        self._gameView.updateSpinState(SpinStateFruitMachine.Normal);
                    }
                    else{
                        if (this._gameCore.lastAutoSpinState || this._gameCore.bAutoSpin) {
                            this._gameCore.lastAutoSpinState = false;
                            this._gameView.setSpinAutoState(true);
                            let self = this;
                            this.scheduleOnce(() => {
                                self._gameView.onClickSpin();
                            }, 1.8)
                        }
                    }
                    break;
                }
                case SpinStateFruitMachine.Single: {
                    if (self._gameCore.resultData.wildnum <= 1 || self._gameCore.resultData.wildtype <= WildtypeInfo.NON) {
                        self._gameView.updateSpinState(SpinStateFruitMachine.Normal);
                    }
                    else{
                        if (this._gameCore.lastAutoSpinState || this._gameCore.bAutoSpin) {
                            this._gameCore.lastAutoSpinState = false;
                            this._gameView.setSpinAutoState(true);
                            let self = this;
                            this.scheduleOnce(() => {
                                self._gameView.onClickSpin();
                            }, 1.8)
                        }
                    }
                    break;
                }
                case SpinStateFruitMachine.Free: {
                    if (self._gameCore.nLastFreeTimes > 0) {
                        self.scheduleOnce(() => {
                            self._gameView.autoStartFreeGame();
                        }, 1.8)
                    } else {
                        self._gameView.updateSpinState(SpinStateFruitMachine.Normal);
                    }
                    break;
                }
                case SpinStateFruitMachine.Normal: {
                    if (self._gameCore.resultData.wildtype == WildtypeInfo.DOUBLE) { //大转盘中wild双线
                        self._gameView.updateSpinState(SpinStateFruitMachine.Double);
                    } else if (self._gameCore.resultData.wildtype == WildtypeInfo.SINGLE) {//大转盘中wild单线
                        self._gameView.updateSpinState(SpinStateFruitMachine.Single);
                    } else if (resultData.freetimes > 0) {//是否中了免费
                        self._gameCore.lastAutoSpinState = self._gameCore.bAutoSpin;
                        self._gameView.setSpinAutoState(false);
                        self.playFreeAnim();//弹免费次数
                    } else {
                        if (self._gameCore.bAutoSpin) {
                            self._gameView.setSpinAutoState(true);
                            self.scheduleOnce(() => {
                                self._gameView.onClickSpin();
                            }, 1.8)
                        }
                    }
                    break;
                }
            }
        }
        if (0 < self._gameCore.resultData.luckyspin) {
            self.showTurnTable(
                {
                    // callback: () => {
                    //     onMachineEnd();
                    // }
                }
            );
        } else {
            onMachineEnd();
        }
    }

    //播放免费动画
    playFreeAnim() {
        AudioHelper.instance.playEffect(`res/sounds/pop_free`);
        this.LayerItems.Panel_free.active = true;
        this.LayerItems.freeAni.active = true;
        let spinNode = this.LayerItems.Panel_free.getChildByName('spinNode');
        spinNode.active = true;
        spinNode.getComponent(sp.Skeleton).setAnimation(0, 'win0', false);
        spinNode.getComponent(sp.Skeleton).setCompleteListener(() => {
            spinNode.getComponent(sp.Skeleton).setAnimation(0, 'win1', true);
        })

        let numText = this.LayerItems.Panel_free.getChildByName('num').getComponent(cc.Label);

        this._gameView.scrollNumber({
            txt: numText,
            began: 0,
            end: this._gameCore.resultData.freetimes,
            format: (nValueEx) => {
                numText.string = Common.toInt(nValueEx);
            },
            callback: () => {
                this.LayerItems.Panel_free.stopAllActions();
                cc.tween(this.LayerItems.Panel_free)
                    .delay(7)
                    .call(() => {
                        this._gameView.onClickStartFreeGame();
                    })
                    .start()
            }
        });
    }

    /**转盘动画 */
    showTurnTable(data_: any) {
        let self = this;
        
        this.LayerItems.turnTableFirstShowAni.active = true;
        this.LayerItems.turnTableFirstShowAni.getComponent(sp.Skeleton).setAnimation(0, 'animation', false);

        cc.audioEngine.stopAllEffects();
        AudioHelper.instance.playMusic("res/sounds/spin_bg");
        (<any>self.LayerItems.turnTableFirstShowAni).callback = false;
        if(this._gameView.turnTableLayer){
            this._gameView.turnTableLayer.stopAllActions();
            this._gameView.turnTableLayer.removeFromParent();
            this._gameView.turnTableLayer = null;
        }
        this._gameView.turnTableLayer = cc.instantiate(self._gameView.turnTablePrefab)
        SceneManager.instance.addChildNode(this._gameView.turnTableLayer,10);
        this._gameView.turnTableLayer.getComponent('FMWTurtableLayer').setCloseCallback(this.updateTurnTableEnd.bind(this));
        this._gameView.turnTableLayer.opacity = 0;

        cc.tween(this._gameView.turnTableLayer)
        .delay(3)
        .to(0.5,{opacity : 255})
        .call(()=>{
            self._gameView.turnTableLayer.stopAllActions();
            self._gameView.turnTableLayer.getComponent('FMWTurtableLayer').setData();
        })
        .start();
    }

    //更新中奖结束信息
    updateTurnTableEnd(){
        let self = this;

        if(this._gameView.turnTableLayer){
            this._gameView.turnTableLayer.stopAllActions();
            this._gameView.turnTableLayer.removeFromParent();
            this._gameView.turnTableLayer = null;
        }

        if((<any>self.LayerItems.turnTableFirstShowAni).callback){
            return
        }
        (<any>self.LayerItems.turnTableFirstShowAni).callback = true;
        // console.error('=============进入了几次================')
        self.LayerItems.blockInput_turntable.active = false;
        self._gameCore.bFruitIsStop = true;
        self._gameView.changeAddOrSubBtnColor();
        if (!!self._gameCore.turntableData) {
            if (self._gameCore.turntableData.luckyspinid >= TURN_TABLE_TYPE.free5 && self._gameCore.turntableData.luckyspinid <= TURN_TABLE_TYPE.free15) {
                let freetimesList = [0, 5, 10, 15];//转盘中的免费次数  //索引对应 TURN_TABLE_TYPE中数值定义
                //免费次数
                let curFreetimes = freetimesList[self._gameCore.turntableData.luckyspinid];
                self._gameCore.nLastFreeTimes = curFreetimes;
                self._gameCore.nLastMaxFreeTimes = curFreetimes;
                self._gameCore.freeTimesEndFlag = FREE_TYPE.non;

                self._gameView.updateSpinState(SpinStateFruitMachine.Free,true);

                self._gameView.autoStartFreeGame();
            }
            else{
                //非免费转盘结束  直接更新自己余额 免费等转完所有免费次数再更新
                self._gameCore.playScoreMoney = this._gameCore.myselfMoney;
                self._gameView.showSelfMoney();

                if (self._gameCore.turntableData.luckyspinid == TURN_TABLE_TYPE.wild_single) {
                    self._gameView.updateSpinState(SpinStateFruitMachine.Single,true);
                }
                else if (self._gameCore.turntableData.luckyspinid == TURN_TABLE_TYPE.wild_double) {
                    self._gameView.updateSpinState(SpinStateFruitMachine.Double,true);
                }
                else {
                    cc.audioEngine.stopAllEffects();
                    self._gameView.updateSpinState(SpinStateFruitMachine.Normal,true);
                }
            } 
        }
        else{
            cc.audioEngine.stopAllEffects();
            self._gameView.updateSpinState(SpinStateFruitMachine.Normal,true);
        }
    }

    /**播放每列结束音效 */
    playColScrollEndAudio(nCol: number) {
        AudioHelper.instance.playEffect(`res/sounds/slotFruitStop${nCol + 1}`);
    }

    /**判断是不是wild列*/
    isWildCol(index_, flag_ = false): boolean {

        let wildnum = this._gameCore.resultData.wildnum;
        if (SpinStateFruitMachine.Single == this._gameCore.nSpinState || this._gameCore.resultData.wildtype == WildtypeInfo.SINGLE) {//最后一次将wild切换状态时 轮播线判断wild 
            return index_ == wildnum - 1;
        } else if (SpinStateFruitMachine.Double == this._gameCore.nSpinState || this._gameCore.resultData.wildtype == WildtypeInfo.DOUBLE) {
            return index_ == wildnum - 1 || index_ == wildnum - 2;
        } else {
            return false;
        }
    }

    //显示流光动画
    showLightAni(index_) {
        if (this._gameCore.mCurLightIndex >= index_) {
            return;
        }

        this._gameCore.mCurLightIndex = index_;
        if (3 >= index_ && this._gameCore.bLightFlag[index_ + 1]) {
            this.playStreamerLightAnim(index_ + 1);
        }
    }

    /**检测大奖预警*/
    checkGrandPrizeForecast(index_) {
        if (this.isWildCol(index_, true)) {
            return
        }
        let tcowData = this._gameCore.m_nCowPosData[index_] || [];
        let tcowCount = tcowData.length;
        if (2 <= tcowCount) {
            //播放牛动画
            for (let i = 0; i < tcowCount; i++) {
                let nRow = tcowData[i].row + 1;
                let nCol = tcowData[i].col;
                if (!(<any>this._gameCore.machineElement[nRow][nCol].node).bPlay) {
                    let machineElement = this._gameCore.machineElement[nRow][nCol];
                    machineElement.node.getChildByName('icon').active = false;
                    let aniNode = machineElement.node.getChildByName('prizeSpin');
                    aniNode.active = true;
                    aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${ICON_TYPE.NUM_NIU + 1}`, true);

                    (<any>this._gameCore.machineElement[nRow][nCol].node).bPlay = true;
                }
            }
        }

        let tdeerData = this._gameCore.m_nDeerPosData[index_] || []
        let tdeerCount = tdeerData.length;
        if (2 <= tdeerCount) {
            if (tdeerCount == 2 && index_ == Constant.COL_SHOW - 1) {//最后2列是鹿 不播放动画
                return;
            }
            //播放鹿动画
            for (let i = 0; i < tdeerCount; i++) {
                let nRow = tdeerData[i].row + 1;
                let nCol = tdeerData[i].col;
                if (!(<any>this._gameCore.machineElement[nRow][nCol].node).bPlay) {
                    let machineElement = this._gameCore.machineElement[nRow][nCol];
                    machineElement.node.getChildByName('icon').active = false;
                    let aniNode = machineElement.node.getChildByName('prizeSpin');
                    aniNode.active = true;
                    aniNode.getComponent(sp.Skeleton).setAnimation(0, `element_${ICON_TYPE.NUM_LU + 1}`, true);

                    (<any>this._gameCore.machineElement[nRow][nCol].node).bPlay = true;
                }
            }
        }
    }

    /**停止wild动画*/
    stopWildAnim(index_) {
        let wildSpin = this._gameCore.wildSpinList[index_];
        if (!!wildSpin) {
            wildSpin.node.active = false;
            wildSpin.paused = true;
        }
    }

    /**播放流光动画*/
    playStreamerLightAnim(index_: number) {
        this.hideStreamerLightAnim();
        AudioHelper.instance.playEffect(`res/sounds/grand_prize`);
        this._gameCore.liuGuangLightSpinList[index_].node.active = true
        this._gameCore.liuGuangLightSpinList[index_ + 1] && (this._gameCore.liuGuangLightSpinList[index_ + 1].node.active = true);
    }

    /**隐藏流光动画*/
    hideStreamerLightAnim() {
        this._gameCore.liuGuangLightSpinList.forEach(item => item.node.active = false);
    }

    /**获取大奖预警数据*/
    getGrandPrizeForecastData() {
        this._gameCore.mCurLightIndex = -1;

        // 牛大奖预警数据处理
        let tcount: number = 0;
        let telements = this._gameCore.fruitData;
        let tcows: any[] = [];
        for (var i = 0; i < Constant.COL_SHOW; ++i) {
            tcows[i] = [];
            for (var j = 0; j < Constant.ROW_SHOW; ++j) {
                if (ICON_TYPE.NUM_NIU == telements[j][i]) {
                    tcount++;
                    tcows[i].push({ row: j + 1, col: i });
                }
            }
        }

        /**每一列完成后牛元素的个数*/
        for (var i = 0; i < Constant.COL_SHOW; ++i) {
            this._gameCore.m_nCowPosData[i] = [];
            for (var j = 0; j < i + 1; ++j) {
                if (tcows[j] && tcows[j]?.length > 0) {
                    this._gameCore.m_nCowPosData[i] = this._gameCore.m_nCowPosData[i].concat(tcows[j]);
                }
            }

            if (i < Constant.COL_SHOW - 1) {//2个或以上牛 
                if (this._gameCore.m_nCowPosData[i].length >= 2) { //增加步数及延时
                    this._gameCore.m_nDoLen[i + 1] = 90;
                    this._gameCore.m_nOnceMoveLen[i + 1] = 0.4;
                }
                this._gameCore.bLightFlag[i + 1] = this._gameCore.m_nCowPosData[i].length >= 2;
            }
        }

        //统计有多少列包含鹿
        this.countColLuNum();
        //鹿大奖预警数据处理
        for (let nCol = 1; nCol < Constant.COL_SHOW; nCol++) {
            let tdeerDataCol = this.splitSameElementCol(this._gameCore.fruitData, ICON_TYPE.NUM_LU, 2, nCol)
            if (tdeerDataCol && 0 < tdeerDataCol.length) {
                this._gameCore.m_nDeerPosData[nCol] = []
                tdeerDataCol.forEach((v) => {
                    if (this._gameCore.m_nDeerCount[nCol] < v.length) {
                        this._gameCore.m_nDeerCount[nCol] = v.length
                    }
                    v.forEach((item) => {
                        this._gameCore.m_nDeerPosData[nCol].push({ row: item.pos.row + 1, col: item.pos.col });
                    })
                })

                this._gameCore.m_nDoLen[nCol + 1] = 90;
                this._gameCore.m_nOnceMoveLen[nCol + 1] = 0.4;
                this._gameCore.bLightFlag[nCol + 1] = true;
            }
        }
    }

    //拆分相同元素
    splitSameElementCol(arr_, val_, count_, index_) {
        let tdata = []
        for (let i = 0; i < 5; i++) {
            tdata[i] = { val: -1 };
        }
        for (let i = 0; i < arr_.length; i++) {
            for (let j = 0; j < arr_[i].length; j++) {
                if (val_ == arr_[i][j] && 0 > tdata[j].val) {
                    tdata[j].val = val_
                    tdata[j].pos = { row: i, col: j }
                }
            }
        }
        let tret = []
        let tsameArrr = []
        for (let i = 0; i < 5; i++) {
            tsameArrr[i] = []
            for (let j = i; j >= 0; j--) {
                if (0 < tdata[j].val) {
                    if (!index_ || index_ == i) {
                        tsameArrr[i].push(tdata[j])
                    }
                } else {
                    break
                }
            }
            if (count_ <= tsameArrr[i].length) {
                tret.push(tsameArrr[i])
            }
        }
        return tret
    }

    /**隐藏大奖预警*/
    hideGrandPrizeForecast() {
        this.hideStreamerLightAnim();
    }
}