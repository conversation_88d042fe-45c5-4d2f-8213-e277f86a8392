import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import PopupLayer from "../../../../script/frame/component/PopupLayer";
import Config from "../../../../script/frame/config/Config";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import EventManager from "../../../../script/frame/manager/EventManager";
import { Constant, GameEvent, Sounds, TURN_TABLE_TYPE } from "../core/FMWDefine";
import FMWGameCore from "../core/FMWGameCore";
import FMWGameView from "./FMWGameView";


const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slotniu/FMWTurtableLayer')
export default class FMWTurtableLayer extends PopupLayer {

	//Go按钮节点
	@property(cc.Node)
	Panel_go: cc.Node = null;

	//转盘节点
	@property(cc.Node)
	Panel_turnTable: cc.Node = null;

	//转盘氛围动画
	@property(cc.Node)
	Node_fenWei: cc.Node = null;
	
	//火动画
	@property(cc.Node)
	mFireAni: cc.Node = null;

	//关闭节点
	@property(cc.Node)
	clickCloseNode: cc.Node = null;

	//界面没有完全显示前 禁止点击
	@property(cc.Node)
	showBlockInput: cc.Node = null;

	//奖池金额
	@property(cc.Node)
	allJackpotNum1: cc.Node = null;
	
	//jackpot
	@property(cc.Node)
	FileNode_jackpot: cc.Node = null;
	
	//中了多少个牛
	@property(cc.Node)
	niuNode: cc.Node = null;

	@property(cc.Node)
	niuItem: cc.Node = null;

	//中jackpot动画
	@property(cc.Node)
	jackpotPrizeAni: cc.Node = null;

	@property(cc.Node)
	jackpot_prize: cc.Node = null;

	@property(cc.Node)
	Panel_jackpotPrize: cc.Node = null;

	@property(cc.Node)
	jackpotSpinGoldNode: cc.Node = null;
			

	/** 游戏主对象 */
	_gameCore: FMWGameCore;
	_gameView: FMWGameView = null;

	mCallBack: any;

	bClickFlag = false; //是否点击 防止重复点击
	reqSuccess: boolean = false;//是否请求成功 防止重复请求
	bCloseFlag = false;
	scheduleClose = null;
		
	onLoad() {
		//动态导入所有节点
        this.LoadAllLayerObject(this.node)
		this._gameCore = cc.Canvas.instance.getComponent("FMWGameCore"); 
		this._gameView = cc.Canvas.instance.getComponent("FMWGameView"); 
		this.showBlockInput.active = true;

		EventManager.instance.on(GameEvent.TURN_TABLE_INFO, this.onGetTurnTableInfo, this);	
	}

	start() {
		this.bClickFlag = false;
		this.reqSuccess = false;
		this.clickCloseNode.active = false;
	}

	setData(){
		this.Node_fenWei.active = true;
		this.showBlockInput.active = false;
		let self = this;
		let allJackpotNumLabel = this.allJackpotNum1.getComponent(cc.Label);
		let luckyjackpot = self._gameCore.resultData?.luckyjackpot ?? self._gameCore.reconnectLuckyjackpot;
		let niunum =  self._gameCore.resultData?.niunum ?? self._gameCore.reconnectNiuNum;
		
        this._gameView.scrollNumber({
            txt: allJackpotNumLabel,
            began: 0,
            end: luckyjackpot/Config.SCORE_RATE,
            format: (nValue) => {
                allJackpotNumLabel.string = `${self._gameView.formatNumberForComma(Common.toInt(nValue), true)}`;
            }
        });

		this.niuNode.removeAllChildren();
		for(let i = 0;i < niunum;i++){
			let item = cc.instantiate(this.niuItem);
			item.active = true;
			this.niuNode.addChild(item);
		}
		let delayTime = 7;
		if(this._gameCore.bAutoSpin){
			delayTime = 3;
		}

		this.bClickFlag = false;
		this.reqSuccess = false;
		this.scheduleOnce(()=>{
			self.onClickGo(null,null);
		},delayTime)
	}

	onDestroy() {
        EventManager.instance.off(GameEvent.TURN_TABLE_INFO, this.onGetTurnTableInfo, this);
    }

	//关闭转盘回调
    setCloseCallback(callback){
		this.mCallBack = callback;
    }

	onGetTurnTableInfo(info: any){
		if(!info || this.reqSuccess){
			return;
		}

		this.reqSuccess = true;

		let tspine = this.Panel_go.getComponent(sp.Skeleton)
		tspine.setAnimation(0, `anniu_click`, false);

		let tspine1 = this.Node_fenWei.getComponent(sp.Skeleton)
		tspine1.setAnimation(0, `fenwei_click`, false);

		let self = this;
		tspine.setCompleteListener(() => {
			tspine.setAnimation(0, `anniu_idle`, true);
			self.scheduleOnce(()=>{
				tspine1?.setAnimation(0, `fenwei_idle`, true);
			},0.5)
		});
		
		this.playTurnTableAnim(info)
	}

	onClickGo(target: any, customEventData: any) {
		
		if(this.bClickFlag || this.reqSuccess){
			console.error('测试============')
			return;
		}

		this.bClickFlag = true;
		let self = this;
		this.scheduleOnce(()=>{
			self && (self.bClickFlag = false);
		},3)
		this._gameCore.sendTurnTableStart();//转盘
	}


	/** 转盘动画 
	 * nType 转盘类型 0:无  1：free_5  2：free_10   3：free_15   4： single_wild  5： double_wild  6： jackpot1   7： jackpot2   8： jackpot3    
	 * */
	playTurnTableAnim(info: any): void {
		// this.Panel_turnTable.setRotationFromEuler(0, 0, 0);
		
		let tangel = [0,135, 225, 360,270, 90,315, 45, 180]
		let luckyspinid: number = info["luckyspinid"];
		
		if (TURN_TABLE_TYPE.wild_single == luckyspinid || TURN_TABLE_TYPE.wild_double == luckyspinid) {
			luckyspinid = (0.5 < Math.random()) ? TURN_TABLE_TYPE.wild_double : TURN_TABLE_TYPE.wild_single;
		}
	
		this.Panel_turnTable.stopAllActions();
		AudioHelper.instance.playEffect("res/sounds/spin_go");
		let self = this;
		cc.tween(this.Panel_turnTable)
			.to(1.5, { angle: 360 * 10 + tangel[luckyspinid] })
			.call(() => {
				// 转完直接播放免费，wild 声音
				self.mFireAni.active = true;
				let tspine = self.mFireAni.getComponent(sp.Skeleton);
				tspine.setAnimation(0, `huode`, true);
				if (TURN_TABLE_TYPE.free5 <= luckyspinid && TURN_TABLE_TYPE.free15 >= luckyspinid) {
					AudioHelper.instance.playEffect(Sounds.GET_FREE);
				} else if (TURN_TABLE_TYPE.wild_single == luckyspinid || TURN_TABLE_TYPE.wild_double == luckyspinid) {
					AudioHelper.instance.playEffect(Sounds.GET_WILD);
				}
				else if(luckyspinid >= TURN_TABLE_TYPE.jackpot1){
					AudioHelper.instance.stopEffectByPath(`res/sounds/jackpot`)
					AudioHelper.instance.playEffect(`res/sounds/jackpot`);
				}
			})
			.delay(1)
			.call(() => {
				if (luckyspinid >= TURN_TABLE_TYPE.jackpot1) {
					// 播放爆奖池动画
					self.playJackpotAnim(info.winmoney)
				} else {
					self.Panel_turnTable.stopAllActions();
					cc.tween(self.Panel_turnTable)
						.delay(1.5)
						.call(() => {
							if (self.mCallBack) {
								self.mCallBack();
							}
							// self.onClickClose();
						})
						.start()
				}

			})
			.start();
	}

	/**爆奖池 */
	playJackpotAnim(jackpotNum) {
		let self = this;
		this.clickCloseNode.active = false;

		this.Panel_jackpotPrize.active = true;
		let jackpotSpinGoldNode = this.jackpotSpinGoldNode.getComponent(sp.Skeleton);
        jackpotSpinGoldNode.setAnimation(0, 'jinbi0', false);
        jackpotSpinGoldNode.setCompleteListener(()=>{
            jackpotSpinGoldNode.setAnimation(0, 'jinbi1', true);
        })
		let ani1 = this.jackpotPrizeAni.getComponent(cc.Animation);
		let animState1 = ani1.play('animation0');
		animState1.wrapMode = cc.WrapMode.Normal;
		ani1.on('finished',(()=>{
			let animState1 = ani1.play('animation1');
			animState1.wrapMode = cc.WrapMode.Loop;
		}), this);

		let jackpot_prize = self.jackpot_prize.getComponent(cc.Label);
			self.scheduleOnce(() => {
				self._gameView.scrollNumber({
					txt: jackpot_prize,
					began: 0,
					end: jackpotNum/Config.SCORE_RATE,
					format: (nValue, dataEx) => {
						jackpot_prize.string = `${self._gameView.formatNumberForComma(Common.toInt(nValue), true)}`;
					},
					callback: () => {
						jackpot_prize.node.stopAllActions();
						self._gameView.nodeRunActionOncs(jackpot_prize.node, () => {
							self.Panel_jackpotPrize.active = false;
							self.mCallBack && self.mCallBack()
						}, 2);
					},
				});
			}, 0.5);

		// let ani: cc.Animation = this.FileNode_jackpot.getComponent(cc.Animation);
		// let animState = ani.play('animation0');
		// animState.wrapMode = cc.WrapMode.Normal;
		
		// if(self._gameCore.resultData.luckyjackpot != jackpotNum){
		// 	let allJackpotNumLabel = this.allJackpotNum1.getComponent(cc.Label)
		// 	this._gameView.scrollNumber({
		// 		txt: allJackpotNumLabel,
		// 		began: self._gameCore.resultData.luckyjackpot/Config.SCORE_RATE,
		// 		end: jackpotNum/Config.SCORE_RATE,
		// 		format: (nValue) => {
		// 			allJackpotNumLabel.string = `${self._gameView.formatNumberForComma(Common.toInt(nValue), true)}`;
		// 		}
		// 	});
		// }
		
		// ani.on('finished',(()=>{
		// 	let animState = ani.play('animation1');
		// 	animState.wrapMode = cc.WrapMode.Loop;
		// 	self.clickCloseNode.active = true;
		// 	self.scheduleClose = self.scheduleOnce(()=>{
		// 		self && !self.bCloseFlag && callbackFunc && callbackFunc();
		// 		self && (self.bCloseFlag = true);
		// 	},10)
		// }), this);
	}

	//滚动完jackpot后允许手动点击关闭界面
	onClickCloseJackpot(){
		if(this.bCloseFlag){
			return;
		}
		this.bCloseFlag = true;
		// if (this.scheduleClose) {
        //     this.unschedule(this.scheduleClose);
        //     this.scheduleClose = null;
        // }

		this.unscheduleAllCallbacks();
		this.mCallBack && this.mCallBack();
		// this.onClickClose();
	}

	public onClickClose() {
		// this.close();
	}

}
