import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import PopupLayer from "../../../../script/frame/component/PopupLayer";
import EventManager from "../../../../script/frame/manager/EventManager";
import { Constant, GameEvent, JACKPOT_PERCENT } from "../core/FMWDefine";
import FMWGameCore from "../core/FMWGameCore";
import FMWGameLogic from "../core/FMWGameLogic";
import FMWGameView from "./FMWGameView";

const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slotniu/FMWJackpotRecordLayer')
export default class FMWJackpotRecordLayer extends BaseLayer {

    /** 游戏主对象 */
    _gameCore: FMWGameCore;
    _gameview: FMWGameView;
    _gameLogic :FMWGameLogic = null;
    
    onLoad() {
        EventManager.instance.on(GameEvent.JACKPOT_RECORD_INFO, this.onUpdateRecordInfo, this);
        this._gameCore = cc.Canvas.instance.getComponent("FMWGameCore"); 
        this._gameview = cc.Canvas.instance.getComponent("FMWGameView"); 
        this._gameLogic = cc.Canvas.instance.getComponent("FMWGameLogic"); 
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)
    }

    start() {
        
    }

    setData(){
        this._gameCore.sendJackpotRecord();
    }

    onDestroy() {
        EventManager.instance.off(GameEvent.JACKPOT_RECORD_INFO, this.onUpdateRecordInfo, this);
    }

    /*
        SC_SLOTNIU_JPLIST_P //响应-Jackpot中奖列表
        参数1：数组返回结果
        参数1："playerid" //玩家ID
        参数2："name" //玩家昵称
        参数3："headid" //玩家系统头像id
        参数4："wxheadurl" //自定义头像url
        参数5："niunum" //牛的个数
        参数6："bet" //玩家下注额
        参数7："winscore" //玩家赢的金币
        参数8："time" //当局时间
        参数9："mult" //Jackpot倍数
    */
    //jackpot记录
    onUpdateRecordInfo(info: any){
        if(Common.isNull(info)){
            return;
        }
        
        this.LayerItems.content.removeAllChildren();
        for (let key in info) {
            let player = info[key];
            if(Common.isNull(player.playerid)){
                continue;
            }

            let item = cc.instantiate(this.LayerItems.Panel_item);
            this.LayerItems.content.addChild(item);
            item.active = true;
            
            let headImg = item.getChildByName('Panel').getChildByName('imgMask').getChildByName('headImg').getComponent(cc.Sprite);
            super.setPlayerHead(headImg, player.headid, player.wxheadurl??"");
            item.getChildByName('name').getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");
            item.getChildByName('time').getComponent(cc.Label).string = player.time?? '';
            item.getChildByName('rewards').getComponent(cc.Label).string = '₹' + this._gameview.moneyFormat(player.winscore ?? 0);
            item.getChildByName('total_bet').getComponent(cc.Label).string = '₹' + this._gameview.moneyFormat(player.bet ?? 0,1);
            item.getChildByName('type').getComponent(cc.Label).string = 'x' + player.niunum??1;
            item.getChildByName('multiples').getComponent(cc.Label).string = player.mult??1 + 'x';
        }
        
    }

    public onClickClose() {
        this._gameLogic.popUpEffect(this.node,false);
    }

}
