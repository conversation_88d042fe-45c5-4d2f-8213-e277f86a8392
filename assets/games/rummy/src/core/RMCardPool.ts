import RMCardItem from "./RMCardItem";


const DEFAULT_CARD_COUNT = 60;        // 默认生成个数
const MAX_CARD_COUNT = 10;           // 默认生成个数

const { ccclass } = cc._decorator;
@ccclass
export default class RMCardPool  {
    private static SINGLETON_MSG: string = "[RMCardPool] @func ctor : singleton already constructed!";
    private static _instance: RMCardPool;
    public static get instance(): RMCardPool {
        if (!RMCardPool._instance) {
            RMCardPool._instance = new RMCardPool();
        }
        return RMCardPool._instance;
    }

    constructor() {
        if (RMCardPool._instance) throw Error(RMCardPool.SINGLETON_MSG);
    }

    private _cardPool = new cc.NodePool('RMCardItem');
    private _curSize = 0;
    private _cardItemNode: cc.Prefab = null;

    public init(cardItemNode: cc.Prefab,size: number = DEFAULT_CARD_COUNT) {
        if(cardItemNode){
            this._cardItemNode = cardItemNode
            for (let i = 0; i < size; ++i) {
                let node = cc.instantiate(cardItemNode);
                this._cardPool.put(node);
                this._curSize++;
            }
        }
    }

    /**
     * @description 清空对象池
     */
    public clear() {
        this._cardPool.clear();
        this._curSize = 0;
    }

    public get(): cc.Node | null {
        if (this._cardPool.size() > 0) {
            let card = this._cardPool.get();
            return card;
        } else if (this._curSize < MAX_CARD_COUNT) {
            this.init(this._cardItemNode,30);
            return this.get();
        } else {
            return null;
        }
    }

    public put(card: cc.Node) {
        if (!!card && cc.isValid(card, true)) {
            let cls = card.getComponent(RMCardItem);
            if (!cls.isRelease) {
                // card.stopAllActions();
                cls.isRelease = true;
                card.getComponent(RMCardItem).resetUI()
                this._cardPool.put(card);
            } else {
                // console.error('ChipPoolAssisCom put failed!.');
            }
        }
    }

    public getSize(){
        return this._curSize;
    }
}