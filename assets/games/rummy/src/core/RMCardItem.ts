import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import { CardColor, CardData, CardGetState } from "./RMDefine";

const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('rummy/RMCardItem')
export default class RMCardItem extends cc.Component {

    //牌面
    @property(cc.Node)
    node_font: cc.Node = null;

    //牌底
    @property(cc.Node)
    img_font: cc.Node = null;

    //牌值
    @property(cc.Node)
    img_num: cc.Node = null;

    //大小王牌值
    @property(cc.Node)
    img_num_joker: cc.Node = null;

    //花色（小）
    @property(cc.Node)
    img_type: cc.Node = null;

    //花色（大）
    @property(cc.Node)
    img_logo: cc.Node = null;

    //大小王大图
    @property(cc.Node)
    img_logo_joker: cc.Node = null;

    //癞子
    @property(cc.Node)
    img_laizi: cc.Node = null;

    //遮罩
    @property(cc.Node)
    img_shadow: cc.Node = null;

    //遮罩1
    @property(cc.Node)
    img_shadow1: cc.Node = null;

    //背面
    @property(cc.Node)
    img_back: cc.Node = null;

    //牌灯光
    @property(cc.Node)
    light: cc.Node = null;

    @property(cc.SpriteAtlas)
    cardsAtlas: cc.SpriteAtlas = null;
     
    //当前节点是否释放
    private _release: boolean;
    //牌值
    private _cardValue: number = 1;
    //花色
    private _cardColor: number = 3;
    //牌索引
    public index: number = -1;
    //当前所在分组
    public group: number = -1;
    //是否点击弹起了
    moveUP: boolean = false;
    //当前位置
    pos: cc.Vec3 = cc.v3(0,0);
      /** 是否当前一轮拿到的牌 当超时之后会直接丢出当前一轮拿到的牌 */
    getCard: boolean = false;
    
    public reuse() {
        this._release = false;
    }

    public unuse() {
        this._release = true;
    }

    public set isRelease(release: boolean) {
        this._release = release;
    }
    public get isRelease(): boolean {
        return this._release;
    }

    public getCardValue(){
        return this._cardValue;
    }

    public getCardColor(){
        return this._cardColor;
    }

    // 显示牌背
    public showCardBack() {
       this.node_font.active = false;
       this.img_back.active = true;
    }

    //显示遮罩
    public showCardshadow(show: boolean){
        this.img_shadow.active = show;
    }

     //显示遮罩
     public showCardshadow1(show: boolean){
        this.img_shadow1.active = show;
    }
    
    //显示牌灯光
    public showCardLight(show: boolean,bPlayAni?: boolean){
        this.light.active = show;
        this.light.stopAllActions();
        this.light.opacity = 255;
        if(bPlayAni){
            cc.tween(this.light)
            .then(cc.fadeOut(0.5))
            .then(cc.fadeIn(0.5))
            .union()
            .repeatForever()
            .start();
        }
    }

    //显示癞子
    public showCardLaizi(show: boolean){
        this.img_laizi.active = show;
    }


    // 显示牌数据
    public showCardData(color: number, value: number,isLaizi: boolean = false,isShowShadow: boolean = false,isShowLight: boolean = false) {
        // 判断扑克花色是否正确
        if (color < 2 || color > 6) {
            console.error("RMCardLayer.setCardData: color is invalid. color: " + color);
            return;
        }
        value = value == 1 ? 14 : value;
        // 判断扑克数字是否正确
        if (value < 2 || value > 16) {
            console.error("RMCardLayer.setCardData: value is invalid. value: " + value);
            return;
        }
                
        this._cardValue = value;
        this._cardColor = color;
        this.showCardInfo(value >= 15,isLaizi,isShowShadow,isShowLight);
        if(value >= 15){//大小王
            let jokerNumList = ["num_small_joker","num_big_joker"];
            let jokerIconList = ["icon_small_joker","icon_big_joker"];
            this.img_num_joker.getComponent(cc.Sprite).spriteFrame = this.cardsAtlas.getSpriteFrame(jokerNumList[value - 15]);
            this.img_logo_joker.getComponent(cc.Sprite).spriteFrame = this.cardsAtlas.getSpriteFrame(jokerIconList[value - 15]);
        }
        else{
            let typeStrList = ["logodiamond","logoclub","logoheart","logospade"];// 方块 梅花 红桃 黑桃
            let colorTypeStr = color%2 == 1 ? "red_" : "black_";
            if (value == 14){
                value = 1;
            }
            let numStr = "num_" + colorTypeStr + value;
            let typeStr = typeStrList[color - CardColor.diamond];
            this.img_num.getComponent(cc.Sprite).spriteFrame = this.cardsAtlas.getSpriteFrame(numStr);
            this.img_type.getComponent(cc.Sprite).spriteFrame = this.cardsAtlas.getSpriteFrame(typeStr);
            this.img_logo.getComponent(cc.Sprite).spriteFrame = this.cardsAtlas.getSpriteFrame(typeStr);
        }
    }

    //显示牌信息
    showCardInfo(isJoker: boolean,isLaizi: boolean = false,isShowShadow: boolean = false,isShowLight: boolean = false){
        this.node.active = true;
        this.node_font.active = true;
        this.img_font.active = true;
        this.img_back.active = false;
        this.showCardLaizi(isLaizi);
        this.showCardshadow(isShowShadow);
        this.showCardLight(isShowLight);
        this.img_num_joker.active = isJoker;
        this.img_logo_joker.active = isJoker;
        this.img_num.active = !isJoker;
        this.img_type.active = !isJoker;
        this.img_logo.active = !isJoker;
    }
    
    //重置
    public resetUI() {
       this.node.active = false;
       this.node.position = cc.v3(0,0);
       this.node.opacity = 255;
       this.node.scale = 1;
       this.img_font.active = false;
       this.node_font.active = false;
       this.img_back.active = true;
       this.img_logo_joker.active = false;
       this.img_num_joker.active = false;
       this._cardValue = 1;
       this._cardColor = 3;
       this.showCardLaizi(false);
       this.showCardshadow(false);
       this.showCardLight(false);
       this.node.is3DNode = false;
       this.index = -1;
       this.group = -1;
       this.moveUP = false;
       this.pos = cc.v3(0,0);
       this.getCard = false;
    }

    //翻转牌
    flip(time, timeDelay?,cardValue?: CardData) {
        let cardNode = this.node;
        if(timeDelay==undefined) timeDelay=0;
        cardNode.is3DNode = true;
        var curScale = cardNode.scale;
         let angles1 = cardNode.eulerAngles.clone();
        angles1.y = -90;
        let angles2 = cardNode.eulerAngles.clone();
        angles2.y = 0;

        let self = this;
        cardNode.stopAllActions();
        cc.tween(cardNode)
        .delay(timeDelay)
        .parallel(
            cc.tween().by(0.25 * time,{position:cc.v3(5,5)}),
            cc.tween().to(0.25 * time, { eulerAngles: angles1 }),
            cc.tween().then(cc.scaleTo(0.25 * time, 0.85 * curScale).easing(cc.easeIn(1.0)))
        )
        .call(()=>{
            self.showCardData(cardValue.color, cardValue.number);
        })
        .parallel(
            cc.tween().by(0.75 * time,{position:cc.v3(-5,-5)}),
            cc.tween().to(0.75 * time, { eulerAngles: angles2 },{ easing: (dt: number) => cc.easeOut(1.0).easing(dt) }),
            cc.tween().to(0.75 * time, { scale: 1 * curScale }),
        )
        .call(() => {
            self.showCardData(cardValue.color, cardValue.number);
        })
        .start()
    }

    //翻转牌
    flip1(cardValue?: CardData,isJoker?: boolean) {
        let cardNode = this.node;
        cardNode.is3DNode = true;
        let time = 0.15
        let self = this;
        
        cc.tween(cardNode)
        .parallel(
            cc.tween().by(time, { position: cc.v3(5,5)}),
            cc.tween().to(time, { eulerAngles:cc.v3(10,90,10)})
        )
        .call(()=>{
            self.showCardData(cardValue.color, cardValue.number);
            if (isJoker) {
                self.showCardLaizi(true);
                self.showCardshadow(true);
            }
        })
        .parallel(
            cc.tween().to(time, { eulerAngles:cc.v3(10,0,-10)}),
            cc.tween().to(time ,{scale:1.1}),
        )
        .delay(0.15)
        .parallel(
            cc.tween().by(0.05, { position: cc.v3(-5,-5)}),
            cc.tween().to(0.05, { eulerAngles: cc.v3(0,0,0) },{ easing: (dt: number) => cc.easeIn(1.0).easing(dt) }),
            cc.tween().to(0.05 ,{scale:1}),
        )
        .start()
    }

    //翻一张牌动画
    runFlipOneCardAni(cardNode,isShowLight?: boolean,delayFlag?: boolean,cardData?: CardData){
        cardNode.is3DNode = true;
        let angles1 = cardNode.eulerAngles.clone();
        angles1.y = 90;
        let angles2 = cardNode.eulerAngles.clone();
        angles2.y = 0;
        let delay = 0;
        let delay1 = 0;
        isShowLight && (delay = 0.3)
        delayFlag && (delay1 = 0.5)
        let self = this;
        cc.tween(cardNode)
        .delay(delay1)
        .to(0.2, { eulerAngles: angles1 })
        .call(() => {
            self.showCardData(cardData.color,cardData.number);
        })
        .to(0.2, { eulerAngles: angles2 })
        .delay(delay)
        .call(() => {
            if(isShowLight){
                self.showCardLight(true,true)
                AudioHelper.instance.playEffect('res/sound/first_player')
            }
        })
        .start();
    }
}
