import EventManager from "../../../../script/frame/manager/EventManager";
import { CardColor, CardGetState, Constant, GameEvent } from "./RMDefine";

const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('rummy/RMGroupItem')
export default class RMGroupItem extends cc.Component {

    //底图
    @property(cc.Node)
    groupTypeBg: cc.Node = null;

    //分组描述
    @property(cc.Label)
    txt_type: cc.Label = null;

    //左边小箭头
    @property(cc.Node)
    arrow_left: cc.Node = null;
    
    //右边小箭头
    @property(cc.Node)
    arrow_right: cc.Node = null;

    //点击分组
    @property(cc.Node)
    btn_add_here: cc.Node = null;
    
    @property(cc.SpriteAtlas)
    mainUiAtlas: cc.SpriteAtlas = null;
    //当前分组索引
    public index: number = -1;
    
    //重置
    public resetUI() {
       this.node.active = false;
       this.node.position = cc.v3(0,0);
       this.node.opacity = 255;
       this.node.scale = 1;
       this.isShowAddHere(false);
       this.setNodeIndex(-1);
    }

    //显示当前分组
    showUI(groupItemWidth: number,groupItemPosX: number,isShowArrowLeft?: boolean,isShowArrowRight?: boolean){
        this.groupTypeBg.setContentSize(cc.size(groupItemWidth,this.groupTypeBg.getContentSize().height));
        this.node.zIndex = 80;
        this.node.x = groupItemPosX;
        this.node.y = Constant.groupItem_y;
        this.isShowArrowLeft(isShowArrowLeft);
        this.isShowArrowRight(isShowArrowRight)
        this.arrow_left.x = -Math.abs((groupItemWidth - 6)/2 - 10); 
        this.arrow_right.x = Math.abs(this.arrow_left.x); 
    }

    //设置分组颜色 绿色（必须有一个真顺子才会显示） 黄色（假顺子或组 且没有真顺子时显示） 橙色（未定义组）
    setGroupTypeColor(type: number){
        let colorbgList = ['rummy_ui_9','rummy_ui_10','rummy_ui_11'];//橙色 绿色 黄色
        let colorArrowList = ['rummy_ui_19','rummy_ui_20','rummy_ui_21'];
        let colorAddHereList = ['rummy_ui_22','rummy_ui_23','rummy_ui_24'];
        this.groupTypeBg.getComponent(cc.Sprite).spriteFrame = this.mainUiAtlas.getSpriteFrame(colorbgList[type]);
        this.arrow_left.getChildByName('sprite').getComponent(cc.Sprite).spriteFrame = this.mainUiAtlas.getSpriteFrame(colorArrowList[type]);
        this.arrow_right.getChildByName('sprite').getComponent(cc.Sprite).spriteFrame = this.mainUiAtlas.getSpriteFrame(colorArrowList[type]);
        this.btn_add_here.getChildByName('sprite').getComponent(cc.Sprite).spriteFrame = this.mainUiAtlas.getSpriteFrame(colorAddHereList[type]);
    }

    //类型文字描述 0:真顺子 1:假顺子 2:set 3：未定义 4:单张牌时显示的数值（非癞子或大小王） 5:单张牌是癞子或大小王
    setGroupTypeStr(type: number,colorType: number,score: string){
        let typeStrList = ['Pure Sequene','Impure Sequene','Set',`Invalid(${score})`,`(${score})`,''];
        let colorList = [cc.color(193,105,57),cc.color(16,105,82),cc.color(161,137,27)]
        this.txt_type.string = typeStrList[type];
        this.txt_type.node.color = colorList[colorType];
    }


    //设置当前索引
    setNodeIndex(index){
        this.index = index;
    }

    //获得当前索引
    getNodeIndex(){
        return this.index;
    }

    //是否显示add_here分组
    isShowAddHere(isShow: boolean){
        this.btn_add_here.active = isShow;
    }

    //是否显示左箭头
    isShowArrowLeft(isShow: boolean){
        this.arrow_left.active = isShow;
    }

    //是否显示右箭头
    isShowArrowRight(isShow: boolean){
        this.arrow_right.active = isShow;
    }

    //交换牌组位置
    swapCardsGroupPos(target: any, customEventData: any){
        let index = Number(customEventData);
        EventManager.instance.emit(GameEvent.SWAP_CARDS_GROUP,{arrowTypeIndex:index,curGroupIndex:this.index});  
    }

    //在当前位置添加分组
    addCardsGroupHere(target: any, customEventData: any){
        EventManager.instance.emit(GameEvent.ADD_CARDS_GROUP_HERE,this.index);  
    }
}
