import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";

const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('rummy/RMAudioMng')
export default class RMAudioMng extends BaseLayer {

    playMusic() {
        let path = "res/sound/bg";
        AudioHelper.instance.playMusic(path, true);

    }
    pauseMusic() {
        AudioHelper.instance.pauseMusic();
    }

    resumeMusic() {
        AudioHelper.instance.resumeMusic();
    }

    _loadPlaySFX(path) {
        AudioHelper.instance.playEffect(path);
    }
}

