import Common from "../../../../script/frame/common/Common";
import { CardColor, CardData, CardInterface, CardNumDesc, Constant, GroupType, CardSuit } from "./RMDefine";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property, disallowMultiple, menu } = cc._decorator;

// 假设常量定义在某处
const CARD_SUIT_COUNT = 4;
const CARD_COUNT_PER_SUIT = 13;

@ccclass
@disallowMultiple()
@menu('rummy/RMGameLogic')
export default class RMGameLogic extends cc.Component {

    jokerData: CardData = null;
    


    //////////////////////////////////////////////////////////////////////////////
    // 排列用户手牌
    public sortCardData(cards: Array<any>) {
        let self = this;
        cards.sort((a: any, b: any): number => {
            let value1 = Common.toInt(a["number"]);
            let value2 = Common.toInt(b["number"]);
            if (value1 == value2) {
                let color1 = self.cardColorIndex(a["color"]);
                let color2 = self.cardColorIndex(b["color"]);
                if (color1 < color2) {
                    return -1;
                }
            }
            else if (value1 < value2) {
                return -1;
            }
            return 1;
        });
    }

    
    //设置当前癞子
    setJoker(cardData: CardData) {
        this.jokerData = cardData
        if(cardData.number >= CardNumDesc.NUM_JOKER_S){//翻出来的癞子是大小王时，A是癞子
            this.jokerData.number = CardNumDesc.NUM_A;
            this.jokerData.color = CardColor.diamond;
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    // 获取牌花色的排列索引
    private cardColorIndex(color: any): number {
        color = Common.toInt(color);
        switch (color) {
            case 6: return 0; break;
            case 4: return 1; break;
            case 5: return 2; break;
            case 3: return 3; break;
        }
        return 0;
    }

    //获取对应花
    getCardType(card) {
        return this.cardColorIndex(Common.toInt(card["color"]));
    }

    //获取牌值
    getCardNum(card) {
        return Common.toInt(card["number"]);
    }

    
    //return map
    getListCardBySuit(cardList) {
        let resultMap = new Map();
        for (let i = 0; i < cardList.length; i++) {
            if (!resultMap.has(this.getCardType(cardList[i]))) {
                resultMap.set(this.getCardType(cardList[i]), [cardList[i]]);
            } else {
                let temple = resultMap.get(this.getCardType(cardList[i]));
                temple.push(cardList[i]);
                resultMap.set(this.getCardType(cardList[i]), temple);
            }
        }
        return resultMap;
    };

    getListCardByType(cardList) {
        let resultMap = new Map();
        for (let i = 0; i < cardList.length; i++) {
            if (!resultMap.has(this.getCardNum(cardList[i]))) {
                resultMap.set(this.getCardNum(cardList[i]), [cardList[i]]);
            } else {
                let temple = resultMap.get(this.getCardNum(cardList[i]));
                temple.push(cardList[i]);
                resultMap.set(this.getCardNum(cardList[i]), temple);
            }
        }
        return resultMap;
    };

    /** 检查当前牌是否为癞子 */
    public getJoker(cardData: CardData) {
        if (cardData == undefined){
            console.log("---------------------cardData is undefined-------------------", cardData)
            return false;
        }

        if (this.jokerData.number == cardData.number) {
            return true;
        }
        return false;
    }

    //计算分组中的牌是否顺子
    public groupCards_isSequene(cards: CardData[]) {
        if (cards.length < 3) {
            return GroupType.NUMBER;
        }

        let jokercount = 0
        let trumpcount = 0;
        let jokervalue: CardData = null;
        let new_card: CardData[] = [];
        let hasNumA: boolean = false; //是否含有A
        for (let i = 0; i < cards.length; i++) {
            if(cards[i] != null && cards[i] != undefined){
                if (this.getJoker(cards[i])) {
                    jokercount++;
                    jokervalue = cards[i];
                } else if (cards[i].number == CardNumDesc.NUM_JOKER_S || cards[i].number == CardNumDesc.NUM_JOKER_B) {//
                    trumpcount++;
                } else {
                    new_card.push(cards[i]);
                }
                if (cards[i].number == CardNumDesc.NUM_A) {
                    hasNumA = true;
                }
            }
        }
        if (new_card.length <= 1) {//全是癞子或大小王 或有只有一张不是癞子的牌
            return GroupType.SEQUENCE;
        }

        new_card.sort((a, b) => {
            return a.number - b.number;
        })

        let bIsSequene = true;
        for (let i = 0; i < new_card.length - 1; i++) {//去掉大小王及癞子后
            if (new_card[i].number == new_card[i + 1].number) {//有重复数值 肯定不是顺子
                bIsSequene = false;
                break
            }
        }

        if (!bIsSequene) {//非顺子
            return GroupType.NUMBER;
        }

        //去掉癞子及大小王按花色分组
        let resultMap = this.getListCardBySuit(new_card);
        if (resultMap.size > 1) {//去掉癞子后 有多个花色 
            return GroupType.NUMBER;
        }
        let color = null;
        resultMap.forEach((value: any, key: number) => {
            color = value[0].color;//只有一种花色
        })

        //是否真顺子
        if (trumpcount == 0) {//没有大小王
            let tempCardList = cards.slice(0);
            let len = 1;
            hasNumA && (len = 2);//有A的情况下 要判断是A23或QKA
            for (let k = 0; k < len; k++) {
                tempCardList.forEach(item => {
                    if (item.number == 1) {
                        item.number = k == 0 ? 1 : 14;
                    }
                })

                tempCardList.sort((a, b) => {
                    return a.number - b.number;
                })
                let bPURE_SEQUENCE = true;//是否真顺子
                for (let i = 0; i < tempCardList.length - 1; i++) {
                    if (tempCardList[i].color == color && tempCardList[i].number + 1 == tempCardList[i + 1].number) {
                        continue;
                    }
                    else {
                        bPURE_SEQUENCE = false;
                    }
                }
                if (bPURE_SEQUENCE) {
                    return GroupType.PURE_SEQUENCE;
                }
            }
        }

        let tempCardList = new_card.slice(0);

        let len = 1;
        hasNumA && (len = 2);//有A的情况下 要判断是A23或QKA
        for (let k = 0; k < len; k++) {

            let wNeedJokerCardCount = 0;
            let cardIndex = [];
            cardIndex.length = 14;
            cardIndex.fill(0);

            for (let i = 0; i < cards.length; i++) {
                let value = tempCardList[i]?.number ?? 0;
                value = value == 1 && k == 1 ? value + 13 : value;
                if (value - 1 >= 0) {
                    cardIndex[value - 1]++;
                }
            }

            let stepSize = cards.length;
            for (let i = 0; i < 15 - cards.length; i++) {
                wNeedJokerCardCount = 0;
                for (let j = i; j < stepSize; j++) {
                    if (cardIndex[j] > 1) {
                        return GroupType.NUMBER;
                    }
                    if (cardIndex[j] == 0) {
                        wNeedJokerCardCount++;
                    }
                }

                if (jokercount + trumpcount > 0 && jokercount + trumpcount - wNeedJokerCardCount >= 0) {
                    return GroupType.SEQUENCE;
                } else if (jokercount + trumpcount == 0 && wNeedJokerCardCount == 0) {
                    return GroupType.PURE_SEQUENCE;
                }
                stepSize++;
            }
        }

        return GroupType.NUMBER;
    }

    //是否set
    public groupCards_isSet(cards: CardData[]) {
        if (cards.length < 3) {
            return GroupType.NUMBER;
        }
        //set 组合最多只能为4张牌
        if (cards.length > 4) {
            return GroupType.NUMBER;
        }
        let new_card: CardData[] = [];
        for (let i = 0; i < cards.length; i++) {
            if (!this.isJoker(cards[i])) {
                new_card.push(cards[i]);
            }
        }

        if (new_card.length <= 1) {
            return GroupType.SEQUENCE;
        }

        //去掉癞子及大小王按花色分组 必须不同花色
        let resultMap = this.getListCardBySuit(new_card);
        if (resultMap.size != new_card.length) {//去掉癞子后 有重复花色 
            return GroupType.NUMBER;
        }

        let type = GroupType.SET;
        for (let i = 0; i < new_card.length - 1; i++) {
            if (new_card[i].number != new_card[i + 1].number) {
                type = GroupType.NUMBER;
                break
            }
        }

        return type
    }

    //是否癞子 大小王是癞子时，A则为癞子
    isJoker(cardList: CardData) {
        return this.getJoker(cardList) || cardList.number >= CardNumDesc.NUM_JOKER_S;
    }

    //累计分数
    countScoreNew(cardList: CardData[]) {
        let score = 0;
        for (let i = 0; i < cardList.length; i++) {
            score += this.getScoreNew(cardList[i]);
        }

        if (score > Constant.max_score) {
            score = Constant.max_score;
        }

        return score + '';
    };

    //牌值对应分数
    getScoreNew(cardNum: CardData) {
        let score = 0;
        if (this.isJoker(cardNum)) {//癞子及大小王 不计分
            score = 0;
        }
        else if (cardNum.number == CardNumDesc.NUM_A || cardNum.number > CardNumDesc.NUM_9) {
            score = 10;
        }
        else {
            score = cardNum.number;
        }
        return score;
    }



    /*========================================从别人处翻译的破解代码==============================begin */
    tableNumberCard = 54;
    CardType1 = {
        _NONE: 0,
        _2: 1,
        _3: 2,
        _4: 3,
        _5: 4,
        _6: 5,
        _7: 6,
        _8: 7,
        _9: 8,
        _10: 9,
        _J: 10,
        _Q: 11,
        _K: 12,
        _A: 13,
        _JOKER: 14,
    };
    wildCard: number = null;

    //将自己的花色转换成别人牌的花色
    converToOtherSuit(color: number){
        switch (color) {
            case 6: return 0; break;
            case 5: return 1; break;
            case 4: return 2; break;
            case 3: return 3; break;
        }
    }

    /* 别人的牌值
        cardNumList = [
            50,2,6,10,14,18,22,26,30,34,38,42,46,   红桃A ~ K
            52,4,8,12,16,20,24,28,32,36,40,44,48,   方块A ~ K
            51,3,7,11,15,19,23,27,31,35,39,43,47,   梅花A ~ K
            49,1,5,9,13,17,21,25,29,33,37,41,45,   黑桃A ~ K   
            53,54,                                 小王 大王
        ]    
    */


    // （花色 + 1） + (点数 - 1 - 1) * 4 (别人牌值是 CardType1 ，与自己的牌值 CardNumDesc 有区别))   转换成别人的牌值
    convertOtherNum(card: CardData) {
        let suit = this.converToOtherSuit(card.color);
        let poit = 0;
        if(card.number == CardNumDesc.NUM_JOKER_S){
            poit = 53;
            return poit;
        }
        else if(card.number == CardNumDesc.NUM_JOKER_B){
            poit = 54;
            return poit;
        }
        
        if(card.number == CardNumDesc.NUM_A){
            poit = 14;
        }
        else{
            poit = card.number;
        }
        
        return (suit + 1) + (poit - 2) * 4;
    }

    //牌值转换列表
    convertOtherNumList(cardList: CardInterface[]){
        let list: number[] = [];
        for(let i = 0; i < cardList.length;i++){
            list.push(this.convertOtherNum(cardList[i].cardData));
        }
        return list;
    }

    //将别人的花色转换成自己牌的花色
    converToSelfSuit(color: number){
        switch (color) {
            case 0: return 6; break;
            case 1: return 5; break;
            case 2: return 4; break;
            case 3: return 3; break;
        }
    }

    //将别人牌值转换回来
    convertSelfSuit(cardNum: number){
        if (cardNum > 52) return 0;
        return this.converToSelfSuit((cardNum - 1) % 4);
    }
   
    
    //将别人牌值转换回来
    convertSelfNum(cardNum: number){
        let cardData = <CardData>{};
        if(cardNum == 53){//小王
            cardData.color = CardColor.diamond;
            cardData.number = CardNumDesc.NUM_JOKER_S;
        }
        else if(cardNum == 54){//大王
            cardData.color = CardColor.diamond;
            cardData.number = CardNumDesc.NUM_JOKER_B;
        }
        else{
            let point = ((cardNum - 1) >> 2) + 1;
            if(point == this.CardType1._A){
                point = CardNumDesc.NUM_A;
            }
            else{
                point = point + 1;//别人的牌2值是1 我们牌2的值是2
            }
            let color = this.convertSelfSuit(cardNum);
            cardData.color = color;
            cardData.number = point;
        }
        
        return cardData;
    }

    getScore(type) {
        switch (type) {
            case this.CardType1._2:
            case this.CardType1._3:
            case this.CardType1._4:
            case this.CardType1._5:
            case this.CardType1._6:
            case this.CardType1._7:
            case this.CardType1._8:
            case this.CardType1._9:
              return type + 1;
            case this.CardType1._10:
            case this.CardType1._J:
            case this.CardType1._Q:
            case this.CardType1._K:
            case this.CardType1._A:
              return 10;
            default:
              return 0;
          }
    }

    getType(id) {
        if (id > 52) return this.CardType1._JOKER;
        return ((id - 1) >> 2) + 1;
    }

    getSuit(id) {
        if (id > 52) return 0;
        return (id - 1) % 4;
    }

    sortBySet(cards: number[]): number[][] {
        let singleCards = this.convertToSingle(cards);

        //let sorted_cards = _.sortBy(singleCards, (c) => c);
        let sorted_cards = singleCards.sort((left, right) => {
            var a = left;
            var b = right;
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })
        // console.log("===========sortBy9===========", sorted_cards)
        let result: number[][] = [[sorted_cards[0]]];
        let last_card = sorted_cards[0];
        let cur_group_index = 0;
        for (let i = 1; i < sorted_cards.length; ++i) {
            if (this.getType(sorted_cards[i]) == this.getType(last_card)) {
                result[cur_group_index].push(sorted_cards[i]);
            } else {
                cur_group_index++;
                result[cur_group_index] = [sorted_cards[i]];
                last_card = sorted_cards[i];
            }
        }

        //result = _.sortBy(result, (g) => -g.length);

        result = result.sort((left, right) => {
            var a = -left.length;
            var b = -right.length;
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })
        // console.log("===========sortBy10===========", result)

        // merge single cards together
        let mergeResult: number[][] = [];
        let single: number[] = [];
        for (let i = 0; i < result.length; ++i) {
            if (result[i].length > 1) {
                mergeResult.push(result[i]);
            } else if (result[i].length == 1) {
                single.push(result[i][0]);
            }
        }
        mergeResult.push(single);
        return mergeResult;
    }

    compareCards(listInit: number[], listSorted: number[]): boolean {
        if (listInit.length != listSorted.length) {
            return false;
        }

        //listInit = _.sortBy(listInit, (c) => parseInt(c as any));
        //listSorted = _.sortBy(listSorted, (c) => parseInt(c as any));


        listInit = listInit.sort((left, right) => {
            var a = parseInt(left as any);
            var b = parseInt(right as any)
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })



        listSorted = listSorted.sort((left, right) => {
            var a = parseInt(left as any);
            var b = parseInt(right as any)
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })
        // console.log("===========sortBy11===========", listInit)
        // console.log("===========sortBy12===========", listSorted)

        for (let i = 0; i < listInit.length; ++i) if (listInit[i] != listSorted[i]) {
            return false;
        }

        return true;
    }

    compareListCard(listInit: any[], listSorted: any[]): boolean {
        if (listInit.length !== listSorted.length) {
            return false;
        }
        for (let i = 0; i < listInit.length; i++) {
            if (listInit[i].length !== listSorted[i].length) {
                return false;
            }
            for (let j = 0; j < listInit[i].length; j++) {
                if (listInit[i][j].getId() != listSorted[i][j].getId()) {
                    return false;
                }
            }
        }
        return true;
    }

    checkCardGroup(cardList, wildCard = null) {
        if (this.isPureSequence(cardList))
            return GroupType.PURE_SEQUENCE
        else if (this.isImpureSequence(cardList, wildCard))
            return GroupType.SEQUENCE
        else if (this.isSet(cardList))
            return GroupType.SET
        else
            return GroupType.NUMBER


    }


    isPureSequence(cardList) {
        if (!cardList || cardList.length < 3) return false
        let numAces = 0;
        let remainList = []
        let aceList = []
        for (let i = 0; i < cardList.length; ++i) {
            if (cardList[i] > 52) return false
            if (cardList[i] >= 49 && cardList[i] <= 52) {
                numAces++;
                aceList.push(cardList[i])
            } else {
                remainList.push(cardList[i])
            }
        }

        if (numAces > 1) return false


        var cardSort = remainList.sort((a, b) => {
            return a - b
        })
        if (numAces > 0) {
            if (cardSort[0] > 4 && cardSort[0] < 41) {  //3 - j
                return false
            }
            if (this.getSuit(aceList[0]) != this.getSuit(cardSort[0])) return false
        }
        for (let index = 0; index < cardSort.length; index++) {
            const element = cardSort[index];
            if (cardSort[index + 1]) {
                if (this.getSuit(element) != this.getSuit(cardSort[index + 1])) return false
                else if (this.getType(element) != this.getType(cardSort[index + 1]) - 1) return false
            }
        }


        return true;
    }

    isSet(cardList) {
        if (!cardList || cardList.length < 3) return false

        let remainList = []
        let wildCardList = []
        for (let index = 0; index < cardList.length; index++) {
            const element = cardList[index];
            if (this.isWildCard(element)) {
                wildCardList.push(element)
            } else {
                remainList.push(element)
            }
        }

        let cardSuitList = [0, 0, 0, 0]
        let cardNum = this.getType(remainList[0])
        for (let index = 0; index < remainList.length; index++) {
            const element = remainList[index];
            if (this.getType(element) != cardNum) return false
            cardSuitList[this.getSuit(element)] = cardSuitList[this.getSuit(element)] + 1

        }
        for (let index = 0; index < cardSuitList.length; index++) {
            if (cardSuitList[index] > 1) return false
        }


        return true
    }

    isImpureSequence(cardList, wildCard: number) {
        if (!cardList || cardList.length < 3) return false
        let remainList = []
        let wildCardList = []
        for (let index = 0; index < cardList.length; index++) {
            const element = cardList[index];
            if (this.isWildCard(element)) {
                wildCardList.push(element)
            } else {
                remainList.push(element)
            }
        }

        if (wildCardList.length < 1) return false

        remainList = remainList.sort((a, b) => {
            return a - b
        })

        let cz = 0
        for (let index = 0; index < remainList.length; index++) {
            const element = remainList[index];
            if (remainList[index + 1]) {
                if (this.getSuit(element) != this.getSuit(remainList[index + 1])) return false
                cz = cz + this.getType(remainList[index + 1]) - this.getType(element) - 1
            }
        }

        if (cz > wildCardList.length) return false

        return true

    }

    //花色排序
    sortBySuit(cards: number[], needMerge?: boolean): number[][] {
        if (cards.length <= 0) return [];

        if (needMerge === undefined) needMerge = false;
        let singleCards = this.convertToSingle(cards);

        //let sorted_cards = _.sortBy(singleCards, (c) => [this.getSuit(c), c]);

        let sorted_cards = singleCards.sort((left, right) => {
            var a = this.getSuit(left);
            var b = this.getSuit(right);
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            else {
                return left - right
            }
            return 1
        })

        // console.log("===========sortBy4===========", sorted_cards)
        let result: number[][] = [[sorted_cards[0]]];
        let last_card = sorted_cards[0];
        let cur_group_index = 0;
        for (let i = 1; i < sorted_cards.length; ++i) {
            if (this.getSuit(sorted_cards[i]) == this.getSuit(last_card)) {
                result[cur_group_index].push(sorted_cards[i]);
            } else {
                cur_group_index++;
                result[cur_group_index] = [sorted_cards[i]];
                last_card = sorted_cards[i];
            }
        }

        // sort inside suit group
        for (let i = 0; i < result.length; ++i) {
            //result[i] = _.sortBy(result[i], (c) => this.getType(c));
            result[i] = result[i].sort((left, right) => {
                var a = this.getType(left);
                var b = this.getType(right);
                if (a !== b) {
                    if (a > b || a === void 0) return 1;
                    if (a < b || b === void 0) return -1;
                }
                return 1
            })
            // console.log("===========sortBy8===========", result)
        }

        //result = _.sortBy(result, (g) => -g.length);

        result = result.sort((left, right) => {
            var a = -left.length;
            var b = -right.length;
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })

        // console.log("===========sortBy8===========", result)

        if (needMerge) {
            // merge single cards together
            let mergeResult: number[][] = [];
            let single: number[] = [];
            for (let i = 0; i < result.length; ++i) {
                if (result[i].length > 1) {
                    mergeResult.push(result[i]);
                } else if (result[i].length == 1) {
                    single.push(result[i][0]);
                }
            }
            mergeResult.push(single);
            return mergeResult;
        }

        return result;
    }

    getSplittedDuplicate(
        cards: number[]
    ): {
        group1: number[];
        group2: number[];
    } {
        cards = this.convertToSingle(cards);

        let counter: number[] = [];
        for (let i = 1; i < this.tableNumberCard; ++i) counter[i] = 0;
        for (let i = 0; i < cards.length; ++i) {
            counter[cards[i]]++;
        }
        let arr1: number[] = [],
            arr2: number[] = [];
        for (let i = 0; i < this.tableNumberCard; ++i) {
            if (counter[i] > 0) {
                arr1.push(i);
                counter[i]--;
            }
        }

        for (let i = 0; i < this.tableNumberCard; ++i) {
            if (counter[i] > 0) {
                arr2.push(i);
                counter[i]--;
            }
        }

        return {
            group1: arr1,
            group2: arr2,
        };
    }

    findImpureSequence(cards: number[], isAllCase?: boolean) {
        //console.json("origin: ", cards);
        let tryFindPure = this.findPureSequence(cards);
        if (tryFindPure.list_pures.length > 0) {
            // console.warn("findImpureSequence: have pure sequence in array, ignored it");
            // console.json("pures: ", tryFindPure.list_pures);
        }
        let cardsToFind = [...cards];
        // just find impure sequence
        if (!isAllCase) {
            cardsToFind = tryFindPure.list_remains;
        }
        // console.json("remain: ", cardsToFind);

        let listWildCards: number[] = [];
        let listNormalCards: number[] = [];
        for (let i = 0; i < cardsToFind.length; ++i) {
            if (this.isWildCard(cardsToFind[i])) {
                listWildCards.push(cardsToFind[i]);
            } else {
                listNormalCards.push(cardsToFind[i]);
            }
        }
        //console.json("wild card: ", listWildCards);
        let res = {
            list_impures: [],
            list_remains: []
        }

        if (listWildCards.length <= 0) {
            //console.log("=============没有检测到万能牌=============")
            // console.error("no wild card, cant create impure sequence");
            res.list_remains = cards;
            return res;
        }

        let cardsBySuit = this.sortBySuit(listNormalCards);
        // console.json("suited: ", cardsBySuit);
        let listExpect: number[][] = [];
        let listRemains: number[] = [];
        let checkImpure2: number[][] = [];
        let checkImpure3: number[][] = [];
        // TODO: fix this function if want to find longest length 1st
        for (let i = 0; i < cardsBySuit.length; ++i) {
            if (cardsBySuit[i].length > 0) {
                if (this.getSuit(cardsBySuit[i][0]) != 0) {
                    let splittedDuplicate = this.getSplittedDuplicate(cardsBySuit[i]);

                    //console.log("splittedDuplicate" + JSON.stringify(splittedDuplicate));

                    let checkImpure = this.splitSameSuitToGroup(splittedDuplicate.group1, isAllCase);
                    //console.log("checkImpure" + JSON.stringify(checkImpure));

                    if (checkImpure.list_expects.length > 0) listExpect = listExpect.concat(checkImpure.list_expects);
                    if (checkImpure.list_remains.length > 0) listRemains = listRemains.concat(checkImpure.list_remains);

                    if (splittedDuplicate.group2.length > 0) {
                        checkImpure = this.splitSameSuitToGroup(splittedDuplicate.group2, isAllCase);
                        if (checkImpure.list_expects.length > 0) listExpect = listExpect.concat(checkImpure.list_expects);
                        if (checkImpure.list_remains.length > 0) listRemains = listRemains.concat(checkImpure.list_remains);
                    }

                    if (isAllCase) {
                        if (listWildCards.length >= 2) {
                            let groups: number[][] = [
                                [-12, -8, -4, 8, 12],
                                [-12, -8, 4, 8, 12],
                                [-12, -8, 0, 8, 12],
                                [-12, -4, 4, 8, 12],
                                [-12, -8, -4, 4, 12],
                                [-12, 0, 4, 8, 12],
                                [-12, -8, -4, 0, 12],
                                [-8, -4, 8, 12],
                                [-12, -8, -4, 8],
                                [-8, 4, 8, 12],
                                [-12, -8, 4, 8],
                                [-12, -8, 0, 8],
                                [-8, 0, 8, 12],
                                [-12, -4, 4, 8],
                                [-4, 4, 8, 12],
                                [-12, -8, -4, 4],
                                [-8, -4, 4, 12],
                                [-12, 0, 4, 8],
                                [-8, -4, 0, 12],
                                [-8, -4, 8],
                                [-4, 8, 12],
                                [-8, -4, 8],
                                [-8, 4, 8],
                                [-12, -8, 4],
                                [-8, 0, 8],
                                [-12, -4, 4],
                                [-4, 4, 12],
                                [-8, 4],
                            ];
                            checkImpure2 = this.splitSameSuitToGroup(splittedDuplicate.group1, isAllCase, groups).list_expects;
                            if (splittedDuplicate.group2.length > 0) {
                                checkImpure2.concat(this.splitSameSuitToGroup(splittedDuplicate.group2, isAllCase, groups).list_expects);
                            }
                            //console.log("checkImpure 2:" + JSON.stringify(checkImpure2));

                            if (checkImpure2.length > 0) {
                                for (let j = 0; j < checkImpure2.length; j++) {
                                    checkImpure2[j] = this.sortGroupImpure2(checkImpure2[j], listWildCards.slice(0, 2));
                                    res.list_impures.push(checkImpure2[j]);
                                }
                            }
                            for (let k = 0; k < cardsBySuit[i].length; k++) {
                                let cards = [cardsBySuit[i][k]];
                                cards = cards.concat(listWildCards.slice(0, 2));
                                res.list_impures.push(cards);
                            }
                        }

                        if (listWildCards.length >= 3) {
                            let groups: number[][] = [
                                [-12, 0, 8, 12],
                                [-12, 0, 4, 12],
                                [-12, 0, -4, 12],
                                [-12, -4, 4, 12],
                                [-12, -4, 8, 12],
                                [-12, -8, 0, 12],
                                [-12, -8, -4, 12],
                                [-12, -8, 4, 12],
                                [-12, -8, 8, 12],
                            ];
                            checkImpure3 = this.splitSameSuitToGroup(splittedDuplicate.group1, isAllCase, groups).list_expects;
                            if (splittedDuplicate.group2.length > 0) {
                                checkImpure3.concat(this.splitSameSuitToGroup(splittedDuplicate.group2, isAllCase, groups).list_expects);
                            }
                            //console.log("checkImpure 2:" + JSON.stringify(checkImpure2));

                            if (checkImpure3.length > 0) {
                                for (let k = 0; k < checkImpure3.length; ++k) {
                                    checkImpure3[k] = this.sortGroupImpure2(checkImpure3[k], listWildCards.slice(0, 3));
                                    res.list_impures.push(checkImpure3[k]);
                                }
                            }
                        }
                    }
                }
            }
        }


        listExpect = listExpect.sort((left, right) => {
            var a = left.length;
            var b = right.length;
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })

        //listExpect = _.sortBy(listExpect, (c) => c.length);
        //listRemains = _.sortBy(listRemains, (c) => Card.getScore(c));
        listRemains = listRemains.sort((left, right) => {
            var a = this.getScore(left);
            var b = this.getScore(right);
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })

        // console.log("=========111==sortBy13===========", JSON.parse(JSON.stringify(listExpect)))
        //console.log("=========2222==sortBy14===========", JSON.parse(JSON.stringify(listRemains)))
        // TODO: fix if dont want to group wild card together

        // merge expects with wild cards
        let numMerge = Math.min(listExpect.length, listWildCards.length);
        for (let i = 0; i < numMerge; ++i) {
            listExpect[i] = this.sortGroupImpure(listExpect[i], listWildCards[i]);
            res.list_impures.push(listExpect[i]);
        }

        if (isAllCase && listWildCards.length > 0) {
            for (let i = numMerge; i < listExpect.length; ++i) {
                listExpect[i] = this.sortGroupImpure(listExpect[i], listWildCards[0]);
                res.list_impures.push(listExpect[i]);
            }
        }

        listExpect.splice(0, numMerge);
        listWildCards.splice(0, numMerge);

        let len = listWildCards.length;
        let lenRemain = listRemains.length;

        // TODO: fix if dont want to group wild card together

        //sort list remain
        while (len >= 2 && lenRemain > 0) {
            // group 2 wild card & 1 card to make impure sequence
            listRemains = listRemains.sort((a, b) => b - a);
            //case special  J JK JK K
            if (listRemains.length > 0 && Math.abs(listRemains[listRemains.length - 1] - listRemains[listRemains.length - 2]) == 8) {
                res.list_impures.push(listRemains.shift(), [listWildCards.shift(), listWildCards.shift(), listRemains.shift()]);
            } else {
                res.list_impures.push([listWildCards.shift(), listWildCards.shift(), listRemains.shift()]);
            }

            len -= 2;
            lenRemain--;
        }

        while (listWildCards.length >= 3) {
            // group 3 wild card to make impure
            res.list_impures.push([listWildCards.shift(), listWildCards.shift(), listWildCards.shift()]);
        }

        // no more wild card, convert list expect back to normal
        if (len == 0) {
            for (let i = 0; i < listExpect.length; ++i) {
                for (let j = 0; j < listExpect[i].length; ++j) {
                    res.list_remains.push(listExpect[i][j]);
                }
            }
        }

        res.list_remains = res.list_remains.concat(listWildCards);
        res.list_remains = res.list_remains.concat(listRemains);

        //remove douplicate in new sort
        if (isAllCase) {
            res.list_impures = this.uniqArr(res.list_impures, JSON.stringify);
        }
        // console.json("result find impure: ", res);
        return res;
    }

    lengthNestedArray(arr: any[]): number {
        let length = 0;
        for (let i = 0; i < arr.length; i++) {
            if (arr[i] != null && Array.isArray(arr)) {
                arr[i] = arr[i].filter((el: any) => el != null);
                length += arr[i].length;
            }
        }
        return length;
    }

    removeArrFromArr(arr1: number[], arr2: number[]) {
        let newArr = [...arr1];
        for (let i = 0; i < arr2.length; i++) {
            if (newArr.indexOf(arr2[i]) != -1) {
                newArr.splice(newArr.indexOf(arr2[i]), 1);
            }
        }
        return newArr;
    }

    getAllCaseSequence(arr: number[][]) {
        let result: number[][] = [];
        if (arr.length == 0) {
            return;
        }
        //console.log("Get all case" +  JSON.stringify(arr))
        for (let i = 0; i < arr.length; i++) {
            if (arr[i].length > 0) {
                for (let j = 0; j < arr[i].length - 2; j++) {
                    for (let k = j + 2; k < arr[i].length; k++) {
                        result.push(arr[i].slice(j, k + 1));
                    }
                }
            }
        }

        return result;
    }

    calScoreByListId(arr: number[]): number {
        let score = 0;
        for (let i = 0; i < arr.length; i++) {
            if (!this.isWildCard(arr[i])) {
                score += this.getScore(this.getType(arr[i]));
            }
        }
        return score
    }

    //大小王是万能牌的时候 A是癞子
    isWildCard(id: number): boolean {
        if (this.getType(id) == this.CardType1._JOKER) return true;
        let wildCard = this.getType(this.wildCard) == this.CardType1._JOKER ? 49 : this.wildCard
        if (wildCard == null) return false;
        let begin = Math.floor((wildCard - 1) / 4) * 4;
        for (let i = 1; i <= 4; ++i) {
            if (id == begin + i) {
                return true;
            }
        }

        return false;
    }

    sortGroupImpure(cards: number[], wildCard: number) {
        cards = this.specialSort(cards);
        for (let i = 0; i < cards.length - 1; i++) {
            let point = 0;
            if (cards[i] > 48 && i == 0) {
                point = Math.abs(cards[i] - cards[i + 1] - 52);
            } else {
                point = Math.abs(cards[i] - cards[i + 1]);
            }
            if (point > 4) {
                cards.splice(i + 1, 0, wildCard);
                return cards;
            }
        }

        cards.push(wildCard);
        return cards;
    }

    convertToSingle(cards: any[]): number[] {
        let singleCards: number[] = [];
        for (let i = 0; i < cards.length; ++i) {
            if (cards[i] instanceof Array) {
                for (let j = 0; j < cards[i].length; ++j) {
                    singleCards.push(cards[i][j]);
                }
            } else {
                singleCards.push(cards[i]);
            }
        }
        return singleCards;
    }

    specialSort(arr: number[]) {
        if (arr[0] <= 4 && arr[arr.length - 1] > 48 && this.getType(this.wildCard) != this.CardType1._A) {
            arr.sort((a, b) => {
                if (a > 48) {
                    a = a - 52;
                }
                if (b > 48) {
                    b = b - 52;
                }
                return a - b;
            });
        } else if (arr[arr.length - 1] > 48 && this.getType(this.wildCard) != this.CardType1._A) {
            /*arr.sort((a, b) => {
              if (a > 48) {
                a = a - 52;
              }
              if (b > 48) {
                b = b - 52;
              }
              return a- b;
            });*/
        }
        return arr;
    }

    splitSameSuitToGroup(
        cards: number[],
        isAllCase?: boolean,
        groups2?: number[][]
    ): {
        list_expects: number[][];
        list_remains: number[];
    } {
        let groups: number[][] = [
            [-8, -4, 4, 8],
            [-8, -4, 4],
            [-4, 4, 8],
            [-4, 4],
            [-8, -4],
            [4, 8],
        ];

        if (isAllCase) {
            if (groups2 != undefined) {
                groups = groups2;
                //console.log("groups2 " + JSON.stringify(groups2))
            } else {
                groups = [
                    [-16, -12, -8, -4, 4, 8, 12, 16],
                    [-16, -12, -8, -4, 4, 8, 12],
                    [-12, -8, -4, 4, 8, 12, 16],
                    [-16, -12, -8, -4, 4, 8],
                    [-8, -4, 4, 8, 12, 16],
                    [-12, -8, -4, 4, 8, 12],
                    [-16, -12, -8, -4, 4],
                    [-4, 4, 8, 12, 16],
                    [-8, -4, 4, 8, 12],
                    [-12, -8, -4, 4, 8],
                    [-16, -12, -8, -4],
                    [4, 8, 12, 16],
                    [-4, 4, 8, 12],
                    [-12, -8, -4, 4],
                    [-8, -4, 4, 8],
                    [-12, -8, -4],
                    [-8, -4, 4],
                    [-4, 4, 8],
                    [-4, 4],
                    [-8, -4],
                    [4, 8],
                ];
            }
        }

        let res: {
            list_expects: number[][];
            list_remains: number[];
        } = {
            list_expects: [],
            list_remains: [],
        };

        // check ACEs in cards can make A 2 3 by add a virtual card with Id (0, -1, -2, -3)
        let curArr = cards;
        for (let i = 0; i < curArr.length; ++i) {
            if (this.getType(curArr[i]) == this.CardType1._A) {
                curArr.push(this.getSuit(curArr[i]) - 3);
            }
        }

        //curArr = _.sortBy(curArr, (c) => c);

        curArr = curArr.sort((left, right) => {
            var a = left;
            var b = right;
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })
        // console.log("===========sortBy1===========", curArr)

        let curArr2 = [...curArr];
        let length = curArr2.length;
        for (let n = 0; n < length && (isAllCase || n == 0); n++) {
            curArr = [...curArr2];
            for (let i = 0; i < groups.length; ++i) {
                let checkRemove: boolean[] = [];
                for (let j = 0; j < curArr.length; ++j) {
                    checkRemove[j] = false;
                }
                for (let j = 0; j <= curArr.length - groups[i].length;) {
                    let mustSame = curArr[j] - groups[i][0];
                    let found = true;
                    for (let k = 0; k < groups[i].length; ++k) {
                        // ACE cant be add to another group
                        if (curArr[j + k] - groups[i][k] != mustSame || checkRemove[j + k]) {
                            found = false;
                            break;
                        }
                    }
                    if (found) {
                        let group: number[] = [];
                        for (let k = 0; k < groups[i].length; ++k) {
                            // ACEs fake
                            if (curArr[j + k] <= 0) {
                                group.push(curArr[j + k] + 52);
                            } else {
                                group.push(curArr[j + k]);
                            }

                            checkRemove[j + k] = true;

                            // check if it's ACEs
                            if (curArr[j + k] <= 0) {
                                for (let l = 0; l < curArr.length; ++l) {
                                    if (curArr[l] == curArr[j + k] + 52) {
                                        checkRemove[l] = true;
                                    }
                                }
                            } else if (this.getType(curArr[j + k]) == this.CardType1._A) {
                                for (let l = 0; l < curArr.length; ++l) {
                                    if (curArr[l] == curArr[j + k] - 52) {
                                        checkRemove[l] = true;
                                    }
                                }
                            }
                        }
                        res.list_expects.push(group);
                        j += groups[i].length;
                    } else {
                        j++;
                    }
                }

                let newArray: number[] = [];
                for (let j = 0; j < curArr.length; ++j) {
                    if (checkRemove[j] == false) newArray.push(curArr[j]);
                }
                curArr = newArray;
            }
            curArr2.shift();
        }

        //res.list_remains = _.filter(curArr, (c) => c > 0);

        return res;
    }

    sortGroupImpure2(cards: number[], listWildCards: number[]) {
        let cards2 = this.specialSort(cards);
        let indexs: number[] = [];
        for (let i = 0; i < cards2.length - 1; i++) {
            let point = 0;
            if (cards2[i] > 48 && i == 0) {
                point = Math.abs(cards2[i] - cards2[i + 1] - 52);
            } else {
                point = Math.abs(cards2[i] - cards2[i + 1]);
            }
            if (point > 4) {
                indexs.push(i + indexs.length + 1);
            }
            if (point > 8) {
                indexs.push(i + indexs.length + 1);
            }
            if (point > 12) {
                indexs.push(i + indexs.length + 1);
            }
        }

        for (var i = 0; i < indexs.length; i++) {
            if (listWildCards[i] != null) {
                cards2.splice(indexs[i], 0, listWildCards[i]);
            }
        }
        console.log("============开始检测=删除===", cards, listWildCards)
        listWildCards = listWildCards.slice(i);
        for (let i = 0; i < listWildCards.length; i++) {
            cards2.push(listWildCards[i]);
        }

        return cards2;
    }

    uniqArr(arr: any[], key: (item: any) => any) {
        let index: any[] = [];
        return arr.filter((item) => (index.indexOf(key(item)) >= 0 ? false : index.push(key(item))));
    }

    uniq(oSingleCards) {   //去重
        //console.log("==========去重去重============", oSingleCards)
        return oSingleCards
  
      }

    findPureSequenceInSameSuit(
        oSingleCards: number[]
    ): {
        pures: number[][];
        remains: number[];
    } {
        // console.json("check original: ", oSingleCards);
        let numAces = 0;
        for (let i = 0; i < oSingleCards.length; ++i) {
            if (oSingleCards[i] >= 49 && oSingleCards[i] <= 52) {
                numAces++;
            }
        }

        // remove same cards
        let singleCards = this.uniq(oSingleCards);
        // check length
        if (singleCards.length < 3)
            return {
                pures: [],
                remains: oSingleCards,
            };
        // check same suit
        let suit = this.getSuit(singleCards[0]);
        for (let i = 1; i < singleCards.length; ++i) {
            if (this.getSuit(singleCards[i]) != suit) {
                // console.error("findPureSequenceInSameSuit: not same suit");
                return {
                    pures: [],
                    remains: oSingleCards,
                };
            }
        }

        //singleCards = _.sortBy(singleCards, (c) => c);


        singleCards = singleCards.sort((left, right) => {
            var a = left;
            var b = right;
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })
        // console.log("===========sortBy2===========", singleCards)
        let curCard = singleCards[0];
        let offset = 4;
        let listPures: number[][] = [];
        let listNone: number[][] = [];
        let j: number;

        for (let i = 1; i < singleCards.length; ++i) {
            if (singleCards[i] - curCard == offset) {
                // ok, continue find
                offset += 4;

                // no more cards
                if (i == singleCards.length - 1) {
                    let cards: number[] = [];
                    for (j = curCard; j < curCard + offset; j += 4) {
                        cards.push(j);
                    }
                    listPures.push(cards);
                }
            } else {
                let pure: number[] = [];
                for (j = curCard; j < curCard + offset; j += 4) {
                    pure.push(j);
                }
                listPures.push(pure);
                curCard = singleCards[i];
                offset = 4;

                // no more cards
                if (i == singleCards.length - 1) {
                    listPures.push([curCard]);
                }
            }
        }

        // check special group xxxQK, 23xxx
        for (let i = 0; i < listPures.length; ++i) {
            let len = listPures[i].length;
            if (len >= 2) {
                if (this.getType(listPures[i][1]) == this.CardType1._3 && this.getType(listPures[i][0]) == this.CardType1._2) {
                    // find ACEs
                    let found = -1;
                    for (let j = 0; j < listPures.length; ++j) {
                        for (let k = 0; k < listPures[j].length; ++k) {
                            if (listPures[j][k] >= 49 && listPures[j][k] <= 52) {
                                // not in any pure sequence
                                if (listPures[j].length < 3 || numAces == 2) {
                                    found = listPures[j][k];
                                }
                                // remove ACEs in list
                                if (listPures[j].length < 3) {
                                    let newArr: number[] = [];
                                    for (let l = 0; l < listPures[j].length; ++l) {
                                        if (listPures[j][l] != found) newArr.push(listPures[j][l]);
                                    }
                                    listPures[j] = newArr;
                                }
                                break;
                            }
                        }
                        if (found != -1) break;
                    }
                    if (found != -1) {
                        listPures[i].push(found);
                        break;
                    }
                }
            }
        }

        let res: {
            pures: number[][];
            remains: number[];
        } = {
            pures: [],
            remains: [],
        };
        // clean pure and remain
        for (let i = 0; i < listPures.length; ++i) {
            if (listPures[i].length >= 3) {
                res.pures.push(listPures[i]);
            }
        }
        let counter: number[] = [];
        for (let i = 1; i <= 54; ++i) counter[i] = 0;
        for (let i = 0; i < oSingleCards.length; ++i) {
            counter[oSingleCards[i]]++;
        }
        for (let i = 0; i < res.pures.length; ++i) {
            for (let j = 0; j < res.pures[i].length; ++j) {
                counter[res.pures[i][j]]--;
            }
        }
        for (let i = 1; i <= 54; ++i) {
            for (let j = 0; j < counter[i]; ++j) {
                res.remains.push(i);
            }
        }

        return res;
    }

    findSet(cards: number[], isAllCase?: boolean) {
        cards = this.convertToSingle(cards);

        // just find impure sequence
        let listWildCards: number[] = [];
        let listNormalCards: number[] = [];
        for (let i = 0; i < cards.length; ++i) {
            if (this.isWildCard(cards[i])) {
                listWildCards.push(cards[i]);
            } else {
                listNormalCards.push(cards[i]);
            }
        }
        //let sortedCards = _.sortBy(listNormalCards, (c) => c);

        let sortedCards = listNormalCards.sort((left, right) => {
            var a = left;
            var b = right;
            if (a !== b) {
                if (a > b || a === void 0) return 1;
                if (a < b || b === void 0) return -1;
            }
            return 1
        })
        // console.log("===========sortBy3===========", sortedCards)

        let splittedDuplicate = this.getSplittedDuplicate(sortedCards);

        let res: {
            list_sets: number[][];
            list_remains: number[];
        } = {
            list_sets: [],
            list_remains: [],
        };

        let group1 = this.groupCardsByType(splittedDuplicate.group1);
        let group2 = this.groupCardsByType(splittedDuplicate.group2);
        let groupByType = group1.concat(group2);

        let numWildCard = listWildCards.length;
        let firstWildCard;
        if (numWildCard > 0) {
            firstWildCard = listWildCards[0];
        }
        for (let i = 0; i < groupByType.length; ++i) {
            if (groupByType[i].length >= 3) {
                res.list_sets.push(groupByType[i]);
                if (isAllCase && firstWildCard) {
                    groupByType[i].push(firstWildCard);
                    res.list_sets.push(groupByType[i]);
                }
            } else if (groupByType[i].length == 2) {
                if (listWildCards.length > 0) {
                    groupByType[i].push(listWildCards.shift()!);
                    res.list_sets.push(groupByType[i]);
                } else {
                    if (isAllCase && firstWildCard) {
                        groupByType[i].push(firstWildCard);
                        res.list_sets.push(groupByType[i]);
                    }
                }
            }
        }

        // get remaining cards
        for (let i = 0; i < groupByType.length; ++i) {
            if (groupByType[i].length < 3) {
                for (let j = 0; j < groupByType[i].length; ++j) {
                    res.list_remains.push(groupByType[i][j]);
                }
            }
        }

        res.list_remains = res.list_remains.concat(listWildCards);

        return res;
    }

    groupCardsByType(cards: number[]): number[][] {
        if (cards.length <= 0) return [];

        let groupByType: number[][] = [[cards[0]]];
        let lastCard = cards[0];
        let curIndex = 0;

        for (let i = 1; i < cards.length; ++i) {
            if (this.getType(cards[i]) == this.getType(lastCard)) {
                groupByType[curIndex].push(cards[i]);
            } else {
                curIndex++;
                lastCard = cards[i];
                groupByType[curIndex] = [lastCard];
            }
        }

        return groupByType;
    }




    //查找真顺子
    findPureSequence(cards: number[]) {
        let cards_sorted = this.sortBySuit(cards);

        //console.log("=============findPureSequence=============", JSON.parse(JSON.stringify(cards)), JSON.parse(JSON.stringify(cards_sorted)))

        let res: {
            list_pures: number[][];
            list_remains: number[];
        } = {
            list_pures: [],
            list_remains: [],
        };

        for (let i = 0; i < cards_sorted.length; ++i) {
            if (cards_sorted[i].length > 0) {
                if (this.getSuit(cards_sorted[i][0]) != 0) {
                    let splittedDuplicate = this.getSplittedDuplicate(cards_sorted[i]);

                    let checkPure = this.findPureSequenceInSameSuit(splittedDuplicate.group1);
                    if (checkPure.pures.length > 0) res.list_pures = res.list_pures.concat(checkPure.pures);
                    if (checkPure.remains.length > 0) res.list_remains = res.list_remains.concat(checkPure.remains);

                    if (splittedDuplicate.group2.length > 0) {
                        let checkPure2 = this.findPureSequenceInSameSuit(splittedDuplicate.group2);
                        if (checkPure2.pures.length > 0) res.list_pures = res.list_pures.concat(checkPure2.pures);
                        if (checkPure2.remains.length > 0) res.list_remains = res.list_remains.concat(checkPure2.remains);
                    }
                } else {
                    res.list_remains = res.list_remains.concat(cards_sorted[i]);
                }
            }
        }
        //sort case A 2 3 for new sort
        for (let i = 0; i < res.list_pures.length; i++) {
            res.list_pures[i] = this.specialSort(res.list_pures[i]);
        }
        return res;
    }

    //重新排序
    new_sort(arr: number[]) {
        //转换癞子成别人牌值
        this.wildCard = this.convertOtherNum(this.jokerData); 
        let remain = arr;
        // Sort cart decrease
        console.log("List sort before sort" + JSON.stringify(arr) + JSON.stringify(this.wildCard));
        arr = arr.sort((a, b) => a - b);
        console.log("List sort init after sort: " + JSON.stringify(arr));


        let list_pures = this.findPureSequence(arr)
        console.log("List sort findPureSequence: " + JSON.stringify(list_pures));
        let listPure = this.getAllCaseSequence(list_pures.list_pures);

        console.log("===========new_sort=======找到真顺子======", listPure)
        let finalResult: number[][] = [];
        let score = 140;
        let canDeclare = false;
        //console.log("List pure: " + JSON.stringify(listPure));
        if (listPure != undefined && listPure.length > 0) {
            for (let i = 0; listPure != undefined && i < listPure.length && !canDeclare; i++) {
                let pure = listPure[i];
                let remains = this.removeArrFromArr(arr, pure);
                let result = [pure];
                //console.log("impure" + JSON.stringify(this.findImpureSequence(remains, true).list_impures));
                //console.log("pure" + JSON.stringify(this.findPureSequence(remains).list_pures));
                let impures = this.getAllCaseSequence(
                    this.findImpureSequence(remains, true).list_impures.concat(this.findPureSequence(remains).list_pures)
                );
                //console.log("List impure:     " + JSON.stringify(impures));
                //console.log("List remain:     " + JSON.stringify(remains));
                //console.log("Result:          " + JSON.stringify(result));
                if (impures == undefined || impures.length == 0) {
                    //cal diem
                    let newScore = this.calScoreByListId(remains);
                    console.log("=======遍历真顺子========", pure, newScore, JSON.parse(JSON.stringify(remains)))
                    if (newScore < score) {
                        score = newScore;
                        remain = remains;
                        finalResult = [...result];
                        // console.log(score, remain, finalResult);
                    }
                    if (this.lengthNestedArray(result) >= 13) {
                        finalResult = [...result];
                        canDeclare = true;
                        remain = remains;
                        break;
                    }
                    continue;
                }
                for (let k = 0; k < impures.length && !canDeclare; k++) {
                    let impure = impures[k];
                    result = [pure, impure];
                    let remains2 = this.removeArrFromArr(remains, impure);
                    if (this.lengthNestedArray(result) >= 13) {
                        finalResult = [...result];
                        canDeclare = true;
                        remain = remains2;
                        break;
                    }
                    let newImpures = this.getAllCaseSequence(
                        this.findImpureSequence(remains2, true).list_impures.concat(
                            this.findPureSequence(remains2).list_pures
                        )
                    );
                    let newSets = this.getAllCaseSequence(this.findSet(remains2, true).list_sets);
                    //console.log("List impure 1:     " + JSON.stringify(newImpures));
                    //console.log("List set 1:        " + JSON.stringify(newSets));
                    //console.log("List remain 1:     " + JSON.stringify(remains2));
                    //console.log("Result 1:          " + JSON.stringify(result));
                    if ((newImpures == undefined || newImpures.length == 0) && (newSets == undefined || newSets.length == 0)) {
                        let newScore = this.calScoreByListId(remains2);
                        if (newScore < score) {
                            score = newScore;
                            remain = remains2;
                            finalResult = [...result];
                            console.log(score, remain, finalResult);
                        }
                        if (this.lengthNestedArray(result) >= 13) {
                            finalResult = [...result];
                            canDeclare = true;
                            remain = remains2;
                            break;
                        }
                        continue;
                    }
                    for (let n = 0; newImpures != undefined && n < newImpures.length && !canDeclare; n++) {
                        result = [pure, impure, newImpures[n]];
                        let remains3 = this.removeArrFromArr(remains2, newImpures[n]);
                        if (this.lengthNestedArray(result) >= 13) {
                            finalResult = [...result];
                            canDeclare = true;
                            console.log(score, remains3, finalResult);
                            break;
                        }
                        let newPure2s = this.findPureSequence(remains3).list_pures;
                        let newImpure2s = this.findImpureSequence(remains3).list_impures.concat(
                            newPure2s.filter((item) => JSON.stringify(item) != JSON.stringify(pure))
                        );
                        let newSet2s = this.findSet(remains3, true).list_sets;
                        //console.log("List impure 2:     " + JSON.stringify(newImpure2s));
                        //console.log("List set 2:        " + JSON.stringify(newSet2s));
                        //console.log("List remain 2:     " + JSON.stringify(remains3));
                        //console.log("Result 2:          " + JSON.stringify(result));
                        if ((newImpure2s != null && newImpure2s.length > 0) || (newSet2s != null && newSet2s.length > 0)) {
                            for (let m = 0; newImpure2s != null && m < newImpure2s.length; m++) {
                                result = [pure, impure, newImpures[n], newImpure2s[m]];
                                let remains4 = this.removeArrFromArr(remains3, newImpure2s[m]);
                                let newScore = this.calScoreByListId(remains4);
                                if (newScore <= score) {
                                    score = newScore;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                }
                                if (this.lengthNestedArray(result) >= 13) {
                                    finalResult = [...result];
                                    remain = remains4;
                                    canDeclare = true;
                                    console.log(score, remain, finalResult);
                                    break;
                                }
                            }
                            for (let m = 0; newSet2s != null && m < newSet2s.length; m++) {
                                result = [pure, impure, newImpures[n], newSet2s[m]];
                                let remains4 = this.removeArrFromArr(remains3, newSet2s[m]);
                                let newScore = this.calScoreByListId(remains4);
                                if (newScore <= score) {
                                    score = newScore;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                }
                                if (this.lengthNestedArray(result) >= 13) {
                                    canDeclare = true;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                    break;
                                }
                            }
                        } else {
                            let newScore = this.calScoreByListId(remains3);
                            if (newScore < score) {
                                score = newScore;
                                remain = remains3;
                                finalResult = [...result];
                                console.log(score, remain, finalResult);
                            }
                        }
                    }
                    for (let n = 0; newSets != undefined && n < newSets.length && !canDeclare; n++) {
                        result = [pure, impure, newSets[n]];
                        let remains3 = this.removeArrFromArr(remains2, newSets[n]);
                        if (this.lengthNestedArray(result) >= 13) {
                            finalResult = [...result];
                            canDeclare = true;
                            remain = remains3;
                            console.log(score, remains, finalResult);
                            break;
                        }
                        let newPure2s = this.findPureSequence(remains3).list_pures;
                        let newImpure2s = this.findImpureSequence(remains3).list_impures.concat(
                            newPure2s.filter((item) => JSON.stringify(item) != JSON.stringify(pure))
                        );
                        let newSet2s = this.findSet(remains3, true).list_sets;
                        //console.log("List impure 3:     " + JSON.stringify(newImpure2s));
                        //console.log("List set 3:        " + JSON.stringify(newSet2s));
                        //console.log("List remain 3:     " + JSON.stringify(remains3));
                        //console.log("Result 3:          " + JSON.stringify(result));
                        if ((newImpure2s != null && newImpure2s.length != 0) || (newSet2s != null && newSet2s.length != 0)) {
                            for (let m = 0; newImpure2s != undefined && m < newImpure2s.length; m++) {
                                result = [pure, impure, newSets[n], newImpure2s[m]];
                                let remains4 = this.removeArrFromArr(remains3, newImpure2s[m]);
                                let newScore = this.calScoreByListId(remains4);
                                if (newScore <= score) {
                                    score = newScore;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                }
                                if (this.lengthNestedArray(result) >= 13) {
                                    finalResult = [...result];
                                    remain = remains4;
                                    canDeclare = true;
                                    console.log(score, remain, finalResult);
                                    break;
                                }
                            }
                            for (let m = 0; newSet2s != null && m < newSet2s.length; m++) {
                                result = [pure, impure, newSets[n], newSet2s[m]];
                                let remains4 = this.removeArrFromArr(remains3, newSet2s[m]);
                                let newScore = this.calScoreByListId(remains4);
                                if (newScore <= score) {
                                    score = newScore;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                }
                                if (this.lengthNestedArray(finalResult) >= 13) {
                                    finalResult = [...result];
                                    remain = remains4;
                                    canDeclare = true;
                                    console.log(score, remain, finalResult);
                                    break;
                                }
                            }
                        } else {
                            let newScore = this.calScoreByListId(remains3);
                            if (newScore < score) {
                                score = newScore;
                                remain = remains3;
                                finalResult = [...result];
                                console.log(score, remain, finalResult);
                            }
                        }
                    }
                }
            }
            remain = remain.filter((el) => el != null);
            let remains = [];
            if (listPure.length > 0 && finalResult.length >= 2) {
                for (let i = 0; i < remain.length; i++) {
                    if (this.isWildCard(remain[i])) {
                        finalResult[1].push(remain[i]);
                        remains.push(remain[i]);
                    }
                }
            }
            remain = this.removeArrFromArr(remain, remains);

            if (this.lengthNestedArray(finalResult) >= 13) {
                canDeclare = true;
            }
        }
        else {
            //寻找假顺子
            let remains = arr;
            let result = [];

            let impures = this.getAllCaseSequence(
                this.findImpureSequence(arr, true).list_impures
            );

            let isContinum = true
            if (impures == undefined || impures.length == 0) {
                isContinum = false
            }

            let impureLenght = 3
            if (isContinum) {
                console.log("================寻找假顺子============", JSON.parse(JSON.stringify(impures)))
                for (let k = 0; k < impures.length && !canDeclare; k++) {
                    let impure = impures[k];
                    result = [impure];
                    let remains2 = this.removeArrFromArr(remains, impure);
                    if (this.lengthNestedArray(result) >= 13) {
                        finalResult = [...result];
                        canDeclare = true;
                        remain = remains2;
                        break;
                    }
                    let newImpures = this.getAllCaseSequence(
                        this.findImpureSequence(remains2, true).list_impures.concat(
                            this.findPureSequence(remains2).list_pures
                        )
                    );
                    let newSets = this.getAllCaseSequence(this.findSet(remains2, true).list_sets);
                    //console.log("List impure 1:     " + JSON.stringify(newImpures));
                    //console.log("List set 1:        " + JSON.stringify(newSets));
                    //console.log("List remain 1:     " + JSON.stringify(remains2));
                    //console.log("Result 1:          " + JSON.stringify(result));
                    if ((newImpures == undefined || newImpures.length == 0) && (newSets == undefined || newSets.length == 0)) {
                        let newScore = this.calScoreByListId(remains2);
                        let curLenght = impure.length
                        console.log("================假顺子排序============", JSON.parse(JSON.stringify(impure)), newScore)
                        if (curLenght >= impureLenght) {
                            impureLenght = curLenght
                            if (newScore < score) {
                                score = newScore;
                                remain = remains2;
                                finalResult = [...result];
                                console.log(score, remain, finalResult);
                            }
                        }
                        if (this.lengthNestedArray(result) >= 13) {
                            finalResult = [...result];
                            canDeclare = true;
                            remain = remains2;
                            break;
                        }
                        continue;
                    }
                    for (let n = 0; newImpures != undefined && n < newImpures.length && !canDeclare; n++) {
                        result = [impure, newImpures[n]];
                        let remains3 = this.removeArrFromArr(remains2, newImpures[n]);
                        if (this.lengthNestedArray(result) >= 13) {
                            finalResult = [...result];
                            canDeclare = true;
                            console.log(score, remains3, finalResult);
                            break;
                        }
                        let newPure2s = this.findPureSequence(remains3).list_pures;
                        let newImpure2s = this.findImpureSequence(remains3).list_impures.concat(
                            newPure2s.filter((item) => JSON.stringify(item) != JSON.stringify([]))
                        );
                        let newSet2s = this.findSet(remains3, true).list_sets;
                        //console.log("List impure 2:     " + JSON.stringify(newImpure2s));
                        //console.log("List set 2:        " + JSON.stringify(newSet2s));
                        //console.log("List remain 2:     " + JSON.stringify(remains3));
                        //console.log("Result 2:          " + JSON.stringify(result));
                        if ((newImpure2s != null && newImpure2s.length > 0) || (newSet2s != null && newSet2s.length > 0)) {
                            for (let m = 0; newImpure2s != null && m < newImpure2s.length; m++) {
                                result = [impure, newImpures[n], newImpure2s[m]];
                                let remains4 = this.removeArrFromArr(remains3, newImpure2s[m]);
                                let newScore = this.calScoreByListId(remains4);
                                if (newScore <= score) {
                                    score = newScore;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                }
                                if (this.lengthNestedArray(result) >= 13) {
                                    finalResult = [...result];
                                    remain = remains4;
                                    canDeclare = true;
                                    console.log(score, remain, finalResult);
                                    break;
                                }
                            }
                            for (let m = 0; newSet2s != null && m < newSet2s.length; m++) {
                                result = [impure, newImpures[n], newSet2s[m]];
                                let remains4 = this.removeArrFromArr(remains3, newSet2s[m]);
                                let newScore = this.calScoreByListId(remains4);
                                if (newScore <= score) {
                                    score = newScore;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                }
                                if (this.lengthNestedArray(result) >= 13) {
                                    canDeclare = true;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                    break;
                                }
                            }
                        } else {
                            let newScore = this.calScoreByListId(remains3);
                            if (newScore < score) {
                                score = newScore;
                                remain = remains3;
                                finalResult = [...result];
                                console.log(score, remain, finalResult);
                            }
                        }
                    }

                    for (let n = 0; newSets != undefined && n < newSets.length && !canDeclare; n++) {
                        result = [impure, newSets[n]];
                        let remains3 = this.removeArrFromArr(remains2, newSets[n]);
                        if (this.lengthNestedArray(result) >= 13) {
                            finalResult = [...result];
                            canDeclare = true;
                            remain = remains3;
                            console.log(score, remains, finalResult);
                            break;
                        }
                        let newPure2s = this.findPureSequence(remains3).list_pures;
                        let newImpure2s = this.findImpureSequence(remains3).list_impures.concat(
                            newPure2s.filter((item) => JSON.stringify(item) != JSON.stringify([]))
                        );
                        let newSet2s = this.findSet(remains3, true).list_sets;
                        //console.log("List impure 3:     " + JSON.stringify(newImpure2s));
                        //console.log("List set 3:        " + JSON.stringify(newSet2s));
                        //console.log("List remain 3:     " + JSON.stringify(remains3));
                        //console.log("Result 3:          " + JSON.stringify(result));
                        if ((newImpure2s != null && newImpure2s.length != 0) || (newSet2s != null && newSet2s.length != 0)) {
                            for (let m = 0; newImpure2s != undefined && m < newImpure2s.length; m++) {
                                result = [impure, newSets[n], newImpure2s[m]];
                                let remains4 = this.removeArrFromArr(remains3, newImpure2s[m]);
                                let newScore = this.calScoreByListId(remains4);
                                if (newScore <= score) {
                                    score = newScore;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                }
                                if (this.lengthNestedArray(result) >= 13) {
                                    finalResult = [...result];
                                    remain = remains4;
                                    canDeclare = true;
                                    console.log(score, remain, finalResult);
                                    break;
                                }
                            }
                            for (let m = 0; newSet2s != null && m < newSet2s.length; m++) {
                                result = [impure, newSets[n], newSet2s[m]];
                                let remains4 = this.removeArrFromArr(remains3, newSet2s[m]);
                                let newScore = this.calScoreByListId(remains4);
                                if (newScore <= score) {
                                    score = newScore;
                                    remain = remains4;
                                    finalResult = [...result];
                                    console.log(score, remain, finalResult);
                                }
                                if (this.lengthNestedArray(finalResult) >= 13) {
                                    finalResult = [...result];
                                    remain = remains4;
                                    canDeclare = true;
                                    console.log(score, remain, finalResult);
                                    break;
                                }
                            }
                        } else {
                            let newScore = this.calScoreByListId(remains3);
                            if (newScore < score) {
                                score = newScore;
                                remain = remains3;
                                finalResult = [...result];
                                console.log(score, remain, finalResult);
                            }
                        }
                    }
                }
            }
            console.log("=====寻找假顺子=====impuresimpures=========", finalResult)
        }


        let sortSet = this.sortBySet(remain);
        let sortSuit = this.sortBySuit(remain, true);
        return {
            res: finalResult.concat(sortSet),
            sortBySuit: finalResult.concat(sortSuit),
            sortBySet: finalResult.concat(sortSet),
            count: [0, 0, 0],
            canDeclare: canDeclare ? 1 : 0,
        };
    }

    /* ========================================从别人处翻译的破解代码==============================end */





    //弹出效果
    popUpEffect(node: cc.Node, isOpen: boolean = false, callback: Function = null) {

        if (!node) {
            return;
        }
        let maskLayer = node.getChildByName('mask');
        node.stopAllActions();
        if (isOpen) {
            if (!!maskLayer) {
                maskLayer.opacity = 0;
            }
            node.active = true;
            node.scale = 0;
            node.opacity = 0;
            cc.tween(node)
                .parallel(
                    cc.tween().to(0.15, { scale: 0.75 }, { easing: 'backIn' }),
                    cc.tween().to(0.15, { opacity: 30 })
                )
                .parallel(
                    cc.tween().by(0.05, { opacity: 225 }),
                    cc.tween().by(0.05, { scale: 0.25 }, { easing: 'backOut' })
                )
                .call(() => {
                    if (!!maskLayer) {
                        cc.tween(maskLayer)
                            .to(0.2, { opacity: 153 })
                            .start()
                    }
                    callback && callback();
                })
                .start();
        }
        else {
            if (!!maskLayer) {
                cc.tween(maskLayer)
                    .to(0.05, { opacity: 0 })
                    .start()
            }

            cc.tween(node)
                .to(0.2, { scale: 0 }, { easing: 'backIn' })
                .call(() => {
                    callback && callback();
                    node.active = false;
                    // node.removeFromParent();
                })
                .start();
        }
    }

    //弹出效果
    popUpEffect1(node: cc.Node, isOpen: boolean = false, callback: Function = null) {

        if (!node) {
            return;
        }
        let maskLayer = node.getChildByName('mask');
        node.stopAllActions();
        if (isOpen) {
            if (!!maskLayer) {
                maskLayer.opacity = 0;
            }
            node.scale = 0;
            node.opacity = 0;
            node.active = true;
            cc.tween(node)
                .parallel(
                    cc.tween().then(cc.scaleTo(0.7,1).easing(cc.easeElasticOut(1.0))),
                    cc.tween().to(0.15, { opacity: 255 })
                )
                .call(() => {
                    if (!!maskLayer) {
                        cc.tween(maskLayer)
                            .to(0.75, { opacity: 153 })
                            .start()
                    }
                    callback && callback();
                })
                .start();
        }
        else {
            if (!!maskLayer) {
                cc.tween(maskLayer)
                    .to(0.05, { opacity: 0 })
                    .start()
            }

            cc.tween(node)
                .to(0.3, { scale: 0.5 }, { easing: 'backIn' })
                .call(() => {
                    callback && callback();
                    node.active = false;
                    node.opacity = 0;
                })
                .start();
        }
    }


    //排序
    public resortGroup(group: number[], wildcardPoint: number): number[] {
        let wildList: number[] = [];
        let newGroup: Array<{ suit: CardSuit, point: number }> = [];
        let result: number[] = [];

        for (const card of group) {
            const cardPoint = this.convertServerCardToLocalCard(card);
            if (cardPoint.suit === CardSuit.S_Joker || cardPoint.point === wildcardPoint) {
                // 癞子列表
                wildList.push(card);
            } else {
                // 如果不是癞子牌，就提出来
                newGroup.push({ suit: cardPoint.suit, point: cardPoint.point });
            }
        }

        // 排序
        wildList.sort((a, b) => a - b);
        const cgt = this.is_Set_or_Seq(group, wildcardPoint);

        if (cgt === GroupType.SET) {
            // 豹子
            for (const card of newGroup) {
                const resultCard = this.convertLocalCardToServerCard(card.suit, card.point);
                result.push(resultCard);
            }
            // 排序
            result.sort((a, b) => a - b);
            for (const card of wildList) {
                result.push(card);
            }
        } else if (cgt === GroupType.SEQUENCE) {
            // 顺子
            let tmpList: number[] = [];
            for (const card of newGroup) {
                const tmpCard = this.convertLocalCardToServerCard(card.suit, card.point);
                tmpList.push(tmpCard);
            }
            tmpList.sort((a, b) => a - b);

            // 判断第一张牌是不是2，如果是的话，看最后一张牌是不是A，如果是就把A放到最前面来
            let firstCard = this.convertServerCardToLocalCard(tmpList[0]);
            if (tmpList.length >= 2) {
                const lastCard = this.convertServerCardToLocalCard(tmpList[tmpList.length - 1]);
                if (firstCard.point === 2 && lastCard.point === 1) {
                    tmpList.unshift(tmpList.pop()!);
                }
            }

            let idx = 0;
            const len = tmpList.length;
            let theCard = tmpList[idx++];
            let maxCardPoint = 0;
            firstCard = this.convertServerCardToLocalCard(theCard);
            result.push(theCard);

            while (true) {
                maxCardPoint = firstCard.point;
                if (idx >= len) break;

                let tmpCard = tmpList[idx];
                const nextCard = this.convertServerCardToLocalCard(tmpCard);

                if (nextCard.point === firstCard.point + 1 || (nextCard.point === 1 && firstCard.point === 13)) {
                    result.push(tmpCard);
                    idx++;
                } else {
                    tmpCard = wildList.shift()!;
                    result.push(tmpCard);
                }
                firstCard.point++;
            }

            // 如果还有癞子牌
            if (wildList.length > 0) {
                if (maxCardPoint === 14) {  // Assuming K+1 means 14
                    result.unshift(...wildList);
                } else {
                    result.push(...wildList);
                }
            }
        } else {
            // 散牌
            for (let i = 0; i <= CardSuit.S_Joker; i++) {
                let tmpList: number[] = [];
                for (const card of group) {
                    const lCard = this.convertServerCardToLocalCard(card);
                    if (lCard.suit === i) {
                        tmpList.push(card);
                    }
                }
                tmpList.sort((a, b) => a - b);
                result.push(...tmpList);
            }
        }

        return result;
    }

    // 牌型列表
    public is_Set_or_Seq(origin_group: number[], wildcardPoint: number): GroupType {
        let ret: GroupType = GroupType.NUMBER;

        let group = JSON.parse(JSON.stringify(origin_group));
        if (group.length < 3 || group.length > 13) {
            // 不满足条件
            if (group.length == 1){
                // 判断是单张散牌
                const cardPoint = this.convertServerCardToLocalCard(group[0]);
                if (cardPoint.point == wildcardPoint){
                    return GroupType.NUMBER_SINGLE_JOKER;
                } else {
                    return GroupType.NUMBER_SINGLE_NOT_JOKER;
                }
            }

            return ret;
        }

        group.sort((a, b) => a - b);

        let newGroup: Array<{ suit: CardSuit, point: number }> = [];
        let newPointGroup: number[] = [];
        let wildList: number[] = [];
        let sortGroup : Array<{ suit: CardSuit, point: number }> = [];

        for (const card of group) {
            const cardPoint = this.convertServerCardToLocalCard(card);
            // console.log("计算牌型，牌值转换：", cardPoint.point, wildcardPoint);
            if (cardPoint.suit === CardSuit.S_Joker || cardPoint.point === wildcardPoint) {
                wildList.push(card);
            } else {
                newGroup.push({ suit: cardPoint.suit, point: cardPoint.point });
                newPointGroup.push(cardPoint.point);
            }
            sortGroup.push({ suit: cardPoint.suit, point: cardPoint.point });
        }

        if (newGroup.length >= 2) {
            const firstCard = newGroup[0];
            const secondCard = newGroup[1];
            const isSeq = (firstCard.suit === secondCard.suit);
            if (isSeq) {
                let isSameSuit = true;
                let hasSame = false;

                newPointGroup.sort((a, b) => a - b);

                for (let i = 1; i < newPointGroup.length; i++) {
                    if (newPointGroup[i] === newPointGroup[i - 1]) {
                        hasSame = true;
                        break;
                    }
                }

                if (!hasSame) {
                    for (let i = 1; i < newGroup.length; i++) {
                        if (newGroup[i].suit !== newGroup[i - 1].suit) {
                            isSameSuit = false;
                            break;
                        }
                    }
                }

                if (isSameSuit && !hasSame) {
                    let dist = newPointGroup[newPointGroup.length - 1] - newPointGroup[0];
                    if (dist <= wildList.length + newPointGroup.length - 1) {
                        ret = GroupType.SEQUENCE;
                    } else {
                        if (newPointGroup[0] === 1) {
                            newPointGroup.shift();
                            newPointGroup.push(14);
                        }

                        dist = newPointGroup[newPointGroup.length - 1] - newPointGroup[0];
                        if (dist <= wildList.length + newPointGroup.length - 1) {
                            ret = GroupType.SEQUENCE;
                        }
                    }
                }
            } else {
                if (group.length <= 4) {
                    let isSame = true;
                    for (let i = 1; i < newGroup.length; i++) {
                        if (firstCard.point !== newGroup[i].point) {
                            isSame = false;
                            break;
                        }
                    }

                    if (isSame) {
                        ret = GroupType.SET;
                    }
                }
            }
        } else if (newGroup.length === 1) {
            ret = GroupType.SEQUENCE;
        }

        // 如果是顺子，需要判断是真顺子还是假顺子
        if(ret == GroupType.SEQUENCE){
            let last_card = sortGroup[0];
            let isSeq = true;
            for(let i = 1; i < sortGroup.length; i++){
                if(sortGroup[i].suit != last_card.suit) {
                    isSeq = false;
                    break;
                }

                if(last_card.point==13) {
                    if(sortGroup[i].point != 1) {
                        isSeq = false;
                        break;
                    }
                } else if(sortGroup[i].point == 1) {
                    if(sortGroup[0].point!=2) {
                        isSeq = false;
                        break;
                    }
                } else {
                    if(sortGroup[i].point != last_card.point+1) {
                        isSeq = false;
                        break;
                    }
                }

                last_card = sortGroup[i];
            }
            
            if(isSeq){
                ret = GroupType.PURE_SEQUENCE;
            }
        }

        return ret;
    }

    // 获取卡片花色
    getCardSuit(card: number): CardSuit {
        if (card >= 53) {
            return CardSuit.S_Joker;
        }

        const ret = (card - 1) % CARD_SUIT_COUNT;
        if (ret >= CardSuit.S_Spades && ret <= CardSuit.S_Joker) {
            return ret as CardSuit;
        } else {
            return CardSuit.S_Unknown;
        }
    }

    // 获取卡片点数
    getCardPoint(card: number): number {
        if (card >= 53) {
            return card % 53 + 1;
        }

        let point = Math.floor((card - 1) / CARD_SUIT_COUNT);
        if (point === 12) {
            point = 1;
        } else {
            point = point + 2;
        }
        return point;
    }

    // 转换服务器卡片为本地卡片
    convertServerCardToLocalCard(serverCard: number): { suit: CardSuit, point: number } {
        const cs = this.getCardSuit(serverCard);
        const point = this.getCardPoint(serverCard);

        return { suit: cs, point: point };
    }

    convertLocalCardToServerCard(suit: CardSuit, localCard: number): number {
        let serverCard = 0;
        if (suit === CardSuit.S_Joker) {
            serverCard = CARD_COUNT_PER_SUIT * CARD_SUIT_COUNT + localCard;
            return serverCard;
        }

        if (localCard === CardNumDesc.NUM_A) {
            serverCard = CardNumDesc.NUM_A;
        } else {
            serverCard = localCard - 1;
        }

        serverCard = (serverCard - 1) * CARD_SUIT_COUNT + suit + 1;

        return serverCard;
    }

    get_faild_points(group, wildcardPoint) {
        let score: number = 0;
        
        for(const card of group) {
            let cardPoint = this.convertServerCardToLocalCard(card);
            if(cardPoint.point!=wildcardPoint && cardPoint.suit!=CardSuit.S_Joker) {
                if(cardPoint.point==1 || cardPoint.point>=10) {
                    score += 10;
                } else {
                    score += cardPoint.point;
                }
            }
        }

        return score;
    }

    get_total_score(card_groups, wildcardPoint) {
        let seq_count: number = 0, real_seq_count: number = 0;
        let seq_point: number = 0, set_point: number = 0, other_point: number = 0;
        let number_count: number = 0;
        let tmp_groups = JSON.parse(JSON.stringify(card_groups));

        for(let i=0; i<tmp_groups.length; i++) {
            let group_card = tmp_groups[i].list;
            
            let group = [];
            for(let i = 0; i < group_card.length; i++){
                group.push(this.convertOtherNum(group_card[i]));
            }

            let gtype = this.is_Set_or_Seq(group, wildcardPoint);
            if(gtype==GroupType.SEQUENCE) {
                seq_count ++;
                let pt = this.get_faild_points(group, wildcardPoint);
                seq_point += pt;
                
                // console.error(i , " => ", pt)
            } else if(gtype==GroupType.PURE_SEQUENCE) {
                seq_count ++;
                real_seq_count ++;
            } else if(gtype==GroupType.SET) {
                let pt = this.get_faild_points(group, wildcardPoint);
                set_point += pt;

                // console.error(i , " => ", pt)
            } else {
                let pt = this.get_faild_points(group, wildcardPoint);
                other_point += pt;
                number_count += group.length;

                // console.error(i , " => ", pt)
            }
        }

        let ret: number = 0;
        if(seq_count>=2 && real_seq_count>=1) {
            ret = other_point;
        } else {
            ret = seq_point + set_point + other_point;
        }
        
        if(ret>80) {
            ret = 80;
        }

        return {seq_count: seq_count, real_seq_count: real_seq_count, score: ret, number_count: number_count};
    }
}