import GameCore from "../../../../script/frame/model/GameCore";
import { GameEvent, QuitReason, RoomState } from "../../../../script/frame/common/Define";
import { Constant, Protos, GameState, GameTextTips,  EM_RUMMY_PLAYERSTATE, updateGameTextTips, OperateState, CardData } from "./RMDefine";
import Common from "../../../../script/frame/common/Common";
import RMGameView from "../view/RMGameView";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import EventManager from "../../../../script/frame/manager/EventManager";
import { TextTips } from "../../../../script/frame/common/Language";
import RMAudioMng from "./RMAudioMng";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import { HallActivity } from "../../../../script/frame/common/Protocol";
import HallManager from "../../../../script/frame/manager/HallManager";
import Config from "../../../../script/frame/config/Config";
import RMCardPool from "./RMCardPool";


//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('rummy/RMGameCore')
export default class RMGameCore extends GameCore {
    // 玩家ID=>操作对象
    private _mapPlayers = new Map<number, any>();
    // 游戏视图对象
    public gameView: RMGameView = null;
    // 游戏逻辑对象
    public gameLogic: any = null;
    //声音
    public audioMng: RMAudioMng = null;
    // 游戏的低分
    public difen: number = 0;
    // 游戏入场分
    public inmoney: number = 0;
    // 游戏入场分
    public minmoney: number = 0;
    // 房间的状态
    public roomState: number = 0;
    // 游戏的状态
    public gameState: number = -1;
    // 庄家用户ID
    public bankerId: number = 0;
    //庄家坐位号
    public bankerLocalId: number = -1;
    // 自己是否在游戏中
    public meIsGaming: boolean = false;
    /** 当前操作用户视图座位 */
    public optLocalSeatId: number = 0;
    // 是否被踢
    public isKicked: boolean = false;
    /** 操作时间 */
    public optsumtime: number = 0;

    public quitChangeMoney: number = 0;

     /** 玩家本轮操作状态 */
     public PlayerOperate: OperateState = OperateState.waitDrop;
     
    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this.gameView = this.node.getComponent("RMGameView");
        this.gameLogic = this.node.getComponent("RMGameLogic");
        this.audioMng = this.node.getComponent("RMAudioMng");
        console.log("onLoad开始加载游戏----------------")
        this.updateGameLan();
        super.onLoad();
    }

    //////////////////////////////////////////////////////////////////////////////

    // 玩家退出游戏
    public quitGame(info?) {
        UIHelper.clearAll();
        super.quitGame(info);
    }

    //////////////////////////////////////////////////////////////////////////////
    public start() {
        this.bindGameMessage(Protos.SC_RUMMY_START_P, this.onStart, this); //开始
        this.bindGameMessage(Protos.SC_RUMMY_SENDCARD_P, this.onSendCard, this); //发牌
        this.bindGameMessage(Protos.SC_RUMMY_FOLD_P, this.onCardDrop, this); //弃牌
        this.bindGameMessage(Protos.SC_RUMMY_WAITOPT_P, this.onWaitOpt, this) //等待操作
        this.bindGameMessage(Protos.SC_RUMMY_JIESHUAN_P, this.onJieShuan, this); //结算
        this.bindGameMessage(Protos.SC_RUMMY_GETCARD, this.onGetCard, this); //拿牌
        this.bindGameMessage(Protos.SC_RUMMY_ABANDONCARD, this.onAbandonCard, this); //出牌
        this.bindGameMessage(Protos.SC_DECLARE, this.onDeclare, this); //宣布胜利
        this.bindGameMessage(Protos.SC_RUMMY_UPDATE_CARD, this.onUpdateCard, this); //更新牌顺序后返回Score信息
        this.bindGameMessage(Protos.SC_RUMMY_SORD_CARD, this.onGetSortCard, this); //获取组牌数据
        this.bindGameMessage(Protos.SC_RUMMY_FINISH, this.onFinish, this); //完成
        this.bindGameMessage(Protos.SC_RUMMY_ALL_DECLARE, this.onAllDeclare, this); // 通知所有人Declare
        this.bindGameMessage(Protos.SC_RUMMY_REUPDATE, this.onReUpdate, this);

        EventManager.instance.on(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        
        SceneManager.instance.setNoticePos(cc.v2(103, 339),true);
        this.setRecExitTime();

        super.start();
    }


    public exit() {
        this.unbindGameMessage(Protos.SC_RUMMY_START_P, this.onStart, this); //开始
        this.unbindGameMessage(Protos.SC_RUMMY_SENDCARD_P, this.onSendCard, this); //发牌
        this.unbindGameMessage(Protos.SC_RUMMY_FOLD_P, this.onCardDrop, this); //弃牌
        this.unbindGameMessage(Protos.SC_RUMMY_WAITOPT_P, this.onWaitOpt, this); //等待操作
        this.unbindGameMessage(Protos.SC_RUMMY_JIESHUAN_P, this.onJieShuan, this); //结算
        this.unbindGameMessage(Protos.SC_RUMMY_GETCARD, this.onGetCard, this); //拿牌
        this.unbindGameMessage(Protos.SC_RUMMY_ABANDONCARD, this.onAbandonCard, this); //还牌
        this.unbindGameMessage(Protos.SC_DECLARE, this.onDeclare, this); //宣布胜利
        this.unbindGameMessage(Protos.SC_RUMMY_UPDATE_CARD, this.onUpdateCard, this); //更新牌顺序后返回Score信息
        this.unbindGameMessage(Protos.SC_RUMMY_SORD_CARD, this.onGetSortCard, this); //获取组牌数据
        this.unbindGameMessage(Protos.SC_RUMMY_FINISH, this.onFinish, this); //完成
        this.unbindGameMessage(Protos.SC_RUMMY_ALL_DECLARE, this.onAllDeclare, this); // 通知所有人Declare
        this.unbindGameMessage(Protos.SC_RUMMY_REUPDATE, this.onReUpdate, this);

        EventManager.instance.off(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);

        TextTips["GameTextTips"] = {};
        super.exit();
    }
    
    // 玩家加入
    public onPlayerEnter(info: any) {
        console.log("用户进入：", info)
        super.onPlayerEnter(info);

        // 判断玩家是否存在
        let playerid = Common.toInt(info["playerid"]);
        info.playerid = playerid;

        // 判断座位上是否有玩家
        let seat = Common.toInt(info["seat"]);
        let localSeatId = this.getLocalSeatId(seat);
        info.seat = seat;
        
        // 添加玩家到队列
        this._mapPlayers.set(playerid, info);
        // 处理玩家进入视图逻辑
        this.gameView.showPlayer(localSeatId, info);
        // 设置玩家等待入桌
        // if (this.roomInfo.mode != 1) {
        //     this.gameView.setPlayerAwaiting(localSeatId);
        // }

        if (this.roomState != RoomState.GAME) {//只需要处理游戏状态
            this.gameView.isShowWaitStartTips(true);
        }
    }

    // 玩家离开
    public onPlayerQuit(info: any) {
        console.log("用户离开：", info)
        super.onPlayerQuit(info);
        //匹配
        // if (this.roomInfo.mode == 1
        //     && (this.gameState == GameState.EM_TEENPATTI_GAMESTATE_END || this.gameState == -1)) return;

        // 处理玩家离开逻辑
        let reason = Common.toInt(info["reason"]);
        let playerid = Common.toInt(info["playerid"]);
        if (playerid == this.playerid) { // 是否为自己离开
            if (this.isKicked) { // 玩家破产
                console.log('isKicked');
                this.quitGame({reason:1,needRs:this.minmoney});
                return;
            }
            if (reason == QuitReason.HUANZUO) { // 玩家换桌
                return;
            }

            this.quitGame();
        }
        else {
            let plyInfo = this._mapPlayers.get(playerid);
            if (plyInfo) {
                let localSeatId = this.getLocalSeatId(plyInfo.seat);
                this.gameView.hidePlayer(localSeatId);
                this._mapPlayers.delete(playerid);
            }
            if (this.roomState != RoomState.GAME) {//只需要处理游戏状态
                this.gameView.isShowWaitStartTips(true);
            }
            AudioHelper.instance.playEffect(`res/sound/leave_room`)
        }
    }

    // 游戏结束,你条件不满足被踢出房间,如果你在暂离状态,也会被踢出房间
    public onDeletePlayer(info: any) {
        super.onDeletePlayer(info);
        this.meIsGaming = false;
        this.isKicked = true;
    }

    // 玩家状态
    public onPlayerState(info: any) {
        super.onPlayerState(info);
    }

    // 更新玩家金币
    public updatePlayerMoney(info: any) {
        console.log("更新玩家金币：", info)
        super.updatePlayerMoney(info);

        // 更新玩家的金币
        let money = Common.toInt(info["coin"]);
        let playerid = Common.toInt(info["playerid"]);
        if(playerid == this.playerid){
            this.gameView.updatePlayerMoney(Constant.self_local_pos, money);
        }
    }

    public resetData(){
        this.gameState = -1;
        this.bankerId = 0;
        this.bankerLocalId = -1;
        this.meIsGaming = false;
        this.PlayerOperate = OperateState.waitDrop;
        console.log("GameCore 重置数据--------------------------------")
        this._mapPlayers.clear();
    }

    // 进入房间，房间信息
    public onRoomInfo(info: any) {
        console.log("进入房间：", info)
        super.onRoomInfo(info);

        this.maxPlayers = Common.toInt(info["maxplayers"]);
        if(!this.maxPlayers){
            this.maxPlayers = Constant.player_count;
        }
        
        // 重置游戏界面与玩家数据sh
        this.gameView.resetAllUI();

        
        // 基础信息
        this.difen = Common.toInt(info["difen"]);
        this.inmoney = Common.toInt(info["inmoney"]);
        this.minmoney = Common.toInt(info["minmoney"]);
        this.roomState = Common.toInt(info["roomstate"]);

        // 玩家信息
        let playerlist = info["playerlist"];
        if (playerlist && typeof (playerlist) == "object") {
            // 先找出自己的位置
            for (let key in playerlist) {
                let plyInfo = playerlist[key];
                let playerid = Common.toInt(plyInfo["playerid"]);
                if (playerid == this.playerid) {
                    this.mySeatId = Common.toInt(plyInfo["seat"]);
                    this.gameView.initPlayerInfo();
                    break;
                }
            }

            // 显示所有玩家
            for (let key in playerlist) {
                let plyInfo = playerlist[key];
                let isGaming = Common.toInt(plyInfo["isgaming"]);
                let playerid = Common.toInt(plyInfo["playerid"]);
                let seat = Common.toInt(plyInfo["seat"]);

                // 显示玩家信息
                let localSeatId = this.getLocalSeatId(seat);

                if (playerid == this.playerid) {
                    this.meIsGaming = isGaming == 1;
                }

                // 添加玩家到队列
                console.log("添加用户到队列 ============================", plyInfo)
                this._mapPlayers.set(playerid, plyInfo);
                this.gameView.showPlayer(localSeatId, plyInfo);

                // 玩家是否在游戏中
                if (playerid == this.playerid) {
                    if (!this.meIsGaming && this.roomInfo.mode != 1) {
                        this.gameView.setPlayerAwaiting(localSeatId);
                    }
                }
            }

        }
        // 换桌
        if (this.roomInfo.mode != 1) {
            // this.gameView.showHuanZhuo(this.meIsGaming == false);

            if (this.meIsGaming == false) {
            }
        }

    }

    // 房间状态
    public onRoomState(info: any) {
        console.log("房间状态：", info)
        super.onRoomState(info);
        // 房间状态
        this.roomState = Common.toInt(info["roomstate"]);
        if (this.roomState == RoomState.GAME) {//只需要处理游戏状态
            this.meIsGaming = true;
            console.log("房间状态处理 ================================")
            this._mapPlayers.forEach((value: any, key: number) => {
                let localSeatId = this.getLocalSeatId(value.seat);
                this.gameView.setPlayerGaming(localSeatId);
                value.gameState = EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY;
            });
            this.gameView.isShowWaitStartTips(false);
        }
        else {
            this._mapPlayers.forEach((value: any, key: number) => {
                value.gameState = EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_NONE;
            });

            this.meIsGaming = false;
            this.gameView.isShowWaitStartTips(true);
        }
    }

    // 房间信息(继线重入)
    public onToOtherRoom(info: any) {
        console.log("断线重连：", info)

        super.onToOtherRoom(info);
        // this.gameView.resetStart();

        this.maxPlayers = Common.toInt(info["maxplayers"]);
        if(!this.maxPlayers){
            this.maxPlayers = Constant.player_count;
        }

        this.difen = Common.toInt(info["difen"]);
        this.gameState = Common.toInt(info["state"]);
        this.optsumtime = Common.toInt(info["optsumtime"]);
        let waittime = Common.toInt(info["waittime"]);
       

        this.optLocalSeatId = this.getLocalSeatId(Common.toInt(info["optseatid"]));
        let localZhuangSeatId = this.getLocalSeatId(Common.toInt(info["banker"]));
        this.quitChangeMoney=0;
    
        console.log("断线重连处理=============================")
        for (let idx = 1; idx <= this.maxPlayers; idx++) {
            let tInfo = info["playerlist"][idx];
            if (!tInfo) continue;

            let playerid = Common.toInt(tInfo["playerid"]);
            let playerLayer = this._mapPlayers.get(playerid);
            if (!playerLayer) continue;

            playerLayer.gameState = tInfo["state"];

            // 是否为庄家
            let localSeatId = this.getLocalSeatId(playerLayer.seat);
            if (localZhuangSeatId == localSeatId) {
                this.bankerId = playerid;
                this.bankerLocalId = localSeatId;
            }
        }

        this.gameView.restore_game(info, this.mySeatId);
    }

    // 玩家暂离
    public onPlayerZanLi(info: any) {
        // this.isZanLi = true;
        // this.quitChangeMoney=this.playersBet[this.meLocalSeatId];
        // this.gameView.doZanLi();
    }

    // 玩家暂离返回成功
    public onZanLiComback(info: any) {
        // this.gameView.doZanLiComback();
        // this.isZanLi = false;
        // this.quitChangeMoney=0;
    }


    // 开始匹配
    public onStartMatch(info: any) {
        this.gameView.resetAllUI();
        // this.gameView.showLoading(true);
    }

    // 匹配成功
    public onFinishMatch(info: any) {
        // this.gameView.showLoading(false);
    }

    // 聊天信息
    public onRoomChat(info: any) {
        let sendPlayerid = Common.toInt(info["sendPlayerid"]);
        let receiverPlayerid = Common.toInt(info["receiverPlayerid"]);
        let type = info["type"];
        let content = info["content"];
        let sendPlayerLayer = this._mapPlayers.get(sendPlayerid);
        let recPlayerLayer = this._mapPlayers.get(receiverPlayerid);
        if (!sendPlayerLayer) return;

        let sendLocalSeatId = this.getLocalSeatId(sendPlayerLayer.seat);

        if (!recPlayerLayer) {
            // this.gameView.doRoomChat(sendLocalSeatId, type, content);
        } else {
            let recLocalSeatId = this.getLocalSeatId(recPlayerLayer.seat);
            this.gameView.doRoomIntertChat(sendLocalSeatId, recLocalSeatId, content);
        }

    }

    public onRoomTipDealer(info: any){
        let localSeatId = this.getLocalSeatId(Common.toInt(info["seatid"]));
        // this.gameView.doTipDealer(localSeatId);
    }
    

    //////////////////////////////////////////////////////////////////////////////
    //开始
    private onStart(info: any) {
        console.log("开始游戏---------------------", info)
        // this.gameView.resetStart();
        this.gameView.resetAllUI();
        this.difen = Common.toInt(info["difen"]);
        this.gameState = Common.toInt(info["state"]);
        let localZhuangSeatId = this.getLocalSeatId(Common.toInt(info["banker"]));
        this.bankerLocalId = localZhuangSeatId;
        this.meIsGaming = true;
        this.quitChangeMoney=0;
    }

    // 游戏消息-发牌
    private onSendCard(info: any) {
        console.log("开始发牌--------------", JSON.stringify(info))
        this.gameState = Common.toInt(info["state"]);

        let self = this;
        // this.curtimes = Common.toInt(info["curtimes"]);
        //去除离开玩家
        this._mapPlayers.forEach(function (item, key) {
            console.log("去除离开的玩家：", item)
            if (item.isQuit === true) {
                console.log("isQuit判断为真，隐藏玩家：", item.isQuit)
                let localSeatId = self.getLocalSeatId(item.seat);
                self.gameView.hidePlayer(localSeatId);
                self._mapPlayers.delete(item.playerid);
            }
        });

        this.gameView.start_game(info);
    }

    // 游戏消息-弃牌
    private onCardDrop(info: any) {
        console.log("弃牌：", info)
        let playerid = Common.toInt(info["playerid"]);
        let seatid = Common.toInt(info["seatid"]);
        let localSeatId = this.getLocalSeatId(seatid);

        let playerLayer = this._mapPlayers.get(playerid);
        if (playerLayer) {
            playerLayer.gameState = EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_FOLD;
        }

        this.gameView.user_drop_info(localSeatId);
    }

    private onFinish(info: any){
        console.log("用户Finish: ", info)
        let seatId = info.seatid;
        if (this.mySeatId == seatId){
            console.log("我自己Finish：", info)
            this.gameView.my_finish(info);
        } else {
            this.gameView.otherPlayerShow(info)
        }
    }

    // 等待操作
    private onWaitOpt(info: any) {
        console.log("等待操作-----------", info)
        let seatid = Common.toInt(info["optseatid"]);
        this.optsumtime = info["optsumtime"];
        let self = this;
        setTimeout(function() {
            self.gameView.changeAwait(seatid, self.optsumtime, self.optsumtime);
        }, 500);
        
    }

    // 游戏消息-结算
    private onJieShuan(info: any) {
        console.log("结算：", info)
        if (!info || typeof (info) != "object") {
            console.error("RMGameCore.onJieShuan: info is invalid.");
            return;
        }

        this.gameState = GameState.gameEnd;
        //关闭当前等待的用计时
        this.gameView.changeAwait(-1, 0, 0);
        // this.gameView.updateOptState(false);
        // this.gameView.hideNeedRecharge();
        this.gameView.game_end(info);
        
        console.log("游戏结算--------------------------------")
        this._mapPlayers.forEach((value: any, key: number) => {
            value.gameState = EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_NONE;
        });
       
    }

     // 拿牌
    private onGetCard(info: any) {
        console.log("拿牌：", info)
        let seatId =  info["seatid"];
        if(this.mySeatId == seatId) {
            let card = {
                color: info["card"]["cardcolor"],
                number: info["card"]["cardnumber"]
            }
            this.gameView.get_card_to_me(card, info['pos'])
        } else {
            console.log("别人拿牌: ", info)
            this.gameView.otherAddcard(info)
        }
    }

    //出牌
    private onAbandonCard(info: any) {
        console.log("出牌：", info)
        let seatId = info.seatid;
        if (this.mySeatId == seatId){
            console.log("我自己出牌：", info)
            this.gameView.my_discard();
        } else {
            this.gameView.otherDiscard(info)
        }
        
        // this.gameView.otherDiscard(info);
    }

    //宣布胜利
    private onDeclare(info: any) {
        console.log("宣布胜利：", info)
        // this.gameView.isShowOtherDeclareTips(true);

        // this.gameView.isShowDeclareTips(true);
    }

    private onUpdateCard(info: any) {
        console.log("更新后返回Score信息；", info)
        this.gameView.show_total_score(info["score"]);   
    }

    private onGetSortCard(info: any) {
        console.log("获取组牌数据：", info)
        this.gameView.sortCardGroup(info["sorted_card"])
    }

    // 重新提交排序
    private onReUpdate(info: any) {
        console.log("重新获取分组", info)

        this.gameView.sort_to_server();
    }

    // 通知所有人Declare
    private onAllDeclare(info: any){
        console.log("通知所有人Declare: ", info);
        this.gameView.otherDeclare(info);
    }

    //////////////////////////////////////////////////////////////////////////////

    private updateGameLan() {
        updateGameTextTips();
        TextTips["GameTextTips"] = GameTextTips;
    }

    public getPlayingCount(): number {
        let count = 0;
        console.log("获取Playing数量 ==================================")
        this._mapPlayers.forEach((value: any, key: number) => {
            if (value.gameState == EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY) count = count + 1;
        });
        return count;
    }

    public isPlayerMeGaming(): boolean {
        let plyInfo = this._mapPlayers.get(this.playerid);
        return plyInfo.gameState == EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY;
    }

    public isPlayerGaming(playerid: number): boolean {
        let plyInfo = this._mapPlayers.get(playerid);
        return plyInfo.gameState == EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY;
    }

    // 弃牌
    public userDrop() {
        let info = {};
        this.sendGameMessage(Protos.CS_RUMMY_FOLD_P, info);
    }

    // 拿牌
    public getCard(pos) {
        let info = {};
        info['pos'] = pos;
        this.sendGameMessage(Protos.CS_RUMMY_GETCARD, info)
    }

    // 出牌
    public userDisCard(groups: any, card : any, index) {
        let info = {}
        info['groups'] = groups;
        info['card'] = card;
        info['index'] = index;
        this.sendGameMessage(Protos.CS_RUMMY_ABANDONCARD, info)
    }

    // 宣布胜利
    public userDeclare(data: any) {
        let info = {}
        this.sendGameMessage(Protos.CS_DECLARE, info)
    }

    // 更新牌的顺序
    public updateCardSequences(data: any) {
        let info = {
            cardarr : data
        }
        console.log("发送数据：", info)
        this.sendGameMessage(Protos.CS_RUMMY_UPDATE_CARD, info)
    }

    // 获取组牌
    public getSortCard(){
        console.log("发送获取牌组消息：", Protos.CS_RUMMY_SORT_CARD)
        let info = {}
        this.sendGameMessage(Protos.CS_RUMMY_SORT_CARD, info);
    }

    // 发送完成命令
    public sendFinishMsg(card, index){
        let info = {};
        info["card"] = card;
        info["index"] = index
        this.sendGameMessage(Protos.CS_RUMMY_FINISH, info);
    }


    // 返回自己的游戏状态
    public get_user_game_status(){
        return this.meIsGaming;
    }
    /**
     * 
        CS_RUMMY_GETCARD_P   "pos"  0(左边) 1（右边）
        CS_RUMMY_ABANDONCARD  “card” ：{cardcolor，cardnumber}
        CS_DECLARE 宣布胜利
        CS_RUMMY_FOLD_P 弃牌
        CS_RUMMY_UPDATE_CARD 更新牌序：
        {
        “cardarr”：[
        [{"color", "value"}......],
        [{"color", "value"}......],
        [{"color", "value"}......]
        ]
        }
        返回Score信息
     */
}

