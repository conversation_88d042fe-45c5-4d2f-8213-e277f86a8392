//////////////////////////////////////////////////////////////////////////////////

import { LanguageType } from "../../../../script/frame/common/Define"

// 基础数据
export let Constant = {
  player_count: 6,                // 玩家个数
  self_local_pos: 0,  //自己本地位置号     
  stat_card_pos_list: { 0: cc.v2(77, 175), 1: cc.v2(-83.5, -54), 2: cc.v2(5, -130), 3: cc.v2(5, -130), 4: cc.v2(5, -130), 5: cc.v2(96.5, -54) },//各玩家首张亮出的牌位置 
  self_hand_cards_num: 13,//自家手牌数
  cardSpaceX: -84,//每个牌间隔
  card_scale_other: 0.76,//牌缩放
  self_cards_pos_y: -157,//手牌默认Y位置
  cardWid :146, //间宽
  minNumberCardInGroup : 3,//分组最小牌组
  groupItem_y : -253,//分组Y位置
  groupSpaceX : 2, //分组间隔
  max_score: 80,//最多累加分数
  delay_sort_time: 0.6,//延迟排序时间
  player_pos_list : {0: cc.v2(-77, -316), 1: cc.v2(559, 131), 2: cc.v2(319, 259), 3: cc.v2(-5, 259), 4: cc.v2(-330, 259), 5: cc.v2(-572, 131)},

  // 新定义牌数据
  card_offset_x: 62, // 牌的偏移量
  group_offset_x: 2, // 牌组的偏移量
  card_width: 146,  // 牌的宽度
  max_hand_card_count: 14, //最大手牌数
  move_space_for_card_insert: 25, // 当牌移动到左边框多少像素算是覆盖前牌
}

// 游戏协议
export enum Protos {
  SC_RUMMY_START_P = 1000,    //开始
  SC_RUMMY_SENDCARD_P,      //发牌
  CS_RUMMY_FOLD_P,        //弃牌
  SC_RUMMY_FOLD_P,        //弃牌
  SC_RUMMY_WAITOPT_P,        //等待操作
  SC_RUMMY_JIESHUAN_P,      //结算

  CS_RUMMY_GETCARD,        // 拿牌
  SC_RUMMY_GETCARD,        // 拿牌

  CS_RUMMY_ABANDONCARD,      // 还牌
  SC_RUMMY_ABANDONCARD,      // 还牌
  
  CS_DECLARE,            // 宣布胜利
  SC_DECLARE,            // 宣布胜利

  CS_RUMMY_UPDATE_CARD,      // 更新牌的顺序
  SC_RUMMY_UPDATE_CARD,      // 更新牌顺序后返回Score信息

  CS_RUMMY_SORT_CARD,        // 刷新牌序
  SC_RUMMY_SORD_CARD,        // 刷新牌序结果

  CS_RUMMY_FINISH,           // 完成
  SC_RUMMY_FINISH,        // 有人宣布 finish

  CS_RUMMY_GETHISTORY,          // 获取历史记录
  SC_RUMMY_GETHISTORY,          // 获取历史记录

  SC_RUMMY_ALL_DECLARE,      // 所有人都可以Declare
  SC_RUMMY_REUPDATE,           // 重新提交排序
};


//牌花色值
export let CardColor = {
  diamond: 3,//方块
  club: 4,//梅花
  heart: 5,//红桃
  spade: 6 //黑桃
}

// 新定义卡片花色
export enum CardSuit {
  S_Spades = 0,      // 黑桃
  S_Hearts = 1,      // 红桃
  S_Clubs = 2,      // 梅花
  S_Diamonds = 3,      // 方块
  S_Joker = 4,      // 大小鬼
  S_Unknown = 5,      // 未知牌型
  S_CardSuit_COUNT = 6  // 数组大小
};

// 发送消息到服务器的状态
export enum SendServerMsgStatus {
  NET_STATUS_WAITING,             // 等待发送
  NET_STATUS_SENDING,             // 正在发送
  NET_STATUS_SENDED_OK,           // 发送成功
  NET_STATUS_SEND_FAILED          // 发送失败
}
// 游戏状态
// export let GameState = {
//   EM_TEENPATTI_GAMESTATE_START: 0, //游戏开始
//   EM_TEENPATTI_GAMESTATE_SENDCARD: 1, //发牌状态
//   EM_TEENPATTI_GAMESTATE_BET: 2, //下注状态
//   EM_TEENPATTI_GAMESTATE_COMPETITION: 3, //比牌状态
//   EM_TEENPATTI_GAMESTATE_END: 4, //结束状态
//   EM_TEENPATTI_GAMESTATE_WAITRECHARGE: 5, //等待充值
// }

// 游戏阶段
export enum EM_RUMMY_GAMESTATE        //游戏阶段
{
  EM_RUMMY_GAMESTATE_START,    //游戏开始
  EM_RUMMY_GAMESTATE_SENDCARD,  //发牌状态
  EM_RUMMY_GAMESTATE_PLAY,    //游戏状态
  EM_RUMMY_GAMESTATE_FINISH,    //有人 Finish
  EM_RUMMY_GAMESTATE_DECLARE,    //Declare状态 所有人都可以进行Declare
  EM_RUMMY_GAMESTATE_END,      //结束状态
};

// 玩家游戏状态
export enum EM_RUMMY_PLAYERSTATE {
  EM_TEENPATTI_PLAYER_NONE,			//无效状态
  EM_TEENPATTI_PLAYER_PLAY,			//游戏状态
  EM_TEENPATTI_PLAYER_FOLD,			//弃牌状态
  EM_RUMMY_PLAYER_DECLARE,			//输牌状态
}

let GameTextCH = {

}

let GameTextEnglish = {
  max_group: "Sorry, you can't have more than {0} groups.",
  other_declare: "{0} has declared.Group your cards and declare.",
  turn_tips: "It's your turn to play.",
  wait_other: "Waiting for other players to join this game.",
  wait_start: "waiting for dealing cards {0}s...",
  play_first: "{0} won the toss and will play first.",
  drop: "Drop",
  finish: "Finish",
  group: "Group",
  discard: "Discard",
  sort: "Sort",
  objective:"Rummy Objective",
  pureSequene: "Pure Sequene",
  sequene: "Sequene",
  score: "Score = 0",
  group_declare: "Pleas group your cards and declare.",
  declare: "Declare",
  declare_confim: "Ara you sure to declare?",
  declare_cancel: "Cancel",
  prompt: "PROMPT",
  drop_tips: "You will lose ₹{0}. Are you sure you want to leave the game?",
  sure: "Sure",
  wait_sort: "Please try again later"
}
let GameTextIndia = {
  max_group: "क्षमा करें, आपके पास 6 से अधिक समूह नहीं हो सकते.",
  other_declare: "{0} ने घोषणा कर दी है। अपने कार्डों को समूहित करें और घोषणा करें।",
  turn_tips: "खेलने की आपकी बारी है",
  wait_other: "इस खेल में अन्य खिलाड़ियों के शामिल होने का इंतजार है।",
  wait_start: "डीलिंग कार्ड का इंतजार {0}s...",
  play_first: "{0} ने टॉस जीता और पहले खेलेंगे.",
  drop: "ड्रॉप",
  finish: "फिनिश",
  group: "एकत्रित करें",
  discard: "रद्द करें",
  sort: "सुलझाना",
  objective:"रम्मी उद्देश्य",
  pureSequene: "प्योर सिक्वेन्स",
  sequene: "सिक्वेन्स",
  score: "स्कोर = 0",
  group_declare: "कृपया अपने कार्डों को समूहीकृत करें और घोषित करें",
  declare: "घोषित करें",
  declare_confim: "क्या आप निश्चित रूप से घोषित कर रहे हैं?",
  declare_cancel: "रद्द करें",
  prompt: "PROMPT",
  drop_tips: "आप ₹{0} खो देंगे. क्या आप निश्चित रूप से खेल छोड़ना चाहते हैं?",
  sure: "ठीक",
  wait_sort: "कृपया बाद में पुनः प्रयास करें"
}
// 文本内容
export let GameTextTips = GameTextEnglish

export let updateGameTextTips = () => {
  let curLanguage = cc.sys.localStorage.getItem('curLanguage') || 1;
  if (curLanguage == LanguageType.CHINESE) {
    // GameTextTips = GameTextCH;
  } else if (curLanguage == LanguageType.ENGLISH) {
    GameTextTips = GameTextEnglish;
  } else if (curLanguage == LanguageType.INDIA) {
    GameTextTips = GameTextIndia;
  }
}

export interface CardInterface {
  /** 对应牌 */
  card?: cc.Node,
  /** 牌号 */
  id?: number,
  /** 牌对象 */
  cardData?: CardData,
  /** 当前位置 */
  pos?: cc.Vec3,
  /** 是否已经升起 进入分组 弃牌.... */
  moveUP?: boolean,
  /** 是否当前一轮拿到的牌 当超时之后会直接丢出当前一轮拿到的牌 */
  getCard?: boolean,
  /** 标记当前牌为无效牌 不参与移动 结束之后会被清除 */
  visild?: boolean,
  /** 是否准备进行show 的牌 */
}

export interface CardGroup {

  /** 组合牌类型 */
  type: GroupType;

  /** 组合里面的牌表 */
  list: CardData[];

  /** 位置列表 */
  IndexList: number[];

  cardTypeNode?: cc.Node;
}

export interface CardData {
  //牌点数
  number?: number,
  //牌花色
  color?: number,
  //位置
  index?: number,
}

export enum GameState {

  ready = 0,

  gameing,
  /**  */
  /** 自己回合开始 */
  selfRound,
  /** 自己回合结束 */
  selfRoundEnd,
  /** 确认牌型阶段 */
  confim,
  /** 别人操作阶段 */
  otherRound,

  gameEnd,

  /** 观战阶段 */
  waitNextRound,
}

/** 玩家操作状态 */
export enum OperateState {
  /** 别人操作 */
  otherOperate = 0,
  /** 自己等待抓牌 */
  waitDrop,
  /** 已经抓牌 */
  dropCard,
  /** 已经丢牌 */
  disCard,
}

/* 玩家操作状态 */
export enum UserOperateState {
  /* 别人操作 */
  otherOperate = 0,
  /* 自己等待抓牌 */
  waitGetCard,
  /* 正在抓拍 */
  gettingCard,
  /* 已经抓牌 */
  gotCard,
  /* 已经丢牌 */
  disCard,
}

//分组类型
export enum GroupType {
  PURE_SEQUENCE =  0, //真顺子
  SEQUENCE      =  1, //假顺子
  SET           =  2, //组（3条或炸弹）
  NUMBER        =  3, //散牌
  NUMBER_SINGLE_NOT_JOKER = 4,//单张非癞子
  NUMBER_SINGLE_JOKER = 5,//单张且是癞子
};

//牌值描述
export enum CardNumDesc {
  Non =  0, //
  NUM_2 = 2,
  NUM_3,
  NUM_4,
  NUM_5,
  NUM_6,
  NUM_7,
  NUM_8,
  NUM_9,
  NUM_10,
  NUM_J,
  NUM_Q,
  NUM_K,
  NUM_A,
  NUM_JOKER_S, //小王
  NUM_JOKER_B, //大王
};

//分组底色
export enum GroupTypeColor {
  Orange = 0, //散牌色
  Gree , //有真顺子显示色
  Yellow  , //无真顺子 但是牌型是假顺子或者set
};

export const GameEvent = {
  SWAP_CARDS_GROUP:"SWAP_CARDS_GROUP",//交换分组位置
  ADD_CARDS_GROUP_HERE:"ADD_CARDS_GROUP_HERE",//指定牌到当前分组
  CLICK_HEAD_SHOW_GIFT:"CLICK_HEAD_SHOW_GIFT",//点击头像显示道具
}

export enum ClickShowCardState {
  click = 1,//点击按钮show card
  move = 2, //移动牌到showcard位置
};

//玩家要牌
export let CardGetState = {
  Non: 0,//没有要牌
  cardPool: 1,//牌堆 1从剩余牌中补一张
  cardGetPlayers: 2,//从玩家弃牌堆中拿的牌（只能补最上面一张，即最近出的一张）
}

//玩家移动牌状态
export let CardMoveState = {
  Non: -1,
  cardToPool: 1,//移到牌堆
  cardToFinish: 2,//移到中间Finish胡牌
}

//提示文字类型
export enum showTipsType {
  wait_other = 0, //等待其它玩家
  wait_start = 1,//等待开始倒计时
  play_first = 2,//哪个玩家先出牌

};

// 左上角的提示颜色
export const Left_Tips_Color = {
  Color_Enabled : cc.color(12, 116, 195),
  Color_Normal : cc.color(100, 100, 100)
};

//////////////////////////////////////////////////////////////////////////////////
