import Common from "../../../../script/frame/common/Common";
import { Direction, ZOrder } from "../../../../script/frame/common/Define";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import RMAudioMng from "../core/RMAudioMng";
import { Constant, GameEvent, GameTextTips } from "../core/RMDefine";
import RMGameCore from "../core/RMGameCore";
import RMGameView from "./RMGameView";
import RMGameLogic from "../core/RMGameLogic";
import RMClockLayer from "./RMClockLayer";
import RMCardItem from "../core/RMCardItem";
import RMCardPool from "../core/RMCardPool";
import EventManager from "../../../../script/frame/manager/EventManager";

/**玩家数据 */
export type playersData = {
    //玩家id
    playerid?:number,
    //玩家名字
    playerName?:string,
    //玩家本地位置 
    localPos?: number,
    //玩家所在服务器位置
    serverPos?: number,
    //玩家身上金币
    curMoney?: number,
    //玩家是否游戏中
    isGaming?: boolean,
    //玩家是否庄家
    isBanker?: boolean,
    //是否自己
    isMePlayer?: boolean,
    //玩家头像方向
    userDirection?: Direction,
}

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('rummy/RMPlayerLayer')
export default class RMPlayerLayer extends BaseLayer {
    //头像节点
    @property(cc.Node)
    headInfo: cc.Node = null;
    //头像
    @property(cc.Sprite)
    headSp: cc.Sprite = null;
    //自家信息
    @property(cc.Node)
    selfNode: cc.Node = null;
    //自身余额
    @property(cc.Label)
    selfMoney: cc.Label = null;   
    //自己名字 
    @property(cc.Label)
    selfName: cc.Label = null;   
    //自己分数
    @property(cc.Label)
    selfScore: cc.Label = null;   
    //其它玩家
    @property(cc.Node)
    otherNode: cc.Node = null;
    //其它玩家名字 
    @property(cc.Label)
    otherName: cc.Label = null;   
    //头像计时器节点
    @property(cc.Node)
    countTimeNode: cc.Node = null;
    //庄家标志
    @property(cc.Node)
    dealFlag: cc.Node = null;
    //发牌起始节点
    @property(cc.Node)
    StartCard: cc.Node = null;
    //点击头像
    @property(cc.Node)
    clickHead: cc.Node = null;
    //遮罩
    @property(cc.Node)
    shadow: cc.Node = null;
    //弃牌
    @property(cc.Node)
    drop: cc.Node = null;
    //结算信息
    @property(cc.Node)
    settleNode: cc.Node = null;
    //输
    @property(cc.Node)
    loseNode: cc.Node = null;
    //赢
    @property(cc.Node)
    winNode: cc.Node = null;
    //输分
    @property(cc.Label)
    loseNum: cc.Label = null;
    
    //玩家数据
    playerData: playersData = <playersData>{};

    // 扑克数据
    private _cardDatas: any = [];
    
    /** 头像倒计时组件 */
    private _clockLayer: RMClockLayer;

    /** 游戏界面 */
    private _gameView: RMGameView;
    
    //首发牌
    StartCardItem: cc.Node = null;
    // 声音对象
    private _audioMng: RMAudioMng = null;

    onLoad() {
        this._gameView = cc.Canvas.instance.getComponent(RMGameView);
        this._clockLayer = this.countTimeNode.getComponent(RMClockLayer);
        this._audioMng = cc.Canvas.instance.getComponent(RMAudioMng);
        
    }
    onDestroy() {
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
        
        if(this.StartCardItem){
            RMCardPool.instance.put(this.StartCardItem);
        }           
    }
    //////////////////////////////////////////////////////////////////////////////
    // 清理数据
    public clearData() {
        this.playerData.playerid = 0;
        this.playerData.playerName = '';
        this.playerData.isGaming = false;
        this.playerData.isBanker = false;
        this.playerData.serverPos = -1;
        this.playerData.curMoney = 0;
        this._cardDatas = [];
        this.resetBase();
    }

    /**
     * 重置玩家基础UI
     */
    public resetBase() {
        if (this._clockLayer) {
            this._clockLayer.stopTime();
            this._clockLayer.node.stopAllActions();
        }
        this.showAwait(false);    
        this.headInfo.scale = 1;
        this.showBankerFlag(false);
        this.showHeadShadow(false);
        this.showDropText(false);
        this.showSettleNodeInfo(false);
        this.StartCard.active = false;
    }
    
     // 显示或隐藏庄家标记
     public showBankerFlag(isShow: boolean) {
        this.dealFlag.active = isShow;
        this.playerData.isBanker = isShow;
    }

    //是否显示遮罩
    public showHeadShadow(isShow: boolean){
        this.shadow.active = isShow;
    }

    //是否显示弃牌文字
    public showDropText(isShow: boolean){
        this.drop.active = isShow;
    }

    //是否显示结算信息
    public showSettleNodeInfo(isShow: boolean, winNum?: number, callback?: CallableFunction){
        this.settleNode.active = isShow;
        if(isShow){
            let isWin = winNum >= 0;
            this.loseNode.active = !isWin;
            this.winNode.active = isWin;
            this.loseNum.node.active = false;
            let self = this;
            let node = isWin ? this.winNode : this.loseNode;
            isWin ? AudioHelper.instance.playEffect(`res/sound/win`) : AudioHelper.instance.playEffect(`res/sound/lose`);
            node.stopAllActions();
            node.scale = 0.7;
            cc.tween(node)
            .to(0.1,{scale:1},{easing:'sineIn'})
            .call(()=>{
                if(!isWin){
                    self.loseNum.node.active = true;
                    self.loseNum.string = self._gameView.moneyFormat(winNum);
                    self.loseNum.node.stopAllActions();
                    self.loseNum.node.y = -19;
                    cc.tween(self.loseNum.node)
                    .to(0.5,{position: cc.v3(6,5)},{easing: "sineOut"})
                    .start()
                }
                if (callback != null && callback != undefined ){
                    callback()
                }
            })
            .start()
        }
    }

    /**
     * 切换等待用户
     * @param isShow 是否显示
     * @param showallin 是否显示allin
     * @param waittime  等待时间
     * @param optsumtime  等待总时间
     */
    public switchToAwait(isShow: boolean, waittime: number, optsumtime: number) {
        this._clockLayer.node.stopAllActions();
        if (isShow && waittime > 0) {
            console.log("this._clockLayer: ", this._clockLayer)
            if (this._clockLayer) {
                this._clockLayer.stopTime();
                this._clockLayer.resetUI(waittime, optsumtime);
            }
            let self = this;
            if (this.playerData.isMePlayer && optsumtime > 6) {
                cc.tween(this._clockLayer.node).delay(optsumtime - 6).call(() => {
                    self._audioMng._loadPlaySFX(`res/sound/count_down`)
                }).delay(3).call(() => {
                    self._audioMng._loadPlaySFX(`res/sound/count_down`)
                }).start();
            }
        } else {
            if (this._clockLayer) {
                this._clockLayer.stopTime();
            }
        }
    }

    /** 显示操作界面 */
    public showAwait(isShow: boolean = true) {
        if (this._clockLayer) {
            this._clockLayer.node.active = isShow;
        }
    }


    public exitInGaming() {
        this.resetBase();
        this.unscheduleAllCallbacks();
        if (!this.playerData.isMePlayer) {
            this.node.active = false;
        }
    }

    // // 设置玩家的本地位置
    public setLocalSeatId(seatId: number) {
        this.playerData.localPos = seatId;
    }

    public getLocalSeatId() {
        return this.playerData.localPos;
    }

    // 设置玩家为游戏中
    public setGameing() {
        this.playerData.isGaming = true;
    }

    // 更新玩家的金币
    public updateMoney(money: number) {
        this.selfMoney.string = this._gameView.moneyFormat(money,2);
        this.playerData.curMoney = money;
    }

    //道具显示方向
    setDirection(userDirection: Direction){
        this.playerData.userDirection = userDirection;
    }

    // 设置用户信息
    public setUserInfo(userInfo: any,localSeatId: number,maxPlayers: number) {
        this.playerData.isMePlayer = localSeatId == Constant.self_local_pos;
        this.playerData.playerid = userInfo.playerid;
        this.playerData.isGaming = Common.toInt(userInfo["isgaming"]) == 1;
        this.playerData.serverPos = userInfo.seat;
        this.StartCard.position = Constant.stat_card_pos_list[localSeatId];
        this.StartCard.scale = localSeatId == Constant.self_local_pos ? 1 : 0.5;
        this.playerData.playerName = userInfo.nickname;

        let userDirectionList = [Direction.DOWN,Direction.RIGHT,Direction.RIGHT,Direction.UP,Direction.LEFT,Direction.LEFT]
        this.setDirection(maxPlayers == 2 ? Direction.UP : userDirectionList[localSeatId]);
                
        //首次每人发的一张定位的牌
        if(!this.StartCardItem){
            this.StartCardItem = RMCardPool.instance.get();
            this.StartCard.addChild(this.StartCardItem);
            this.StartCardItem.active = true;
            this.StartCardItem.name = 'startCardItem';
        }
        else{
            this.StartCardItem.getComponent(RMCardItem).showCardBack();
        }
        this.StartCard.active = false;

        this.node.active = true;
        if(this.playerData.isMePlayer){
            this.selfMoney.string = this._gameView.moneyFormat(userInfo.money,2);
            this.selfName.string = Common.textClamp(userInfo.nickname, 10, "...");
            this.selfScore.string = `Score: ${0}`;
            this.selfNode.active = true;
            this.otherNode.active = false;
            this.playerData.curMoney = userInfo.money;
        }
        else{
            this.selfNode.active = false;
            this.otherNode.active = true;
            this.otherName.string = Common.textClamp(userInfo.nickname, 10, "...");
        }
        this.setPlayerHead(this.headSp, userInfo.headid);
        this.runHeadAni();
    }

    
    // 设置扑克数据
    public setPlayerPokerData(cards: any) {
        this._cardDatas = [];

        if (cards && typeof (cards) == "object") {
            for (let key in cards) {
                this._cardDatas.push(cards[key]);
            }
        }
    }

    //当前牌组分数
    updateSelfScore(score: number){
        this.selfScore.string = `Score: ${score}`; 
    }

    //头像动作
    runHeadAni(){
        this.headInfo.stopAllActions();
        cc.tween(this.headInfo)
        .to(0.5,{scale:1.1},{easing:"sineIn"})
        .to(0.5,{scale:1},{easing:"sineOut"})
        .start()
    }

    //初始每人一张首发动画
    runFirstOneCardAni(isShowLight: boolean){
        this.StartCard.active = true;
        this.StartCardItem.getComponent(RMCardItem).runFlipOneCardAni(this.StartCardItem,isShowLight,true);
    }

    
    // 获取头像的世界坐标
    public headWorldPos(): cc.Vec2 {
        return this.headInfo.convertToWorldSpaceAR(this.headInfo.getAnchorPoint())
    }

    //////////////////////////////////////////////////////////////////////////////
    // 点击玩家头像
    private onClickHead(target: any, customEventData: any) {
        let self = this; 
        if(!this.playerData.playerid)return;
        if (!this.playerData.isMePlayer) {
            let emojiNode = this._gameView.gameEmojiNode;
            let showPos = emojiNode.convertToNodeSpaceAR(this.headWorldPos());

            if (this.playerData.userDirection == Direction.LEFT) {
                showPos.x += 350;
            }
            else if (this.playerData.userDirection == Direction.RIGHT) {
                showPos.x -= 350;
            } else if (this.playerData.userDirection == Direction.UP) {
                showPos.y -= 105;
            } else if (this.playerData.userDirection == Direction.DOWN) {
                showPos.y += 150;
            }
            EventManager.instance.emit(GameEvent.CLICK_HEAD_SHOW_GIFT,{playerid:this.playerData.playerid,showPos:showPos});  
        } else {
            // this._gameView.openMeChat();
        }
    }

    //////////////////////////////////////////////////////////////////////////////

}

