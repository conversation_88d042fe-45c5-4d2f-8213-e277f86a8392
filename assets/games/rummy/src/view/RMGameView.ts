import Common from "../../../../script/frame/common/Common";
import { Direction, RoomState, ZOrder } from "../../../../script/frame/common/Define";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import Config from "../../../../script/frame/config/Config";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import EventManager from "../../../../script/frame/manager/EventManager";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import RMAudioMng from "../core/RMAudioMng";
import RMCardItem from "../core/RMCardItem";
import RMCardPool from "../core/RMCardPool";
import { CardData, UserOperateState, CardGetState, CardGroup, CardInterface, CardMoveState, CardNumDesc, 
        ClickShowCardState, Constant, GameEvent, GameState, GameTextTips, GroupType, GroupTypeColor, 
        showTipsType, OperateState, SendServerMsgStatus, CardColor, EM_RUMMY_PLAYERSTATE, EM_RUMMY_GAMESTATE,
        Left_Tips_Color } from "../core/RMDefine";
import RMGameCore from "../core/RMGameCore";
import RMGameLogic from "../core/RMGameLogic";
import RMGroupItem from "../core/RMGroupItem";
import RMPlayerLayer from "./RMPlayerLayer";
import RMSettleLayer from "./RMSettleLayer";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property, disallowMultiple, menu } = cc._decorator;

interface Item {
    color: number;
    number: number;
}

interface GroupedItem {
    cardData: Item[];
    type: number;
}

interface CardDataItem {
    color: number;
    number: number;
}
  
interface CardGroupItem {
    cardData: Record<string, CardDataItem>;
    px: number;
    cardtype: number;
}
  
type CardDataArray = CardDataItem[];


@ccclass
@disallowMultiple()
@menu('rummy/RMGameView')
export default class RMGameView extends BaseLayer {

    TempCardDataList = []
    //玩家预制体
    @property(cc.Prefab)
    playerItemPrefab: cc.Prefab = null;

    //结算预制体
    @property(cc.Prefab)
    settlePrefab: cc.Prefab = null;

    //牌
    @property(cc.Prefab)
    cardItemPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    groupItemPrefab: cc.Prefab = null;

    // 游戏核心对象
    private _gameCore: RMGameCore = null;
    // 声音对象
    private _audioMng: RMAudioMng = null;
    private _gameLogic: RMGameLogic = null;

    // 游戏玩家操作对象
    private _allPlayers: RMPlayerLayer[] = [];

    // 座位号=>操作对象
    private _mapSeatPlayers = new Map<number, RMPlayerLayer>();
    /** 当前操作id */
    private _curAwaitSeatId: number = -1;

    //左边菜单
    private _isOpenMenuLeft: boolean = false;
    //右边菜单
    private _isOpenMenuRight: boolean = false;

    //初始位置
    _menuLeftOrgPos: cc.Vec2;
    _menuRightOrgPos: cc.Vec2;
    _topOrgPos: cc.Vec2;
    _bottomOrgPos: cc.Vec2;
    _sortOrgPos: cc.Vec2;

    // 自动排序多长时间可以请求一次
    private time_second_to_auto_sort : number = 3;
    /** 是否允许触摸牌 */
    private canTouchCard: boolean = false;

    //选中的牌
    private selected_group_cards: number[] = [];
    /** 界面上的牌组 */
    private cardGroups: CardGroup[] = [];

    //分组节点
    groupTypeNodeList: cc.Node[] = [null, null, null, null, null, null];

    //用来记录当前分组是否改变
    groupTypeList: GroupType[] = [GroupType.NUMBER, GroupType.NUMBER, GroupType.NUMBER, GroupType.NUMBER, GroupType.NUMBER, GroupType.NUMBER];

    // 1 是点击按钮show card   2 直接移动牌到showcard位置
    private clickState: number = ClickShowCardState.click;

    //是否排序
    isSort: boolean = true;

    //是否显示了记录
    isShowCardsRecord: boolean = false;

    //道具节点
    gameEmojiNode: cc.Node = null;

    //结算界面
    settleNode: cc.Node = null;

    //最大分组 宽屏6个 窄屏5个
    max_group: number = 6;

    //与上次对比是否需要弹出帮助提示
    isShowHelp = [false, false, false];

    //是否正在播放坐下声音
    isPlayingSitSound: boolean = false;

    // 记录元始手牌是否初始化完成
    private is_hand_card_init: boolean = false;

    //牌堆底下固定的牌
    cardPoolBottomList: cc.Node[] = [];
    //自己手牌
    selfHandCardList: cc.Node[] = [];
    //弃牌堆最上面一张牌
    upCard: cc.Node = null;
    //弃牌堆最上面一张牌 牌移动时，临时绘制显示的一张
    upCardTemp: cc.Node = null;
    //显示的癞子牌
    showLaiziCard: cc.Node = null;
    //中间showCard
    showCard: cc.Node = null;

    ///////////////////////////////重新定义//////////////////////////////////////////
    //癞子牌值
    private laizi_card: CardData;
    // 临时牌组
    private temp_groups = [];
    private unselected_temp_groups = [];
    private last_larger_card = null;
    private card_moved = false;
    private card_move_to_other_group = false;
    private card_draged = false;
    private card_discarded = false;
    
    // 记录当前移动到的位置
    private origin_groups_index: number = -1;
    private origin_groups_cards_index: number = -1;
    private now_move_groups_index: number = -1;
    private now_move_groups_cards_index: number = -1;
    private selected_card = [];
    // 移动牌分组时牌的索引
    private touch_move_card_index: number = 0;
    // 记录是否移动牌
    // 服务器是否返回排序
    /**
     * is_server_sorted = 0
     */
    private is_server_sorted: number = 0;
    // 服务器是否返回
    private is_server_return: boolean = false;
    // 界面通知
    private ui_sort_notice: number = 0;
    //定时器ID
    private interval_Id = null;
    // 用户摸牌状态
    private user_operate_status: UserOperateState = UserOperateState.otherOperate;
    // 创建每个完成庄牌节点
    private banker_card_node: cc.Node[] = []

    // 记录下服务器返回的牌组分数
    private card_score: number = 0;

    // 发送服务器消息的状态
    private send_server_msg_status : SendServerMsgStatus = SendServerMsgStatus.NET_STATUS_WAITING;

    // 上次排序更新时间
    private last_sort_update_time : number = 0;

    //前一张打出的牌
    private last_droped_card_info = {color: -1, number: -1};

    // 是否自动抽牌出来
    private is_auto_up_card: boolean = false;

    // 定时器ID
    private remainingTime: number = 0;
    private timerHandle: any = null;


    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)

        // 游戏核心对象
        this._gameCore = this.node.getComponent("RMGameCore");
        this._audioMng = this.node.getComponent("RMAudioMng");
        this._gameLogic = this.node.getComponent("RMGameLogic");
        this.gameEmojiNode = this.LayerItems.gameEmojiNode;
        EventManager.instance.on(GameEvent.CLICK_HEAD_SHOW_GIFT, this.onShowGift, this);
        EventManager.instance.on(GameEvent.ADD_CARDS_GROUP_HERE, this.onAddCardsGroupHere, this);

        if (cc.winSize.width / cc.winSize.height < 2) {
            this.max_group = 5;
        }

        this.initCardInfo();
        
        if (this.interval_Id == null){
            this.interval_Id = setInterval(() => {
                this.thread_server_sort();
            }, 50);
        }

        // 隐藏相关节点
        this.clearUI();
    }

    //重新设置位置
    resetPlayersPos() {
        if (this._gameCore.maxPlayers <= 2) {
            return;
        }

        let userDirectionList = [Direction.DOWN,Direction.RIGHT,Direction.RIGHT,Direction.UP,Direction.LEFT,Direction.LEFT]
        let temp = this._allPlayers.slice(0);
        let self_pos_info = temp.shift()
        this._allPlayers = this.shufflerArray(temp);
        this._allPlayers.unshift(self_pos_info);
        for (let i = 0; i < this._allPlayers.length; i++) {
            let player = this._allPlayers[i];
            if (player) {
                this._allPlayers[i].node.name = 'playerItem' + i;
                player.setLocalSeatId(i);
                player.setDirection(userDirectionList[i]);
                this._allPlayers[i].node.stopAllActions();
                cc.tween(this._allPlayers[i].node)
                    .to(0.25, { position: cc.v3(Constant.player_pos_list[i]) }, { easing: (dt: number) => cc.easeIn(1.0).easing(dt) })
                    .start()
            }
        }
    }

    //初始玩家
    initPlayerInfo() {
        // 游戏玩家层操作对象
        for (let i = 0; i < this._gameCore.maxPlayers; ++i) {
            let localPos = i;
            if (this._gameCore.maxPlayers == 2 && i == 1) {//2人场固定坐对家
                localPos = 3;
            }

            let player = this.LayerItems.players.getChildByName('playerItem' + i);
            if (player) {
                player.removeFromParent();
            }
            player = cc.instantiate(this.playerItemPrefab);
            this.LayerItems.players.addChild(player);
            player.name = 'playerItem' + i;
            player.position = Constant.player_pos_list[localPos];
            player.active = false;
            let playerLayer = player.getComponent(RMPlayerLayer);
            playerLayer.showDropText(false);
            playerLayer.clearData();
            this._allPlayers.push(playerLayer);
            playerLayer.setLocalSeatId(i);
        }

        this.init_banker_data();
    }


    //初始化一些牌信息
    initCardInfo() {
        RMCardPool.instance.init(this.cardItemPrefab);

        //手牌
        for (let i = 0; i < Constant.max_hand_card_count; i++) {
            let card = RMCardPool.instance.get();
            card.name = "viewCard";
            this.LayerItems.viewCardParent.addChild(card);
            card.active = false;
            card.position = this.LayerItems.card_mid.position;
            card.scale = Constant.card_scale_other;
            this.selfHandCardList.push(card);
            this.addCardListener(card);
        }

        //分组类型 先绘制 最多六组
        let maxGroup = this.max_group;
        this.groupTypeNodeList.length = maxGroup;
        for (let i = 0; i < maxGroup; i++) {
            this.groupTypeNodeList[i] = cc.instantiate(this.groupItemPrefab);
            this.LayerItems.viewCardParent.addChild(this.groupTypeNodeList[i]);
            this.groupTypeNodeList[i].active = false;
            this.groupTypeNodeList[i].getComponent(RMGroupItem).setNodeIndex(i);
        }


        //底下固定牌堆
        let card_pool_num = 6;
        for (let i = 0; i < card_pool_num; i++) {
            let card = RMCardPool.instance.get();
            this.LayerItems.card_pool_nodes.addChild(card);
            card.x = -15 + i * 3;
            card.active = true;
            this.cardPoolBottomList.push(card);
        }
        this.LayerItems.card_pool_nodes.active = false;
        this.LayerItems.card_pool_nodes.zIndex = 2;

        //增加显示的癞子牌
        this.showLaiziCard = RMCardPool.instance.get();
        this.LayerItems.cardposNode.addChild(this.showLaiziCard);
        this.showLaiziCard.name = 'showLaiziCard';
        this.showLaiziCard.scale = Constant.card_scale_other;
        this.showLaiziCard.position = this.LayerItems.card_mid.position;
        this.showLaiziCard.active = false;
        this.showLaiziCard.zIndex = 9;

        //增加弃牌堆牌顶的牌
        this.upCard = RMCardPool.instance.get();
        this.LayerItems.cardposNode.addChild(this.upCard);
        this.upCard.name = 'upCard';
        this.upCard.scale = Constant.card_scale_other;
        this.upCard.position = this.LayerItems.card_right.position;
        this.upCard.active = false;
        this.upCard.zIndex = 10;

        //移动牌时，如果可丢弃到牌堆 临时显示一下
        this.upCardTemp = RMCardPool.instance.get();
        this.LayerItems.cardposNode.addChild(this.upCardTemp);
        this.upCardTemp.name = 'upCardTemp';
        this.upCardTemp.scale = Constant.card_scale_other;
        this.upCardTemp.position = this.LayerItems.card_right.position;
        this.upCardTemp.getComponent(RMCardItem).showCardshadow1(true);
        this.upCardTemp.active = true;
        this.upCardTemp.zIndex = 102;

        //增加show card 位置的牌
        this.showCard = RMCardPool.instance.get();
        this.LayerItems.cardposNode.addChild(this.showCard);
        this.showCard.scale = Constant.card_scale_other;
        this.showCard.name = "showCard";
        this.showCard.position = this.LayerItems.card_mid.position;
        this.showCard.zIndex = 10;
        this.showCard.active = false;

        //初始化结算界面
        this.settleNode = cc.instantiate(this.settlePrefab);
        this.settleNode.getComponent(RMSettleLayer).initUI();
        this.settleNode.zIndex = 100;
        this.settleNode.active = false;
        this.LayerItems.resultNode.addChild(this.settleNode);

        this.is_server_return = false;

        // 界面通知
        this.ui_sort_notice = 0;
    }


    start() {
        this._audioMng.playMusic();
        let sizeX = (cc.winSize.width - cc.Canvas.instance.designResolution.width) / 2;
        let sizeY = (cc.winSize.height - cc.Canvas.instance.designResolution.height) / 2;
        this._menuLeftOrgPos = cc.v2(this.LayerItems.left.position.x + this.LayerItems.left.getContentSize().width - sizeX, this.LayerItems.left.position.y);
        this._menuRightOrgPos = cc.v2(this.LayerItems.right.position.x - this.LayerItems.right.getContentSize().width + sizeX, this.LayerItems.right.position.y);
        this._topOrgPos = cc.v2(this.LayerItems.top.position.x, this.LayerItems.top.position.y - this.LayerItems.top.getContentSize().height + sizeY);
        this._bottomOrgPos = cc.v2(this.LayerItems.bottom.position.x, this.LayerItems.bottom.position.y + this.LayerItems.bottom.getContentSize().height - sizeY);
        this._sortOrgPos = cc.v2(this.LayerItems.sortBtn.position.x - this.LayerItems.sortBtn.getContentSize().width + sizeX, this.LayerItems.sortBtn.position.y);
        this.moveUiAni();
    }

    //UI渐入
    moveUiAni() {
        cc.tween(this.LayerItems.top)
            .to(0.5, { position: this._topOrgPos }, { easing: "sineOut" })
            .start()

        cc.tween(this.LayerItems.bottom)
            .to(0.5, { position: this._bottomOrgPos }, { easing: "sineOut" })
            .start()

        cc.tween(this.LayerItems.sortBtn)
            .to(0.5, { position: this._sortOrgPos }, { easing: "sineOut" })
            .start()
    }

    onDestroy() {
        this.unscheduleAllCallbacks();

        this.cardPoolBottomList.forEach(item => {
            RMCardPool.instance.put(item);
        })

        this.selfHandCardList.forEach(item => {
            RMCardPool.instance.put(item);
        })

        RMCardPool.instance.put(this.showLaiziCard);
        RMCardPool.instance.put(this.upCard);
        RMCardPool.instance.put(this.showCard);
        RMCardPool.instance.put(this.upCardTemp);

        RMCardPool.instance.clear();
        EventManager.instance.off(GameEvent.ADD_CARDS_GROUP_HERE, this.onAddCardsGroupHere, this);
        EventManager.instance.off(GameEvent.CLICK_HEAD_SHOW_GIFT, this.onShowGift, this);

        clearInterval(this.interval_Id);
        this.interval_Id = null;
    }

    // 重置所有界面数据
    public resetAllUI() {
        this._curAwaitSeatId = -1;
        this.canTouchCard = false;
        this.selected_group_cards = [];
        this.cardGroups = [];
        this.groupTypeList = [GroupType.NUMBER, GroupType.NUMBER, GroupType.NUMBER, GroupType.NUMBER, GroupType.NUMBER, GroupType.NUMBER];
        this.clickState = ClickShowCardState.click;
        this.isShowCardsRecord = false;
        this.isShowHelp = [false, false, false];
        this.upCardTemp.active = false;
        this.isPlayingSitSound = false;
        this.is_hand_card_init = false;
        this.is_server_return = false;
        this.last_droped_card_info = {color: -1, number: -1};

        // 界面通知
        this.ui_sort_notice = 0;
        
        for(let i = 0; i < this._allPlayers.length; i++){
            let palyer = this._allPlayers[i];
            palyer.resetBase();
            if(this._gameCore.bankerLocalId == palyer.getLocalSeatId()){
                palyer.showBankerFlag(true);
            }
        }

        // 清除位置列表
        // this._mapSeatPlayers.clear();
        this._gameCore.resetData();

        this.clearUI();
        this.resetCardsInfo()
    }

    private init_banker_data(){
        // 初始化每个玩家的庄家牌Node
        for(let i = 0; i < Constant.player_count; i++){
            let banker_card = this.LayerItems.cardposNode.getChildByName("banker_card_" + i);
            if (!banker_card){
                let node = RMCardPool.instance.get();
                node.name = "banker_card_" + i;
                let local_pos = this.LayerItems.cardposNode.convertToNodeSpaceAR(this._allPlayers[i].headWorldPos())
                node.setPosition(local_pos);
                if (i == 0){
                    node.scale = 1.1;
                    node.setPosition(local_pos.x + 60, local_pos.y + 200);
                } else {
                    node.scale = 0.7;
                    if (i % 5 == 1){
                        node.setPosition(local_pos.x - 100, local_pos.y - local_pos.y / 2);
                    } else if (i % 5 > 1){
                        node.setPosition(local_pos.x, local_pos.y - 140);
                    } else {
                        node.setPosition(local_pos.x + 130, local_pos.y - local_pos.y / 2);
                    }
                }
                
                node.zIndex = 1000;
                node.active = false;
                let light = cc.instantiate(this.LayerItems.light_right);
                light.active = false;
                light.name = "banker_light";
                light.scale = 1.0;
                light.zIndex = 1010;
                node.addChild(light);
                this.banker_card_node.push(node);
                this.LayerItems.cardposNode.addChild(this.banker_card_node[i]);
            }
        }
    }

    //清理桌子
    public clearUI() {
        this.LayerItems.viewCardParent.children.forEach((item) => {
            item.active = false;
        })

        this.isShowWaitStartTips(false);
        this.LayerItems.tableInfo.active = false;
        this.LayerItems.cardNode.active = false;
        this.runArrowAndLightAni(false);
        this.LayerItems.descNode.active = false;
        this.LayerItems.turnTips.active = false;
        this.LayerItems.btn_cur_record.active = false;
        this.LayerItems.cur_recordNode.active = false;
        this.LayerItems.tipsNode.stopAllActions();
        this.LayerItems.tipsNode.active = false;
        this.LayerItems.txt0_true.active = false;
        this.LayerItems.txt1_true.active = false;
        this.LayerItems.txt2_true.active = false;
        this.isShowDeclareTips(false);
        this.LayerItems.declarConfirmTips.active = false;
        this.LayerItems.prompt.active = false;
        this.setBtnDiscardState(false);
        this.setBtnDropState(false);
        this.setBtnFinishState(false);
        this.setBtnGroupState(false);
        if (this.settleNode) {
            this.settleNode.active = false;
        }
    }

    // 获取所有玩家对象
    public get_all_player(){
        return this._allPlayers;
    }

    // 玩家离开
    public user_level(){
        // 重置玩家界面
        for (let i = 0; i < this._allPlayers.length; ++i) {
            if (i != Constant.self_local_pos) {
                let playerLayer = this._allPlayers[i];
                if (playerLayer.node.active) {
                    playerLayer.clearData();
                }
                playerLayer.node.active = false;
            }
        }
    }

    //还原牌数据
    resetCardsInfo() {
        for (let i = 0; i < this.selfHandCardList.length; i++) {
            let card = this.selfHandCardList[i];
            card.getComponent(RMCardItem).resetUI();
            card.position = this.LayerItems.card_mid.position;
            card.scale = Constant.card_scale_other;
            card.zIndex = i + 1;
        }

        for (let i = 0; i < this.max_group; i++) {
            this.groupTypeNodeList[i].getComponent(RMGroupItem).resetUI();
            this.groupTypeNodeList[i].getComponent(RMGroupItem).setNodeIndex(i);
        }

        this.LayerItems.card_pool_nodes.position = cc.v2(0, 0);
        this.LayerItems.card_pool_nodes.active = false;
        this.LayerItems.card_pool_nodes.zIndex = 2;

        this.showLaiziCard.scale = Constant.card_scale_other;
        this.showLaiziCard.position = this.LayerItems.card_mid.position;
        this.showLaiziCard.active = false;
        this.showLaiziCard.zIndex = 9;

        this.upCard.scale = Constant.card_scale_other;
        this.upCard.position = this.LayerItems.card_right.position;
        this.upCard.active = false;
        this.upCard.zIndex = 10;

        this.showCard.scale = Constant.card_scale_other;
        this.showCard.position = this.LayerItems.card_mid.position;
        this.showCard.zIndex = 10;
        this.showCard.active = false;
    }

    // 开始重置
    public resetStart() {
        this.clearUI();

        this._mapSeatPlayers.forEach((value: any, key: number) => {
            value.reset();
            value.switchToGaming();
        });

        this._allPlayers.forEach((value: any, key: number) => {
            if (!this.hasSeatPlayer(key)) {
                value.clearData();
                value.node.active = false;
            }
        });
    }
    //////////////////////////////////////////////////////////////////////////////
    // 业务操作相关接口
    //////////////////////////////////////////////////////////////////////////////
    // 指定的位置上是否有玩家 
    public hasSeatPlayer(localSeatId: number): boolean {
        if (this._mapSeatPlayers.has(localSeatId)) {
            return true;
        }
        return false;
    }

    // 设置玩家为游戏中
    public setPlayerGaming(localSeatId: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.setGameing();
        }
    }
    
    // 设置玩家为等待
    public setPlayerAwaiting(localSeatId: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.clearData();
        }
    }

    /** 切换等待用户 */
    public changeAwait(seatId: number, waittime: number, optsumtime: number, is_local_seatid: boolean = false) {
        //判断是否自己操作
        let localSeatId = 0;
        let my_seat_id = this._gameCore.getLocalSeatId(this._gameCore.mySeatId);
        if(is_local_seatid){
            localSeatId = seatId;
        } else {
            localSeatId = this._gameCore.getLocalSeatId(seatId);
        }
         
        if(my_seat_id == localSeatId) {
            this.user_operate_status = UserOperateState.waitGetCard;
            this.runArrowAndLightAni(true);
            this.setBtnDropState(true);
            // 我自己操作，显示提示
            this.LayerItems.turnTips.active = true;
        } else {
            this.runArrowAndLightAni(false);
            this.user_operate_status = UserOperateState.otherOperate;
            this.setBtnDropState(false);
        }

        let prevPlayerLayer = this._mapSeatPlayers.get(this._curAwaitSeatId);
        if (prevPlayerLayer) {
            prevPlayerLayer.switchToAwait(false, waittime, 0);
        }

        this._curAwaitSeatId = localSeatId;
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.switchToAwait(true, waittime, optsumtime);
        }
    }

    /** 切换自己不可操作 */
    public cancelAwait(localSeatId: number, opt: boolean) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.switchToAwait(false, 0, 0,);
        }
    }

    // 更新玩家的金币
    public updatePlayerMoney(localSeatId: number, money: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.updateMoney(money);
        }
    }

    // 游戏结算
    public game_end(data){
        // 显示结算界面癞子牌
        this.settleNode.getComponent(RMSettleLayer).setJokerNodeData(this.laizi_card);
        // this.doSettlement(localSeatId, amount)
        let self = this;
        let player_data = data["conclude"];

        for (let key in player_data) {
            let tInfo = player_data[key];
            let seatid = Common.toInt(tInfo["seatid"]);
            let localSeatId = this._gameCore.getLocalSeatId(seatid);
            let amount = Common.toInt(tInfo["amount"]);

            let prevPlayerLayer = this._allPlayers[this._curAwaitSeatId];
            if (prevPlayerLayer) {
                prevPlayerLayer.switchToAwait(false, 0, 0);
            }
            let PlayerLayer = this._allPlayers[localSeatId];

            if (!PlayerLayer){
                cc.error("winPlayer is not found!");
                return;
            }

            PlayerLayer.showSettleNodeInfo(true, amount, function() {
                let lenght = Object.getOwnPropertyNames(player_data).length
                if (Common.toInt(key) == lenght){
                    self.showSettleLayer(player_data);
                }
            });
        }
    }

    // 处理游戏结算
    public doSettlement(playerLocalSeatId: any, amount: any) {
        
    }

    // 弃牌，显示弃牌信息
    public user_drop_info(local_seatid, amount = 0){
        for(let i=0; i < this._allPlayers.length; i++){
            let player = this._allPlayers[i].getComponent(RMPlayerLayer);
            if(player.getLocalSeatId() == local_seatid){
                player.showDropText(true);
                if (amount > 0){
                   player.showSettleNodeInfo(true, amount)
                } else {
                    player.showSettleNodeInfo(true)
                }
            }
        }
    }


    // 处理暂离回来
    public doZanLiComback() {
        // this.maskNode.active = false;
    }

    public doRoomIntertChat(sendLocalSeatId, recLocalSeatId, content) {
        let sendPlayerLayer = this._mapSeatPlayers.get(sendLocalSeatId);
        if (!sendPlayerLayer) return;

        let recPlayerLayer = this._mapSeatPlayers.get(recLocalSeatId);
        if (!recPlayerLayer) return;

        let startPos = cc.v3(this.gameEmojiNode.convertToNodeSpaceAR(sendPlayerLayer.headWorldPos()));
        let endPos = cc.v3(this.gameEmojiNode.convertToNodeSpaceAR(recPlayerLayer.headWorldPos()));

        UIHelper.playInteractExpression(this.gameEmojiNode, startPos, endPos, content, recPlayerLayer.playerData.userDirection == Direction.LEFT);
    }

    //显示道具
    onShowGift(info) {
        let playerInfo = {
            sendPlayerid: this._gameCore.playerid, receiverPlayerid: info.playerid,
            singlechatfee: this._gameCore.singlechatfee
        };
        let self = this;
        UIHelper.showInteractExpression(this.gameEmojiNode, info.showPos, playerInfo, (info: any) => {
            self._gameCore.sendChatMessage(info);
        });
    }


    //////////////////////////////////////////////////////////////////////////////
    // 界面操作相关接口

    // 显示玩家信息
    public showPlayer(localSeatId: number, userInfo: any) {
        if (localSeatId < 0) {
            console.error(`RMGameView.showPlayer: invalid local seat. playerid: ${userInfo.playerid}, localSeat: ${localSeatId}`);
            return;
        }

        let playerLayer = this._allPlayers[localSeatId];
        if (playerLayer) {
            let self = this;
            if(!this.isPlayingSitSound){
                this.isPlayingSitSound = true;
                this._audioMng._loadPlaySFX(`res/sound/sitDown`);
                this.scheduleOnce(()=>{
                    self.isPlayingSitSound = false;
                },0.2)
            }
            
            this._mapSeatPlayers.set(localSeatId, playerLayer);
            playerLayer.setUserInfo(userInfo, localSeatId, this._gameCore.maxPlayers);
            // if (this._gameCore.roomState == RoomState.GAME || this._gameCore.gameState == GameState.EM_TEENPATTI_GAMESTATE_END) {
            playerLayer.resetBase();
            // } else {
            //     playerLayer.reset();
            // }
        }
    }

    // 获取金币字符串 count: 0不加K; 1-n: 超过指定位数时加上K.  decimals: 显示小数位数
    moneyFormat(money: number, decimals: number = 0): string {
        return (money / Config.SCORE_RATE).toFixed(decimals);
    }

    // 隐藏玩家信息
    public hidePlayer(localSeatId: number) {
        if (!this._mapSeatPlayers) {
            console.error(`RMGameView.hidePlayer: invalid mapSeatPlayers. localSeat: ${localSeatId}`);
            return;
        }
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.clearData();
            if (this._gameCore.roomState == RoomState.GAME) {
                playerLayer.exitInGaming();
            }
        }
        this._mapSeatPlayers.delete(localSeatId);
    }

    // 显示玩家的庄家标记
    public showPlayerBankerFlag(localSeatId: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.showBankerFlag(true);
        }
    }

    //字符格式化
    public stringFormat(strContent: string, params: any[]): string {
        var str = strContent;
        for (var i = 0; i < params.length; i++) {
            var reg = new RegExp("\\{" + i + "\\}", "gm");
            str = str.replace(reg, params[i]);
        }
        return str;
    }

    //是否显示其它玩家胡牌提示
    isShowOtherDeclareTips(isShow: boolean, tipsName: string = '') {
        this.LayerItems.otherDeclareTips.active = isShow;
        if (isShow) {
            let txt = this.LayerItems.otherDeclareTips.getChildByName('txt').getComponent(cc.Label);
            txt.string = this.stringFormat(GameTextTips.other_declare, [tipsName]);
        }
    }

    //是否显示等待开始
    isShowWaitStartTips(isShow: boolean, txtTipsType: showTipsType = showTipsType.wait_other, time: number = 0, tipsName: string = '') {
        let curTime = time;
        let show = this.LayerItems.waitStartNode.active;
        this.LayerItems.waitStartNode.active = isShow;
        let timeTxt = this.LayerItems.waitStartNode.getChildByName('txt').getComponent(cc.Label);
        timeTxt.node.stopAllActions();
        let self = this;

        if (!this.LayerItems.waitStartNode.active) {
            return;
        }

        if (txtTipsType == showTipsType.wait_other) {
            timeTxt.string = GameTextTips.wait_other;
            if (show) {
                self.LayerItems.waitStartNode.active = false; //有玩家进入时，该提示闪烁一下
                this.scheduleOnce(() => {
                    self.LayerItems.waitStartNode.active = true;
                }, 0.1)
            }
            return;
        }

        if (txtTipsType == showTipsType.play_first) {
            timeTxt.string = self.stringFormat(GameTextTips.play_first, [tipsName]);
            return;
        }

        if (time > 0) {
            timeTxt.string = self.stringFormat(GameTextTips.wait_start, [time]);
            cc.tween(timeTxt.node)
                .delay(1)
                .call(() => {
                    curTime -= 1;
                    if (curTime <= 0) {
                        timeTxt.node.stopAllActions();
                        self.LayerItems.waitStartNode.active = false;
                    }
                    else {
                        timeTxt.string = self.stringFormat(GameTextTips.wait_start, [curTime]);
                    }
                })
                .union()
                .repeatForever()
                .start()
        }
    }

    //匹配
    public onStartMatch() {
        let myscore = this._gameCore.userInfo.money;
        let inmoney = this._gameCore.inmoney;
        if (this._gameCore.isKicked || myscore < inmoney) {
            this.onClickBack();
            return;
        }
        this._gameCore.sendHuanZhuoMessage();
    }

    //箭头及灯光动画
    runArrowAndLightAni(isShow: boolean) {
        this.LayerItems.arrowNode.active = isShow;
        this.LayerItems.arrowNode.stopAllActions();
        this.LayerItems.light_left.active = isShow;
        this.LayerItems.light_left.stopAllActions();
        this.LayerItems.light_right.active = isShow;
        this.LayerItems.light_right.stopAllActions();

        if (isShow) {
            cc.tween(this.LayerItems.arrowNode)
                .to(0.5, { position: cc.v2(0, 10) })
                .to(0.5, { position: cc.v2(0, 0) })
                .union()
                .repeatForever()
                .start()

            cc.tween(this.LayerItems.light_left)
                .then(cc.fadeOut(0.5))
                .then(cc.fadeIn(0.5))
                .union()
                .repeatForever()
                .start();

            cc.tween(this.LayerItems.light_right)
                .then(cc.fadeOut(0.5))
                .then(cc.fadeIn(0.5))
                .union()
                .repeatForever()
                .start();
        }
    }

    public getGameCard() {
        let card = this.selfHandCardList[this.selfHandCardList.length - 1];
        if (card) {
            card.active = true;
            // card.stopAllActions();
            card.scale = 1;
        } else {
            card = RMCardPool.instance.get();
            card.name = "viewCard";
            this.LayerItems.viewCardParent.addChild(card);
        }
        return card;
    }

    //发牌
    private sendCards(cardDataList: CardData[], laiziCard, rightCard) {
        let startPos = Math.floor(cardDataList.length / 2) * Constant.cardSpaceX;
        let remainTime = 1.5;

        let card_count = Constant.self_hand_cards_num;
        let left_count = card_count;
        let self = this;
        for (let i = 0; i < card_count; i++) {
            let index = card_count- 1 - i;
            let card = this.selfHandCardList[index];
            card.active = true;
            // card.stopAllActions();
            cc.tween(card)
                .delay(i * 0.05)
                .call(() => {
                    if (i % 2 == 0) {
                        self._audioMng._loadPlaySFX(`res/sound/deal_card`);
                    }
                })  
                .parallel(
                    cc.tween().then(cc.moveTo(0.65 * remainTime, cc.v2(startPos + Math.abs(i * Constant.cardSpaceX), Constant.self_cards_pos_y))
                    .easing(cc.easeBackInOut())),
                    cc.tween().then(cc.scaleTo(0.65, 1)),
                    cc.tween().then(
                        cc.sequence(
                            cc.delayTime(0.6 * 0.65 * remainTime),
                            cc.callFunc(function () {
                                card.zIndex = i + 1;
                                card.getComponent(RMCardItem).index = cardDataList[i].index;
                            })
                        )),
                )
                .call(function () {
                    card.getComponent(RMCardItem).flip(0.2 * remainTime,  0.13 * remainTime, cardDataList[i]);
                    if (i % 3 == 0) {
                        self._audioMng._loadPlaySFX(`res/sound/card_deal_3`);
                    }
                    
                    left_count --;
                    if(left_count==0) {
                        self.event_after_send_cards(laiziCard, rightCard);
                    }
                })
                .start()
        }
    }

    private event_after_send_cards(laiziCard, rightCard) {
        let self = this;
        // 最后一张牌完成动画后，需要执行
        self.LayerItems.descNode.active = false;
        self.LayerItems.turnTips.active = false;
        // 牌堆
        self.LayerItems.card_pool_nodes.position = self.LayerItems.card_mid.getPosition();
        self.LayerItems.card_pool_nodes.active = true;
        // 
        self.LayerItems.btn_cur_record.active = false;

        self._gameLogic.setJoker(laiziCard);
        self.sendCardRightTopCard(rightCard);
        self.sendCardLeftLaiziCard(laiziCard);
        
        self.is_hand_card_init = true;

        if (self.isSort){
            self.start_server_sort(false, false);
        }
        
        setTimeout(function() {
            if (self.isSort){
                self.update_ui_notice();
            } else {
                // 显示
                self.reshow_group(false);
            }
        }, 1000);
    }

    //右边获取牌的第一张牌
    sendCardRightTopCard(cardData: CardData) {
        let card = this.upCard
        let time = 0.3
        let self = this;
        // card.stopAllActions();
        cc.tween(card)
            .call(() => {
                card.zIndex = 101;
            })
            .delay(2.0)
            // .to(0.5,{opacity:255})
            .parallel(
                cc.tween().by(time, { position: cc.v2(15, 15) }, { easing: (dt: number) => cc.easeIn(1.0).easing(dt) }),
                cc.tween().then(cc.scaleTo(time, 1.1)),
                cc.tween().by(time, { angle: 10 })
            )
            .call(() => {
                self.showLaiziCard.active = true;
                card.active = true;
            })
            .delay(time)
            .parallel(
                cc.tween().to(time, { position: cc.v2(self.LayerItems.card_right.position.x + 15, self.LayerItems.card_right.position.y + 15) }, { easing: (dt: number) => cc.easeIn(1.0).easing(dt) }),
                cc.tween().then(cc.scaleTo(time, 0.85)),
                cc.tween().by(time, { angle: -10 })
            )
            .call(() => {
                self._audioMng._loadPlaySFX(`res/sound/flip_open`);
                card.getComponent(RMCardItem).runFlipOneCardAni(card, false, false, cardData)
            })
            .delay(0.5)
            .parallel(
                cc.tween().to(0.1, { angle: 0 }),
                cc.tween().by(0.1, { position: cc.v3(-15, -15) }, { easing: (dt: number) => cc.easeIn(0.5).easing(dt) }),
                cc.tween().then(cc.scaleTo(0.1, Constant.card_scale_other))
            )
            .start()
    }

    //左边牌的癞子牌
    sendCardLeftLaiziCard(cardData: CardData) {
        let card = this.showLaiziCard;
        let time = 0.25
        let self = this;
        // card.stopAllActions();
        cc.tween(card)
            .call(() => {
                card.zIndex = 100;
            })
            .delay(2.0)
            .parallel(
                cc.tween().by(time, { position: cc.v2(15, 15) }, { easing: (dt: number) => cc.easeIn(1.0).easing(dt) }),
                cc.tween().then(cc.scaleTo(time, 1.1)),
                cc.tween().by(time, { angle: 10 })
            )
            .call(() => {
                card.getComponent(RMCardItem).runFlipOneCardAni(card, false, false, cardData);
                self.scheduleOnce(()=>{
                    card.getComponent(RMCardItem).showCardLight(true);    
                    self._audioMng._loadPlaySFX(`res/sound/wild_card`);
                },0.4)
            })
            .delay(0.5)
            .parallel(
                cc.tween().to(time, { angle: 0 }),
                cc.tween().to(time, { position: cc.v2(self.LayerItems.card_left.position.x - 54, self.LayerItems.card_left.position.y) }, { easing: (dt: number) => cc.easeIn(0.5).easing(dt) }),
                cc.tween().then(cc.scaleTo(time, Constant.card_scale_other))
            )
            .delay(0.2)
            .call(() => {
                card.zIndex = 1;
                card.getComponent(RMCardItem).showCardLaizi(true);
                card.getComponent(RMCardItem).showCardshadow(true);
                card.getComponent(RMCardItem).showCardLight(false);
                self.LayerItems.card_pool_nodes.stopAllActions();
                cc.tween(self.LayerItems.card_pool_nodes)
                    .call(()=>{
                        self.scheduleOnce(()=>{
                            self._audioMng._loadPlaySFX(`res/sound/card_deal_5`);
                        },0.15)
                    })
                    .to(time, { position: cc.v2(self.LayerItems.card_left.position.x + 2, self.LayerItems.card_left.position.y) })
                    .start()
            })
            .start()

    }

    private addCardListener(card: cc.Node) {
        card.on(cc.Node.EventType.TOUCH_START, this.onListenCardStart, this);
        card.on(cc.Node.EventType.TOUCH_MOVE, this.onListenCardMove, this);
        card.on(cc.Node.EventType.TOUCH_END, this.onListenCardEnd, this);
        card.on(cc.Node.EventType.TOUCH_CANCEL, this.onListenCardCancel, this);
    }

    private removeCardListener(card: cc.Node) {
        card.off(cc.Node.EventType.TOUCH_START, this.onListenCardStart, this);
        card.off(cc.Node.EventType.TOUCH_MOVE, this.onListenCardMove, this);
        card.off(cc.Node.EventType.TOUCH_END, this.onListenCardEnd, this);
        card.off(cc.Node.EventType.TOUCH_CANCEL, this.onListenCardCancel, this);
    }

    protected onListenCardStart(event: cc.Event.EventTouch) {
        if (!this.canTouchCard) {
            return;
        }
        let target: cc.Node = event.target;
        // target.stopAllActions();

        // 深度拷贝牌组
        this.temp_groups = JSON.parse(JSON.stringify(this.cardGroups));
        this.unselected_temp_groups = JSON.parse(JSON.stringify(this.cardGroups));

        let card_node = target.getComponent(RMCardItem);

        this.touch_move_card_index = card_node.index;

        // 牌的临时显示位置
        this.origin_groups_index = -1;
        this.now_move_groups_index = -1;
        this.origin_groups_cards_index = -1;
        this.now_move_groups_cards_index = -1;
        this.card_moved = false;
        this.card_move_to_other_group = false;
        this.card_discarded = false;
        this.card_draged = false;
    }

    private calculation_new_pos(card_pos) {
        // 默认将當前刪除的牌加入到臨時隊列中，注意：如果當前牌所在的組只有自己一張牌的時候，
        // 那這個組的牌就會被移動為0長度，但是這個組是需要佔半個牌長度的
        // 这里计算出没有这张牌的时候，所有组的位置及牌跟组的相对的位置
        let group_len = this.temp_groups.length;

        // 获取剩余的牌的所有位置右边
        let total_width = 0;
        let group_right_array = [];
        for(let i=0; i<group_len; i++) {
            if(i != group_len-1) {
                total_width += Constant.group_offset_x;
            }

            let card_len = this.temp_groups[i].list.length;
            if(card_len==0) {
                total_width += (Constant.card_width / 2);
            } else {
                total_width += (Constant.card_offset_x * (card_len-1) + Constant.card_width);
            }

            group_right_array.push(total_width);
        }
        // 计算当前移动的牌在第几组的坐标内
        let group_index = -1, card_index = -1;
        let move_card_left = (card_pos.x - (Constant.card_width / 2));
        let start_point_x = 0 - (total_width / 2);
        let const_group_dist = Math.abs(start_point_x);

        // 修正坐标
        for(let i=0; i<group_right_array.length; i++) {
            group_right_array[i] = group_right_array[i] + start_point_x;
        }
        
        if(move_card_left <= start_point_x) {
            // 超出左边缘，直接定义为第一张
            group_index = 0;
            card_index = 0;
        } else if(move_card_left >= const_group_dist) {
            // 超出右边缘，直接定义为最后一张
            group_index = group_len - 1;
            if(this.now_move_groups_index==group_index) {
                card_index = this.temp_groups[group_index].list.length - 1;
            } else {
                card_index = this.temp_groups[group_index].list.length;
            }
        }

        if(group_index==-1 && card_index==-1) {
            let str = '';
            for(let i=0; i<group_len; i++) {
                str += group_right_array[i] + ", ";
                //这里要判断实际的位置
                if(move_card_left<group_right_array[i]) {
                    group_index = i;
                    break;
                }
            }
        }

        // 这里计算牌号，由于移动的牌加入这组，所以这组牌的长度会加长
        if(card_index==-1) {
            let temp_card_len = this.temp_groups[group_index].list.length;
            if(this.now_move_groups_index!=group_index) {
                // 如果不是最早的组，那就需要增加一张牌
                temp_card_len ++;
            }

            let temp_group_width = (temp_card_len - 1) * Constant.card_offset_x + Constant.card_width;
            let card_right_dist = group_right_array[group_index] - move_card_left;
            for(let i=0; i<temp_card_len; i++) {
                let temp_right_dist = temp_group_width - (i * Constant.card_offset_x) - Constant.move_space_for_card_insert;
                if(card_right_dist>temp_right_dist) {
                    card_index = i;
                    break;
                }
            }

            if(card_index==-1) {
                // 最后一位
                card_index = temp_card_len - 1;
            }
        }

        // 计算这张牌在所有牌中的新序号
        let new_index = 0;
        if(group_index>0) {
            for(let i=0; i<group_index; i++) {
                new_index += this.temp_groups[i].list.length;
            }
        }
        new_index += card_index;

        // 将牌把位置移动
        this.temp_groups = JSON.parse(JSON.stringify(this.unselected_temp_groups));
        this.temp_groups[group_index].list.splice(card_index, 0, this.selected_card);

        // 计算新坐标
        let group_width_list = [];
        let total_group_width = 0;
        for(let i=0; i<group_len; i++) {
            if(i>0) {
                // 增加组间隙
                total_group_width += Constant.group_offset_x;
            }

            let group_width = 0;
            let card_len = this.temp_groups[i].list.length;
            if(card_len==0) {
                group_width = Constant.card_width / 2;
            } else {
                group_width = (card_len - 1) * Constant.card_offset_x + Constant.card_width;
            }

            let group_right = total_group_width + group_width;
            group_width_list.push(group_width);

            total_group_width = group_right;
        }
        let v_data = {width: total_group_width, group_width: group_width_list};

        // 用 new_pos去便利臨時隊列，找到符合的點坐標后，就返回該坐標的牌的序號
        return {newpos_data: v_data, group_index: group_index, card_index: card_index, new_index: new_index};
    }

    /** 
     *  当前轮到自己操作， 并且已经抓牌之后， 拖动牌到排列外松手视为自动打出
     */
    protected onListenCardMove(event: cc.Event.EventTouch) {
        if (!this.canTouchCard) {
            // 处理完善动画
            return;
        }

        if (!this.card_draged){
            this.card_draged = true;
            this.last_larger_card = null;
            let count = 0;
            let breakall = false;
            for(let i = 0; i < this.temp_groups.length; i++){
                let group = this.temp_groups[i];
                for(let n = 0; n < group.list.length; n++, count++){
                    if(count == this.touch_move_card_index) {
                        // 把牌从临时牌中抽出来，保存下来剩余的牌
                        this.origin_groups_index = i;
                        this.now_move_groups_index = i;
                        this.origin_groups_cards_index = n;
                        this.now_move_groups_cards_index = n;
                        this.selected_card = group.list[n];
                        
                        // 记录删除的牌
                        this.unselected_temp_groups[i].list.splice(n, 1);

                        let card = this.selfHandCardList[this.touch_move_card_index];
                        cc.tween(card)
                            .to(0.05, { scale: 1.08 }) // 动画持续时间和目标缩放比例
                            .start(); // 开始动画

                        // 找到当前牌，直接结束查找
                        breakall = true;
                        break;
                    }
                }

                if(breakall) {
                    break;
                }
            }
        }

        let touches = event.getTouches();        
        let first_touch_pos = touches[0].getLocation();

        let old_pos = event.target.parent.convertToNodeSpaceAR(touches[0].getPreviousLocation());
        let new_pos = event.target.parent.convertToNodeSpaceAR(first_touch_pos);
        let sub_pos = old_pos.sub(new_pos);

        event.target.x = event.target.x - sub_pos.x;
        event.target.y = event.target.y - sub_pos.y;

        // 判断当前牌的位置
        if(new_pos.y>0 && this.user_operate_status == UserOperateState.gotCard){
            // discard
            if(!this.card_discarded) {
                this.card_discarded = true;
            
                this.upCardTemp.getComponent(RMCardItem).showCardData(event.target.getComponent(RMCardItem).getCardColor(), event.target.getComponent(RMCardItem).getCardValue());
                this.upCardTemp.active = true;
            } else {
                if(this.last_larger_card!=null) {
                    cc.tween(this.last_larger_card)
                        .to(0.2, { scale: 1 }) // 动画持续时间和目标缩放比例
                        .start(); // 开始动画
                    this.last_larger_card = false;
                }
            }
        } else {
            // 卡牌
            if(this.card_discarded && this.user_operate_status == UserOperateState.gotCard && this.upCardTemp.active) {
                this.upCardTemp.active = false;
            }
            this.card_discarded = false;

            // 重新计算拖动牌的位置
            let card_center_pos = {x: event.target.x, y: event.target.y};
            let new_data = this.calculation_new_pos(card_center_pos);

            // 只有在分组改变的时候才处理
            if(new_data.group_index!=this.now_move_groups_index || new_data.card_index!=this.now_move_groups_cards_index) {
                // 牌的分组或顺序有改变
                this.card_moved = true;
                if(new_data.group_index != this.now_move_groups_index) {
                    // 分组有改变
                    this.card_move_to_other_group = true;
                } else {
                    // 分组无改变
                    this.card_move_to_other_group = false;
                }
                this.now_move_groups_index = new_data.group_index;
                this.now_move_groups_cards_index = new_data.card_index;

                // 这里需要重绘手牌信息
                let left_x = 0 - (new_data.newpos_data.width / 2) + (Constant.card_width / 2);
                let hand_card_index = 0;
                let new_pos = 0, pre_pos = -1;

                for(let i=0; i<this.temp_groups.length; i++) {
                    if(i>0) {
                        // 增加组间隙
                        left_x += Constant.group_offset_x;
                    }
                    let group = this.temp_groups[i].list;

                    // let jump_pos = 0;
                    let group_width = new_data.newpos_data.group_width[i];
                    for(let n=0; n<group.length; n++, new_pos++) {
                        if(hand_card_index>=this.selfHandCardList.length) {
                            event.target.zIndex = new_pos * 2 + 1;
                        } else {
                            let card = this.selfHandCardList[hand_card_index];
                            let x = left_x + n * Constant.card_offset_x;

                            let handcard = card.getComponent(RMCardItem);
                            if(i==new_data.group_index && n==new_data.card_index) {
                                // 拖动的手牌不修改位置
                                event.target.zIndex = new_pos * 2 + 1;
                                pre_pos = new_pos;
                            } else {
                                if(handcard.index == this.touch_move_card_index) {
                                    new_pos++;
                                    // 跳过当前牌，因为当前牌是被拖动的牌
                                    hand_card_index++;
                                    card = this.selfHandCardList[hand_card_index];
                                }

                                if(card.active) {
                                    // 其他手牌
                                    if(card.x != x) {
                                        card.setPosition(cc.v2(x, Constant.self_cards_pos_y));
                                    }
                                }
    
                                hand_card_index ++;
                            }
                        }
                    }
                    // 将这组牌的长度加上
                    left_x += group_width;
                }

                // 重画type
                let start_x = 0 - (new_data.newpos_data.width / 2);
                for(let i=0; i<this.groupTypeNodeList.length; i++) {
                    let typeitem = this.groupTypeNodeList[i];
                    if(typeitem.active) {
                        let js = typeitem.getComponent(RMGroupItem);

                        // 计算起点与偏移位置
                        let group_width = new_data.newpos_data.group_width[i];
                        start_x += (group_width / 2);

                        js.showUI(group_width, start_x, i > 0, i < this.cardGroups.length - 1);

                        // 将起点调整到下一个type的起点
                        start_x += (group_width / 2) + Constant.group_offset_x;
                    }
                }

                // 将刚才放大的牌恢复成原大小
                if(this.last_larger_card!=null) {
                    cc.tween(this.last_larger_card)
                        .to(0.2, { scale: 1 }) // 动画持续时间和目标缩放比例
                        .start(); // 开始动画
                }

                // 找到前一张牌
                if(pre_pos>0) {
                    let card = this.selfHandCardList[pre_pos-1];
                    this.last_larger_card = card;
                    cc.tween(card)
                        .to(0.2, { scale: 1.08 }) // 动画持续时间和目标缩放比例
                        .start(); // 开始动画
                }
            } else {
                this.card_moved = false;
                this.card_move_to_other_group = false;
            }
        }
    }

    protected onListenCardEnd(event: cc.Event.EventTouch) {
        if (!this.canTouchCard) {
            return;
        }
        this.onMoveListenEnd(event.target, event.getLocation());
    }

    protected onListenCardCancel(event: cc.Event.EventTouch) {
        if (!this.canTouchCard) {
            return;
        }

        this.onMoveListenEnd(event.target, event.getLocation());
    }

    private onMoveListenEnd(target: cc.Node, Location: cc.Vec2) {
        let self = this;
        // 将刚才放大的牌收回去
        if(this.last_larger_card!=null) {
            cc.tween(this.last_larger_card)
                    .to(0.10, { scale: 1 }) // 动画持续时间和目标缩放比例
                    .start(); // 开始动画
        }

        if(this.upCardTemp.active){
            this.upCardTemp.active = false;
        }

        if(this.card_discarded) {
            // discard
            this.clearClickGroups();
            this.selected_group_cards.push(target.getComponent(RMCardItem).index);
            if(this.upCardTemp.active){
                this.upCardTemp.active = false;
            }
            this.onClickButtonDiscard();
            return;
        } 

        if(this.card_draged) {
            // 牌被拖动过
            if(this.origin_groups_index!=this.now_move_groups_index) {
                // 牌移动过位置
                if(this.isSort) {
                    // 关闭自动排序
                    this.isSort = false;
                    this.LayerItems.sortSel.active = false;
                }

                // 清除已经点击的数据
                this.clearClickGroups();

                // 删除空组
                for(let i=this.temp_groups.length-1; i>=0; i--) {
                    if(this.temp_groups[i].list.length==0) {
                        this.temp_groups.splice(i, 1);
                    }
                }

                // 将牌还原成原始大小
                cc.tween(target)
                        .to(0.1, { scale: 1 })
                        .call(() => {
                            self.card_controller(self.temp_groups, true, true, true, function(){
                                self.canTouchCard = true;

                                // 将分组数据提交到服务器
                                self.sort_to_server();
                            });
                        })
                        .start();
            } else {
                // 这里拖动过牌，直接复原
                // 牌的顺序没改变
                // 将牌还原成原始大小
                cc.tween(target)
                        .to(0.1, { scale: 1 })
                        .call(() => {
                            self.card_controller(self.cardGroups, false, true, true, function(){
                                self.canTouchCard = true;
                            });
                        })
                        .start();
            }
        } else {
            // 这里判断这个牌是否加入到selected列表中了
            this.checkCardGroups(target);
        }

        this.set_btn_state();
    }

    private set_btn_state() {
        if (this.user_operate_status == UserOperateState.gotCard){
            if(this.selected_group_cards.length >= 2){
                this.setAddHereInfo(true);
                this.setBtnGroupState(true);
                this.setBtnDiscardState(false);
                this.setBtnFinishState(false);
            } else if (this.selected_group_cards.length == 1){
                this.setAddHereInfo(true);
                this.setBtnGroupState(false);
                this.setBtnDiscardState(true);
                this.setBtnFinishState(true);
            } else {
                this.setAddHereInfo(false);
                this.setBtnGroupState(false);
                this.setBtnDiscardState(false);
                this.setBtnFinishState(false);
            }
        } else if(this.user_operate_status == UserOperateState.otherOperate 
                    || this.user_operate_status == UserOperateState.waitGetCard 
                    || this.user_operate_status == UserOperateState.disCard) {
            if(this.selected_group_cards.length >= 2){
                this.setAddHereInfo(true);
                this.setBtnGroupState(true);
                this.setBtnDiscardState(false);
                this.setBtnFinishState(false);
            } else if(this.selected_group_cards.length==1) {
                this.setAddHereInfo(true);
                this.setBtnGroupState(false);
                this.setBtnDiscardState(false);
                this.setBtnFinishState(false);
            } else {
                this.setAddHereInfo(false);
                this.setBtnGroupState(false);
                this.setBtnDiscardState(false);
                this.setBtnFinishState(false);
            }
        } else {
            this.setAddHereInfo(false);
                this.setBtnGroupState(false);
                this.setBtnDiscardState(false);
                this.setBtnFinishState(false);
        }
    }

    //设置牌addhere点击信息
    setAddHereInfo(groupState1: boolean) {
        this.groupTypeNodeList.forEach(item => {
            item.getComponent(RMGroupItem).isShowAddHere(groupState1);
        })
    }

    /** 设置分组和出牌状态 */
    private setButtonGroupAndDiscardState(groupState: boolean, discardState: boolean) {
        this.setBtnGroupState(groupState);
        this.setBtnDiscardState(discardState);
        this.setBtnFinishState(discardState);
    }


    /** 检测出牌情况 */
    private checkCardGroups(target: cc.Node) {
        let card_index = target.getComponent(RMCardItem).index;
        let found_idx = -1;
        for(let i=0; i<this.selected_group_cards.length; i++) {
            if(this.selected_group_cards[i]===card_index) {
                found_idx = i;
                break;
            }
        }

        if(found_idx>=0) {
            // 找到了，删除
            this.selected_group_cards.splice(found_idx, 1);

            cc.tween(target)
                .to(0.1, { y: Constant.self_cards_pos_y })
                .start();
            this._audioMng._loadPlaySFX('res/sound/rummyCardDeselect')
        } else {
            // 没有找到
            this.selected_group_cards.push(card_index);

            let onMoveToUP = Constant.self_cards_pos_y + 33;
            cc.tween(target)
                .to(0.1, { y: onMoveToUP })
                .start();
            this._audioMng._loadPlaySFX('res/sound/rummyCardSelect')
        }

    }

    /** 点击展示牌 
     *  点击 Finish
    */
    private onClickButtonShow() {
        if (this.user_operate_status != UserOperateState.gotCard) {
            return;
        }
        
        //需要检测丢弃这张牌之后是否能赢  如果能赢 将直放到 show hero 位置 不能就弹出确认
        if (this.selected_group_cards.length == 0 || this.selected_group_cards.length > 1) {
            console.warn("当前show错误");
            return;
        }
        
        let card_info = this.get_touch_card_index();
        if(card_info==null) {
            console.warn("当前show错误");
            return;
        }

        this.isShowDeclareTips(true);
        // this.isShowOtherDeclareTips(true);
        this._gameCore.sendFinishMsg(card_info.cardData, card_info.group_index);
        this.discardtoShowhere();
        this.setBtnDiscardState(false);
        this.setBtnFinishState(false);
        this.setBtnDropState(false);
        this.setAddHereInfo(false);
    }

    /** 清除或者取消需要分组的牌 */
    private clearClickGroups() {
        // 清空数组
        if(this.selected_group_cards.length>0) {
            for(let i=0; i<this.selfHandCardList.length; i++) {
                let target = this.selfHandCardList[i];
                for(let n=0; n<this.selected_group_cards.length; n++) {
                    if(target.getComponent(RMCardItem).index==this.selected_group_cards[n]) {
                        // 把提起来的牌恢复原位
                        target.position.y = Constant.self_cards_pos_y;
                    }
                }
            }
            
            this._audioMng._loadPlaySFX('res/sound/rummyCardDeselect');
            this.selected_group_cards.length = 0;
        }
    }

    // 我自己Finish
    public my_finish(data: any){
        // 处理finish后显示declare按钮
        this.isShowOtherDeclareTips(false);
        this.LayerItems.turnTips.active = false;
        let time = data["declareTime"] / 1000;
        this.isShowDeclareTips(true, time);
    }

    // 其他人declare
    public otherDeclare(data){
        let declare_seatid = this._gameCore.getLocalSeatId(data.optseatid);
        this.LayerItems.turnTips.active = false;
        
        if (this._gameCore.mySeatId != data.optseatid){
            // 显示自己的declare提示和declare操作按钮
            this.isShowDeclareTips(true, data["time"]);
        }

        // 所有人显示的操作
        for(let i = 0; i < this._allPlayers.length; i++){
            let local_seatid = this._allPlayers[i].getLocalSeatId();
            if(local_seatid != declare_seatid){
                console.error("显示其他人declare:", data)
                this.changeAwait(local_seatid, data["time"], data["time"], true)    
            }
        }
    }

    /** 打出当前选中的牌到 showcard 区域 */
    private discardtoShowhere() {

        if (this.selected_group_cards.length==0 || this.selected_group_cards.length > 1) {
            cc.warn("当前准备show card 的牌数错误");
        }
        let showCard = this.showCard;
        let card = this.selfHandCardList[this.touch_move_card_index];
        card.stopAllActions();
        this.clearClickGroups();

        //移动牌到 show here 区域
        let endPos = showCard.position;

        let card_item = card.getComponent(RMCardItem);
        let cardData = {color: card_item.getCardColor(), number: card_item.getCardValue()};

        let self = this;
        // card.stopAllActions();
        cc.tween(card)
            .then(
                cc.spawn(
                    cc.moveTo(0.2, cc.v2(endPos.x, endPos.y)),
                    cc.scaleTo(0.2, Constant.card_scale_other)
                )
            ).call(() => {
                //显示最新的牌
                card.active = false;
                showCard.active = true;

                let card_count = 0;
                for(let i = 0; i< self.cardGroups.length; i++){
                    for(let n= 0; n < self.cardGroups[i].list.length; n++){
                        if(self.cardGroups[i].list[n].index == self.selected_group_cards[0]){
                            self.cardGroups[i].list.splice(n, 1);
                            card_count = 1;
                            break;
                        }
                    }
                    if(card_count == 1){
                        break;
                    }
                }

                showCard.getComponent(RMCardItem).showCardData(cardData.color, cardData.number);
                if (self._gameLogic.isJoker(cardData)) {
                    showCard.getComponent(RMCardItem).showCardLaizi(true);
                    showCard.getComponent(RMCardItem).showCardshadow(true);
                }
                self.card_controller(self.cardGroups, false, true, true, function(){
                    self.canTouchCard = true;
                });
            }).start();

    }

    // 转换成服务器的拍数据
    private convert_server_datas(card_groups) {
        let res = [];
        
        for(let i=0; i < card_groups.length; i++) {
            let group = card_groups[i].list;
            let new_group = [];
            for(const card of group) {
                new_group.push({color: card.color, number: card.number});
            }
            res.push(new_group);
        }
        return res;
    }
    /**
     * 获取选中牌的位置索引
     */
    private get_touch_card_index() {
        let index = this.selected_group_cards[0];
        let cardObj = null;
        for(let i=0; i<this.selfHandCardList.length; i++) {
            let card = this.selfHandCardList[i];
            if(card.getComponent(RMCardItem).index==index) {
                cardObj = card;
                break;
            }
        }

        if(cardObj==null) {
            return null;
        }
        
        let card_item = cardObj.getComponent(RMCardItem)
        let cardData = {color: card_item.getCardColor(), number: card_item.getCardValue()};
        this.selected_group_cards.splice(0, 1);

        let group_index = -1;
        let group_card_index = -1;
        //找到牌在group中的index 
        let i_count = 0, found_card = false;
        for(let i = 0; i < this.cardGroups.length; i++){
            let group = this.cardGroups[i].list;
            for(let n = 0; n < group.length; n++, i_count++){
                if (i_count == index) {
                    group_index = i;
                    group_card_index = n;
                    group.splice(n, 1);
                    found_card = true;
                    break;
                }
            }

            if(found_card) {
                break;
            }
        }

        // 清空空的数据
        for(let i=this.cardGroups.length-1; i>=0; i--) {
            if(this.cardGroups[i].list.length==0) {
                this.cardGroups.splice(i, 1);
            }
        }

        return {hand_index: index, group_index: group_index, group_card_index: group_card_index, cardData: cardData};
    }

    /** 点击丢牌 
    *  只有当前抓牌之后才能丢弃一张牌 如果丢弃这张牌之后能  show  就直接请求show
   */
    private onClickButtonDiscard() {
        if (this.user_operate_status != UserOperateState.gotCard) {
            return;
        }

        if (this.selected_group_cards.length == 0 || this.selected_group_cards.length > 1) {
            console.warn("当前出牌数据错误");
            return;
        }

        // 改变状态
        this.user_operate_status = UserOperateState.disCard;

        // 记录打之前的数据
        let tmp_groups = JSON.parse(JSON.stringify(this.cardGroups));

        // 打牌
        let card_info = this.get_touch_card_index();
        if(card_info==null) {
            console.warn("当前出牌数据错误");
            return;
        }
        
        // 将数据转换为服务器数据
        let up_groups = this.convert_server_datas(tmp_groups);
        
        this._gameCore.userDisCard(up_groups, card_info.cardData, card_info.group_index);
        this.disCardtodiscardArea(card_info);

        //牌值表里面标记为无效数据
        this.setBtnDiscardState(false);
        this.setBtnFinishState(false);
        this.setBtnDropState(false);
        this.setAddHereInfo(false);
    }

    /** 打出当前牌到弃牌区 */
    private disCardtodiscardArea(card_info) {
        let card = this.selfHandCardList[card_info.hand_index];
        //出牌动作
        let upCard = this.upCard;
        this.last_droped_card_info = {color: upCard.getComponent(RMCardItem).getCardColor(), number: upCard.getComponent(RMCardItem).getCardValue()};
        let endPos = this.LayerItems.card_right.position;
        card.zIndex = 10;
        let card_item = card.getComponent(RMCardItem);
        let playerDcard = {color: card_item.getCardColor(), number: card_item.getCardValue()};
        let self = this;
        console.log("打出当前牌到弃牌区：", endPos)
        cc.tween(card)
            .then(
                // cc.spawn(
                    cc.moveTo(0.2, cc.v2(endPos.x, endPos.y))
                    // cc.scaleTo(0.2, Constant.card_scale_other)
                // )
            )
            .call(() => {
                //显示最新的牌
                card.active = false;
                //显示最新的牌
                upCard.active = true;
                // 重新显示
                self.reshow_group(false);

                upCard.getComponent(RMCardItem).showCardData(playerDcard.color, playerDcard.number);
                if (self._gameLogic.isJoker(playerDcard)) {
                    upCard.getComponent(RMCardItem).showCardLaizi(true);
                    upCard.getComponent(RMCardItem).showCardshadow(true);
                }
            })
            .start();
    }

    //declarTips
    isShowDeclareTips(isShow: boolean, time: number = 0) {
        this.LayerItems.declarTips.active = isShow;
        if (isShow){
            this.stopCountdown();
            this.setCountdownTime(time);
        }
    }

    // 设置倒计时时间的方法
    public setCountdownTime(time: number) {
        this.remainingTime = time;
        let timeText = this.LayerItems.declareTimeCountNum.getComponent(cc.Label);
        if (timeText) {
            this.updateLabel(this.remainingTime);
            this.startCountdown();
        }
    }

    startCountdown() {
        // 清除任何现有的定时器
        if (this.timerHandle != null) {
            clearInterval(this.timerHandle);
            this.timerHandle = null;
        }
        let self = this;
        // 创建定时器
        this.timerHandle = setInterval(() => {
            self.remainingTime -= 1;
            if (self.remainingTime <= 0) {
                self.remainingTime = 0;
                self.updateLabel(self.remainingTime);
                self.stopCountdown();
            } else {
                self.updateLabel(self.remainingTime);
            }
        }, 1000)
    }

    stopCountdown() {
        if (this.timerHandle != null) {
            this.remainingTime = 0;
            clearInterval(this.timerHandle);
            this.timerHandle = null;
        }
    }

    updateLabel(time) {
        let timeTxt = this.LayerItems.declareTimeCountNum.getComponent(cc.Label);
        if (timeTxt) {
            timeTxt.string = time;
        }
    }

    // 获取癞子牌的点数
    private get_wildcard_point() {
        if(this.laizi_card==null) {
            return;
        }

        let laizi_value = this.laizi_card['number'];
        if(this.laizi_card['number'] == CardNumDesc.NUM_A){
            laizi_value = 1;
        } else if(this.laizi_card['number'] == CardNumDesc.NUM_JOKER_B || this.laizi_card['number'] == CardNumDesc.NUM_JOKER_S) {
            laizi_value = 1;
        }

        return laizi_value;
    }

    //显示牌型
    private show_card_group_type(group_type_width_list, viewGroups){
        for (let i = 0; i < this.max_group; i++) {
            this.groupTypeNodeList[i].getComponent(RMGroupItem).resetUI();
            this.groupTypeNodeList[i].getComponent(RMGroupItem).setNodeIndex(i);
        }

        let laizi_value = this.get_wildcard_point();

        let allow_green = false;
        let seq_count = 0, real_seq_count = 0;
        for(let i = 0; i < viewGroups.length; i++) {
            let group = viewGroups[i].list;
            let group_card = [];
            for(let i = 0; i < group.length; i++){
                let tmp_data = this._gameLogic.convertOtherNum(group[i]);
                group_card.push(tmp_data)
            }

            let gt = this._gameLogic.is_Set_or_Seq(group_card, laizi_value);
            if(gt==GroupType.SEQUENCE) {
                seq_count ++;
            } else if(gt==GroupType.PURE_SEQUENCE) {
                seq_count ++;
                real_seq_count ++;
            }
        }
        if(real_seq_count>=1 && seq_count>=2) {
            allow_green = true;
        }

        for(let i = 0; i < viewGroups.length; i++) {
            let group = this.groupTypeNodeList[i].getComponent(RMGroupItem);
            let width = group_type_width_list[i][1];
            
            let group_left = group_type_width_list[i][0];

            let viewGroupItem = viewGroups[i];
            this.showGroupType2(group, viewGroupItem, width, group_left, i > 0, i < viewGroups.length-1, allow_green);
            this.groupTypeNodeList[i].active = true;
        }

        for(let i = viewGroups.length; i < this.max_group; i++){
            this.groupTypeNodeList[i].active = false;
        }

        this._audioMng._loadPlaySFX(`res/sound/group_success`);
    }

    //显示分组
    private showGroupType2(group, viewGroupItem, width, group_left, show_left, show_right, allow_green) {
        group.setGroupTypeColor(GroupTypeColor.Gree);
        group.setGroupTypeStr(GroupType.NUMBER_SINGLE_NOT_JOKER, GroupTypeColor.Gree, "100");

        group.showUI(width, group_left, show_left, show_right);
        //牌值转换
        let group_card = [];
        
        for(let i = 0; i < viewGroupItem.list.length; i++){
            let tmp_data = this._gameLogic.convertOtherNum(viewGroupItem.list[i]);
            group_card.push(tmp_data)
        }

        let laizi_value = this.get_wildcard_point();
        let card_type = this._gameLogic.is_Set_or_Seq(group_card, laizi_value);

        let colorType = GroupTypeColor.Orange;
        if(card_type==GroupType.PURE_SEQUENCE) {
            colorType = GroupTypeColor.Gree;
        } else if (card_type==GroupType.SET || card_type==GroupType.SEQUENCE) {
            colorType = allow_green ? GroupTypeColor.Gree : GroupTypeColor.Yellow;
        } else {
            colorType = GroupTypeColor.Orange;
        }

        let strTxtType = card_type;
        let score = this._gameLogic.countScoreNew(viewGroupItem.list);

        if (viewGroupItem.list.length == 1) {//只有一张牌
            if (this._gameLogic.isJoker(viewGroupItem.list[0])) {//是癞子或大小王 显示绿色底
                score = ''
                colorType = GroupTypeColor.Gree;
                strTxtType = GroupType.NUMBER_SINGLE_JOKER;
            }
            else {
                strTxtType = GroupType.NUMBER_SINGLE_NOT_JOKER;
            }
        }

        group.setGroupTypeColor(colorType);
        group.setGroupTypeStr(strTxtType, colorType, score);
    }

    //更新牌类型信息
    updateGroupType(groupIndex) {
        let group = this.cardGroups[groupIndex];
        let groupItem = this.groupTypeNodeList[groupIndex];
        let js = groupItem.getComponent(RMGroupItem);

        group.list.sort((a, b) => {
            if (a.color == b.color) {
                return a.number - b.number;
            }
            return a.color - b.color;
        })

        //牌值转换
        let group_card = [];

        for(let i = 0; i < group.list.length; i++){
            let tmp_data = this._gameLogic.convertOtherNum(group.list[i]);
            group_card.push(tmp_data) 
        }

        let laizi_value = this.get_wildcard_point();
        let card_type = this._gameLogic.is_Set_or_Seq(group_card, laizi_value);

        this.cardGroups[groupIndex].type = card_type;

        let hasPureSequene: boolean = false;//是否有真顺子

        //查找分组牌中是否有真顺子
        this.cardGroups.forEach(item => {
            if (item.type == GroupType.PURE_SEQUENCE) {
                hasPureSequene = true;
            }
        })

        let type = this.cardGroups[groupIndex].type;
        let colorType = GroupTypeColor.Orange;
        if (type <= GroupType.SET) {
            colorType = hasPureSequene ? GroupTypeColor.Gree : GroupTypeColor.Yellow;
        }

        let strTxtType = type;
        let score = this._gameLogic.countScoreNew(group.list);

        if (group.list.length == 1) {//只有一张牌
            if (this._gameLogic.isJoker(group.list[0])) {//是癞子或大小王 显示绿色底
                score = ''
                colorType = GroupTypeColor.Gree;
                strTxtType = GroupType.NUMBER_SINGLE_JOKER;
            }
            else {
                strTxtType = GroupType.NUMBER_SINGLE_NOT_JOKER;
            }
        }

        js.setGroupTypeColor(colorType);
        js.setGroupTypeStr(strTxtType, colorType, score);
        groupItem.active = true;
    }

    //更新当前牌组分数
    updateCardsGroupScore() {
        let isShowHelp: boolean = false; //是否弹提示
        let hasPureSequene: boolean = false;//是否含有真顺子
        let hasSequene: boolean = false; //是否含有假顺子
        let isSumScoreZero: boolean = false;//总分是否为0

        let score = 0;
        for (let i = 0; i < this.cardGroups.length; i++) {
            let group = this.cardGroups[i];
            let score1 = Number(this._gameLogic.countScoreNew(group.list));
            if (group.type <= GroupType.SET) {//真假顺子 及set不计分
                score1 = 0;
            }
            score += score1;
            if (group.type == GroupType.PURE_SEQUENCE) {
                hasPureSequene = true;

                let lastHasPureSequene: boolean = false;//之前是否已经含有真顺子 有的话只提示一次
                this.groupTypeList.forEach(item => {
                    if (item == GroupType.PURE_SEQUENCE) {
                        lastHasPureSequene = true;
                    }
                })

                if (this.groupTypeList[i] != group.type) {
                    this.groupTypeList[i] = group.type;
                    !lastHasPureSequene && (isShowHelp = true);
                }
            }
            else if (group.type == GroupType.SEQUENCE) {
                hasSequene = true;
                if (this.groupTypeList[i] != group.type) {
                    this.groupTypeList[i] = group.type;
                    isShowHelp = true;
                }
            }
            else {
                this.groupTypeList[i] = GroupType.NUMBER;
            }
        }

        if (score > Constant.max_score) {
            score = Constant.max_score;
        }

        this.show_total_score(score);

        if (score == 0) {
            isShowHelp = true;
            isSumScoreZero = true;
        }

        this.LayerItems.txt0_true.active = hasPureSequene;
        this.LayerItems.txt1_true.active = hasSequene;
        this.LayerItems.txt2_true.active = isSumScoreZero;

        if (this.isShowHelp[0] == hasPureSequene && this.isShowHelp[1] == hasSequene && this.isShowHelp[2] == isSumScoreZero) {
            return
        }

        if (isShowHelp) {
            this.onClickHelp(null, null);
        }

        this.isShowHelp[0] = hasPureSequene;
        this.isShowHelp[1] = hasSequene;
        this.isShowHelp[2] = isSumScoreZero;
    }

    //洗牌 或重排位置 随机打乱数组
    shufflerArray(arr) {
        let newArr = arr.slice(0);
        let len = arr.length;
        let indexArr = [];
        for (let i = 0; i < len; i++) {
            if (indexArr[i]) {
                continue;
            }
            let random = Math.floor(Math.random() * len);
            while (random === i) {
                random = Math.floor(Math.random() * len);
            }
            indexArr[random] = indexArr[i] = true;
            let swap = newArr[i];
            newArr[i] = newArr[random];
            newArr[random] = swap;
        }
        return newArr;
    }

    //指定牌到当前分组
    onAddCardsGroupHere(curClickGroupIndex: number) {
        if(this.selected_group_cards.length==0) {
            return;
        }

        // 禁止触摸
        this.canTouchCard = false;

        this._audioMng._loadPlaySFX(`res/sound/group_success`)
        this.setAddHereInfo(false);
        this.setBtnGroupState(false);
        this.setBtnDiscardState(false);
        this.setBtnFinishState(false);

        let tmp_groups = JSON.parse(JSON.stringify(this.cardGroups));

        let new_group = [];
        for (let i=0; i <tmp_groups.length; i++) {
            let group = tmp_groups[i].list;
            for(let n=group.length-1; n>=0; n--) {
                let card = group[n];
                for(let k=0; k<this.selected_group_cards.length; k++) {
                    if(this.selected_group_cards[k]==card.index) {
                        new_group.push(card);
                        group.splice(n, 1);
                        break;
                    }
                }
            }
        }

        if(new_group.length>0) {
            for(let i=0; i<new_group.length; i++) {
                let card = new_group[i];
                tmp_groups[curClickGroupIndex].list.push(card);
            }
        }

        // 删除所有的空组
        for(let i=tmp_groups.length-1; i>=0; i--) {
            if(tmp_groups[i].list.length==0) {
                tmp_groups.splice(i, 1);
            }
        }

        this.clearClickGroups();

        this._audioMng._loadPlaySFX(`res/sound/group_success`)

        // 关闭自动排序
        if(this.isSort) {
            this.isSort = false;
            this.LayerItems.sortSel.active = false;
        }

        // 显示
        let self = this;
        this.card_controller(tmp_groups, true, true, true, function(){
            self.canTouchCard = true;
        });
    }
    //设置group按钮是否禁用
    setBtnGroupState(bDisable: boolean) {
        let btn_group = this.LayerItems.GroupBtn;
        let btnGroup: ButtonLayer = btn_group.getComponent(ButtonLayer);
        btnGroup.enableTouch = bDisable;
        btn_group.getChildByName('sel').active = bDisable;
    }

    //设置Discard按钮是否禁用
    setBtnDiscardState(bDisable: boolean) {
        let btn_discard = this.LayerItems.DiscardBtn;
        let button: ButtonLayer = btn_discard.getComponent(ButtonLayer);
        button.enableTouch = bDisable;
        btn_discard.getChildByName('sel').active = bDisable;
    }

    //设置Finish按钮是否禁用
    setBtnFinishState(bDisable: boolean) {
        let btn_finish = this.LayerItems.FinishBtn;
        let btnFinish: ButtonLayer = btn_finish.getComponent(ButtonLayer);
        btnFinish.enableTouch = bDisable;
        btn_finish.getChildByName('sel').active = bDisable;
    }

    //设置Drop按钮是否禁用
    setBtnDropState(bDisable: boolean) {
        let Btn_Drop = this.LayerItems.DropBtn;
        let btnDrop: ButtonLayer = Btn_Drop.getComponent(ButtonLayer);
        btnDrop.enableTouch = bDisable;
        Btn_Drop.getChildByName('sel').active = bDisable;
    }

    //菜单动画
    runMenuAni(runNodeIndex: number) {
        let runNode = this.LayerItems.left;
        let isShow = this._isOpenMenuLeft;
        let pos = this._menuLeftOrgPos;
        let addPos = -350;

        if (runNodeIndex == 1) {//右边菜单
            runNode = this.LayerItems.right;
            isShow = this._isOpenMenuRight;
            pos = this._menuRightOrgPos;
            addPos = 350;
        }

        runNode.stopAllActions();
        let maskLayer = runNode.getChildByName('maskLayer');
        if (isShow) {
            if (!!maskLayer) {
                cc.tween(maskLayer)
                    .to(0.05, { opacity: 0 })
                    .start()
            }
            cc.tween(runNode)
                .to(0.3, { position: cc.v2(pos.x + addPos, pos.y) })
                .call(() => {
                    runNode.active = false;
                })
                .start()
        }
        else {
            maskLayer.opacity = 0;
            runNode.active = true;
            cc.tween(runNode)
                .to(0.3, { position: cc.v2(pos.x, pos.y) }, { easing: "sineOut" })
                .call(() => {
                    if (!!maskLayer) {
                        cc.tween(maskLayer)
                            .to(0.2, { opacity: 120 })
                            .start()
                    }
                })
                .start()
        }

        runNodeIndex == 1 ? this._isOpenMenuRight = !this._isOpenMenuRight : this._isOpenMenuLeft = !this._isOpenMenuLeft;
    }

    //是否显示declarConfirmTips
    isShowDeclarConfirm(isShow: boolean) {
        this._gameLogic.popUpEffect(this.LayerItems.declarConfirmTips, isShow);
    }

    /** 确认超时 同时处理点击confim 和超时confim 操作 */
    private confimNetResponse() {
        this.setBtnGroupState(false);
        this.setBtnDiscardState(false);
        this.setBtnDropState(false);
        this.setBtnFinishState(false);
        this.isShowDeclareTips(false);

        this.stopViewOpearatingCard();
        // 
        this._gameCore.userDeclare(this.cardGroups); //发送confim 确认手牌
    }

    /** 禁止操作界面牌 */
    private stopViewOpearatingCard() {
        if (!this.canTouchCard) {
            return;
        }
        this.canTouchCard = false;
        this.card_controller(this.cardGroups, false, false, true, null);
        
        this.clearClickGroups();
        this.setAddHereInfo(false);
    }



    /** 确认点击或者移动到 show here区域  show card 
    */
    private clickButtonShowOrMoveRequest() {

        this.setBtnDiscardState(false);
        this.setBtnDropState(false);
        this.setBtnFinishState(false);
        this.isShowDeclarConfirm(false);

    }

    //drop提示
    isShowDropTips(isShow: boolean) {
        this._gameLogic.popUpEffect(this.LayerItems.prompt, isShow);
    }

    // 通知界面更新数据
    public update_ui_notice(){
        if (this.isSort){
            this.ui_sort_notice = 1;
        } else {
            cc.log("未启用自动排序")
        }
    }

    // 自己出完牌
    public my_discard(){
        this.LayerItems.turnTips.active = false;
        // if(this.isSort) {
        //     this.start_server_sort(true, true);
        // }
    }
    public try_auto_up_card() {
        this.is_auto_up_card = true;
    } 

    /** 给自己增加一张牌 */
    public get_card_to_me(cardData: CardData, card_pos: number) {
        this._audioMng._loadPlaySFX('res/sound/discard')
        this.isShowDropTips(false);
        
        //取消所有准备分组的牌
        this.clearClickGroups();

        //隐藏箭头灯光
        this.runArrowAndLightAni(false);

        //设置
        this.setBtnGroupState(false);
        let groupNum = this.cardGroups.length;
        let tmp_groups = JSON.parse(JSON.stringify(this.cardGroups));

        let add_new_group : boolean = false;
        if (tmp_groups.length < this.max_group) {
            //从牌堆补牌
            let newGroup: CardGroup = {
                type: GroupType.NUMBER,
                IndexList: [],
                list: [],
            }
            newGroup.list.push(cardData);
            tmp_groups.push(newGroup);
            this.cardGroups.push(newGroup);

            add_new_group = true;
        } else {
            //添加到最后一组
            let lastGroup = tmp_groups[groupNum - 1];
            if (lastGroup) {
                lastGroup.list.push(cardData);

                this.cardGroups[groupNum - 1].list.push(cardData);
            }
        }
        
        // 删除空的组
        for(let i=tmp_groups.length-1; i>=0; i--) {
            if(tmp_groups[i].list.length==0) {
                tmp_groups.splice(i, 1);
            }
        }

        // 计算最后一张牌的位置
        let last_card_pos_x = 0;
        for(let i=0; i<tmp_groups.length; i++) {
            if(i>0) {
                last_card_pos_x += Constant.group_offset_x;
            }

            let card_len = tmp_groups[i].list.length;
            last_card_pos_x += ((card_len-1) * Constant.card_offset_x + Constant.card_width);
        }

        let card_count = 0;
        for(let i=0; i<tmp_groups.length; i++) {
            for(let n=0; n<tmp_groups[i].list.length; n++) {
                card_count++;
            }
        }

        last_card_pos_x = (last_card_pos_x / 2);
        // 计算最后一张牌的偏移数据
        if(add_new_group) {
            last_card_pos_x -= Constant.card_width;
        } else {
            last_card_pos_x -= Constant.card_offset_x;
        }

        let lastCardPos = cc.v2(last_card_pos_x, Constant.self_cards_pos_y);

        //将牌放置在最后一个位置
        let card = null;
        for(let i=0; i<this.selfHandCardList.length; i++) {
            if(!this.selfHandCardList[i].active) {
                card = this.selfHandCardList[i];
                break;
            }
        }

        if (this._gameLogic.isJoker(cardData)) {
            card.getComponent(RMCardItem).showCardLaizi(true);
            card.getComponent(RMCardItem).showCardshadow(true);
        }

        let new_card_pos = (card_pos==0) ? 1 : 2;
        if (new_card_pos == CardGetState.cardPool) {
            // 从牌堆飞
            card.getComponent(RMCardItem).showCardBack();
            card.position = cc.v2(this.LayerItems.card_left.position.x + 2, this.LayerItems.card_left.position.y);
        } else {
            // 从右边牌飞
            let startPos = this.LayerItems.card_right.position;
            card.getComponent(RMCardItem).showCardData(cardData.color, cardData.number);
            card.position = cc.v2(startPos.x + 2, startPos.y);
        }
        
        let self = this;
        card.stopAllActions();
        card.active = true;
        card.zIndex = 50;

        cc.tween(card)
            .delay(0.2)
            .then(
                cc.spawn(
                    cc.moveTo(0.2, cc.v2(lastCardPos.x, lastCardPos.y)).easing(cc.easeSineOut()),
                    cc.scaleTo(0.2, 1)
                )
            )
            .call(() => {
                // 先重排
                self.card_controller(tmp_groups, false, false, true, (
                    ()=>{
                        //增加翻牌效果
                        if (new_card_pos == CardGetState.cardPool) {
                            cc.tween(card)
                                .call(()=>{
                                    card.getComponent(RMCardItem).flip1(cardData,self._gameLogic.isJoker(cardData));
                                })
                                .delay(1.0)
                                .call(() => {
                                    self.after_got_card();
                                })
                                .start();
                        } else {
                            if(self.last_droped_card_info.color!=-1) {
                                self.upCard.getComponent(RMCardItem).showCardData(self.last_droped_card_info.color, self.last_droped_card_info.number);
                                if (self._gameLogic.isJoker(self.last_droped_card_info)) {
                                    self.upCard.getComponent(RMCardItem).showCardLaizi(true);
                                    self.upCard.getComponent(RMCardItem).showCardshadow(true);
                                }
                            } else {
                                self.upCard.active = false;
                            }

                            cc.tween(card)
                                .delay(0.6)
                                .call(() => {
                                    self.after_got_card();
                                }).start();
                        }
                    }));                
            }).start();
    }

    // 拿牌以后得回调
    private after_got_card(){
        this.user_operate_status = UserOperateState.gotCard;
        this.clearClickGroups();

        if(this.isSort){
            // 请求服务器排序
            this.start_server_sort(false, false);
            this.try_auto_up_card();
        }

        let self = this;
        setTimeout(function() {
            if(self.isSort){
                self.update_ui_notice();
            } else {
                // 将数据覆盖到底层数据上
                self.reshow_group(false);
            }
        },100)
    }

    /** 
     * 其他玩家抓牌
     */
    public otherAddcard(data) {

        this._audioMng._loadPlaySFX('res/sound/discard')
        let addCard: CardData = data.card || null;
        let card = this.getGameCard();
        card.scale = Constant.card_scale_other;

        // comp.Info.betting = 1;
        let localSeatId = this._gameCore.getLocalSeatId(data.seatid);
        let headPos = this._allPlayers[localSeatId].headWorldPos();
        let endPos = this.LayerItems.viewCardParent.convertToNodeSpaceAR(headPos);
        let lastScale = 0.4;
        let self = this;
        let local_pos = data.pos + 1;
        if (local_pos == CardGetState.cardPool) {
            card.getComponent(RMCardItem).showCardBack();
            card.position = cc.v3(this.LayerItems.card_left.position.x + 2, this.LayerItems.card_left.position.y);
            cc.tween(card)
                .then(cc.spawn(
                    cc.moveTo(0.25, cc.v2(endPos.x + 6, endPos.y + 3)),
                    cc.scaleTo(0.25, lastScale)
                ))
                .delay(0.4)
                .call(() => {
                    card.active = false;
                    // self.selfHandCardList.push(card);
                    // if(comp){
                    //     comp.Info.betting = 0;
                    // }
                })
                .start();
        } else {
            if (this._gameCore.gameState == GameState.waitNextRound) {
                // card.getComponent(RMCardItem).showCardData();
            } else {
                card.getComponent(RMCardItem).showCardData(addCard.color, addCard.number);
                if (this._gameLogic.isJoker(addCard)) {
                    card.getComponent(RMCardItem).showCardLaizi(true);
                    card.getComponent(RMCardItem).showCardshadow(true);
                }
            }

            let upCard = this.upCard;
            card.position = upCard.position;

            //显示最新的牌
            if (data.cards.length == 0) {
                upCard.active = false;
            } else {

                if (this._gameCore.gameState == GameState.waitNextRound) {
                    // upCard.getComponent(RMCardItem).showCardData();
                } else {
                    let cardData = data.cards[data.cards.length - 1];
                    upCard.getComponent(RMCardItem).showCardData(cardData.color, cardData.number);
                    if (this._gameLogic.isJoker(cardData)) {
                        upCard.getComponent(RMCardItem).showCardLaizi(true);
                        upCard.getComponent(RMCardItem).showCardshadow(true);
                    }
                }
            }
            cc.tween(card)
                .then(cc.spawn(
                    cc.moveTo(0.25, cc.v2(endPos.x + 6, endPos.y + 3)),
                    cc.scaleTo(0.25, lastScale)
                ))
                .delay(0.4)
                .call(() => {
                    card.active = false;
                })
                .start();
        }
    }

    /** 其他玩家弃牌 */
    public otherDiscard(data) {
        this._audioMng._loadPlaySFX('res/sound/discard')

        let card = this.getGameCard();
        card.scale = 0.4;
        if (this._gameCore.gameState == GameState.waitNextRound) {
            // card.getComponent(RMCardItem).showCardData();
        } else {
            card.getComponent(RMCardItem).showCardData(data.card.color, data.card.number);
            if (this._gameLogic.isJoker(data.card)) {
                card.getComponent(RMCardItem).showCardLaizi(true);
                card.getComponent(RMCardItem).showCardshadow(true);
            }
        }

        let localSeatId = this._gameCore.getLocalSeatId(data.seatid);
        let headPos = this._allPlayers[localSeatId].headWorldPos();
        let startPos = this.LayerItems.viewCardParent.convertToNodeSpaceAR(headPos);
        card.position = cc.v3(startPos.x + 6, startPos.y + 3, 0);

        let upCard = this.upCard;
        this.last_droped_card_info = {color: upCard.getComponent(RMCardItem).getCardColor(), number: upCard.getComponent(RMCardItem).getCardValue()};

        let endPos = this.LayerItems.card_right.position;

        let self = this;

        cc.tween(card)
            .delay(0.4)
            .then(cc.spawn(
                cc.moveTo(0.3, cc.v2(endPos.x, endPos.y)),
                cc.scaleTo(0.3, Constant.card_scale_other)
            ))
            .call(() => {
                //显示最新的牌
                upCard.active = true;
                if (self._gameCore.gameState == GameState.waitNextRound) {
                    // card.getComponent(RMCardItem).showCardData();
                } else {
                    upCard.getComponent(RMCardItem).showCardData(data.card.color, data.card.number);
                    if (self._gameLogic.isJoker(data.card)) {
                        upCard.getComponent(RMCardItem).showCardLaizi(true);
                        upCard.getComponent(RMCardItem).showCardshadow(true);
                    }
                }
                card.active = false;
                // self.selfHandCardList.push(card);
            })
            .start();
    }

    /** 其他玩家show 操作 发出一张牌到 show here */
    public otherPlayerShow(data) {
        let showcardData: CardData = data.card;
        let card = this.getGameCard();
        card.scale = 0.4;
        if (this._gameCore.gameState == GameState.waitNextRound) {
            // card.getComponent(RMCardItem).showCardData();
        } else {
            card.getComponent(RMCardItem).showCardData(showcardData.color, showcardData.number);
            if (this._gameLogic.isJoker(showcardData)) {
                card.getComponent(RMCardItem).showCardLaizi(true);
                card.getComponent(RMCardItem).showCardshadow(true);
            }
        }

        // this.cancelBetLastTime();
        let localSeatId = this._gameCore.getLocalSeatId(data.seatid);
        let headPos = this._allPlayers[localSeatId].headWorldPos();
        let startPos = this.LayerItems.viewCardParent.convertToNodeSpaceAR(headPos);
        card.position = cc.v3(startPos.x + 6, startPos.y + 3, 0);

        let self = this;
        let playerMovingCard = () => {
            let showCard = self.showCard
            let endPos = showCard.position;
            cc.tween(card)
                .delay(0.4)
                .then(cc.spawn(
                    cc.moveTo(0.3, cc.v2(endPos.x, endPos.y)),
                    cc.scaleTo(0.3, Constant.card_scale_other)
                ))
                .call(() => {
                    //显示最新的牌
                    showCard.active = true;
                    if (this._gameCore.gameState == GameState.waitNextRound) {
                        // card.getComponent(RMCardItem).showCardData();
                    } else {
                        showCard.getComponent(RMCardItem).showCardData(showcardData.color, showcardData.number);
                        if (this._gameLogic.isJoker(showcardData)) {
                            showCard.getComponent(RMCardItem).showCardLaizi(true);
                            showCard.getComponent(RMCardItem).showCardshadow(true);
                        }
                    }

                    card.active = false;
                })
                .start();
        }
        playerMovingCard();
    }

    //更新当前牌记录
    updateCurCardsRecord(data) {
        let self = this;
        let content = cc.find('view/content', this.LayerItems.scrollView_card);
        content.removeAllChildren();
        this._allPlayers.forEach((item, index) => {
            let nameNode = self.LayerItems["name_item" + index];
            let nameNodeSel = nameNode.getChildByName('sel');
            let nameNodeSelTxt = nameNodeSel.getChildByName('txt');

            if (item.playerData.playerid > 0) {
                nameNode.getComponent(cc.Label).string = Common.textClamp(item.playerData.playerName, 6, '');
                nameNodeSelTxt.getComponent(cc.Label).string = Common.textClamp(item.playerData.playerName, 6, '');
                nameNode.active = true;
                nameNodeSel.active = index == 0;
            }
            else {
                nameNode.active = false;
            }
        });
        for (let i = 0; i < 5; i++) {
            let recordItem = cc.instantiate(this.LayerItems.record_cardItem);
            let subCardItem = recordItem.getChildByName('subCardItem');
            subCardItem.removeAllChildren();
            //这里不用card pool。。。。
            let card = cc.instantiate(this.cardItemPrefab);
            card.scale = 1;
            let cardData = { color: 3, number: i + 1 };
            card.getComponent(RMCardItem).showCardData(cardData.color, cardData.number);
            subCardItem.addChild(card);
            card.active = true;
            let indexBg = recordItem.getChildByName('indexBg');
            let indexOtherBg = recordItem.getChildByName('indexOtherBg');
            let indexNum = indexBg.getChildByName('indexNum').getComponent(cc.Label);
            if (i == 0) {
                indexBg.active = true;
                indexNum.string = '2';
            }
            if (i == 1) {
                indexOtherBg.active = true;
            }

            content.addChild(recordItem);
            recordItem.active = true;
        }
    }

    //是否显示结算界面
    showSettleLayer(player_data) {
        if (!this.settleNode) {
            this.settleNode = cc.instantiate(this.settlePrefab);
            this.LayerItems.resultNode.addChild(this.settleNode);
        }

        let self = this;
        this._gameLogic.popUpEffect1(this.settleNode, true, function(){
            // 设置结算界面数据
            let settle_layer = self.settleNode.getComponent(RMSettleLayer);
            settle_layer.showSettleInfo(player_data, self.laizi_card);
        });
    }

    //////////////////////////////////////////////////////////////////////////////

    public transformData(input: { [key: string]: Item }): GroupedItem[] {
        // 将输入数据转化为数组形式
        const items = Object.values(input);
    
        // 按 color 进行分组
        const groups: { [key: number]: Item[] } = {};
        items.forEach(item => {
            if (!groups[item.color]) {
                groups[item.color] = [];
            }
            groups[item.color].push(item);
        });
    
        // 转化为 GroupedItem 类型并进行排序
        const result: GroupedItem[] = Object.keys(groups).map(key => {
            return {
                cardData: groups[+key].sort((a, b) => a.number - b.number),
                type: 0
            };
        });
    
        // 将 result 按数据中的 number 排序
        result.sort((a, b) => {
            const aMinNumber = Math.min(...a.cardData.map(item => item.number));
            const bMinNumber = Math.min(...b.cardData.map(item => item.number));
            return aMinNumber - bMinNumber;
        });
    
        return result;
    }

    private start_server_sort(is_ui_ready: boolean, must_do: boolean) {
        let tt = performance.now();
        let min_sec = this.time_second_to_auto_sort * 1000;

        //每3秒内只能请求一次
        if(!must_do) {
            if(tt-this.last_sort_update_time<min_sec) {
                this.isSort = false;
                this.LayerItems.sortSel.active = false;

                ToastHelper.show(GameTextTips.wait_sort);
                return;
            }
        }

        if(this.is_server_sorted==0) {
            if(is_ui_ready) {
                this.ui_sort_notice = 1;
            } else {
                this.ui_sort_notice = 0;
            }
            this.is_server_sorted = 1;
        } else {
            ToastHelper.show("waiting sorted ...");
        }
    }

    private stop_server_sort() {
        this.is_server_sorted = 0;
    }

    private thread_server_sort() {
        if(this.is_server_sorted == 0) {
            // 什么都不做
            this.is_auto_up_card = true;
        } else if(this.is_server_sorted == 1) {
            this.last_sort_update_time = performance.now();
            this.is_server_return = false;
            this.send_card_group_msg();
            this.is_server_sorted = 2;
        } else if(this.is_server_sorted == 2) {
            //等待服务器返回
            if(this.is_server_return && this.ui_sort_notice == 1) {
                this.is_server_sorted = 3;
            } else if(this.ui_sort_notice == -1) {
                this.stop_server_sort();
            }
        } else if(this.is_server_sorted == 3) {
            if(this.ui_sort_notice == -1) {
                this.stop_server_sort();   
            } else {
                this.is_server_sorted = 4;
            }
        } else if(this.is_server_sorted == 4) {
            this.stop_server_sort();
            if(this.ui_sort_notice != -1) {
            
                // 开始排序
                this.show_card_group(true, true);
            }
        } else if(this.is_server_sorted == 99){
            this.stop_server_sort();   
            this.show_card_group(true, false);
        } else if(this.is_server_sorted==98) {
            this.stop_server_sort();   
            this.show_card_group(true, true);
        }
    }

    // 重新显示分组
    private reshow_group(is_action){
        this.is_server_sorted = is_action ? 98 : 99;
    }

    // 开始游戏
    public start_game(data:any){
        console.log("执行庄家动画1：", data);
        if (data["zhuangArr"] == null || data["zhuangArr"] == undefined) {
            this.sendCardList(data["cards"], data["laizi"], data["right"])
        } else {
            let zhaung_data = Object.entries(data["zhuangArr"]);
            // 判断是否有zhuangArr，执行翻转牌动画，通过点数确定庄家，点数大的为庄家
            if(zhaung_data.length > 0 && Array.isArray(zhaung_data)){
                console.log("执行庄家动画：", zhaung_data);
                this.LayerItems.cardNode.active = true;
                this.LayerItems.cardposNode.active = true;
                let self = this;
                let banker_seatid = data["zhuangSeatid"];
                for(let i = 0; i < zhaung_data.length; i++){
                    let zhuang_data_item = zhaung_data[i][1]
                    let seatid = this._gameCore.getLocalSeatId(zhuang_data_item["seatid"]);
                    console.log("转换后的seatid:", seatid)
                    this.banker_card_node[seatid].active = true;
                    let banker_card_item = this.banker_card_node[seatid];
                    let remainTime = 1.5;

                    cc.tween(banker_card_item)
                        .call(function(){
                            // 翻牌
                            banker_card_item.getComponent(RMCardItem).flip(0.2 * remainTime,  0.13 * remainTime, zhuang_data_item["card"]);
                            cc.tween(banker_card_item)
                            .delay(0.5)
                            .call(function(){
                                if (i == zhaung_data.length - 1){
                                    // 执行灯光闪烁
                                    let banker_local_seatid = self._gameCore.getLocalSeatId(banker_seatid);
                                    let show_banker_light = self.banker_card_node[banker_local_seatid];
                                    let banker_light = show_banker_light.getChildByName("banker_light");
                                    console.log("执行灯光动画：", banker_light)
                                    banker_light.active = true;
                                    cc.tween(banker_light)
                                        .then(cc.fadeOut(0.5))
                                        .then(cc.fadeIn(0.5))
                                        .union()
                                        .repeat(2.5)
                                        .call(function(){
                                            console.log("准备开始发牌")
                                            let banker_seatid = self._gameCore.getLocalSeatId(data["zhuangSeatid"]);
                                            self.showPlayerBankerFlag(banker_seatid);
                                            self._allPlayers[banker_seatid].getComponent(RMPlayerLayer).showBankerFlag(true);
                                            for(let i = 0; i < self.banker_card_node.length; i++){
                                                self.banker_card_node[i].active = false;
                                            }
                                            self.sendCardList(data["cards"], data["laizi"], data["right"])
                                        })
                                        .start();
                                }
                            })
                            .start();
                        })
                        .start()
                }
            } 
        }
    }

    // 服务器sendCard
    public sendCardList(cardlist: any, laiziCard: any, rightCard:any){
        //执行完成庄家翻转牌动画后执行发牌动画
        this.LayerItems.cardNode.active = true;
        let cardDataList = this.transformData(cardlist)
        this.canTouchCard = false;
        let tempList = [];
        let count = 0;
        this.cardGroups = [];
        for (let i = 0; i < cardDataList.length; i++) {
            let cardGroup = <CardGroup>{};
            cardGroup.list = [];
            for (let j = 0; j < cardDataList[i].cardData.length; j++) {
                let cardData = <CardData>{};
                cardData.color = cardDataList[i].cardData[j].color;
                cardData.number = cardDataList[i].cardData[j].number;
                cardData.index = count;
                cardGroup.type = cardDataList[i].type;
                tempList.push(cardData);
                cardGroup.list.push(cardData);
                count += 1;
            }
            this.cardGroups.push(cardGroup);
        }

        this.TempCardDataList = cardDataList;
        let shuffleCardDataList = this.shufflerArray(tempList)
        // 发牌在最后两张牌的时候，回调执行 “中间显示牌堆并翻左边癞子牌和右边底牌”
        // 牌堆翻转牌完成后，牌堆从中间移动到左边盖住癞子牌（不显示癞子标志）
        // 然后执行分组，执行分组时，同时执行左上角提示，分组执行完成后，回调显示癞子牌标志

        this.laizi_card = laiziCard;
        this.sendCards(shuffleCardDataList, laiziCard, rightCard)
    }

    // 请求服务器组牌
    private send_card_group_msg(){
        let self = this;
        if(!this.is_hand_card_init) {
            setTimeout(function() {
                self.send_card_group_msg();
            }, 100);
            return;
        }
        this._gameCore.getSortCard();
    }

    // 数据转换
    private convertCardDataToarray(data: Record<string, CardGroupItem>): Record<string, CardDataArray> {
        const result: Record<string, CardDataArray> = {};
      
        for (const key in data) {
          const cardGroup = data[key];
          result[key] = Object.values(cardGroup.cardData);
        }
      
        return result;
      }

    public show_total_score(score: number) {
        this._allPlayers[Constant.self_local_pos].updateSelfScore(score);
    }
    //服务器返回组牌
    public sortCardGroup(data:any) {
        //this.cardGroups = [];
        let tmp_groups = [];
        let cardDataList = [];
        const convertdData = this.convertCardDataToarray(data)
        if (data != null && data != undefined) {
            for(const key in data) {
                cardDataList[key] = {}
                cardDataList[key].type = data[key]['cardtype']
                cardDataList[key].cardData = convertdData[key]
            }

            cardDataList = cardDataList.filter(item => item !== undefined);
            for (let i = 0; i < cardDataList.length; i++) {
                let cardGroup = <CardGroup>{};
                cardGroup.list = [];
                cardGroup.type = cardDataList[i].type;
                for (let j = 0; j < cardDataList[i].cardData.length; j++) {
                    let cardData = <CardData>{};
                    cardData.color = cardDataList[i].cardData[j].color;
                    cardData.number = cardDataList[i].cardData[j].number;
                    cardGroup.list.push(cardData);
                }
                tmp_groups.push(cardGroup);
            }
        }

        this.cardGroups = JSON.parse(JSON.stringify(tmp_groups));

        this.is_server_return = true;
    }

    // 手动排序后提交服务器
    // 更新牌组
    public sort_to_server(){
        let card_group_data = this.convert_server_datas(this.cardGroups);
         // = this.cardGroups;
        console.log("提交分组数据到服务器", card_group_data)
        this._gameCore.updateCardSequences(card_group_data)
    }

    ////////////////////////////////////////////////////////////////////////////////////////////
    //*******************************界面牌UI 控制****************************************/

    //显示等待提示
    public show_wait_tips(){
        this.isShowWaitStartTips(true);
    }

    //点击不同玩家牌记录 
    onClickRecordName(target: any, customEventData: any) {
        let index = Number(customEventData);

        for (let i = 0; i < this._gameCore.maxPlayers; i++) {
            let nameSelNode = this.LayerItems["name_item" + i].getChildByName('sel');
            nameSelNode.active = i == index;
        }
    }

    //点击牌记录
    private onClickShowCurCardsRecord() {
        this.isShowCardsRecord = !this.isShowCardsRecord;
        this.LayerItems.cur_recordNode.active = this.isShowCardsRecord
        this.updateCurCardsRecord(null);
    }

    /** 点击牌堆拿牌 */
    private onClickGetCardInPaidui(event: cc.Event.EventTouch) {
        if(this.user_operate_status != UserOperateState.waitGetCard) {
            return;
        }
        
        // 正在拿牌
        this.user_operate_status = UserOperateState.gettingCard;

        // 这个过程中禁止点击
        this.canTouchCard = false;

        this.setBtnDropState(false);
        this._gameCore.getCard(0)
    }

    /** 点击选择最底部的一张牌 需要检测是否只剩下一张牌 避免错误 */
    private onClickGetCardInPlayers(event: cc.Event.EventTouch) {
        if(this.user_operate_status != UserOperateState.waitGetCard) {
            return;
        }

        // 正在拿牌
        this.user_operate_status = UserOperateState.gettingCard;

        this.canTouchCard = false;

        this.setBtnDropState(false);
        this._gameCore.getCard(1)
    }

    //declarConfirm cancel
    private onClickButtonCancel() {
        this.isShowDeclarConfirm(false);
        //点击取消
        if (this.clickState == ClickShowCardState.click) {
            this.clearClickGroups();
            this.setBtnDiscardState(false);
            this.setBtnFinishState(false);
        } else {
            cc.log("将牌移回原位置");
            // this.updateCardInfo();
        }
    }

    //点击排序
    private onClickSort() {
        let btn = this.LayerItems.sortBtn.getComponent(ButtonLayer);
        this.isSort = !this.isSort;
        this.LayerItems.sortSel.active = this.isSort;
        btn.enableTouch = false;
        this.scheduleOnce(() => {
            btn.enableTouch = true;
        }, 0.2)
        
        if(this.isSort){
            this.start_server_sort(true, false)
        } else {
            this.ui_sort_notice = -1;
        }
        
    }

    /** 点击弃权 */
    private onClickButtonDrop() {
        if (this.user_operate_status != UserOperateState.waitGetCard) {
            return;
        }
        
        this.isShowDropTips(true);
    }

    //点击确认取消
    private onClickButtonDropCancel() {
        this.isShowDropTips(false);
    }

    //点击确认drop
    private onClickButtonDropConfim() {
        this.setButtonGroupAndDiscardState(false, false);
        this.setBtnDropState(false);
        this.isShowDropTips(false);
        this.clearClickGroups();

        this._gameCore.userDrop();
    }

    /** 点击确认declare */
    private onClickButtonConfim() {
        // this.cancelBetLastTime();
        this.confimNetResponse();
    }

    /** 点击分组 */
    private onClickButtonGroup() {
        if(this.selected_group_cards.length<2) {
            return;
        }
        this.setAddHereInfo(false);
        this.setBtnGroupState(false);

        // 顺序查找所有的牌
        let tmp_groups = JSON.parse(JSON.stringify(this.cardGroups));
        let new_group : number[] = [];
        for(let i=0; i<tmp_groups.length; i++) {
            let group = tmp_groups[i].list;
            for(let n=group.length-1; n>=0; n--) {
                let card = group[n];
                // 在索引组中查找是否有相同的牌
                for(let k=0; k<this.selected_group_cards.length; k++) {
                    if(card.index==this.selected_group_cards[k]) {
                        new_group.push(card);
                        group.splice(n, 1);
                        break;
                    }
                }
            }
        }
        
        if(new_group.length>0) {
            // 将新组加入到列表中
            tmp_groups.push({type: 0, list: new_group});
        }

        // 删除空组
        for(let i=tmp_groups.length-1; i>=0; i--) {
            if(tmp_groups[i].list.length==0) {
                tmp_groups.splice(i, 1);
            }
        }

        this.clearClickGroups();

        if (tmp_groups.length > this.max_group) {
            //提示用户最多只能分6组
            ToastHelper.show(this.stringFormat(GameTextTips.max_group, [this.max_group]));
            this.clearClickGroups();
            return;
        }

        // 禁止触摸
        this.canTouchCard = false;
        this._audioMng._loadPlaySFX(`res/sound/group_success`)

        // 关闭自动排序
        if(this.isSort) {
            this.isSort = false;
            this.LayerItems.sortSel.active = false;
        }

        let self = this;
        this.card_controller(tmp_groups, true, true, true, function(){
            self.canTouchCard = true;
        });
    }

     // 排序显示
     private show_card_group(is_override_data, is_action){
        let self = this;
        this.card_controller(this.cardGroups, is_override_data, is_action, true, function(){
            self.canTouchCard = true;
            // self.runArrowAndLightAni(true);
        });
    }


    //牌的控制，移动，显示，隐藏
    private card_controller(group_data, is_override_data, is_action, is_show_type, callback) {
        let card_nodes = [];
        let start_x = 0;
        let total_width = 0;
        let card_left_x = 0;
        let card_count = 0;
        this.canTouchCard = false;

        // 计算所有牌的偏移量
        for(let i = 0; i < group_data.length; i++){
            let group = group_data[i];
            if (group.list.length > 0){
                for(let n = 0; n < group.list.length; n++, card_count++){
                    let node = {};
                    node["x"] = start_x;
                    node["card"] = group.list[n];
                    card_nodes.push(node);

                    // 不是最后一张牌才增加牌的偏移量
                    if(n != group.list.length - 1){
                        start_x += Constant.card_offset_x;
                    }
                }
            } else {
                // 计算半张空牌宽度
                start_x += (Constant.card_width / 2);
            }

            // 不是最后一组才增加组的偏移量
            if(i != group_data.length - 1){
                start_x += (Constant.group_offset_x + Constant.card_width);
            }
        }

        // 计算所有牌的整体宽度
        total_width = start_x + Constant.card_width;

        // 计算左边牌的起始坐标
        card_left_x = 0 - total_width / 2;

        let group_type_width_list = [];
        let group_start = 0;
        for(let i = 0; i < group_data.length; i++) {
            let group_width = (group_data[i].list.length-1) * Constant.card_offset_x + Constant.card_width;
            let group_pos = [];
            group_pos.push(group_start);
            group_pos.push(group_width);

            group_type_width_list.push(group_pos);

            group_start += group_width;
            if(i != group_data.length-1) {
                group_start += Constant.group_offset_x;
            }
        }

        let all_group_width = group_start;

        for(let i=0; i<group_type_width_list.length; i++) {
            group_type_width_list[i][0] = group_type_width_list[i][0] - all_group_width / 2 + group_type_width_list[i][1] / 2;
            // 计算后前后各加两个像素
            group_type_width_list[i][0] += 2;
            group_type_width_list[i][1] -= 4;
        }
        
        // 显示牌, 判断是否需要动画，如果不需要，直接修改牌的数据
        if(is_action){
            let new_pos = [];
            let used_card = [];
            
            for(let n = 0; n < card_nodes.length; n++){
                for (let i = 0; i < this.selfHandCardList.length; i++){
                    let card = this.selfHandCardList[i];
                    let card_data = card_nodes[n];
                    let origin_card_data = card.getComponent(RMCardItem);
                    if (card_data["card"].color == origin_card_data.getCardColor() && card_data["card"].number == origin_card_data.getCardValue()) {
                        if(used_card.indexOf(n) >= 0){
                            continue;
                        }
                        used_card.push(n);
                        let pos_x = card_data["x"] + card_left_x + Constant.card_width / 2;
                        new_pos.push(pos_x);
                        break;
                    }
                }
            }

            let self = this;
            let is_show_finish = card_nodes.length;

            for(let i = 0; i < card_nodes.length; i++){
                let card = this.selfHandCardList[i];

                if(!card.active) {
                    card.active = true;
                }
                // card.stopAllActions();
                cc.tween(card)
                    .then(cc.moveTo(0.3, cc.v2(new_pos[i], Constant.self_cards_pos_y)).easing(cc.easeSineIn()))
                    .call(() => {
                        is_show_finish --;
                        if(is_show_finish == 0){
                            self.card_controller(group_data, is_override_data, false, is_show_type, callback);
                        }
                    })
                    .start();
            }
        } else {
            // 打标记
            let n_index = 0;
            for(let i=0; i<group_data.length; i++) {
                let group = group_data[i].list;
                for(let n=0; n<group.length; n++, n_index++) {
                    group[n].index = n_index;
                }
            }

            if(is_override_data) {
                this.cardGroups = JSON.parse(JSON.stringify(group_data));
            }

            // 直接修改牌的数据
            for(let i = 0; i < card_nodes.length; i++){
                let card = this.selfHandCardList[i];
                card.scale = 1;
                card.zIndex = (i + 1) * 2;
                let card_data = card_nodes[i];
                this.selfHandCardList[i].active = true;
                // 以中心点坐标计算，需要加上牌宽度的一半
                let pos_x = card_data["x"] + card_left_x + Constant.card_width / 2;
                card.setPosition(cc.v2(pos_x, Constant.self_cards_pos_y));
                let card_item = card.getComponent(RMCardItem);
                card_item.showCardData(card_data["card"].color, card_data["card"].number);
                card_item.moveUP = false;
                card_item.index = i;
                
                // 显示癞子
                if (this._gameLogic.isJoker(card_data["card"])) {
                    card_item.showCardLaizi(true);
                    card_item.showCardshadow(true);
                }
            }

            // 如果只有13张牌的情况，最后一张不显示
            for(let i = card_nodes.length; i < Constant.max_hand_card_count; i++){
                this.selfHandCardList[i].active = false;
            }
        
            if(is_show_type){
                this.show_card_group_type(group_type_width_list, group_data);
                // 分数
                let laizi_value = this.get_wildcard_point();
                let ret = this._gameLogic.get_total_score(group_data, laizi_value);
                this.show_total_score(ret.score);
                
                if(this.is_auto_up_card) {
                    // 自动抽牌
                    this.auto_up_card(group_data, card_count, ret);
                }
            }

            this.show_help_tips(false);

            if(callback != null && callback != undefined){
                callback();
            }
        }
    }

    // 快要胡牌的时候自动抽牌
    private auto_up_card(group_data, card_count, comi_res) {
        if(card_count==Constant.max_hand_card_count) {
            //14张牌的时候才会用到
            if(comi_res.real_seq_count>=1 && comi_res.seq_count>=2 && comi_res.number_count<=1) {
                let selected_card = null;
                // 这里可以胡牌了
                if(comi_res.number_count==1) {
                    // 有1张单牌
                    for(const group of group_data) {
                        if(group.list.length==1) {
                            selected_card = group.list[0];
                            break;
                        }
                    }
                } else {
                    // 没有单牌
                    for(const group of group_data) {
                        if(group.list.length>3) {
                            let tmp_card = group.list[group.length-1];
                            if(!this._gameLogic.isJoker(tmp_card)) {
                                selected_card = tmp_card;
                            } else if(!this._gameLogic.isJoker(group.list[0])) {
                                selected_card = group.list[0];
                            }
                        }
                    }
                }

                if(selected_card!=null) {
                    // 首先清空已经选中的所有牌
                    this.clearClickGroups();
                    
                    for(const card_node of this.selfHandCardList) {
                        let card = card_node.getComponent(RMCardItem);
                        if(selected_card.index == card.index) {
                            this.checkCardGroups(card_node);
                            this.set_btn_state();
                            break;
                        }
                    }
                }
            }
        }
    }

    // 显示左上角的提示
    private show_help_tips(b_init) {
        let wildcard_point = this.get_wildcard_point();
        let ret = this._gameLogic.get_total_score(this.cardGroups, wildcard_point);

        let node = this.LayerItems.tipsNode;

        let txt = node.getChildByName("txt");
        let txt0 = node.getChildByName("txt0");
        let txt1 = node.getChildByName("txt1");
        let txt2 = node.getChildByName("txt2");
        let txt0_true = node.getChildByName("txt0_true");
        let txt1_true = node.getChildByName("txt1_true");
        let txt2_true = node.getChildByName("txt2_true");

        if(b_init) {
            txt.String = GameTextTips.objective;
            txt0.color = Left_Tips_Color.Color_Normal;
            txt0_true.active = false;
            txt0.String = GameTextTips.pureSequene;
            txt1.color = Left_Tips_Color.Color_Normal;
            txt1_true.active = false;
            txt1.String = GameTextTips.sequene;
            txt2.color = Left_Tips_Color.Color_Normal;
            txt2_true.active = false;
            txt2.String = GameTextTips.score;
        } else if(node.active) {
            if (ret.real_seq_count>0) {
                txt0.color = Left_Tips_Color.Color_Enabled;
                txt0_true.active = true;
            } else {
                txt0.color = Left_Tips_Color.Color_Normal;
                txt0_true.active = false;
            }

            if((ret.seq_count>0 && ret.real_seq_count==0) || ret.seq_count>1) {
                txt1.color = Left_Tips_Color.Color_Enabled;
                txt1_true.active = true;
            } else {
                txt1.color = Left_Tips_Color.Color_Normal;
                txt1_true.active = false;
            }

            if(ret.score>0) {
                txt2.color = Left_Tips_Color.Color_Normal;
                txt2_true.active = false;
            } else {
                txt2.color = Left_Tips_Color.Color_Enabled;
                txt2_true.active = true;
            }
        }
    }
    // 断线重连
    public restore_game(restore_data, my_seatid) {
        let opt_seatid = Common.toInt(restore_data["optseatid"]);
        let player_state = EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_NONE;
        let game_state = restore_data.state;

        let card_count = 0;

        let my_drop_amount = 0;

        // 先找到自己的场景
        let players = restore_data["playerlist"];
        for (let idx = 1; idx <= this._gameCore.maxPlayers; idx++) {
            let m_player = players[idx];
            if (!m_player) continue;

            if(m_player.seatid == opt_seatid) {
                this.setPlayerGaming(m_player.seatid);
            } else {
                this.setPlayerAwaiting(m_player.seatid);
            }

            if(m_player.seatid == my_seatid) {
                this.cardGroups = [];
                let group: CardGroup = {
                    type: GroupType.NUMBER,
                    IndexList: [],
                    list: [],
                }

                if(m_player.cards != null && m_player.cards != undefined) {
                    for(let i in m_player.cards){
                        group.list.push({color: m_player.cards[i].color, number: m_player.cards[i].number});
                        card_count++;
                    }
                    this.cardGroups.push(group);
                }

                player_state = m_player.state;
            }

            this.updatePlayerMoney(m_player.seatid, m_player.pmoney);
        }

        //显示庄家
        let banker_seatid = this._gameCore.getLocalSeatId(restore_data["zhuang"]);
        this.showPlayerBankerFlag(banker_seatid);

        // 显示当前操作玩家
        this.changeAwait(restore_data["optseatid"], restore_data["waittime"], restore_data["waittime"]);

        // 显示牌堆
        this.LayerItems.card_pool_nodes.zIndex = 2;
        this.LayerItems.card_pool_nodes.position = this.LayerItems.card_left.position;
        this.LayerItems.card_pool_nodes.active = true;

        // 显示癞子牌
        this.LayerItems.cardNode.active = true;
        let card = {
            color: restore_data["laizi"].cardcolor,
            number: restore_data["laizi"].cardnumber,
        }
        this.laizi_card = card;
        this._gameLogic.setJoker(card);

        this.showLaiziCard.zIndex = 1;
        this.showLaiziCard.getComponent(RMCardItem).showCardData(card.color, card.number, true);
        this.showLaiziCard.getComponent(RMCardItem).showCardLaizi(true);
        this.showLaiziCard.getComponent(RMCardItem).showCardshadow(true);
        this.showLaiziCard.setPosition(cc.v2(this.LayerItems.card_left.position.x - 54, this.LayerItems.card_left.position.y));
        let laizi_card_item = this.showLaiziCard.getComponent(RMCardItem);
        this.showLaiziCard.active = true;
        
        // 显示右边牌
        let right_card = {
            color: restore_data["right"]["color"],
            number: restore_data["right"]["number"]
        }
        this.upCard.active = true;
        this.upCard.getComponent(RMCardItem).showCardData(right_card.color, right_card.number);

        if(player_state==EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY) {
            if(game_state==EM_RUMMY_GAMESTATE.EM_RUMMY_GAMESTATE_PLAY || game_state==EM_RUMMY_GAMESTATE.EM_RUMMY_GAMESTATE_SENDCARD) {
                if(card_count >= 14){
                    this.runArrowAndLightAni(false);
                    this.user_operate_status = UserOperateState.gotCard;
                } else {
                    this.user_operate_status = UserOperateState.waitGetCard;
                }
                
                this.set_btn_state();
                this.canTouchCard = false;
                let self = this;
                self.is_hand_card_init = true;
                this.card_controller(this.cardGroups, false, false, false, () => {
                    self.canTouchCard = true;
                    self.start_server_sort(true, false);
                });
            }
        } else if(player_state == EM_RUMMY_PLAYERSTATE.EM_TEENPATTI_PLAYER_FOLD){
            let local_my_seatid = this._gameCore.getLocalSeatId(my_seatid);
            this.user_drop_info(local_my_seatid, 200)
        }
    }

    // 删除空组
    private remove_empty_group() {
        for(let i=this.cardGroups.length-1; i>=0; i--) {
            if(this.cardGroups[i].list.length==0) {
                this.cardGroups.splice(i, 1);
            }
        }
    }

////////////////////////////////////////////////////////////////////////////
/******************************功能性按钮***************************************/
    // 显示菜单层
    private onClickMenu(target: any, customEventData: any) {
        let index = Number(customEventData);
        this.runMenuAni(index);
    }
    

    // 点击换桌按钮
    private onClickHuanZhuo() {
        let self = this;
        // if (this._gameCore.isPlayerMeGaming()) {
        //     AlertHelper.instance.showExitAlert(this._gameCore.getPlayerMeBet(), () => {
        //         self.onStartMatch();
        //     });
        // } else {
            this.onStartMatch();
        // }
        // this.showSettleLayer()
    }

    // 返回大厅
    private onClickBack() {
        let self = this;
        // if (this._gameCore.isPlayerMeGaming()) {
        //     AlertHelper.instance.showExitAlert(this._gameCore.getPlayerMeBet(), () => {
        //         self._gameCore.quitGame();
        //     });
        // } else {
        this._gameCore.quitGame();
        // }

    }

    //帮助
    private onClickHelp(target: any, customEventData: any) {
        // let tmp_groups = [];
        // let group = [];
        // group.push({color: CardColor.club, number: CardNumDesc.NUM_4});
        // group.push({color: CardColor.club, number: CardNumDesc.NUM_5});
        // group.push({color: CardColor.club, number: CardNumDesc.NUM_6});
        // group.push({color: CardColor.club, number: CardNumDesc.NUM_7});
        // let card_group = JSON.parse(JSON.stringify(group));
        // tmp_groups.push({list: card_group});

        // group.length = 0;
        // group.push({color: CardColor.diamond, number: CardNumDesc.NUM_Q});
        // group.push({color: CardColor.diamond, number: CardNumDesc.NUM_10});
        // group.push({color: CardColor.diamond, number: CardNumDesc.NUM_A});
        // card_group = JSON.parse(JSON.stringify(group));
        // tmp_groups.push({list: card_group});

        // group.length = 0;
        // group.push({color: CardColor.spade, number: CardNumDesc.NUM_2});
        // group.push({color: CardColor.club, number: CardNumDesc.NUM_2});
        // group.push({color: CardColor.diamond, number: CardNumDesc.NUM_2});
        // card_group = JSON.parse(JSON.stringify(group));
        // tmp_groups.push({list: card_group});

        // group.length = 0;
        // group.push({color: CardColor.spade, number: CardNumDesc.NUM_5});
        // group.push({color: CardColor.heart, number: CardNumDesc.NUM_5});
        // group.push({color: CardColor.club, number: CardNumDesc.NUM_5});
        // card_group = JSON.parse(JSON.stringify(group));
        // tmp_groups.push({list: card_group});

        // group.length = 0;
        // group.push({color: CardColor.spade, number: CardNumDesc.NUM_A});
        // card_group = JSON.parse(JSON.stringify(group));
        // tmp_groups.push({list: card_group});

        // group.length = 0;
        // group.push({color: CardColor.club, number: CardNumDesc.NUM_10});
        // card_group = JSON.parse(JSON.stringify(group));
        // tmp_groups.push({list: card_group});

        // this.laizi_card['color'] = CardColor.heart;
        // this.laizi_card['number'] = CardNumDesc.NUM_10;

        // this.card_controller(tmp_groups, true, false, true, null);

        this.LayerItems.tipsNode.stopAllActions();
        let node = this.LayerItems.tipsNode;
        node.stopAllActions();
        if(node.active) {
            cc.tween(node)
                .then(cc.scaleTo(0.7, 0).easing(cc.easeElasticIn(1))) // 缩小到0，并使用弹性进入效果
                .call(() => {
                    node.active = false;
                    node.scale = 0;                    
                }) // 动画完成后隐藏节点
                .start();
        } else {
            let self = this;
            this.show_help_tips(true);
            node.active = true;
            node.scale = 0;
            cc.tween(node)
                .then(cc.scaleTo(0.7, 1).easing(cc.easeElasticOut(1)))
                .delay(1)
                .call(() => {
                    self.show_help_tips(false);
                })
                .start();
        }
    }

    //记录
    private onClickRecord(target: any, customEventData: any) {
        this.settleNode.active = true;   
    }


    //声音
    private onClickSound(target: any, customEventData: any) {
        // let data = { seat: 2, card_group: 2, card: { color: 5, number: 12 }, cards: [] }
        // this.otherAddcard(data)
    }

    //震动
    private onClickVibration(target: any, customEventData: any) {
        // let data = { seat: 2, card_group: 2, card: { color: 5, number: 12 }, cards: [] }
        // this.otherDiscard(data)
        // this._allPlayers[3].switchToAwait(true,29,30)
        // this.resetPlayersPos()

    }
}


