import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";

const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('rummy/RMClockLayer')
export default class RMClockLayer extends BaseLayer {

    @property(cc.ProgressBar)
    countTimeBar: cc.ProgressBar = null;

    @property(cc.Label)
    countTimeNum: cc.Label = null;

    @property(cc.Node)
    countTimeBarNode: cc.Node = null;

    @property(cc.Node)
    countTimeNumBg: cc.Node = null;
        

    private _totalTime: number = 0;
    private _optsumtime: number = 0;
    private _show: boolean = false;

    onLoad() {
        
    }

    update(dt: number) {
        if (this._show) {
            if (this._optsumtime > 0) {
                this._optsumtime -= dt;
                this.updateUI();
            } else {
                this.stopTime();
            }
        }
    }

    /** 
     * 倒计时
     */
    public resetUI(optsumtime: number, totalTime: number) {
        if (totalTime <= 0 || optsumtime <= 0) {
            this.stopTime();
            return;
        }
        this.node.active = true;
        this._totalTime = totalTime;
        this._optsumtime = optsumtime;
        this._show = true;
        this.updateUI();
    }

    private updateUI() {
        let progress = this._optsumtime / this._totalTime;;
        this.countTimeBar.progress = progress;
        this.countTimeNum.string = Common.toInt(this._optsumtime) + '';
        let color = Math.floor(255 / this._totalTime) * Common.toInt(this._optsumtime);//逐渐变红色
        this.countTimeBarNode.color = cc.color(255,color,255);
        this.countTimeNumBg.color = cc.color(255,color,255);
    }

    /**
     * 停止倒计时
     */
    public stopTime() {
        this.node.active = false;
        this._show = false;
        this.countTimeBar.progress = 1;
        this.countTimeBarNode.color = cc.color(255,255,255);
        this.countTimeNumBg.color = cc.color(255,255,255);
    }

}
