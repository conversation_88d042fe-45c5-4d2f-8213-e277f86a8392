import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import { CardData, GroupType, GroupTypeColor } from "../core/RMDefine";
import RMGameCore from "../core/RMGameCore";
import RMGameLogic from "../core/RMGameLogic";
import RMGameView from "./RMGameView";
import RMCardItem from "../core/RMCardItem";
import RMCardPool from "../core/RMCardPool";
import { Constant } from "../core/RMDefine";
import Config from "../../../../script/frame/config/Config";

const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('rummy/RMSettleLayer')
export default class RMSettleLayer extends BaseLayer {

    // 列表容器
    @property(cc.ScrollView)
    settle_scroll: cc.ScrollView = null;

    // 
    @property(cc.Node)
    node_content: cc.Node = null;

    // 结算界面标题背景
    @property(cc.Node)
    settleTitleBg: cc.Node = null;

    // 玩家信息展示Node
    @property(cc.Node)
    Panel_item: cc.Node = null;

    @property(cc.SpriteAtlas)
    cardsAtlas: cc.SpriteAtlas = null;

    // 癞子牌节点
    @property(cc.Node)
    jokerNode: cc.Node = null;

    // 癞子牌
    cardNode: cc.Node = null;

    // Continue 文字
    @property(cc.Label)
    continueTxt: cc.Label = null;       
    
    //纹理
    @property(cc.SpriteAtlas)
    user_winner_flag: cc.SpriteAtlas = null;

    @property(cc.SpriteAtlas)
    mainUiAtlas: cc.SpriteAtlas = null;

    // 玩家信息展示表
    private all_player: cc.Node[] = [];

    // 玩家结算牌
    private player_card: cc.Node[] = [];

    // 玩家信息Item
    private player_item_node: cc.Node = null;

    // 单个牌
    private card_node: cc.Node = null;

    // 癞子牌点数
    private wildcard_point: number = -1;

     /** 游戏主对象 */
    _gameCore: RMGameCore;
    _gameLogic :RMGameLogic = null;
    _gameView: RMGameView = null;
    
    
    onLoad() {
       this._gameCore = cc.Canvas.instance.getComponent("RMGameCore"); 
       this._gameLogic = cc.Canvas.instance.getComponent("RMGameLogic"); 
       this._gameView = cc.Canvas.instance.getComponent("RMGameView");
       this.initUI()
    }

    onDestroy() {
        this.clearData()
    }

    start() {
        // this.updateView(null);
    }

    initUI(){
        console.log("初始化结算界面");
        this.cardNode = RMCardPool.instance.get();
        this.jokerNode.addChild(this.cardNode);
        this.cardNode.name = "jokerCard";
        this.cardNode.scale = 1;
        // this.cardNode.position.x = this.cardNode.position.x + 50;
        // this.cardNode.position.y = this.cardNode.position.y + 50;
        this.cardNode.active = false;
        this.cardNode.zIndex = 10;

        this.settle_scroll.content.removeAllChildren();
        this.settle_scroll.scrollToTop();
        this.settle_scroll.content.height = (6 - 1) * 111 + 52 * 2;

        this.player_item_node = cc.instantiate(this.Panel_item);
        this.player_item_node.active = false;
        this.player_item_node.name = "Palyer_Item";

        this.card_node = RMCardPool.instance.get();

        this.all_player = [];
        // 初始化玩家信息
        for(let i = 0; i < Constant.player_count; i++)
        {
            // let item_node = [];
            let player_item = cc.instantiate(this.player_item_node);            
            player_item.active = false;
            this.settle_scroll.node.active = false;
            player_item.name = "Palyer_" + i;
            player_item.zIndex = 10 * i;
            this.settle_scroll.content.addChild(player_item);

            this.all_player.push(player_item)
        }
    }

    public clearData(){
        RMCardPool.instance.put(this.cardNode);
        RMCardPool.instance.put(this.card_node);
    }

    // 设置结算界面数据
    public setPlayerData(player_data){
        const colorbgList = ['rummy_ui_9','rummy_ui_10','rummy_ui_11'];//橙色 绿色 黄色

        this.settle_scroll.node.active = true;
        for (let key in player_data){
            let player_info = player_data[key];
            let player_item = this.all_player[key];
            player_item.active = true;
            // 玩家信息相关
            let player_node = player_item.getChildByName("Panel");
            player_node.active = true;
            let user_head_img = player_node.getChildByName("imgMask").getChildByName("headImg");
            
            let user_name = player_node.getChildByName("name");
            user_name.active = true;
            if(player_info.nick_name != null && player_info.nick_name != undefined){
                user_name.getComponent(cc.Label).string = player_info.nick_name;
            }

            let winner_flag = player_node.getChildByName("winner_flag");
            winner_flag.active = true;
            let flag_sprite_name = player_info.amount > 0 ? "rummy_ui_65" : "rummy_ui_66"
            winner_flag.getComponent(cc.Sprite).spriteFrame = this.user_winner_flag.getSpriteFrame(flag_sprite_name);
            
            // 分数
            let user_score = player_item.getChildByName("score");
            user_score.active = true;
            if(player_info.score != null && player_info.score != undefined){
                user_score.getComponent(cc.Label).string = player_info.score;
            }

            // 输赢钱
            let user_amount = player_item.getChildByName("amount");
            user_amount.active = true;
            if(player_info.amount != null && player_info.amount != undefined){
                let money = (player_info.amount / Config.SCORE_RATE).toFixed(2);
                user_amount.getComponent(cc.Label).string = money;
            }
            
            let user_card_type_desc = player_item.getChildByName("settleCardsTypeDesc");
            user_card_type_desc.active = true;

            // 玩家牌节点
            let user_card_node = player_item.getChildByName("settleCards");
            user_card_node.active = true;
            let card_data = player_info.card;

            // 这里首先查询用户是否满足条件
            let type_list = [];
            let seq_count = 0, real_seq_count = 0;
            for(let n in card_data) {
                let card_list = [];
                for(const i in card_data[n].cardlist) {
                    let card = this._gameLogic.convertLocalCardToServerCard(card_data[n].cardlist[i].color, card_data[n].cardlist[i].value);
                    card_list.push(card);
                }

                let gtype = this._gameLogic.is_Set_or_Seq(card_list, this.wildcard_point);
                if(gtype == GroupType.PURE_SEQUENCE) {
                    seq_count ++;
                    real_seq_count ++;
                } else if(gtype == GroupType.SEQUENCE) {
                    seq_count ++;
                } 

                type_list.push(gtype);
            }

            let is_combi: boolean = false;
            if(real_seq_count>=1 && seq_count>=2) {
                is_combi = true;
            }

            let color_list = [];
            for(let i=0; i<type_list.length; i++) {
                let colortype = GroupTypeColor.Orange;
                if(is_combi) {
                    if(type_list[i]==GroupType.PURE_SEQUENCE || type_list[i]==GroupType.SEQUENCE || type_list[i]==GroupType.SET || type_list[i]==GroupType.NUMBER_SINGLE_JOKER) {
                        colortype = GroupTypeColor.Gree
                    } else {
                        colortype = GroupTypeColor.Orange;
                    }
                } else {
                    if(type_list[i]==GroupType.PURE_SEQUENCE || type_list[i]==GroupType.NUMBER_SINGLE_JOKER) {
                        colortype = GroupTypeColor.Gree
                    } else if(type_list[i]==GroupType.SEQUENCE || type_list[i]==GroupType.SET) {
                        colortype = GroupTypeColor.Yellow;
                    } else {
                        colortype = GroupTypeColor.Orange;
                    }
                }

                color_list.push(colortype)
            }
            
            for(let n in card_data){
                let group_index = Common.toInt(n) - 1;
                let group_item = user_card_node.getChildByName("groupNode" + group_index);
                group_item.removeAllChildren();
                group_item.active = true;
                for (let i in card_data[n].cardlist) {
                    let card = cc.instantiate(this.card_node);
                    if(card != null && card != undefined){
                        let card_item = card.getComponent(RMCardItem);
                        card.name = "card_" + player_info.player_id + n + i;
                        card.active = true;
                        card.position = cc.v3(0, 0, 0);
                        card.scale = 1;
                        card.zIndex = Common.toInt(i) + 1;
                        card_item.showCardData(card_data[n].cardlist[i].color, card_data[n].cardlist[i].number);
                        group_item.addChild(card);
                    } else {
                        cc.error("card is null or undefined!");
                    }
                }

                let type_width = group_item.width;

                let groupTypeBg = cc.instantiate(user_card_type_desc);
                groupTypeBg.name = "card_group_" + n;
                groupTypeBg.active = true;
                groupTypeBg.setContentSize(cc.size(type_width, 30));
                groupTypeBg.position.x = group_index * 150;
                groupTypeBg.getComponent(cc.Sprite).spriteFrame = this.mainUiAtlas.getSpriteFrame(colorbgList[group_index]);
                group_item.addChild(groupTypeBg);
            }

            if(player_info.amount > 0){
                user_name.getComponent(cc.Label).color = cc.color()
            }
        }
        
        this.settle_scroll.content.active = true;
    }

    public onClickClose() {
        this._gameView.resetAllUI();
        this._gameLogic.popUpEffect1(this.node,false);
    }

    //离开
    public onClickLeaveTable(target: any, customEventData: any) {
        this._gameCore.quitGame();
    }

    //继续游戏
    public onClicContinue(target: any, customEventData: any) {
        this.onClickClose();
    }

    // 显示结算数据
    public showSettleInfo(data, card_data){
        if (data == null || data == undefined){
            return;
        }
        
        this.settle_scroll.node.active = true;
        this.settleTitleBg.active = true;

        this.setJokerNodeData(card_data);

        this.setPlayerData(data);
    }

    // 显示日期时间
    public setGameBaseInfo(data){
        
    }

    //显示左下角癞子牌
    public setJokerNodeData(card_data: CardData){
        this.jokerNode.active = true;
        this.cardNode.active = true;
        this.cardNode.getComponent(RMCardItem).showCardData(card_data.color, card_data.number, true);
        this.wildcard_point = card_data.number;
        if(this.wildcard_point==14) {
            this.wildcard_point = 1;
        }
    }
}
