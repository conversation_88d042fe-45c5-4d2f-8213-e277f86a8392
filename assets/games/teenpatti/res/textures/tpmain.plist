<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>DL_bg2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{118,119}</string>
                <key>spriteSourceSize</key>
                <string>{118,119}</string>
                <key>textureRect</key>
                <string>{{364,895},{118,119}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>avatar.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{0,1014},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{105,110}</string>
                <key>spriteSourceSize</key>
                <string>{105,110}</string>
                <key>textureRect</key>
                <string>{{406,553},{105,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{86,86}</string>
                <key>spriteSourceSize</key>
                <string>{86,86}</string>
                <key>textureRect</key>
                <string>{{0,379},{86,86}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{99,88}</string>
                <key>spriteSourceSize</key>
                <string>{99,88}</string>
                <key>textureRect</key>
                <string>{{243,465},{99,88}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_base_map.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{365,52}</string>
                <key>spriteSourceSize</key>
                <string>{365,52}</string>
                <key>textureRect</key>
                <string>{{0,124},{365,52}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_dialog_box.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{245,49}</string>
                <key>spriteSourceSize</key>
                <string>{245,49}</string>
                <key>textureRect</key>
                <string>{{0,73},{245,49}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_shadow.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{128,1014},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_Add.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{191,74}</string>
                <key>spriteSourceSize</key>
                <string>{191,74}</string>
                <key>textureRect</key>
                <string>{{309,305},{191,74}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_betting.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{192,118}</string>
                <key>spriteSourceSize</key>
                <string>{192,118}</string>
                <key>textureRect</key>
                <string>{{0,777},{192,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_check_card.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{192,66}</string>
                <key>spriteSourceSize</key>
                <string>{192,66}</string>
                <key>textureRect</key>
                <string>{{0,234},{192,66}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_close.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{76,71}</string>
                <key>spriteSourceSize</key>
                <string>{76,71}</string>
                <key>textureRect</key>
                <string>{{320,234},{76,71}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_com_tiles-1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{182,118}</string>
                <key>spriteSourceSize</key>
                <string>{182,118}</string>
                <key>textureRect</key>
                <string>{{192,777},{182,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_com_tiles.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{182,118}</string>
                <key>spriteSourceSize</key>
                <string>{182,118}</string>
                <key>textureRect</key>
                <string>{{0,895},{182,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_fold.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{182,118}</string>
                <key>spriteSourceSize</key>
                <string>{182,118}</string>
                <key>textureRect</key>
                <string>{{182,895},{182,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_green1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{243,86}</string>
                <key>spriteSourceSize</key>
                <string>{243,86}</string>
                <key>textureRect</key>
                <string>{{86,379},{243,86}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_mess.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,71}</string>
                <key>spriteSourceSize</key>
                <string>{71,71}</string>
                <key>textureRect</key>
                <string>{{396,234},{71,71}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_recharge.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{202,96}</string>
                <key>spriteSourceSize</key>
                <string>{202,96}</string>
                <key>textureRect</key>
                <string>{{0,553},{202,96}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_rule.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,71}</string>
                <key>spriteSourceSize</key>
                <string>{71,71}</string>
                <key>textureRect</key>
                <string>{{0,305},{71,71}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_yellow.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{243,86}</string>
                <key>spriteSourceSize</key>
                <string>{243,86}</string>
                <key>textureRect</key>
                <string>{{0,465},{243,86}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>d.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{34,36}</string>
                <key>spriteSourceSize</key>
                <string>{34,36}</string>
                <key>textureRect</key>
                <string>{{90,33},{34,36}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>gold_frame1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{133,38}</string>
                <key>spriteSourceSize</key>
                <string>{133,38}</string>
                <key>textureRect</key>
                <string>{{161,33},{133,38}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>gold_frame2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{238,73}</string>
                <key>spriteSourceSize</key>
                <string>{238,73}</string>
                <key>textureRect</key>
                <string>{{71,305},{238,73}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>he_avatr.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{141,143}</string>
                <key>spriteSourceSize</key>
                <string>{141,143}</string>
                <key>textureRect</key>
                <string>{{256,1014},{141,143}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>he_base_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{108,108}</string>
                <key>spriteSourceSize</key>
                <string>{108,108}</string>
                <key>textureRect</key>
                <string>{{298,553},{108,108}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_exit_lobby.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{37,37}</string>
                <key>spriteSourceSize</key>
                <string>{37,37}</string>
                <key>textureRect</key>
                <string>{{124,33},{37,37}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_menu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{20,35}</string>
                <key>spriteSourceSize</key>
                <string>{20,35}</string>
                <key>textureRect</key>
                <string>{{35,33},{20,35}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_play.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{35,35}</string>
                <key>spriteSourceSize</key>
                <string>{35,35}</string>
                <key>textureRect</key>
                <string>{{55,33},{35,35}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_settinge.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{35,34}</string>
                <key>spriteSourceSize</key>
                <string>{35,34}</string>
                <key>textureRect</key>
                <string>{{0,33},{35,34}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_switch_table.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{36,33}</string>
                <key>spriteSourceSize</key>
                <string>{36,33}</string>
                <key>textureRect</key>
                <string>{{457,0},{36,33}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_agree-1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{125,55}</string>
                <key>spriteSourceSize</key>
                <string>{125,55}</string>
                <key>textureRect</key>
                <string>{{365,124},{125,55}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_agree.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{125,55}</string>
                <key>spriteSourceSize</key>
                <string>{125,55}</string>
                <key>textureRect</key>
                <string>{{0,179},{125,55}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_reduse.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{125,55}</string>
                <key>spriteSourceSize</key>
                <string>{125,55}</string>
                <key>textureRect</key>
                <string>{{125,179},{125,55}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_refuse.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{125,55}</string>
                <key>spriteSourceSize</key>
                <string>{125,55}</string>
                <key>textureRect</key>
                <string>{{250,179},{125,55}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_tag_play.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{99,28}</string>
                <key>spriteSourceSize</key>
                <string>{99,28}</string>
                <key>textureRect</key>
                <string>{{358,0},{99,28}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>line.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{358,2}</string>
                <key>spriteSourceSize</key>
                <string>{358,2}</string>
                <key>textureRect</key>
                <string>{{0,0},{358,2}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>linght_green.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{96,96}</string>
                <key>spriteSourceSize</key>
                <string>{96,96}</string>
                <key>textureRect</key>
                <string>{{202,553},{96,96}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>linght_red.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{175,175}</string>
                <key>spriteSourceSize</key>
                <string>{175,175}</string>
                <key>textureRect</key>
                <string>{{0,1157},{175,175}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>linght_yellow.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{175,175}</string>
                <key>spriteSourceSize</key>
                <string>{175,175}</string>
                <key>textureRect</key>
                <string>{{175,1157},{175,175}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mine_avatr.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{114,114}</string>
                <key>spriteSourceSize</key>
                <string>{114,114}</string>
                <key>textureRect</key>
                <string>{{0,663},{114,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mine_base.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{380,114}</string>
                <key>spriteSourceSize</key>
                <string>{380,114}</string>
                <key>textureRect</key>
                <string>{{114,663},{380,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>score_unit.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{39,40}</string>
                <key>spriteSourceSize</key>
                <string>{39,40}</string>
                <key>textureRect</key>
                <string>{{294,33},{39,40}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>tp_img_liwu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{49,51}</string>
                <key>spriteSourceSize</key>
                <string>{49,51}</string>
                <key>textureRect</key>
                <string>{{245,73},{49,51}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>vs.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,68}</string>
                <key>spriteSourceSize</key>
                <string>{128,68}</string>
                <key>textureRect</key>
                <string>{{192,234},{128,68}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>tpmain.png</string>
            <key>size</key>
            <string>{511,1332}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:4230cce6d258929e0faffdb61aab3728:5a049e2e3b0eab47b5497f59a79928c8:f34c8503d291f4058a705dd3152ef5e6$</string>
            <key>textureFileName</key>
            <string>tpmain.png</string>
        </dict>
    </dict>
</plist>
