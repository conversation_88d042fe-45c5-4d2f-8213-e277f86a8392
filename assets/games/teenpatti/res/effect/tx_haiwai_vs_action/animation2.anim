{"__type__": "cc.AnimationClip", "_name": "animation2", "_objFlags": 0, "_rawFiles": null, "_duration": 0.1, "sample": 60, "speed": 0.5, "wrapMode": 1, "curveData": {"paths": {"Panel_1": {"props": {"position": [], "anchorX": [], "anchorY": [], "color": []}}, "VS_bg_blue_2/VS_v_4": {"props": {"position": [], "scaleX": [], "scaleY": [], "color": [], "opacity": [], "angle": []}}, "VS_bg_blue_2/img_win": {"props": {"position": [{"frame": 0, "value": [-149.7959, -75.9905]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.1, "value": 255}]}}, "VS_bg_blue_2/img_lose": {"props": {"position": [{"frame": 0, "value": [-149.7959, -75.9905]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.1, "value": 0}]}}, "VS_bg_blue_2": {"props": {"position": [{"frame": 0, "value": [-265.1597, 85.1169]}], "scaleX": [{"frame": 0, "value": 1}, {"frame": 0.1, "value": 1}], "scaleY": [{"frame": 0, "value": 1}, {"frame": 0.1, "value": 1}], "opacity": [{"frame": 0, "value": 255}], "color": [{"frame": 0, "value": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}], "angle": [{"frame": 0, "value": 0}]}}, "VS_bg_red_3/VS_s_5": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}, "VS_bg_red_3/img_win": {"props": {"position": [], "opacity": [{"frame": 0, "value": 0}]}}, "VS_bg_red_3/img_lose": {"props": {"position": [], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.1, "value": 255}]}}, "VS_bg_red_3": {"props": {"position": [{"frame": 0, "value": [215.3276, 15.1289]}], "scaleX": [{"frame": 0, "value": 1}, {"frame": 0.06666666666666667, "value": 0.95}], "scaleY": [{"frame": 0, "value": 1}, {"frame": 0.06666666666666667, "value": 0.95}], "opacity": [{"frame": 0, "value": 255}], "color": [{"frame": 0, "value": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"frame": 0.06666666666666667, "value": {"__type__": "cc.Color", "r": 127, "g": 127, "b": 127, "a": 255}}], "angle": [{"frame": 0, "value": 0}]}, "comps": {"cc.Sprite": {"spriteFrame": [{"frame": 0, "value": {"__uuid__": "eb5d93da-47a3-4cac-a5e6-b25093359cca"}}]}}}, "tx_HBC_jinji_lizi_00_10": {"props": {"position": [], "opacity": []}}, "liandui_5_00000_11": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}, "guangxiao_ty_12": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}, "tx_guangxiao2_13": {"props": {"position": [], "scaleX": [], "scaleY": [], "anchorX": [], "anchorY": [], "opacity": [], "angle": []}}, "VS_bg_blue_2_0": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}, "VS_bg_red_3_0": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}, "Node_1/tx_haiwai_vs_shandian_00001_26": {"props": {"opacity": []}}, "Node_1_0/tx_haiwai_vs_shandian_00001_26": {"props": {"opacity": []}}, "Node_1_0": {"props": {"position": [], "scaleX": [], "scaleY": []}}}}, "events": []}