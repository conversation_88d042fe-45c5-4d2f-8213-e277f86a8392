<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>TP_txt_Blind.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,204},{84,38}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{84,38}}</string>
                <key>sourceSize</key>
                <string>{84,38}</string>
            </dict>
            <key>TP_txt_Blind_ydy.png</key>
            <dict>
                <key>frame</key>
                <string>{{186,194},{84,38}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{84,38}}</string>
                <key>sourceSize</key>
                <string>{84,38}</string>
            </dict>
            <key>TP_txt_Chaal.png</key>
            <dict>
                <key>frame</key>
                <string>{{94,170},{90,32}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,32}}</string>
                <key>sourceSize</key>
                <string>{90,32}</string>
            </dict>
            <key>TP_txt_Chaal_ydy.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,170},{90,32}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,32}}</string>
                <key>sourceSize</key>
                <string>{90,32}</string>
            </dict>
            <key>TP_txt_Show.png</key>
            <dict>
                <key>frame</key>
                <string>{{94,132},{90,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,36}}</string>
                <key>sourceSize</key>
                <string>{90,36}</string>
            </dict>
            <key>TP_txt_Show_ydy.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,132},{90,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{90,36}}</string>
                <key>sourceSize</key>
                <string>{90,36}</string>
            </dict>
            <key>TP_txt_Side.png</key>
            <dict>
                <key>frame</key>
                <string>{{88,240},{76,34}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{76,34}}</string>
                <key>sourceSize</key>
                <string>{76,34}</string>
            </dict>
            <key>TP_txt_Side_ydy.png</key>
            <dict>
                <key>frame</key>
                <string>{{88,204},{76,34}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{76,34}}</string>
                <key>sourceSize</key>
                <string>{76,34}</string>
            </dict>
            <key>TP_txt_bx2.png</key>
            <dict>
                <key>frame</key>
                <string>{{186,163},{43,29}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{43,29}}</string>
                <key>sourceSize</key>
                <string>{43,29}</string>
            </dict>
            <key>TP_txt_cx2.png</key>
            <dict>
                <key>frame</key>
                <string>{{186,132},{43,29}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{43,29}}</string>
                <key>sourceSize</key>
                <string>{43,29}</string>
            </dict>
            <key>lol_glow_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{252,128}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{252,128}}</string>
                <key>sourceSize</key>
                <string>{252,128}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>tx_haiwai_TP_txt.png</string>
            <key>size</key>
            <string>{256,512}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:217a1bc15e6408ef200d4c287611f04b$</string>
            <key>textureFileName</key>
            <string>tx_haiwai_TP_txt.png</string>
        </dict>
    </dict>
</plist>
