<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>flare01_02_00.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{254,256}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{1,0},{254,256}}</string>
                <key>sourceSize</key>
                <string>{256,256}</string>
            </dict>
            <key>tx_bipai_shandian_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,260},{194,417}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{194,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
            <key>tx_bipai_shandian_00001.png</key>
            <dict>
                <key>frame</key>
                <string>{{258,2},{194,417}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{194,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
            <key>tx_bipai_shandian_00002.png</key>
            <dict>
                <key>frame</key>
                <string>{{421,198},{194,417}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{194,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
            <key>tx_bipai_shandian_00003.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,456},{194,417}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{194,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
            <key>tx_bipai_shandian_00004.png</key>
            <dict>
                <key>frame</key>
                <string>{{813,421},{172,417}}</string>
                <key>offset</key>
                <string>{11,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,0},{172,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
            <key>tx_bipai_shandian_00005.png</key>
            <dict>
                <key>frame</key>
                <string>{{198,456},{194,417}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{194,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
            <key>tx_bipai_shandian_00006.png</key>
            <dict>
                <key>frame</key>
                <string>{{813,2},{180,417}}</string>
                <key>offset</key>
                <string>{-7,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{180,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
            <key>tx_bipai_shandian_00007.png</key>
            <dict>
                <key>frame</key>
                <string>{{394,813},{182,417}}</string>
                <key>offset</key>
                <string>{-6,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{182,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
            <key>tx_bipai_shandian_00008.png</key>
            <dict>
                <key>frame</key>
                <string>{{394,617},{194,417}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{194,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
            <key>tx_bipai_shandian_00009.png</key>
            <dict>
                <key>frame</key>
                <string>{{617,198},{194,417}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{194,417}}</string>
                <key>sourceSize</key>
                <string>{194,417}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>tx_haiwai_bipai_shandian1.png</string>
            <key>size</key>
            <string>{995,997}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:e84723d3e73323be736b9f905766d419:861bd03d19e81a85c5216ff22c075298:4bf07c92c5450eb580a60df9f83f0676$</string>
            <key>textureFileName</key>
            <string>tx_haiwai_bipai_shandian1.png</string>
        </dict>
    </dict>
</plist>
