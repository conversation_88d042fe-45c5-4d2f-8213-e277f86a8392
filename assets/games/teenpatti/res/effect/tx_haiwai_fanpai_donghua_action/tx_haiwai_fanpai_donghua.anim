{"__type__": "cc.AnimationClip", "_name": "tx_haiwai_fanpai_donghua", "_objFlags": 0, "_rawFiles": null, "_duration": 0.4166666666666667, "sample": 60, "speed": 0.5, "wrapMode": 1, "curveData": {"paths": {"imgBg1": {"props": {"position": [{"frame": 0.21666666666666667, "value": [-5.9764, -6.1694]}, {"frame": 0.31666666666666665, "value": [-508.9764, -6.1693]}, {"frame": 0.36666666666666664, "value": [-485.9764, -6.1693]}], "scaleX": [{"frame": 0.31666666666666665, "value": 1}], "scaleY": [{"frame": 0.31666666666666665, "value": 1}], "opacity": [{"frame": 0.2, "value": 0}, {"frame": 0.21666666666666667, "value": 255}, {"frame": 0.31666666666666665, "value": 255}], "angle": [{"frame": 0.31666666666666665, "value": 0}]}}, "imgBg2": {"props": {"position": [{"frame": 0.21666666666666667, "value": [-5.9764, -6.1694]}, {"frame": 0.26666666666666666, "value": [-5.9764, -6.1694]}, {"frame": 0.36666666666666664, "value": [-265.9764, -6.1693]}, {"frame": 0.4166666666666667, "value": [-246.9764, -6.1693]}], "scaleX": [{"frame": 0.36666666666666664, "value": 1}], "scaleY": [{"frame": 0.36666666666666664, "value": 1}], "opacity": [{"frame": 0.2, "value": 0}, {"frame": 0.21666666666666667, "value": 255}, {"frame": 0.26666666666666666, "value": 255}, {"frame": 0.36666666666666664, "value": 255}], "angle": [{"frame": 0.36666666666666664, "value": 0}]}}, "imgBg3": {"props": {"position": [{"frame": 0, "value": [-5.9756, 304.0923]}, {"frame": 0.13333333333333333, "value": [-5.9763, 22.2334]}, {"frame": 0.21666666666666667, "value": [-5.9764, -6.1694]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.13333333333333333, "value": 255}]}}, "imgBg4": {"props": {"position": [{"frame": 0.21666666666666667, "value": [-5.9764, -6.1694]}, {"frame": 0.26666666666666666, "value": [-5.9764, -6.1694]}, {"frame": 0.36666666666666664, "value": [256.3823, -6.1689]}, {"frame": 0.4166666666666667, "value": [234.6765, -6.1692]}], "opacity": [{"frame": 0.2, "value": 0}, {"frame": 0.21666666666666667, "value": 255}, {"frame": 0.26666666666666666, "value": 255}]}}, "imgBg5": {"props": {"position": [{"frame": 0.21666666666666667, "value": [-5.9764, -6.1694]}, {"frame": 0.31666666666666665, "value": [497.9788, -6.1682]}, {"frame": 0.36666666666666664, "value": [474.3862, -6.1693]}], "opacity": [{"frame": 0.2, "value": 0}, {"frame": 0.21666666666666667, "value": 255}]}}, "txtClickTips": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.4, "value": 0}, {"frame": 0.4166666666666667, "value": 255}]}}}}, "events": []}