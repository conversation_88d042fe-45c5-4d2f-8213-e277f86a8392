[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_rawFiles": null, "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 19}, {"__id__": 22}], "_tag": -1, "_active": true, "_components": [{"__id__": 25}], "_prefab": {"__id__": 26}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Node_4", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 8}, {"__id__": 13}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 18}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [4.16, 222.9769, 0, 0, 0, 0, 1, 1, 1.1735, 1]}}, {"__type__": "cc.Node", "_name": "Node_1_0_0", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 7}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_eulerAngles": {"__type__": "cc.Vec3", "x": -50.6691, "y": -50.6628, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9.4422, -153.9556, 0, 0, 0, 0, 1, -0.6406, 0.5533, 1]}}, {"__type__": "cc.Node", "_name": "tx_bipai_shandian2_00000_2", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 5}], "_prefab": {"__id__": 6}, "_id": "", "_opacity": 204, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 151, "height": 225}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.5292, -94.2037, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "_spriteFrame": {"__uuid__": "33bd7ef4-ccf9-4993-a60d-d1edefad2c1f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3d3CUF63pGio+p7au9tGUt", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fe68XUmSFDtYZH5MVLmAI2", "sync": false}, {"__type__": "cc.Node", "_name": "Node_1", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 9}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 12}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.8373, -61.8861, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "tx_bipai_shandian2_00000_2", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_id": "", "_opacity": 101, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 151, "height": 225}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.5292, -94.2037, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_spriteFrame": {"__uuid__": "98b51664-83b8-496b-8af3-c64350187c29"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f9EaadfyJIZqxgup3s0I6S", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "249UF64BRDEp2OIW1jlebX", "sync": false}, {"__type__": "cc.Node", "_name": "Node_1_0", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 14}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 17}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_eulerAngles": {"__type__": "cc.Vec3", "x": 48.8023, "y": 48.8013, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5.4263, -90.4486, 0, 0, 0, 0, 1, -0.7806, 0.5977, 1]}}, {"__type__": "cc.Node", "_name": "tx_bipai_shandian2_00000_2", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 15}], "_prefab": {"__id__": 16}, "_id": "", "_opacity": 153, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 151, "height": 225}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.5292, -94.2037, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_spriteFrame": {"__uuid__": "3dd7169e-5755-48dd-8969-7162c90d1a98"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34mtyBukFEwpZF8KXuIJf6", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "88r1IYJM9ONbjASoTsMwad", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6eyPZe9wZPv5pKnin480ny", "sync": false}, {"__type__": "cc.Node", "_name": "glow_0014_14", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 20}], "_prefab": {"__id__": 21}, "_id": "", "_opacity": 65, "_color": {"__type__": "cc.Color", "r": 30, "g": 144, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 256}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.0438, 1.6445, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_spriteFrame": {"__uuid__": "c28c870b-fd1b-4e98-9b20-6bceec572b78"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b5QwrJP7NC74FbIXjojXgl", "sync": false}, {"__type__": "cc.Node", "_name": "glow_0026_19", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 23}], "_prefab": {"__id__": 24}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 512}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3.4034, 0.6447, 0, 0, 0, 0, 1, 1.207, 0.9499, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_spriteFrame": {"__uuid__": "c3f66c97-02df-4eb9-af37-e0ec20f3a4f8"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f84Hj66JRJiI5MTuWWqFQR", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_defaultClip": null, "_clips": [{"__uuid__": "1e33e767-881d-4ec6-a2d2-fc37f764a0f6"}], "playOnLoad": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50PHwnyxBIepNsC1M28Ef/", "sync": false}]