const { ccclass, property } = cc._decorator;
import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import { CardType } from "./TPDefine";
@ccclass
export default class TPAudioMng extends BaseLayer {

    @property(cc.AudioClip)
    dealcards2:cc.AudioClip=null;

    playMusic() {
        let num = Common.random(1, 2);
        let path = "res/sound/gamebgm"+num;
        AudioHelper.instance.playMusic(path, true);

    }
    pauseMusic() {
        AudioHelper.instance.pauseMusic();
    }

    resumeMusic() {
        AudioHelper.instance.resumeMusic();
    }

    _loadPlaySFX(path) {
        AudioHelper.instance.playEffect("res/sound/" + path);
    }

    _loadPlayRandomSFX(pathArr) {
        let index = Math.floor(Math.random() * (pathArr.length));
        this._loadPlaySFX(pathArr[index]);
    }

    //发牌
    playSendCard() {
        this._loadPlaySFX("dealcards");
    }

    //飞金币
    playFlyGold() {
        this._loadPlaySFX("placeabet");
    }

    //轮到
    playYourTurn() {
        this._loadPlaySFX("yourturn");
    }

    playNoTime() {
        this._loadPlaySFX("timeDown");
    }

    //chaal
    playChaal(double) {
        let num = Common.random(1, 3);
        let name = 's_chaal_';
        if (double) {
            name = name + 'x2_';
        }
        name = name + num;
        this._loadPlaySFX(name);
    }

    //Blind
    playBlind(double) {
        let num = Common.random(1, 3);
        let name = 's_blind_';
        if (double) {
            name = name + 'x2_';
        }
        name = name + num;
        this._loadPlaySFX(name);
    }

    //pack
    playPacked() {
        this._loadPlaySFX("pack");
    }

    //see
    playSee() {
        this._loadPlaySFX("see");
    }

    //Blind
    playCardType(type) {
        let name = ''
        if (type == CardType.EM_TEENPATTI_CARDTYPE_DAN) {
            name = 's_set_hightcards_1';
        } else if (type == CardType.EM_TEENPATTI_CARDTYPE_DUIZI) {
            name = 's_set_pair_2';
        } else if (type == CardType.EM_TEENPATTI_CARDTYPE_SHUNZI) {
            name = 's_set_sequence_4';
        } else if (type == CardType.EM_TEENPATTI_CARDTYPE_TONGHUA) {
            name = 's_set_color_3';
        } else if (type == CardType.EM_TEENPATTI_CARDTYPE_TONGHUASHUN) {
            name = 's_set_puresequence_5';
        } else if (type == CardType.EM_TEENPATTI_CARDTYPE_BAOZI) {
            name = 's_set_trail_6';
        }
        this._loadPlaySFX(name);
    }

    //vs
    playVS() {
        this._loadPlaySFX("vs");
    }

    //thound
    playThound() {
        this._loadPlaySFX("thound");
    }
    //show
    playCompareShow() {
        let num = Common.random(1, 2);
        let name = 's_show_';
        name = name + num;
        this._loadPlaySFX(name);
    }

    //side
    playCompareSide() {
        let num = Common.random(1, 3);
        let name = 's_sideshow_';
        name = name + num;
        this._loadPlaySFX(name);
    }

    //compare
    playCompare() {
        this._loadPlaySFX("compare");
    }

    //accept
    playAccept() {
        let num = Common.random(1, 3);
        let name = 's_accept_ss_';
        name = name + num;
        this._loadPlaySFX(name);
    }
    //refuse
    playRefuse() {
        let num = Common.random(1, 3);
        let name = 's_refuse_';
        name = name + num;
        this._loadPlaySFX(name);
    }

    //win
    playWin() {
        this._loadPlaySFX("winner");
    }

    //win
    playWinGame() {
        let num = Common.random(1, 7);
        let name = 's_win_game_';
        name = name + num;
        this._loadPlaySFX(name);
    }
}

