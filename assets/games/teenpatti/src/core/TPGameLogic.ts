import Common from "../../../../script/frame/common/Common";

//////////////////////////////////////////////////////////////////////////////////
const {ccclass, property} = cc._decorator;
@ccclass
export default class TPGameLogic extends cc.Component {
   //////////////////////////////////////////////////////////////////////////////
    // 排列用户手牌
    public sortCardData(cards: Array<any>) {
        let self = this;
        cards.sort((a: any, b: any): number =>{
            let value1 = Common.toInt(a["number"]);
            let value2 = Common.toInt(b["number"]);
            if (value1 == value2) {
                let color1 = self.cardColorIndex(a["color"]);
                let color2 = self.cardColorIndex(b["color"]);
                if (color1 < color2) {
                    return -1;
                }
            }
            else if (value1 < value2) {
                return -1;
            }
            return 1;
        });
    }

    //////////////////////////////////////////////////////////////////////////////
    // 获取牌花色的排列索引
    private cardColorIndex(color: any): number {
        color = Common.toInt(color);
        switch(color) {
            case 6: return 0; break;
            case 4: return 1; break;
            case 5: return 2; break;
            case 3: return 3; break;
        }
        return 0;
    }
}
