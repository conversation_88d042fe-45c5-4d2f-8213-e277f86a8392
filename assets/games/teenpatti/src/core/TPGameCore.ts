import GameCore from "../../../../script/frame/model/GameCore";
import { GameEvent, QuitReason, RoomState } from "../../../../script/frame/common/Define";
import { Constant, Protos, GameState, GameTextTips, BetType, EM_TEENPATTI_PLAYERSTATE, updateGameTextTips, CardType } from "./TPDefine";
import Common from "../../../../script/frame/common/Common";
import TPGameView from "../view/TPGameView";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import EventManager from "../../../../script/frame/manager/EventManager";
import { TextTips } from "../../../../script/frame/common/Language";
import TPAudioMng from "./TPAudioMng";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import { HallActivity } from "../../../../script/frame/common/Protocol";
import TPTaskNode from "../view/TPTaskNode";
import TPTaskRewardLayer from "../view/TPTaskRewardLayer";
import HallManager from "../../../../script/frame/manager/HallManager";
import Config from "../../../../script/frame/config/Config";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class TPGameCore extends GameCore {

    @property(TPTaskNode)
    taskNode: TPTaskNode = null;

    @property(TPTaskRewardLayer)
    taskRewardLayer: TPTaskRewardLayer = null;

    //////////////////////////////////////////////////////////////////////////////
    // 玩家ID=>操作对象
    private _mapPlayers = new Map<number, any>();
    /** 自己视图座位 */
    public meLocalSeatId: number = 0;

    //////////////////////////////////////////////////////////////////////////////
    // 游戏视图对象
    public gameView: TPGameView = null;
    // 游戏逻辑对象
    public gameLogic: any = null;
    //声音
    public audioMng: TPAudioMng = null;

    // 游戏的低分
    public difen: number = 0;
    // 游戏入场分
    public inmoney: number = 0;
    // 游戏入场分
    public minmoney: number = 0;

    // 房间的状态
    public roomState: number = 0;
    // 游戏的状态
    public gameState: number = -1;
    // 庄家用户ID
    public bankerId: number = 0;
    //庄家坐位号
    public bankerLocalId: number = -1;
    // 自己是否在游戏中
    public meIsGaming: boolean = false;
    // 是否被踢
    public isKicked: boolean = false;
    // 是否暂离
    public isZanLi: boolean = false;
    /** 当前下注倍数 */
    public curtimes: number = 0;
    /** 当前操作用户视图座位 */
    public optLocalSeatId: number = 0;
    /** 当前操作比牌用户视图座位 */
    public optCompareLocalSeatId: number = -1;

    /** 自己下注额 */
    public mybetall: number = 0;
    /** 已下总注金额  */
    public allbet: number = 0;
    /** 下注额 */
    public playersBet = [];

    /** 当前轮数 */
    public turnnum: number = 0;
    /** 看牌*/
    public islookArr = [];
    public isMeLook = false;


    /** 操作时间 */
    public optsumtime: number = 0;
    /** 最大总注 */
    public potlimit: number = 0;
    /** 单注封顶  */
    public chaallimit: number = 0;
    /** 暗注最大回合  */
    public blindroundlimit: number = 0;
    /** 最小比牌轮数  */
    public compminturnnum: number = 0;
    /** 每局可拒绝比牌的次数  */
    public rejectcompnum: number = 0;
    /** 每局拒绝比牌的次数  */
    public rejectnum: number = 0;
    /** 进入房间的时间  */
    public enterRoomTime: number = 0;

    public firstEnter = true;
    public isCompareing = false;
    public quitChangeMoney: number = 0;

    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this.gameView = this.node.getComponent("TPGameView");
        this.gameLogic = this.node.getComponent("TPGameLogic");
        this.audioMng = this.node.getComponent("TPAudioMng");
        this.maxPlayers = Constant.PLAYER_COUNT;
        this.updateGameLan();
        super.onLoad();
    }

    //////////////////////////////////////////////////////////////////////////////


    // 玩家下注 1-跟注, 2-加注(加一倍)
    public callScore(fill: number) {
        let plyInfo = this._mapPlayers.get(this.playerid);
        let curbet=this.curtimes*this.difen*fill;
        if(this.isMeLook){
            curbet=curbet*2;
        }
        let needMoney=curbet>this.chaallimit?this.chaallimit:curbet;
        if(plyInfo.money<needMoney&&this.gameState!=GameState.EM_TEENPATTI_GAMESTATE_WAITRECHARGE){
            this.gameView.showNeedRecharge(this.optsumtime,needMoney);
            return;
        }
        if(this.gameState!=GameState.EM_TEENPATTI_GAMESTATE_WAITRECHARGE){
            this.gameView.hideNeedRecharge();
        }
        let info = { fill: fill };
        this.sendGameMessage(Protos.CS_TEENPATTI_BET_P, info);
    }

    //玩家看牌
    public callLook() {
        let info = {};
        this.sendGameMessage(Protos.CS_TEENPATTI_LOOK_P, info);
    }

    //玩家比牌
    public callCompetition() {
        let plyInfo = this._mapPlayers.get(this.playerid);
        let curbet=this.curtimes*this.difen;
        if(this.isMeLook){
            curbet=curbet*2;
        }
        let needMoney=curbet>this.chaallimit?this.chaallimit:curbet;
        if(plyInfo.money<needMoney&&this.gameState!=GameState.EM_TEENPATTI_GAMESTATE_WAITRECHARGE){
            this.gameView.showNeedRecharge(this.optsumtime,needMoney);
            return;
        }
        if(this.gameState!=GameState.EM_TEENPATTI_GAMESTATE_WAITRECHARGE){
            this.gameView.hideNeedRecharge();
        }

        let info = {};
        this.sendGameMessage(Protos.CS_TEENPATTI_COMPETITION_P, info);
    }

    //玩家比牌确认 true-接受比牌, false-拒绝比牌
    public callCompConfirm(confirm: boolean) {
        let info = { confirm: confirm };
        this.sendGameMessage(Protos.CS_TEENPATTI_COMPCONFIRM_P, info);
    }

    //玩家弃牌
    public callFold() {
        let info = {};
        this.sendGameMessage(Protos.CS_TEENPATTI_FOLD_P, info);
    }

    //玩家等待充值
    public callWaitRecharge(confirm: boolean=false) {
        let info = { confirm: confirm };
        this.sendGameMessage(Protos.CS_TEENPATTI_WAITRECHARGE_P, info);
    }
    //打赏
    public callTipDealer() {
        let info = {};
        this.sendTipDealer(info);
    }
    //任务
    public callGameTask() {
        let info = {gameid:this.roomInfo.gameid};
        HallManager.instance.sendActivityMessage(HallActivity.CS_GAME_TASK_P, info);
    }

    //任务
    public callFetchGameTask(fetchid:number) {
        let info = {fetchid:fetchid};
        HallManager.instance.sendActivityMessage(HallActivity.CS_FETCH_GAME_TASK_AWARD_P, info);
    }

    // 玩家退出游戏
    public quitGame(info?) {
        UIHelper.clearAll();
        super.quitGame(info);
    }

    //////////////////////////////////////////////////////////////////////////////
    public start() {
        this.bindGameMessage(Protos.SC_TEENPATTI_START_P, this.onStart, this); //开始
        this.bindGameMessage(Protos.SC_TEENPATTI_SENDCARD_P, this.onSendCard, this); //发牌
        this.bindGameMessage(Protos.SC_TEENPATTI_BET_P, this.onBetData, this); //下注 服务器返回
        this.bindGameMessage(Protos.SC_TEENPATTI_LOOK_P, this.onLookCard, this); //看牌
        this.bindGameMessage(Protos.SC_TEENPATTI_COMPETITION_P, this.onCompetition, this); //比牌
        this.bindGameMessage(Protos.SC_TEENPATTI_COMPCONFIRM_P, this.onCompConfirm, this); //比牌确认
        this.bindGameMessage(Protos.SC_TEENPATTI_FOLD_P, this.onCardFold, this); //弃牌
        this.bindGameMessage(Protos.SC_TEENPATTI_WAITRECHARGE_P, this.onWaitRecharge, this); //等待充值
        this.bindGameMessage(Protos.SC_TEENPATTI_WAITOPT_P, this.onWaitopt, this); //等待
        this.bindGameMessage(Protos.SC_TEENPATTI_JIESHUAN_P, this.onJieShuan, this); //结算

        EventManager.instance.on(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        EventManager.instance.on(GameEvent.HALL_ACTIVITY_GAME_TASK, this.onGameTask, this);
        EventManager.instance.on(GameEvent.HALL_ACTIVITY_FETCH_GAME_TASK, this.onFetchGameTask, this);
        EventManager.instance.on(GameEvent.ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE, this.onGameChargeGetCall, this);
        
        SceneManager.instance.setNoticePos(cc.v2(103, 339),true);
        this.setRecExitTime();

        super.start();
    }


    public exit() {
        this.unbindGameMessage(Protos.SC_TEENPATTI_START_P, this.onStart, this); //开始
        this.unbindGameMessage(Protos.SC_TEENPATTI_SENDCARD_P, this.onSendCard, this); //发牌
        this.unbindGameMessage(Protos.SC_TEENPATTI_BET_P, this.onBetData, this); //下注 服务器返回
        this.unbindGameMessage(Protos.SC_TEENPATTI_LOOK_P, this.onLookCard, this); //看牌
        this.unbindGameMessage(Protos.SC_TEENPATTI_COMPETITION_P, this.onCompetition, this); //比牌
        this.unbindGameMessage(Protos.SC_TEENPATTI_COMPCONFIRM_P, this.onCompConfirm, this); //比牌确认
        this.unbindGameMessage(Protos.SC_TEENPATTI_FOLD_P, this.onCardFold, this); //弃牌
        this.unbindGameMessage(Protos.SC_TEENPATTI_WAITRECHARGE_P, this.onWaitRecharge, this); //等待
        this.unbindGameMessage(Protos.SC_TEENPATTI_WAITOPT_P, this.onWaitopt, this); //等待
        this.unbindGameMessage(Protos.SC_TEENPATTI_JIESHUAN_P, this.onJieShuan, this); //结算

        EventManager.instance.off(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        EventManager.instance.off(GameEvent.HALL_ACTIVITY_GAME_TASK, this.onGameTask, this);
        EventManager.instance.off(GameEvent.HALL_ACTIVITY_FETCH_GAME_TASK, this.onFetchGameTask, this);
        EventManager.instance.off(GameEvent.ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE, this.onGameChargeGetCall, this);

        TextTips["GameTextTips"] = {};

        super.exit();
    }


    // 玩家加入
    public onPlayerEnter(info: any) {
        super.onPlayerEnter(info);
        // 判断玩家是否存在
        let playerid = Common.toInt(info["playerid"]);

        info.playerid = playerid;

        // 判断座位上是否有玩家
        let seat = Common.toInt(info["seat"]);
        let localSeatId = this.getLocalSeatId(seat);
        info.seat = seat;

        // 添加玩家到队列
        this._mapPlayers.set(playerid, info);
        // 处理玩家进入视图逻辑
        this.gameView.showPlayer(localSeatId, info);
        // 设置玩家等待入桌
        if (this.roomInfo.mode != 1) {
            this.gameView.setPlayerAwaiting(localSeatId);
        }
    }

    // 玩家离开
    public onPlayerQuit(info: any) {
        super.onPlayerQuit(info);
        //匹配
        if (this.roomInfo.mode == 1
            && (this.gameState == GameState.EM_TEENPATTI_GAMESTATE_END || this.gameState == -1)) return;

        // 处理玩家离开逻辑
        let reason = Common.toInt(info["reason"]);
        let playerid = Common.toInt(info["playerid"]);
        if (playerid == this.playerid) { // 是否为自己离开
            if (this.isKicked) { // 玩家破产
                console.log('isKicked');
                this.quitGame({reason:1,needRs:this.minmoney});
                return;
            }
            if(this.isZanLi){
                console.log('isZanLi');
                this.quitGame({reason:2,changeRs:-this.quitChangeMoney});
                return;
            }
            if (reason == QuitReason.HUANZUO) { // 玩家换桌
                return;
            }

            this.quitGame();
        }
        else {
            let plyInfo = this._mapPlayers.get(playerid);
            if (plyInfo) {
                let localSeatId = this.getLocalSeatId(plyInfo.seat);
                this.gameView.hidePlayer(localSeatId);
                this._mapPlayers.delete(playerid);
            }
        }
    }

    // 游戏结束,你条件不满足被踢出房间,如果你在暂离状态,也会被踢出房间
    public onDeletePlayer(info: any) {
        super.onDeletePlayer(info);
        this.meIsGaming = false;
        this.isKicked = true;
    }

    // 玩家状态
    public onPlayerState(info: any) {
        super.onPlayerState(info);
    }

    // 更新玩家金币
    public updatePlayerMoney(info: any) {
        super.updatePlayerMoney(info);

        // 更新玩家的金币
        let money = Common.toInt(info["coin"]);
        let playerid = Common.toInt(info["playerid"]);
        let plyInfo = this._mapPlayers.get(playerid);
        if (plyInfo) {
            if(playerid==this.playerid&&money>plyInfo.money ){
                this.gameView.hideNeedRecharge();
            }

            let localSeatId = this.getLocalSeatId(plyInfo.seat);
            this.gameView.updatePlayerMoney(localSeatId, money);
            plyInfo.money = money;
        }
    }

    // 进入房间，房间信息
    public onRoomInfo(info: any) {
        super.onRoomInfo(info);
        this.bankerId = 0;
        this.bankerLocalId = -1;
        this.gameState = -1;
        this.firstEnter=true;
        this.enterRoomTime=Common.getCTime();
        // 重置游戏界面与玩家数据sh
        this.gameView.resetAllUI();
        this._mapPlayers.clear();
        // 基础信息
        this.difen = Common.toInt(info["difen"]);
        this.inmoney = Common.toInt(info["inmoney"]);
        this.minmoney = Common.toInt(info["minmoney"]);
        this.roomState = Common.toInt(info["roomstate"]);

        // 显示游戏提示
        if (this.roomState == RoomState.WAIT && this.roomInfo.mode != 1) {
            let autoreadytime = Common.toInt(info["autoreadytime"]);
            if (autoreadytime > 0) {
                this.gameView.showReadyTips(autoreadytime + 1);
                this.firstEnter=false;
            }
            // this.gameView.showWaitTips();
        }

        // 玩家信息
        let playerlist = info["playerlist"];
        if (playerlist && typeof (playerlist) == "object") {
            // 先找出自己的位置
            for (let key in playerlist) {
                let plyInfo = playerlist[key];
                let playerid = Common.toInt(plyInfo["playerid"]);
                if (playerid == this.playerid) {
                    this.mySeatId = Common.toInt(plyInfo["seat"]);
                    break;
                }
            }

            // 显示所有玩家
            for (let key in playerlist) {
                let plyInfo = playerlist[key];
                let isGaming = Common.toInt(plyInfo["isgaming"]);
                let playerid = Common.toInt(plyInfo["playerid"]);
                let seat = Common.toInt(plyInfo["seat"]);

                // 添加玩家到队列
                this._mapPlayers.set(playerid, plyInfo);

                if (playerid == this.playerid) {
                    this.meIsGaming = isGaming == 1;
                }

                // 显示玩家信息
                let localSeatId = this.getLocalSeatId(seat);
                this.gameView.showPlayer(localSeatId, plyInfo);

                // 玩家是否在游戏中
                if (playerid == this.playerid) {
                    if (!this.meIsGaming && this.roomInfo.mode != 1) {
                        this.gameView.setPlayerAwaiting(localSeatId);
                    }

                }
            }

        }
        // 换桌
        if (this.roomInfo.mode != 1) {
            this.gameView.showHuanZhuo(this.meIsGaming == false);

            if (this.meIsGaming == false) {
                // this.gameView.showWaitTips();
            }
        }

        this.gameView.updateTableCell(this.difen);
    }

    // 房间状态
    public onRoomState(info: any) {
        super.onRoomState(info);
        // 房间状态
        this.roomState = Common.toInt(info["roomstate"]);
        if (this.roomState == RoomState.GAME) {//只需要处理游戏状态
            this.meIsGaming = true;
            this._mapPlayers.forEach((value: any, key: number) => {
                let localSeatId = this.getLocalSeatId(value.seat);
                this.gameView.setPlayerGaming(localSeatId);
                value.gameState = EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY;
            });
            this.gameView.hideWaitTips();
        }
        else {
            this._mapPlayers.forEach((value: any, key: number) => {
                value.gameState = EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_NONE;
            });

            this.meIsGaming = false;
        }

        // 游戏准备计时器
        let autoreadytime = Common.toInt(info["autoreadytime"]);
        if (autoreadytime > 0) {
            this.gameView.showReadyTips(autoreadytime);
            // 换桌
            this.gameView.showHuanZhuo(true);
            this._mapPlayers.forEach((value: any, key: number) => {
                let localSeatId = this.getLocalSeatId(value.seat);
                //this.gameView.setPlayerAwaiting(localSeatId);
            });
        }
        else {
            this.gameView.hideWaitTips();
        }
    }

    // 房间信息(继线重入)
    public onToOtherRoom(info: any) {
        if (CC_DEBUG) {
            console.log('onToOtherRoom',info);
        } 
        super.onToOtherRoom(info);
        this.difen = Common.toInt(info["difen"]);
        this.gameState = Common.toInt(info["state"]);
        this.potlimit = Common.toInt(info["potlimit"]);
        this.chaallimit = Common.toInt(info["chaallimit"]);
        this.blindroundlimit = Common.toInt(info["blindroundlimit"]);
        this.compminturnnum = Common.toInt(info["compminturnnum"]);
        this.rejectcompnum = Common.toInt(info["rejectcompnum"]);
        this.curtimes = Common.toInt(info["curtimes"]);
        this.allbet = Common.toInt(info["allbet"]);
        this.optsumtime = Common.toInt(info["optsumtime"]);
        let waittime = Common.toInt(info["waittime"]);
        this.isCompareing=false;

        let optLocalSeatId = this.getLocalSeatId(Common.toInt(info["optseatid"]));
        this.optLocalSeatId = optLocalSeatId;
        let myLocalSeatId = this.getLocalSeatId(this.mySeatId);
        let localZhuangSeatId = this.getLocalSeatId(Common.toInt(info["banker"]));
        this.turnnum = Common.toInt(info["turnnum"]);
        this.quitChangeMoney=0;

        //比牌用户
        if (this.gameState == GameState.EM_TEENPATTI_GAMESTATE_COMPETITION) {
            this.optCompareLocalSeatId = optLocalSeatId;
        }

        this.gameView.resetStart();

        for (let idx = 1; idx <= Constant.PLAYER_COUNT; idx++) {
            let tInfo = info["playerlist"][idx];
            if (!tInfo) continue;

            let playerid = Common.toInt(tInfo["playerid"]);
            let playerLayer = this._mapPlayers.get(playerid);
            if (!playerLayer) continue;

            playerLayer.gameState = tInfo["state"];

            // 是否为庄家
            let localSeatId = this.getLocalSeatId(playerLayer.seat);
            if (localZhuangSeatId == localSeatId) {
                this.bankerId = playerid;
                this.bankerLocalId = localSeatId;
            }

            if (playerLayer.isgaming == 1) {
                //显示下注
                this.playersBet[localSeatId] = tInfo["mybetall"];
                this.gameView.updateBetMoney(localSeatId, this.playersBet[localSeatId]);

                //看牌
                let islook = tInfo["islook"];
                this.islookArr[localSeatId] = islook;
                let rejectnum = tInfo["rejectnum"];
                if (this.playerid == playerid) {
                    this.isMeLook = islook;
                    this.rejectnum = rejectnum;
                    this.mybetall = Common.toInt(info["mybetall"]);
                    // 是否显示扑克
                    if (islook) {
                        this.gameView.setPlayerPokerData(localSeatId, tInfo);
                    }
        
                } else {
                    if (islook) {
                        this.gameView.doLookCard(localSeatId, info);
                    }
                }
                this.gameView.showPlayerPoker(localSeatId);

                //弃牌状态
                if (playerLayer.gameState == EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_FOLD) {
                    this.gameView.doFoldCard(localSeatId);
                }

                //输牌状态
                if (playerLayer.gameState == EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_LOSE) {
                    this.gameView.doLose(localSeatId);
                }

                //显示操作
                if (this.playerid == playerid && this.gameState > GameState.EM_TEENPATTI_GAMESTATE_START && optLocalSeatId != myLocalSeatId) {
                    let betType = Common.toInt(tInfo["bettype"]);
                    if (betType != BetType.FOLD) {
                        this.gameView.updateOptState(true);
                    }

                }
            }

        }
        if(!this.meIsGaming){
            this.gameView.showWaitTips();
        }
        this.gameView.updateTableAllbet(this.allbet);
        //显示庄家标识
        this.gameView.showPlayerBankerFlag(localZhuangSeatId);
        //设置当前操作玩家
        this.gameView.changeAwait(optLocalSeatId, waittime, this.optsumtime);

        if (this.gameState == GameState.EM_TEENPATTI_GAMESTATE_COMPETITION) {
            this.onCompetition(info, false);
        } else if (this.gameState == GameState.EM_TEENPATTI_GAMESTATE_WAITRECHARGE) {
            this.gameView.doWaitRecharge(optLocalSeatId, Math.floor(waittime));
        }

    }

    // 玩家暂离
    public onPlayerZanLi(info: any) {
        this.isZanLi = true;
        this.quitChangeMoney=this.playersBet[this.meLocalSeatId];
        this.gameView.doZanLi();
    }

    // 玩家暂离返回成功
    public onZanLiComback(info: any) {
        this.gameView.doZanLiComback();
        this.isZanLi = false;
        this.quitChangeMoney=0;
    }


    // 开始匹配
    public onStartMatch(info: any) {
        this.gameView.resetAllUI();
        this.gameView.showLoading(true);
    }

    // 匹配成功
    public onFinishMatch(info: any) {
        this.gameView.showLoading(false);
    }

    // 聊天信息
    public onRoomChat(info: any) {
        let sendPlayerid = Common.toInt(info["sendPlayerid"]);
        let receiverPlayerid = Common.toInt(info["receiverPlayerid"]);
        let type = info["type"];
        let content = info["content"];
        let sendPlayerLayer = this._mapPlayers.get(sendPlayerid);
        let recPlayerLayer = this._mapPlayers.get(receiverPlayerid);
        if (!sendPlayerLayer) return;

        let sendLocalSeatId = this.getLocalSeatId(sendPlayerLayer.seat);

        if (!recPlayerLayer) {
            this.gameView.doRoomChat(sendLocalSeatId, type, content);
        } else {
            let recLocalSeatId = this.getLocalSeatId(recPlayerLayer.seat);
            this.gameView.doRoomIntertChat(sendLocalSeatId, recLocalSeatId, content);
        }

    }

    public onRoomTipDealer(info: any){
        let localSeatId = this.getLocalSeatId(Common.toInt(info["seatid"]));
        this.gameView.doTipDealer(localSeatId);
    }

    public onGameTask(info:any){
        let curtask=info['taskdata'];
        if(curtask){
            this.taskNode.show(curtask['1']);
        }else{
            this.taskNode.hide();
        }  
    }
    
    public onFetchGameTask(info:any){
        let plyInfo = this._mapPlayers.get(this.playerid);
        let localSeatId = this.getLocalSeatId(plyInfo.seat);
        this.gameView.doGetTaskAward(localSeatId,info.fetchaward*Config.SCORE_RATE);
        this.taskRewardLayer.updateAwardInfo(info);
        this.callGameTask();
    }

    public onGameChargeGetCall(result){
        this.callWaitRecharge(true);
    }
    //////////////////////////////////////////////////////////////////////////////
    //开始
    private onStart(info: any) {
        console.log("开始消息：", info)
        this.difen = Common.toInt(info["difen"]);
        this.gameState = Common.toInt(info["state"]);
        this.potlimit = Common.toInt(info["potlimit"]);
        this.chaallimit = Common.toInt(info["chaallimit"]);
        this.blindroundlimit = Common.toInt(info["blindroundlimit"]);
        this.compminturnnum = Common.toInt(info["compminturnnum"]);
        this.rejectcompnum = Common.toInt(info["rejectcompnum"]);
        let localZhuangSeatId = this.getLocalSeatId(Common.toInt(info["banker"]));
        this.bankerLocalId = localZhuangSeatId;
        this.mybetall = 0;
        this.meIsGaming = true;
        this.turnnum = 0;
        this.islookArr = [];
        this.playersBet = [];
        this.allbet = 0;
        this.isMeLook = false;
        this.isCompareing=false;
        this.rejectnum = 0;
        this.optLocalSeatId = -1;
        this.optCompareLocalSeatId = -1;
        this.quitChangeMoney=0;

        this.gameView.resetStart();

    }

    // 游戏消息-发牌
    private onSendCard(info: any) {
        console.log("发牌消息：", info)
        let self = this;
        this.gameState = Common.toInt(info["state"]);
        this.curtimes = Common.toInt(info["curtimes"]);
        //去除离开玩家
        this._mapPlayers.forEach(function (item, key) {
            if (item.isQuit === true) {
                let localSeatId = self.getLocalSeatId(item.seat);
                self.gameView.hidePlayer(localSeatId);
                self._mapPlayers.delete(item.playerid);
            }
        });


        let sendCardCallback = () => {
            self.gameView.updateOptState(true);
        };

        this.gameView.showBanker(this.bankerLocalId);
        this.gameView.doBetDiZhu(function () {
            self.gameView.updateTableAllbet(self.allbet);
            self.gameView.doSendCard(sendCardCallback);
        });
    }

    // 游戏消息-玩家下注
    private onBetData(info: any, hasSound: boolean = true) {
        console.log("玩家下注：", info)
        let playerid = Common.toInt(info["playerid"]);
        let betType = Common.toInt(info["bettype"]);
        let bet = Common.toInt(info["bet"]);
        let pretimes = this.curtimes;
        this.allbet = Common.toInt(info["allbet"]);
        this.curtimes = Common.toInt(info["curtimes"]);
        if (playerid == this.playerid) {
            this.mybetall = Common.toInt(info["mybetall"]);
        }

        let plyInfo = this._mapPlayers.get(playerid);
        if (!plyInfo) {
            console.error(`TPGameCore.onPlayerInfo: player is invalid. playerid: ${playerid}`);
            return;
        }
        let localSeatId = this.getLocalSeatId(plyInfo.seat);
        if (!this.playersBet[localSeatId]) {
            this.playersBet[localSeatId] = 0;
        }
        this.playersBet[localSeatId] += bet;

        //底注不处理
        if (betType == BetType.BOTTOM) return;

        let double = this.curtimes > pretimes;
        let hasSeen = this.islookArr[localSeatId];
        if (betType == BetType.FILL) {
            if (hasSeen) {
                this.audioMng.playChaal(double);
            } else {
                this.audioMng.playBlind(double);
            }
        }

        this.gameView.updateBetMoney(localSeatId, this.playersBet[localSeatId]);
        this.gameView.showPlayerBetTips(localSeatId, betType, double, hasSeen, hasSound);

        if (hasSound) {
            let self = this;
            this.gameView.doBet(localSeatId, bet, function () {
                self.gameView.updateTableAllbet(self.allbet);
            });
        } else {
            this.gameView.updateTableAllbet(this.allbet);
        }

    }

    // 游戏消息-看牌
    private onLookCard(info: any) {
        console.log("看牌消息：", info)
        let playerid = Common.toInt(info["playerid"]);

        let plyInfo = this._mapPlayers.get(playerid);
        if (!plyInfo) {
            console.error(`TPGameCore.onPlayerInfo: player is invalid. playerid: ${playerid}`);
            return;
        }
        let localSeatId = this.getLocalSeatId(plyInfo.seat);
        this.islookArr[localSeatId] = true;

        if (playerid == this.playerid) {
            this.isMeLook = true;
        }

        this.audioMng.playSee();

        this.gameView.doLookCard(localSeatId, info);

    }

    // 游戏消息-比牌
    private onCompetition(info: any, sound: boolean = true) {
        this.gameState = Common.toInt(info["state"]);
        let optseatid = Common.toInt(info["optseatid"]);
        let compseatid = Common.toInt(info["compseatid"]);
        let optLocalSeatId = this.getLocalSeatId(optseatid);
        let compLocalSeatId = this.getLocalSeatId(compseatid);
        this.optCompareLocalSeatId = optLocalSeatId;

        if(optseatid==this.mySeatId||compseatid==this.mySeatId){
            this.isCompareing=true;
        }
        this.gameView.hideNeedRecharge();
        this.gameView.doCompetition(optLocalSeatId, compLocalSeatId, this.optsumtime, sound);
    }

    // 游戏消息-比牌确认
    private onCompConfirm(info: any) {
        let playerid = Common.toInt(info["playerid"]);
        let seatid = Common.toInt(info["seatid"]);
        let confirm = info["confirm"];
        let compseat = info["compseat"];
        let optseat = info["optseat"];
        this.optCompareLocalSeatId = -1;

        if (!confirm && playerid == this.playerid) {
            this.rejectnum += 1;
        }
        let playerCount=this.getPlayingCount();
        if (confirm) {
            if(playerCount>2){
                this.audioMng.playAccept();
            }
        } else {
            this.audioMng.playRefuse();
        }

        //关闭当前等待的用计时
        this.gameView.changeAwait(-1, 0, 0);
        this.gameView.hideNeedRecharge();

        let optLocalSeatId = this.getLocalSeatId(seatid);
        this.gameView.doCompetitionResult(optLocalSeatId, confirm, info);

        if (compseat && optseat) {
            let loseseatid = compseat["iswin"]==1 ? optseat["seatid"] : compseat["seatid"];
            this._mapPlayers.forEach((value: any, key: number) => {
                if (loseseatid == value.seat) {
                    value.gameState = EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_LOSE;
                }
            });

            let comLocalSeatId = this.getLocalSeatId(compseat["seatid"]);
            if(comLocalSeatId==this.meLocalSeatId){
                this.gameView.updateOptState(false);
            }

            if(compseat["seatid"]==this.mySeatId||optseat["seatid"]==this.mySeatId){
                this.isCompareing=true;
            }
        }

    }


    // 游戏消息-等待充值
    private onWaitRecharge(info: any) {
        this.gameState = Common.toInt(info["state"]);
        let seatid = Common.toInt(info["seatid"]);
        let optsumtime = Common.toInt(info["optsumtime"]);
        let optLocalSeatId = this.getLocalSeatId(seatid);
        this.gameView.doWaitRecharge(optLocalSeatId, Math.floor(optsumtime));
    }

    /**
     * 玩家等待
     * @param SC_TP_WAITOPT_P 
     * @param onWaitopt 
     * @param arg2 
     */
    public onWaitopt(info: any) {
        this.gameState = Common.toInt(info["state"]);
        this.turnnum = Common.toInt(info["turnnum"]);
        this.optsumtime = Common.toInt(info["optsumtime"]);

        let localSeatId = this.getLocalSeatId(Common.toInt(info["optseatid"]));
        this.optLocalSeatId = localSeatId;
        let myLocalSeatId = this.getLocalSeatId(this.mySeatId);
        if (myLocalSeatId != localSeatId) {
            //不等于自己隐藏操作
            this.gameView.cancelAwait(myLocalSeatId, true);
        } else {
            this.audioMng.playYourTurn();

            let plyInfo = this._mapPlayers.get(this.playerid);
            let curbet=this.curtimes*this.difen;
            if(this.isMeLook){
                curbet=curbet*2;
            }
            let needMoney=curbet>this.chaallimit?this.chaallimit:curbet;
            if(plyInfo.money<needMoney){
                this.gameView.showNeedRecharge(this.optsumtime,needMoney);
            }
        }

        if(this.gameState != GameState.EM_TEENPATTI_GAMESTATE_COMPETITION){
            this.isCompareing=false;
        }

        this.gameView.hideNeedRecharge();
        this.gameView.changeAwait(localSeatId, this.optsumtime, this.optsumtime);

    }

    // 游戏消息-弃牌
    private onCardFold(info: any) {
        let playerid = Common.toInt(info["playerid"]);
        let seatid = Common.toInt(info["seatid"]);
        let localSeatId = this.getLocalSeatId(seatid);

        let playerLayer = this._mapPlayers.get(playerid);
        if (playerLayer) {
            playerLayer.gameState = EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_FOLD;
        }

        this.audioMng.playPacked();

        this.gameView.doFoldCard(localSeatId);
    }

    // 游戏消息-结算
    private onJieShuan(info: any) {
        if (!info || typeof (info) != "object") {
            console.error("TPGameCore.onJieShuan: info is invalid.");
            return;
        }
        let sysopencard = Common.toInt(info["sysopencard"]);
        let winner = Common.toInt(info["winner"]);
        this.isCompareing=false;

        this.gameState = GameState.EM_TEENPATTI_GAMESTATE_END;
        //关闭当前等待的用计时
        this.gameView.changeAwait(-1, 0, 0);
        this.gameView.updateOptState(false);
        this.gameView.hideNeedRecharge();

        let winResultInfo: any = {};
        let loseResultInfo: any = [];
        let tancount=0;
        let mewincardtype=0;

        for (let key in info["playerlist"]) {
            let tInfo = info["playerlist"][key];
            let playerid = Common.toInt(tInfo["playerid"]);
            let seatid = Common.toInt(tInfo["seatid"]);
            let localSeatId = this.getLocalSeatId(seatid);
            let changemoney = Common.toInt(tInfo["changemoney"]);
            let isWin = changemoney > 0;
            // let plyInfo = this._mapPlayers.get(playerid);
            // if (plyInfo) {

            // }
            let poker = tInfo["cards"];
            if (poker && typeof (poker) == "object") {
                if (this.playerid != playerid || !this.isMeLook) {
                    this.gameView.setPlayerPokerData(localSeatId, tInfo);
                    this.gameView.doTanPai(localSeatId, isWin);
                    tancount++;
                }
            }
            if (isWin) {
                winResultInfo.localSeatId = localSeatId;
                winResultInfo.changemoney = this.playersBet[localSeatId]+changemoney;

                if(this.playerid == playerid){
                    mewincardtype=Common.toInt(tInfo["cardtype"])
                }
            }
            if(!isWin){
                loseResultInfo.push({localSeatId:localSeatId,changemoney:changemoney});
            }
        }
        if(mewincardtype&&tancount>0){
            this.audioMng.playCardType(mewincardtype);
        }

        this._mapPlayers.forEach((value: any, key: number) => {
            value.gameState = EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_NONE;
        });

        if(this.allbet>this.potlimit){
            this.gameView.showPotLimitTips();
        }
        this.gameView.doSettlement(winResultInfo,loseResultInfo,()=>{
            this.callGameTask();
        });
       
    }

    //////////////////////////////////////////////////////////////////////////////

    private updateGameLan() {
        updateGameTextTips();
        TextTips["GameTextTips"] = GameTextTips;
    }

    public getPlayingCount(): number {
        let count = 0;
        this._mapPlayers.forEach((value: any, key: number) => {
            if (value.gameState == EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY) count = count + 1;
        });
        return count;
    }

    public isPlayerMeGaming(): boolean {
        let plyInfo = this._mapPlayers.get(this.playerid);
        return plyInfo.gameState == EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY;
    }

    public isPlayerGaming(playerid: number): boolean {
        let plyInfo = this._mapPlayers.get(playerid);
        return plyInfo.gameState == EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY;
    }

    public getPlayerMeBet(): number {
        return this.mybetall;
    }

    public getCardtypeText(cardtype: number): string {
        let pointStr = GameTextTips.CARD_TYPE_DAN;
        if (cardtype == CardType.EM_TEENPATTI_CARDTYPE_DAN) {
            pointStr = GameTextTips.CARD_TYPE_DAN;
        }
        else if (cardtype == CardType.EM_TEENPATTI_CARDTYPE_DUIZI) { pointStr = GameTextTips.CARD_TYPE_DUIZI }
        else if (cardtype == CardType.EM_TEENPATTI_CARDTYPE_SHUNZI) { pointStr = GameTextTips.CARD_TYPE_SHUNZI }
        else if (cardtype == CardType.EM_TEENPATTI_CARDTYPE_TONGHUA) { pointStr = GameTextTips.CARD_TYPE_TONGHUA }
        else if (cardtype == CardType.EM_TEENPATTI_CARDTYPE_TONGHUASHUN) { pointStr = GameTextTips.CARD_TYPE_TONGHUASHUN }
        else if (cardtype == CardType.EM_TEENPATTI_CARDTYPE_BAOZI) { pointStr = GameTextTips.CARD_TYPE_BAOZI }
        return pointStr;
    }

    public isOthesPlayerGiveup(): boolean {
        let count=0;
        this._mapPlayers.forEach((value: any, key: number) => {
            if (value.gameState == EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_PLAY||value.gameState== EM_TEENPATTI_PLAYERSTATE.EM_TEENPATTI_PLAYER_LOSE) count++;
        });
        return count==1;
    }

    //////////////////////////////////////////////////////////////////////////////

}

