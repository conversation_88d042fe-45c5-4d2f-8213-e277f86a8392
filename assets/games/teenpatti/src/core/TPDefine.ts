//////////////////////////////////////////////////////////////////////////////////

import { LanguageType } from "../../../../script/frame/common/Define"

// 基础数据
export let Constant = {
  PLAYER_COUNT: 5,                // 玩家个数
  SHOW_MULTI_ANI: 20,               
}

// 游戏协议
export enum Protos {
  SC_TEENPATTI_START_P = 1000,		//开始
  SC_TEENPATTI_SENDCARD_P,			//发牌
  CS_TEENPATTI_BET_P,					//下注
  SC_TEENPATTI_BET_P,					//下注
  CS_TEENPATTI_LOOK_P,				//看牌
  SC_TEENPATTI_LOOK_P,				//看牌
  CS_TEENPATTI_COMPETITION_P,			//比牌
  SC_TEENPATTI_COMPETITION_P,			//比牌
  CS_TEENPATTI_COMPCONFIRM_P,			//比牌确认
  SC_TEENPATTI_COMPCONFIRM_P,			//比牌确认
  CS_TEENPATTI_FOLD_P,				//弃牌
  SC_TEENPATTI_FOLD_P,				//弃牌
  CS_TEENPATTI_WAITRECHARGE_P,		//等待充值
  SC_TEENPATTI_WAITRECHARGE_P,		//等待充值
  SC_TEENPATTI_WAITOPT_P,				//等待操作
  SC_TEENPATTI_JIESHUAN_P,			//结算
}

//下注类型
export let BetType = {
  BOTTOM: 0, //底注
  FILL: 1, //下注
  COMPETITION: 2, //比牌
  FOLD: 3, //弃牌
}

export enum CardType {
  EM_TEENPATTI_CARDTYPE_NONE,			//无
  EM_TEENPATTI_CARDTYPE_DAN,			//单牌
  EM_TEENPATTI_CARDTYPE_DUIZI,		//对子
  EM_TEENPATTI_CARDTYPE_TONGHUA,		//金花
  EM_TEENPATTI_CARDTYPE_SHUNZI,		//顺子
  EM_TEENPATTI_CARDTYPE_TONGHUASHUN,	//同花顺
  EM_TEENPATTI_CARDTYPE_BAOZI,		//豹子
}
// 游戏状态
export let GameState = {
  EM_TEENPATTI_GAMESTATE_START: 0, //游戏开始
  EM_TEENPATTI_GAMESTATE_SENDCARD: 1, //发牌状态
  EM_TEENPATTI_GAMESTATE_BET: 2, //下注状态
  EM_TEENPATTI_GAMESTATE_COMPETITION: 3, //比牌状态
  EM_TEENPATTI_GAMESTATE_END: 4, //结束状态
  EM_TEENPATTI_GAMESTATE_WAITRECHARGE: 5, //等待充值
}

export enum EM_TEENPATTI_PLAYERSTATE {
  EM_TEENPATTI_PLAYER_NONE,			//无效状态
  EM_TEENPATTI_PLAYER_PLAY,			//游戏状态
  EM_TEENPATTI_PLAYER_FOLD,			//弃牌状态
  EM_TEENPATTI_PLAYER_LOSE,			//输牌状态
}


let GameTextCH = {
  CARD_TYPE_DAN: "High card",
  CARD_TYPE_DUIZI: "Pair",
  CARD_TYPE_TONGHUA: "Color",
  CARD_TYPE_SHUNZI: "Sequence",
  CARD_TYPE_TONGHUASHUN: "Pure Sequence",
  CARD_TYPE_BAOZI: "Trail",

  OPT_TXT_PACK: "Pack",
  OPT_TXT_PACKED: "Packed",
  OPT_TXT_SEEN: "See",
  OPT_TXT_SEEN_B: "SEEN",
  OPT_TXT_SIDE_SHOW: "Side Show",
  OPT_TXT_SHOW: "Show",
  OPT_TXT_BLIND: "Blind",
  OPT_TXT_CHAAL: "Chaal",

  UI_TXT_REQ_SHOW_CARDS: "requested to show cards",
  UI_TXT_REMAINING_TIMES: "remaining times",
  UI_TXT_REFUSE: "Refuse",
  UI_TXT_AGREE: "Agree",
  UI_TXT_AFTER_CLICKING: "After clicking",
  UI_TXT_RECHARGE250: "You have 250s to recharge",
  UI_TXT_RECHARGE: "Recharge",
  UI_TXT_RECHARGEING: "is recharging, please wait for him",
  UI_TXT_TEENPATTI: "Teen Patti",
  UI_TXT_BOOTAMOUNT: "Boot Amount",
  UI_TXT_MAX_BLINDS: "Max Blinds",
  UI_TXT_CHAAL_LIMIT: "Chaal Limit",
  UI_TXT_POT_LIMIT: "Pot Limit",
  UI_TXT_I_BACK: "I’m back",
  UI_TXT_AUTO_PLAY: "you have been put on Auto-Play for missing a turn",
  UI_TXT_LOSE: "LOSE",
  UI_TXT_UNKNOW: "unknow",
  UI_TXT_WAIT_NEXT_ROUND: "Please wait for the next round to start",
  UI_TXT_WAIT_START_GAME: "The game will start in {0} ",
  UI_TXT_TASK_TITLE_PLAY :"Play the game",
  UI_TXT_TASK_TITLE_WIN:"Win the game",
  UI_TXT_CLICK_CHOOSE_REWARD:"Click to choose a reward",
  UI_TXT_CLICK_TO_CLOSE:"Click to close",
}

let GameTextEnglish = {
  CARD_TYPE_DAN: "High card",
  CARD_TYPE_DUIZI: "Pair",
  CARD_TYPE_TONGHUA: "Color",
  CARD_TYPE_SHUNZI: "Sequence",
  CARD_TYPE_TONGHUASHUN: "Pure Sequence",
  CARD_TYPE_BAOZI: "Trail",

  OPT_TXT_PACK: "Pack",
  OPT_TXT_PACKED: "Packed",
  OPT_TXT_SEEN: "See",
  OPT_TXT_SEEN_B: "SEEN",
  OPT_TXT_SIDE_SHOW: "Side Show",
  OPT_TXT_SHOW: "Show",
  OPT_TXT_BLIND: "Blind",
  OPT_TXT_CHAAL: "Chaal",

  UI_TXT_REQ_SHOW_CARDS: "requested to show cards",
  UI_TXT_REMAINING_TIMES: "remaining times",
  UI_TXT_REFUSE: "Refuse",
  UI_TXT_AGREE: "Agree",
  UI_TXT_AFTER_CLICKING: "After clicking",
  UI_TXT_RECHARGE250: "You have 250s to recharge",
  UI_TXT_RECHARGE: "Recharge",
  UI_TXT_RECHARGEING: "is recharging, please wait for him",
  UI_TXT_TEENPATTI: "Teen Patti",
  UI_TXT_BOOTAMOUNT: "Boot Amount",
  UI_TXT_MAX_BLINDS: "Max Blinds",
  UI_TXT_CHAAL_LIMIT: "Chaal Limit",
  UI_TXT_POT_LIMIT: "Pot Limit",
  UI_TXT_I_BACK: "I’m back",
  UI_TXT_AUTO_PLAY: "you have been put on Auto-Play for missing a turn",
  UI_TXT_LOSE: "LOSE",
  UI_TXT_UNKNOW: "unknow",
  UI_TXT_WAIT_NEXT_ROUND: "Please wait for the next round to start",
  UI_TXT_WAIT_START_GAME: "The game will start in {0} ",
  UI_TXT_TASK_TITLE_PLAY :"Play the game",
  UI_TXT_TASK_TITLE_WIN:"Win the game",
  UI_TXT_CLICK_CHOOSE_REWARD:"Click to choose a reward",
  UI_TXT_CLICK_TO_CLOSE:"Click to close",
}
let GameTextIndia = {
  CARD_TYPE_DAN: "High card",
  CARD_TYPE_DUIZI: "Pair",
  CARD_TYPE_TONGHUA: "Color",
  CARD_TYPE_SHUNZI: "Sequence",
  CARD_TYPE_TONGHUASHUN: "Pure Sequence",
  CARD_TYPE_BAOZI: "Trail",

  OPT_TXT_PACK: "पैक",
  OPT_TXT_PACKED: "पैक्ड",
  OPT_TXT_SEEN: "देखें",
  OPT_TXT_SEEN_B: "देख",
  OPT_TXT_SIDE_SHOW: "साइड शो",
  OPT_TXT_SHOW: "शो",
  OPT_TXT_BLIND: "ब्लाइंड",
  OPT_TXT_CHAAL: "चाल",

  UI_TXT_REQ_SHOW_CARDS: "रिचार्ज कर रहा है, कृपया उसका इंतजार करें",
  UI_TXT_REMAINING_TIMES: "शेष समय",
  UI_TXT_REFUSE: "ठुकराना",
  UI_TXT_AGREE: "सहमत",
  UI_TXT_AFTER_CLICKING: "क्लिक करने के बाद",
  UI_TXT_RECHARGE250: "आपके पास 250 सेकंड्स हैं रिचार्ज करने के लिए",
  UI_TXT_RECHARGE: "कैश जोड़े",
  UI_TXT_RECHARGEING: "रिचार्ज हो रहा है, कृपया उसका इंतजार करें",
  UI_TXT_TEENPATTI: "तीन पत्ती",
  UI_TXT_BOOTAMOUNT: "बूट राशि",
  UI_TXT_MAX_BLINDS: "अधिकतम अंधा",
  UI_TXT_CHAAL_LIMIT: "चाल सीमा",
  UI_TXT_POT_LIMIT: "पॉट लिमिट",
  UI_TXT_I_BACK: "मैं वापस हूँ",
  UI_TXT_AUTO_PLAY: "आपको एक टर्न छोड़ने के लिए ऑटो-प्ले पर रखा गया है",
  UI_TXT_LOSE: "खोया",
  UI_TXT_UNKNOW: "अज्ञात",
  UI_TXT_WAIT_NEXT_ROUND: "कृपया अगले दौर के शुरू होने की प्रतीक्षा करें",
  UI_TXT_WAIT_START_GAME: "The game will start in {0} ",
  UI_TXT_TASK_TITLE_PLAY :"खेल खेलें",
  UI_TXT_TASK_TITLE_WIN:"खेल को जीतो",
  UI_TXT_CLICK_CHOOSE_REWARD:"इनाम चुनने के लिए क्लिक करें",
  UI_TXT_CLICK_TO_CLOSE:"बंद करने के लिए क्लिक",
}
// 文本内容
export let GameTextTips = GameTextEnglish

export let updateGameTextTips = () => {
  let curLanguage = cc.sys.localStorage.getItem('curLanguage') || 1;
  if (curLanguage == LanguageType.CHINESE) {
    GameTextTips = GameTextCH;
  } else if (curLanguage == LanguageType.ENGLISH) {
    GameTextTips = GameTextEnglish;
  } else if (curLanguage == LanguageType.INDIA) {
    GameTextTips = GameTextIndia;
  }
}

//////////////////////////////////////////////////////////////////////////////////
