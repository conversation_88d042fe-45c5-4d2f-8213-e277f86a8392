import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import { BetType } from "../core/TPDefine";
import TPAudioMng from "../core/TPAudioMng";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TPChipsLayer extends BaseLayer {

    @property(cc.Prefab)
    chipLeft: cc.Prefab = null;

    @property(cc.Prefab)
    chipRight: cc.Prefab = null;

    // 声音对象
    private _audioMng: TPAudioMng = null;

    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this._audioMng = cc.Canvas.instance.getComponent(TPAudioMng);
    }

    /** 筹码重置 */
    public resetBet() {
        this.node.removeAllChildren();
    }

    /**
     * 筹码飞动效果
     */
    public chipMove(score: number, starPos: cc.Vec3, endPos: cc.Vec3, left: boolean, callback): any {
        let chipText = Common.moneyString(score);

        // let chipNode = cc.instantiate(left ? this.chipLeft : this.chipRight)
        let chipNode = cc.instantiate(this.chipRight);
        chipNode.setPosition(starPos);
        chipNode.opacity = 255;
        this.node.addChild(chipNode);

        let chipAni = chipNode.getComponent(cc.Animation);

        cc.tween(chipNode)
        .delay(0.02)
        .call(function () {
            chipAni.play();
        })
        .start();

        chipAni.on(cc.Animation.EventType.FINISHED, () => {
            cc.tween(chipNode)
                .to(0.5, { position: endPos })
                .to(0.1, { opacity: 0 })
                .call(function () {
                    if (callback) {
                        callback();
                    }
                })
                .removeSelf()
                .start();

        })
        let chipValNode = chipNode.getChildByName("img_betBg").getChildByName("txt_bet");
        let valLabel = chipValNode.getComponent(cc.Label);
        valLabel.string = chipText;

        this._audioMng.playFlyGold();

    }

    public chipMoveToUser(score: number, starPos: cc.Vec3, endPos: cc.Vec3, callback): any {
        let chipText = Common.moneyString(score);

        let chipNode = cc.instantiate(this.chipRight)
        chipNode.setPosition(starPos);
        chipNode.opacity = 255;
        this.node.addChild(chipNode);

        cc.tween(chipNode)
        .hide()
        .delay(1)
        .show()
        .to(0.5, { position: endPos })
        .to(0.1, { opacity: 0 })
        .call(function () {
            if (callback) {
                callback();
            }
        })
        .removeSelf()
        .start();
        let chipValNode = chipNode.getChildByName("img_betBg").getChildByName("txt_bet");
        let valLabel = chipValNode.getComponent(cc.Label);
        valLabel.string = chipText;

    }

}


