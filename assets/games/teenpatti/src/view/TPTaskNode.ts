import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import EventManager from "../../../../script/frame/manager/EventManager";
import { GameTextTips } from "../core/TPDefine";
import TPGameCore from "../core/TPGameCore";
import TPTaskRewardLayer from "./TPTaskRewardLayer";


const { ccclass, property } = cc._decorator;

@ccclass
export default class TPTaskNode extends BaseLayer {

    @property(TPTaskRewardLayer)
    rewardLayer: TPTaskRewardLayer = null

    @property(cc.Node)
    imgBtn: cc.Node = null;

    @property(cc.Node)
    imgFinishBg: cc.Node = null;

    @property(cc.Prefab)
    progressPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    finishProgressPrefab: cc.Prefab = null;


    private _gameCore: TPGameCore = null;
    private _btnStatus = 1;//1:btn 2:finish 3:award 4:none
    private _completed = false;
    private _taskAni: cc.Animation;
    private _infoData: any;
    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent('TPGameCore');
        this._taskAni = this.getComponent(cc.Animation);
        // this._taskAni.play('tx_haiwai_TP_meirirenwu');

        let self = this;
        this._taskAni.on(cc.Animation.EventType.FINISHED, function (value1, value2) {
            if (value2.name == 'animation0') {
                self._btnStatus=2;
            } else if (value2.name == 'animation1') {
                self._btnStatus=1;
            } else if (value2.name == 'tx_haiwai_TP_meirirenwu') {
                self._btnStatus=1;
            }
        });
        this.showFinishNode();
    }

    protected start(): void {
        this._gameCore.callGameTask();
    }

    private onClickTask() {
        if(this._completed){
            if (this._infoData) {
                this.rewardLayer.show(this._infoData.fetchid);
            }
            return;
        }
        if (this._btnStatus == 1) {
            this.showFinishNode();
        } else if (this._btnStatus == 2) {
            this.stopShowAction();
            this._taskAni.play('animation1');
        }else if (this._btnStatus == 3) {
        
        }
    }

    public showFinishNode() {
        this.stopShowAction();
        this._btnStatus=4;
        this._taskAni.play('animation0');
        cc.tween(this.node)
            .delay(3)
            .call(() => {
                this._taskAni.play('animation1');
            })
            .start();
    }

    public stopShowAction() {
        this.node.stopAllActions();
    }

    public show(info) {
        this.node.active=true;
        this.updateData(info);
    }

    public hide(){
        this.node.active=false;
    }

    public updateData(info) {
        let finish = info.currentnumber >= info.limitnumber;
        this._infoData = info;
        this._completed = finish;
        this._btnStatus=1;

        let nodeBtnLight = this.imgBtn.getChildByName('nodeBtnLight');
        let txtReward = this.imgBtn.getChildByName('txtReward');
        let txtProgress = this.imgBtn.getChildByName('imgProgressBg').getChildByName('txtProgress');
        let txtProgressFinish = this.imgBtn.getChildByName('imgProgressBg').getChildByName('txtProgressFinish');
        let lightAniNode = nodeBtnLight.getChildByName('lightAniNode');
        if (!lightAniNode) {
            lightAniNode = cc.instantiate(this.progressPrefab);
            lightAniNode.name='lightAniNode';
            nodeBtnLight.addChild(lightAniNode);
        }

        nodeBtnLight.active = finish;
        txtProgressFinish.active = finish;
        txtProgress.active = !finish;
        txtReward.getComponent(cc.Label).string = info.totalAward + '';

        if (!finish) {
            txtProgress.getComponent(cc.Label).string = info.currentnumber + '/' + info.limitnumber;
        } else {
            let pAni = lightAniNode.getComponent(cc.Animation);
            let animState = pAni.play('tx_haiwai_TP_renwujindu');
            animState.wrapMode = cc.WrapMode.Loop;
        }

        //finishnode
        let txtTitle = this.imgFinishBg.getChildByName('txtTitle');
        let txtFinishProgress = this.imgFinishBg.getChildByName('txtProgress');
        let nodeProgress = this.imgFinishBg.getChildByName('nodeProgress');
        let ainLight = this.imgFinishBg.getChildByName('aniLight');
        let txtFinishReward = this.imgFinishBg.getChildByName('txtReward');
        let finishLightAniNode = ainLight.getChildByName('finishLightAniNode');
        if (!finishLightAniNode) {
            finishLightAniNode = cc.instantiate(this.finishProgressPrefab);
            finishLightAniNode.name='finishLightAniNode';
            ainLight.addChild(finishLightAniNode);
        }
        ainLight.active = finish;
        txtFinishReward.getComponent(cc.Label).string = info.totalAward + '';
        txtFinishProgress.getComponent(cc.Label).string = info.currentnumber + '/' + info.limitnumber;
        txtTitle.getComponent(cc.Label).string = info.tasktype == 0 ? GameTextTips.UI_TXT_TASK_TITLE_PLAY : GameTextTips.UI_TXT_TASK_TITLE_WIN;
        if (finish) {
            let pAni = finishLightAniNode.getComponent(cc.Animation);
            let animState = pAni.play('tx_haiwia_TP_renwu');
            animState.wrapMode = cc.WrapMode.Loop;
            nodeProgress.getComponent(cc.ProgressBar).progress = 1;
            this.showFinishNode();
        } else {
            nodeProgress.getComponent(cc.ProgressBar).progress = info.currentnumber / info.limitnumber;
        }

    }
}


