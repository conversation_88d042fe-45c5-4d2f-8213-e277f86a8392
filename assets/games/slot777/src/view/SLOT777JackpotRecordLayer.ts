import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import PopupLayer from "../../../../script/frame/component/PopupLayer";
import EventManager from "../../../../script/frame/manager/EventManager";
import { GameEvent } from "../core/SLOT777Define";
import SLOT777GameCore from "../core/SLOT777GameCore";
import SLOT777GameLogic from "../core/SLOT777GameLogic";
import SLOT777GameView from "./SLOT777GameView";

const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slot777/SLOT777JackpotRecordLayer')
export default class SLOT777JackpotRecordLayer extends BaseLayer {

    @property(cc.Node)
    content: cc.Node = null;

    @property(cc.Node)
    Panel_item: cc.Node = null;

    @property(cc.Node)
    aniNode: cc.Node = null;

    @property(cc.Node)
    jackpot_txt1: cc.Node = null;
    
    /** 游戏主对象 */
    _gameCore: SLOT777GameCore;
    _gameview: SLOT777GameView;
    _gameLogic :SLOT777GameLogic = null;
    _mCallback = null;
    nOldJackpotNum = 0;
    jackpotnum = 0;
    
    onLoad() {
        EventManager.instance.on(GameEvent.JACKPOT_RECORD_INFO, this.onUpdateRecordInfo, this);
        EventManager.instance.on(GameEvent.JACKPOT_RECORD, this.updateJackpotNum, this);
        this._gameCore = cc.Canvas.instance.getComponent("SLOT777GameCore"); 
        this._gameview = cc.Canvas.instance.getComponent("SLOT777GameView"); 
        this._gameLogic = cc.Canvas.instance.getComponent("SLOT777GameLogic");     }

    start() {
        
    }

    setData(callback){
        this._mCallback = callback;
        this.jackpot_txt1.getComponent(cc.Label).string =  this._gameview.formatNumberForComma(this._gameCore.jackpotnum, true); 
        this.nOldJackpotNum = this._gameCore.jackpotnum;
        this._gameCore.sendJackpotRecord();
        this.aniNode.getComponent(cc.Animation).play();
    }

    onDestroy() {
        EventManager.instance.off(GameEvent.JACKPOT_RECORD_INFO, this.onUpdateRecordInfo, this);
        EventManager.instance.off(GameEvent.JACKPOT_RECORD, this.updateJackpotNum, this);
    }

    //刷新当前奖池金额
    updateJackpotNum(info) {

        let jackpotnum =Number(this._gameview.moneyFormat(Number(info["jackpot"]??0)));
        if (Common.isNull(jackpotnum)) {
            return;
        }

        this.jackpotnum = jackpotnum;

        let self = this;
        let jackpot_txt1 = self.jackpot_txt1.getComponent(cc.Label)

        this._gameview.scrollNumber({
            txt: jackpot_txt1,
            began: self.nOldJackpotNum,
            end: self.jackpotnum,
            format: (nValue) => {
                jackpot_txt1.string = `${self._gameview.formatNumberForComma(Common.toInt(nValue), true)}`;
            }
        });

        this.nOldJackpotNum = this.jackpotnum;
    }

    /*
        SC_SLOT777_JPLIST_P //响应-Jackpot中奖列表
        参数1：数组返回结果
        参数1："playerid" //玩家ID
        参数2："name" //玩家昵称
        参数3："headid" //玩家系统头像id
        参数4："wxheadurl" //自定义头像url
        参数5："sevennum" //牛的个数
        参数6："bet" //玩家下注额
        参数7："winscore" //玩家赢的金币
        参数8："time" //当局时间
    */
    //jackpot记录
    onUpdateRecordInfo(info: any){
        if(Common.isNull(info)){
            return;
        }
        
        this.content.removeAllChildren();
        for (let key in info) {
            let player = info[key];
            if(Common.isNull(player.playerid)){
                continue;
            }

            let item = cc.instantiate(this.Panel_item);
            this.content.addChild(item);
            item.active = true;
            
            let headImg = item.getChildByName('Panel').getChildByName('imgMask').getChildByName('headImg').getComponent(cc.Sprite);
            super.setPlayerHead(headImg, player.headid, player.wxheadurl??"");
            item.getChildByName('name').getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");
            item.getChildByName('time').getComponent(cc.Label).string = player.time?? '';
            item.getChildByName('rewards').getComponent(cc.Label).string = '₹' + this._gameview.moneyFormat(player.winscore ?? 0,2);
            item.getChildByName('total_bet').getComponent(cc.Label).string = '₹' + this._gameview.moneyFormat(player.bet ?? 0,1);
            let sevennum = player.sevennum??3;
            for(let i = 0;i < 5;i++){
                item.getChildByName('type').getChildByName('Image_jackpot_icon' + i).active = i < sevennum;
            }
        }
        
    }

    public onClickClose() {
        this._gameLogic.popUpEffect(this.node,false);
    }

    public onClickHelp() {
        let self = this;
        this._gameLogic.popUpEffect(this.node,false,(()=>{
            self._mCallback && self._mCallback();
        }));
        
    }
}
