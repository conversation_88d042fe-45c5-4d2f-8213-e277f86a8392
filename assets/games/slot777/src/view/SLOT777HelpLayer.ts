import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import PopupLayer from "../../../../script/frame/component/PopupLayer";
import Config from "../../../../script/frame/config/Config";
import EventManager from "../../../../script/frame/manager/EventManager";
import { Constant, GameEvent, GameTextTips } from "../core/SLOT777Define";
import SLOT777GameLogic from "../core/SLOT777GameLogic";
import SLOT777GameView from "./SLOT777GameView";

const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slot777/SLOT777HelpLayer')
export default class SLOT777HelpLayer extends BaseLayer {

    @property(cc.Node)
    ListView_menu: cc.Node = null;

    @property(cc.Node)
    node_content: cc.Node = null;

    @property(cc.Node)
    List_jackpot_item: cc.Node = null;

    @property(cc.Node)
    Panel_rule_item: cc.Node = null;
    
     /** 游戏主对象 */
     private _gameCore: any;
    _gameLogic :SLOT777GameLogic = null;
    _gameView: SLOT777GameView = null;
    mCurPage = 0;
    
    onLoad() {
       this._gameCore = cc.Canvas.instance.getComponent("SLOT777GameCore"); 
       this._gameLogic = cc.Canvas.instance.getComponent("SLOT777GameLogic"); 
       this._gameView = cc.Canvas.instance.getComponent("SLOT777GameView");
    }

    start() {
        // this.updateView(null);
    }

    public onClickClose() {
        let self = this;
        this._gameLogic.popUpEffect(this.node,false,(()=>{
            if(self.mCurPage != 0){
                self.onClickChangePage(null,0);
            }
        }));
    }

    public onClickChangePage(target: any, customEventData: any) {
        let index = Common.toNumber(customEventData);

        if(this.mCurPage == index){
            return
        }
        this.mCurPage = index;

        this.ListView_menu.children.forEach((item,itemIndex)=>{
            item.getChildByName('Image_btn_normal').active = !(itemIndex == index);
            item.getChildByName('Image_btn_select').active = itemIndex == index; 
        })

        this.node_content.children.forEach((item,itemIndex)=>{
            item.active = itemIndex == index;
        })
    }

    //jackport数据
    updateView(data){
       data = data || [];
       let list = [
        [10,3,5,10],
        [20,5,8,15],
        [100,15,20,25],
        [200,25,30,40]
       ] 
       this.List_jackpot_item.children.forEach((item,index)=>{
        for(let i = 0; i < 4; i++){
            let txt = item.getChildByName('Text_' + (i + 1)).getComponent(cc.Label) ;
            if(i == 0){
                txt.string = list[index][i] + '';
            }
            else{
                txt.string =  GameTextTips.Text_get + ' ' +  list[index][i] + '%';
            }
        }
       })
    }
}
