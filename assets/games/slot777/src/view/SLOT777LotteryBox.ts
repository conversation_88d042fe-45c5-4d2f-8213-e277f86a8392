import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import { Constant, FREE_TYPE, ICON_TYPE, Sounds, WIN_SPIN_NAME } from "../core/SLOT777Define";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import SLOT777GameCore, { FruitMachineBaseMachineElement, SpinStateFruitMachine, WinLinesInfo } from "../core/SLOT777GameCore";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import SLOT777GameView from "./SLOT777GameView";
import Config from "../../../../script/frame/config/Config";

const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slot777/SLOT777LotteryBox')
export default class SLOT777LotteryBox extends BaseLayer {

    // 游戏核心对象
    private _gameCore: SLOT777GameCore = null;
    private _gameView: SLOT777GameView = null;
    schedule_updatePos = null;

    onLoad() {
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)

        this._gameCore = cc.Canvas.instance.getComponent("SLOT777GameCore");
        this._gameView = cc.Canvas.instance.getComponent("SLOT777GameView");

        //初始化水果机元素
        this.initMachineElement();
    }

    start() {

    }

    onDestroy() {
        if(this.schedule_updatePos){
            this.unschedule(this.schedule_updatePos);
            this.schedule_updatePos = null;
        }
    }

    /**初始化水果机元素 */
    initMachineElement() {
        //初始化水果
        this._gameCore.machineElement = [];
        let cSize = this.LayerItems.Panel_container.getContentSize();
        let count = 0;
        let list = [-0.04,-0.07,0,0.03,0.03]
        let list1 = [0,-5,-7,-8,0]
        for (let nRow = 0, nRowMax = Constant.ROW_MAX; nRow < nRowMax; ++nRow) {
            this._gameCore.machineElement[nRow] = [];
            for (let nCol = 0, nColMax = Constant.COL_SHOW; nCol < nColMax; ++nCol) {
                count += 1;
                let node = cc.instantiate(this.LayerItems.item_icon);
                node.name = 'icon' + count;
                this.LayerItems.Panel_container.addChild(node);
                node.active = true;
               
                let nType = Common.random(0, Constant.ELEMENT_MAX_NUM - 1);

                let machineElement: FruitMachineBaseMachineElement = {
                    node: node,
                    nRow: nRow,
                    nCol: nCol,
                    nScale: node.scale
                };

                this._gameCore.machineElement[nRow][nCol] = machineElement;
                //保存引用
                this._gameCore.machineElementMap.set(node, machineElement);
                //刷新显示
                this.updateMachineElement(node, nType,nRow);
                //调整位置
                node.setPosition(cc.v2(
                    (cSize.width / nColMax) * (nCol - nColMax / 2 + 0.5 + list[nCol]),
                    (cSize.height / Constant.ROW_SHOW) * (nRowMax / 2 - nRow - 0.5) + list1[nRow],
                ));
            }
        }  
    }

    /**刷新水果机元素 */
    updateMachineElement(node: cc.Node, nType: number,nRow: number,bSetScale: boolean = false) {
        let info = this._gameCore.machineElementMap.get(node);
        info && (this._gameCore.machineElement[info.nRow][info.nCol].nType = nType);
        !!node && (node.getChildByName('icon').getComponent(cc.Sprite).spriteFrame = this._gameView.mainUiAtlas.getSpriteFrame(`slot777_ui_icon${nType}`));
        !!node && (node.scale = bSetScale ? 0.74 : nRow == 2 ? 1 : 0.74)
    }

    //开始游戏 
    startGame() {
        this._gameView.playYaoGanAni();
        this._gameCore.setResultDataInfo();
        this.doDrawLottery();
    }

    //重置初始数据
    resetData() {
        this.hideAllPrizeIconAni(true);
        this._gameView.checkWinLabelHide();
        this._gameCore.resetDataValue();

        for (let i = 0; i < this._gameCore.machineElement.length; i++) {
            for (let j = 0; j < this._gameCore.machineElement[i].length; j++) {
                let machineElement = this._gameCore.machineElement[i][j];
                machineElement.nStopRow = null;
                machineElement.nMoveIndex = null;
                machineElement.isOK = false;
                (<any>machineElement.node).bPlay = false;
            }
        }
    }

    update2(dt: number) {
        if (this._gameCore.bFruitIsStop || this._gameCore.m_updateIndex > 0) {
            return;
        }

        let t_ptPos = [
            194.5,
            194.5,
            142.3,    
            129.7,
            91.27,
            71.11,
            97.5,
            33.94,
            17.56,
            1.18,
            -15.2,
            -6.5,
            -47.96,
            -64.34,
            -80.72,
            -97.1,
            -114.5,
            -133.64,
            -151.28,
            -169.55,
            -182.15,
            -227.5,
            -227.5,
            -227.5,
            -227.5,
        ]

        //水果机滑动界面大小
        let cSize = this.LayerItems.Panel_container.getContentSize();
        let nMaxRollNum = 25;

        for (let nCol = 0; nCol < Constant.COL_SHOW; nCol++) {
            this._gameCore.m_RollNum[nCol] += 1
            
            let nRollNum = this._gameCore.m_RollNum[nCol]
            let maxRollNum = nCol * this._gameCore.m_nDoLen[nCol] + nMaxRollNum  
            
            if (nRollNum <= maxRollNum) {
                let nImageIndex = null
                for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                    let rollItem = this._gameCore.machineElement[nRow][nCol];
                    this._gameCore.m_indexMovePosList[nCol][nRow] += 1;
                    if(this._gameCore.m_indexMovePosList[nCol][nRow] > nMaxRollNum){
                        //回到顶部F
                        this._gameCore.m_indexMovePosList[nCol][nRow] = 0;
                        //重置图标，只有2-4的是服务器数据图标，修改需要调整下面的赋值
                        if (nRow >= 2 && nRow <= 4) {
                            //重置图标，只有2-4的是服务器数据图标，修改需要调整下面的赋值
                            nImageIndex = this._gameCore.fruitData[nRow - 2][nCol];

                            rollItem.isOK = true;
                            rollItem.nType = nImageIndex;
                            this.updateMachineElement(rollItem.node,nImageIndex,nRow - 1);
                        } else {
                            let rand = Common.random(0, Constant.ELEMENT_MAX_NUM - 1)
                            this.updateMachineElement(rollItem.node, rand,nRow,true);
                        }
                    }

                    rollItem.node.y = t_ptPos[this._gameCore.m_indexMovePosList[nCol][nRow]];                 
                }

                //滚动结束
                if (nRollNum == maxRollNum) {
                    this.playColScrollEndAudio(nCol);
                }
            }
            else {
                let springback = this._gameCore.m_springback[nCol];
                if (springback < Constant.ROW_MAX * Constant.ROW_SHOW) {//界面3*5 最后一个显示的元素
                    for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                        let posY = cSize.height / Constant.ROW_SHOW * (Constant.ROW_MAX / 2 - (nRow + 1) + 1 + 0.5);
                        let rollItem = this._gameCore.machineElement[nRow][nCol];
                        if (nRow >= 2 && nRow <= 4) {
                            if (springback < 7) {
                                rollItem.node.y = (posY - (springback + 1) * 5);
                            } else {
                                rollItem.node.y = (posY - (65 - springback * 5 + (nRow - 2) * 7));
                            }
                        }
                        else{
                            rollItem.node.y = posY;
                        }
                        springback = springback + 1
                        this._gameCore.m_springback[nCol] = springback
                    }
                }

                if (springback == Constant.ROW_MAX * Constant.ROW_SHOW && nCol == Constant.COL_SHOW - 1) {
                    if(this.schedule_updatePos){
                        this.unschedule(this.schedule_updatePos);
                        this.schedule_updatePos = null;
                    }
                    this._gameCore.m_updateIndex = 1; 

                    this.scheduleOnce(()=>{
                        //显示线
                        this.showWinLine();
                    },0.35)
                    
                    break;
                }
            }
        }
    }

    /**开始摇奖 */
    doDrawLottery() {
        //隐藏所有线
        this.hideAllLine();
        this._gameCore.bFruitIsStop = false;
        this._gameView.changeAddOrSubBtnColor();
        //音效
        AudioHelper.instance.playEffect(Sounds.ScrollStart);
        let self = this;
        if(self.schedule_updatePos){
            self.unschedule(self.schedule_updatePos);
            self.schedule_updatePos = null;
        }
        let time = 0;
        self.schedule_updatePos = (dt)=>{
            if(!self){
                return;
            }
            time = time + 1
            self.update2(dt)
            if(time == 2){
                time = 0;
                self.update2(dt)
            }
        }
        self.schedule(self.schedule_updatePos,0.03)
    }

    /**显示中奖线 */
    showWinLine() {
        let resultData = this._gameCore.resultData;
        let winLinesInfo = resultData.winLinesInfo;

        if (winLinesInfo && winLinesInfo.length > 0) {
            //先播所有中奖线及图标动画，然后轮播单条线
            this.repeatPlayMoreLineAni(winLinesInfo);
            if (this._gameCore.nLastFreeTimes <= 0 && this._gameCore.nSpinState == SpinStateFruitMachine.Free) {//免费最后一次 //先更新赢金再弹界面
                this.updateWinNum(() => { this.playWinAnim(() => { this.doFruitMachineEnd() }) }); //刷新赢金
            }
            else {
                this.playWinAnim(() => { this.updateWinNum(() => { this.doFruitMachineEnd() }) }); //刷新赢金
            }
        }
        else {
            if (this._gameCore.nLastFreeTimes <= 0 && this._gameCore.nSpinState == SpinStateFruitMachine.Free) {
                this.updateWinNum(() => { this.playWinAnim(() => { this.doFruitMachineEnd() }) }); //刷新赢金
            }
            else {
                if (resultData.multgoldnum >= 2 || resultData.jackpotcash > 0) {
                    this.playWinAnim(() => { this.updateWinNum(() => { this.doFruitMachineEnd() }) }); //刷新赢金
                }
                else {
                    // //刷新赢金
                    this.updateWinNum();

                    this.doFruitMachineEnd();
                }
            }
        }
    }

    //repeat播放多种线
    repeatPlayMoreLineAni(winLinesInfo: WinLinesInfo[]) {

        if (!winLinesInfo || winLinesInfo?.length == 0) {
            return;
        }
        let self = this;

        AudioHelper.instance.playEffect(`res/sounds/lianxian`);
        //显示所有线
        winLinesInfo.forEach(element => {
            self.setOneLineVisile(element, true, false, true);
        });

        //所有线显示一段时间后，切换至每隔1.5秒轮换每条线
        this._gameCore.curLineIndex = 0;

        (<any>this.LayerItems.Node_Lines_ex).playLineSoundList = [];

        let func = () => {
            self.hideAllLine(false,false);
            if (self._gameCore.curLineIndex >= 0) {
                if (!(<any>this.LayerItems.Node_Lines_ex).playLineSoundList[self._gameCore.curLineIndex]) {
                    self.playLineSound();
                }

                self.setOneLineVisile(winLinesInfo[self._gameCore.curLineIndex], true, false);
            }
            if (++self._gameCore.curLineIndex >= winLinesInfo.length) {
                self._gameCore.curLineIndex = 0;
            }
        }
        //轮流播放每条线
        this.LayerItems.Node_Lines_ex.stopAllActions();
        if (winLinesInfo.length == 1) {
            func();
        }
        else {
            this._gameView.nodeRunActionRepeatForever(self.LayerItems.Node_Lines_ex, func, 1.5);
        }
    }

    //播放连线声音
    playLineSound() {
        let multipleMusic = [4, 15, 25, 35];
        for (let i = 0; i < multipleMusic.length; i++) {//根据倍率播放
            if (this._gameCore.resultData.totalmult < multipleMusic[i]) {
                AudioHelper.instance.playEffect(`res/sounds/line${i + 1}`);
                (<any>this.LayerItems.Node_Lines_ex).playLineSoundList[this._gameCore.curLineIndex] = true;
                break;
            }
            if (i == multipleMusic.length - 1) {
                AudioHelper.instance.playEffect(`res/sounds/lineMore`);
                (<any>this.LayerItems.Node_Lines_ex).playLineSoundList[this._gameCore.curLineIndex] = true;
            }
        }
    }

    /**隐藏所有线 */
    hideAllLine(bStop: boolean = true,bStopOld: boolean = true) {
        this.hideAllPrizeIconAni();
        for (let i = 1; i <= Constant.MAX_LINES; ++i) {
            let winLinesInfo = <WinLinesInfo>{};
            winLinesInfo.nLine = i;
            this.setOneLineVisile(winLinesInfo, false, true,false,bStopOld);
        }
        if (bStop) {
            this.LayerItems.Node_Lines_ex.stopAllActions();
        }
    }

    /**设置单线显隐 */
    setOneLineVisile(data: WinLinesInfo, bVisible: boolean, hideAllPrizeIconAni: boolean = true, playAllPrizeAni: boolean = false,bStopOld: boolean = true) {
        let nViewLine = data.nLine;

        if (nViewLine && nViewLine <= Constant.MAX_LINES) {
            let lineNumNode = this.LayerItems.Node_Lines_ex.getChildByName(`Reward_Line_${nViewLine}`);
            let lineNumNode1 = this.LayerItems.Node_Lines.getChildByName(`prize_Line_${nViewLine}`);
            if (lineNumNode) {
                lineNumNode.active = bVisible;
                lineNumNode1.active = bVisible;
            }
        }

        //切换多条线时，隐藏当前所有中奖后重新显示对应中奖图标动画
        if (hideAllPrizeIconAni) {
            this.hideAllPrizeIconAni();
        }
        if (data.prizeIconList && data.prizeIconList.length > 0) {
            let count = 0;
            let prizeIndexCount = 0;//中奖图标索引累加
            for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                for (let nCol = 0; nCol < Constant.COL_SHOW; nCol++) {
                    count += 1;
                    if (nRow > 0 && nRow < Constant.ROW_MAX - 1) {
                        let subitem = this._gameCore.machineElement[nRow + 1][nCol];
                        let aniNode = subitem.node.getChildByName('prize_7');
                        let nType = subitem.nType;
                        //Constant.COL_SHOW + data.prizeIconList[prizeIndexCount] 第一行隐藏5个图标 中奖索引要加上之前图标
                        if (prizeIndexCount < data.prizeIconList.length && data.prizeIconList[prizeIndexCount] && count == Constant.COL_SHOW + data.prizeIconList[prizeIndexCount]) {
                            subitem.node.getChildByName('prizeLightSpin').active = true;
                            if (!Common.isNull(nType) && nType == ICON_TYPE.NUM_777 && this._gameCore.resultData.sevennum >= 3 && this._gameCore.resultData.jackpotcash > 0) {//nRow + 1  machineElement是最上面隐藏了一行，只显示中间的，所以减掉最上面一行图标值
                                aniNode.active = true; //中3个或以上7显示动画
                                aniNode.getComponent(cc.Animation).play(); 
                            }
                            prizeIndexCount += 1
                        }
                    }
                }
            }
        }
    }

    //隐藏所有图标中奖动画
    hideAllPrizeIconAni(bHidePrize7Ani: boolean = false) {
        this._gameCore.machineElement.forEach((item) => {
            item.forEach((subitem) => {
                subitem.node.getChildByName('prizeLightSpin').active = false;
                bHidePrize7Ani && (subitem.node.getChildByName('prize_7').active = false);
            })
        })
    }

    /**播放赢金动画 */
    playWinAnim(callback: Function = null) {
        let animName: string;
        let resultData = this._gameCore.resultData;
        let goldNum = resultData.winmoney;
        let soundName = '';

        if (this._gameCore.nLastFreeTimes == 0 && this._gameCore.nSpinState == SpinStateFruitMachine.Free) {
            animName = WIN_SPIN_NAME.TOTAL;
            goldNum = this._gameCore.freeWinTotalNum;
            soundName = 'res/sounds/freeTotalWin'

            if(goldNum <= 0){
                callback && callback();
                return;
            }
        }
        else {
            if(this._gameCore.resultData.jackpotcash > 0){//有jackpot中奖
                this.playJackpotAnim(callback);
                return;
            }

            if (resultData.multgoldnum < 2 || this._gameCore.nLastFreeTimes > 0) {
                callback && callback();
                return;
            }
            AudioHelper.instance.playEffect(`res/sounds/win`);
            if (resultData.multgoldnum < 5) {
                animName = WIN_SPIN_NAME.BIG;
                soundName = `res/sounds/smallWin`
            } else if (resultData.multgoldnum < 20) {
                animName = WIN_SPIN_NAME.MEGA;
                soundName = `res/sounds/midWin`
            } else if (resultData.multgoldnum < 40) {
                animName = WIN_SPIN_NAME.EPIC;
                soundName = `res/sounds/bigWin`
            } else {
                animName = WIN_SPIN_NAME.SUPER;
                soundName = `res/sounds/superWin`
            }
        }
        this.scheduleOnce(() => {
            AudioHelper.instance.playEffect(soundName);
        }, 0.02)

        this.LayerItems.Panel_win.active = true;
        let win_num = this.LayerItems.Panel_win.getChildByName('num').getComponent(cc.Label);
        win_num.string = '0.00';
        this.LayerItems.Panel_win.getChildByName('spinNode').getComponent(sp.Skeleton).setAnimation(0, animName, false);
        let spinGoldNode = this.LayerItems.spinGoldNode.getComponent(sp.Skeleton);
        spinGoldNode.setAnimation(0, 'jinbi0', false);
        spinGoldNode.setCompleteListener(()=>{
            spinGoldNode.setAnimation(0, 'jinbi1', true);
        })

        let self = this;
        this.scheduleOnce(() => {
            self._gameView.scrollNumber({
                txt: win_num,
                began: 0,
                end: goldNum,
                format: (nValue, dataEx) => {
                    win_num.string = `${self._gameView.moneyFormat(nValue, 2)}`
                },
                callback: () => {
                    win_num.node.stopAllActions();
                    self._gameView.nodeRunActionOncs(win_num.node, () => {
                        self.LayerItems.Panel_win.active = false;
                        callback && callback();
                    }, 1.5);
                },
            });
        }, 0.2);
    }
    
    /**爆奖池 */
	playJackpotAnim(callback) {
		let self = this;
        AudioHelper.instance.stopEffectByPath(`res/sounds/jackpot`)
		AudioHelper.instance.playEffect(`res/sounds/jackpot`);
		this.LayerItems.Panel_jackpotPrize.active = true;
        let jackpotSpinGoldNode = this.LayerItems.jackpotSpinGoldNode.getComponent(sp.Skeleton);
        jackpotSpinGoldNode.setAnimation(0, 'jinbi0', false);
        jackpotSpinGoldNode.setCompleteListener(()=>{
            jackpotSpinGoldNode.setAnimation(0, 'jinbi1', true);
        })
		let ani1 = this.LayerItems.jackpotPrizeAni.getComponent(cc.Animation);
		let animState1 = ani1.play('animation0');
		animState1.wrapMode = cc.WrapMode.Normal;
		ani1.on('finished',(()=>{
			let animState1 = ani1.play('animation1');
			animState1.wrapMode = cc.WrapMode.Loop;
		}), this);

		let jackpot_prize = self.LayerItems.jackpot_prize.getComponent(cc.Label);
        self.scheduleOnce(() => {
            self._gameView.scrollNumber({
                txt: jackpot_prize,
                began: 0,
                end: self._gameCore.resultData.jackpotcash/Config.SCORE_RATE,
                format: (nValue, dataEx) => {
                    jackpot_prize.string = `${self._gameView.formatNumberForComma(Common.toInt(nValue), true)}`;
                },
                callback: () => {
                    jackpot_prize.node.stopAllActions();
                    self._gameView.nodeRunActionOncs(jackpot_prize.node, () => {;
                        self.LayerItems.Panel_jackpotPrize.active = false;
                        callback && callback();
                    }, 3);
                },
            });
        }, 0.2);
	}

    /**刷新赢金数字 */
    updateWinNum(callback: Function = null) {
        let self = this;
        //先隐藏所有
        this.LayerItems.Node_win_good_luck.active = false;
        this.LayerItems.Node_win_normal.active = false;

        //依据结算信息刷新
        let resultData = this._gameCore.resultData;
        if (!resultData) {
            this.LayerItems.Node_win_good_luck.active = true;
        } else {
            //免费
            if (this._gameCore.nSpinState == SpinStateFruitMachine.Free) {
                this.LayerItems.Node_win_normal.active = true;
                this.LayerItems.Image_win.active = false;
                this.LayerItems.Image_win_total.active = true;
                let winNum = this.LayerItems.Node_win_normal.getChildByName('win_num').getComponent(cc.Label)
                this._gameView.scrollNumber({
                    txt: winNum,
                    began: this._gameCore.lastFreeWinNum,
                    end: this._gameCore.freeWinTotalNum,
                    format: (nValueEx) => {
                        winNum.string = self._gameView.moneyFormat(nValueEx, 2);
                    },
                    callback: () => {
                        callback && callback();
                    }
                });
                this._gameCore.lastFreeWinNum = this._gameCore.freeWinTotalNum;
            }
            //正常
            else {
                if (resultData.winmoney > 0) {
                    this.LayerItems.Node_win_normal.active = true;
                    this.LayerItems.Image_win.active = true;
                    this.LayerItems.Image_win_total.active = false;
                    let winNum = this.LayerItems.Node_win_normal.getChildByName('win_num').getComponent(cc.Label);
                    winNum.string = '0.00';
                    this._gameView.scrollNumber({
                        txt: winNum,
                        nTime: resultData.multgoldnum < 2 ? 0.35 : 1.0,
                        began: 0,
                        end: resultData.winmoney,
                        format: (nValueEx) => {
                            winNum.string = self._gameView.moneyFormat(nValueEx, 2);
                        },
                        callback: () => {
                            this.LayerItems.Node_win_normal.stopAllActions();
                            callback && callback();
                        }
                    });
                } else {
                    this.LayerItems.Node_win_good_luck.active = true;
                    callback && callback();
                }
            }
        }
    }
   
    //动画播放结束
    doFruitMachineEnd() {
        //更新自己余额
        if (this._gameCore.freeTimesEndFlag == FREE_TYPE.end) {//免费完成时累加的总赢金
            this._gameCore.playScoreMoney = this._gameCore.myselfMoney;
            this._gameView.showSelfMoney();
            this._gameCore.freeTimesEndFlag = FREE_TYPE.non;
        }
        else {
            if (this._gameCore.nLastFreeTimes <= 0) {//免费播放时，不直接更新余额
                this._gameCore.playScoreMoney = this._gameCore.myselfMoney;
                this._gameView.showSelfMoney();
            }
        }

        //水果机停止
        this._gameCore.bFruitIsStop = true;
        this._gameView.changeAddOrSubBtnColor();
               
        this.onFruitMachineEnd();
    }

    /**水果机动画完成后回调 */
    onFruitMachineEnd() {
        let resultData = this._gameCore.resultData;

        let self = this;
        let onMachineEnd = () => {
            switch (this._gameCore.nSpinState) {
                case SpinStateFruitMachine.Free: {
                    if (self._gameCore.nLastFreeTimes > 0) {
                        self.scheduleOnce(() => {
                            self._gameView.autoStartFreeGame();
                        }, 1.8)
                    } else {
                        self._gameView.updateSpinState(SpinStateFruitMachine.Normal);
                    }
                    break;
                }
                case SpinStateFruitMachine.Normal: {
                   if (resultData.freetimes > 0) {//是否中了免费
                        self._gameCore.lastAutoSpinState = self._gameCore.bAutoSpin;
                        self._gameView.setSpinAutoState(false);
                        self.playFreeAnim();//弹免费次数
                    } else {
                        if (self._gameCore.bAutoSpin) {
                            self._gameView.setSpinAutoState(true);
                            self.scheduleOnce(() => {
                                self._gameView.onClickSpin();
                            }, 1.8)
                        }
                    }
                    break;
                }
            }
        }
        
        onMachineEnd(); 
    }

    //播放免费动画
    playFreeAnim() {
        AudioHelper.instance.playEffect(`res/sounds/pop_free`);
        this.LayerItems.Panel_free.active = true;
        this.LayerItems.freeAni.active = true;
        let ani = this.LayerItems.Panel_free.getChildByName('tx_haiwai_freespins');
        ani.getComponent(cc.Animation).play();

        let numText = this.LayerItems.freeTotalNum.getComponent(cc.Label);
        let self = this;
        this._gameView.scrollNumber({
            txt: numText,
            began: 0,
            end: self._gameCore.resultData.freetimes,
            nTime: 0.7,
            format: (nValueEx) => {
                numText.string = Common.toInt(nValueEx);
            },
            callback: () => {
                self.LayerItems.Panel_free.stopAllActions();
                cc.tween(self.LayerItems.Panel_free)
                    .call(()=>{
                        self.LayerItems.span_free.active = true;
                        self.LayerItems.Free_Text.getComponent(cc.Label).string = self._gameCore.resultData.freetimes + '';
                    })
                    .delay(2.5)
                    .call(() => {
                        self._gameView.onClickStartFreeGame(); 
                        self.LayerItems.Panel_free.active = false;
                    })
                    .start()
            }
        });
    }


    /**播放每列结束音效 */
    playColScrollEndAudio(nCol: number) {
        AudioHelper.instance.stopEffectByPath(Sounds.ScrollStart)
        AudioHelper.instance.playEffect(`res/sounds/slotFruitStop${nCol + 1}`);
    }
    
    //拆分相同元素
    splitSameElementCol(arr_, val_, count_, index_) {
        let tdata = []
        for (let i = 0; i < 5; i++) {
            tdata[i] = { val: -1 };
        }
        for (let i = 0; i < arr_.length; i++) {
            for (let j = 0; j < arr_[i].length; j++) {
                if (val_ == arr_[i][j] && 0 > tdata[j].val) {
                    tdata[j].val = val_
                    tdata[j].pos = { row: i, col: j }
                }
            }
        }
        let tret = []
        let tsameArrr = []
        for (let i = 0; i < 5; i++) {
            tsameArrr[i] = []
            for (let j = i; j >= 0; j--) {
                if (0 < tdata[j].val) {
                    if (!index_ || index_ == i) {
                        tsameArrr[i].push(tdata[j])
                    }
                } else {
                    break
                }
            }
            if (count_ <= tsameArrr[i].length) {
                tret.push(tsameArrr[i])
            }
        }
        return tret
    }
}