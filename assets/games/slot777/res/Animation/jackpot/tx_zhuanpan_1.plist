<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>tx_yanhua_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,770},{350,328}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,328}}</string>
                <key>sourceSize</key>
                <string>{350,328}</string>
            </dict>
            <key>tx_yanhua_00002.png</key>
            <dict>
                <key>frame</key>
                <string>{{702,694},{350,328}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,328}}</string>
                <key>sourceSize</key>
                <string>{350,328}</string>
            </dict>
            <key>tx_yanhua_00006.png</key>
            <dict>
                <key>frame</key>
                <string>{{1046,1036},{350,312}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,312}}</string>
                <key>sourceSize</key>
                <string>{350,312}</string>
            </dict>
            <key>tx_yanhua_00008.png</key>
            <dict>
                <key>frame</key>
                <string>{{700,1358},{350,310}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,310}}</string>
                <key>sourceSize</key>
                <string>{350,310}</string>
            </dict>
            <key>tx_yanhua_00010.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1100},{350,316}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,316}}</string>
                <key>sourceSize</key>
                <string>{350,316}</string>
            </dict>
            <key>tx_yanhua_00014.png</key>
            <dict>
                <key>frame</key>
                <string>{{1320,1670},{350,276}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,276}}</string>
                <key>sourceSize</key>
                <string>{350,276}</string>
            </dict>
            <key>tx_yanhua_00016.png</key>
            <dict>
                <key>frame</key>
                <string>{{646,1710},{350,278}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,278}}</string>
                <key>sourceSize</key>
                <string>{350,278}</string>
            </dict>
            <key>tx_yanhua_00020.png</key>
            <dict>
                <key>frame</key>
                <string>{{354,1684},{350,290}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,290}}</string>
                <key>sourceSize</key>
                <string>{350,290}</string>
            </dict>
            <key>tx_yanhua_00022.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1712},{350,282}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,282}}</string>
                <key>sourceSize</key>
                <string>{350,282}</string>
            </dict>
            <key>tx_yanhua_00026.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1418},{350,292}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,292}}</string>
                <key>sourceSize</key>
                <string>{350,292}</string>
            </dict>
            <key>tx_yanhua_00028.png</key>
            <dict>
                <key>frame</key>
                <string>{{1012,1670},{350,306}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,306}}</string>
                <key>sourceSize</key>
                <string>{350,306}</string>
            </dict>
            <key>tx_yanhua_00032.png</key>
            <dict>
                <key>frame</key>
                <string>{{354,1368},{340,314}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{340,314}}</string>
                <key>sourceSize</key>
                <string>{340,314}</string>
            </dict>
            <key>tx_yanhua_00034.png</key>
            <dict>
                <key>frame</key>
                <string>{{1046,1350},{342,318}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{342,318}}</string>
                <key>sourceSize</key>
                <string>{342,318}</string>
            </dict>
            <key>tx_yanhua_00038.png</key>
            <dict>
                <key>frame</key>
                <string>{{702,1024},{342,332}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{342,332}}</string>
                <key>sourceSize</key>
                <string>{342,332}</string>
            </dict>
            <key>tx_yanhua_00040.png</key>
            <dict>
                <key>frame</key>
                <string>{{354,1038},{344,328}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{344,328}}</string>
                <key>sourceSize</key>
                <string>{344,328}</string>
            </dict>
            <key>tx_yanhua_00044.png</key>
            <dict>
                <key>frame</key>
                <string>{{354,694},{346,342}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{346,342}}</string>
                <key>sourceSize</key>
                <string>{346,342}</string>
            </dict>
            <key>tx_yanhua_00046.png</key>
            <dict>
                <key>frame</key>
                <string>{{1141,692},{348,342}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{348,342}}</string>
                <key>sourceSize</key>
                <string>{348,342}</string>
            </dict>
            <key>tx_yanhua_00050.png</key>
            <dict>
                <key>frame</key>
                <string>{{1141,348},{350,342}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,342}}</string>
                <key>sourceSize</key>
                <string>{350,342}</string>
            </dict>
            <key>tx_yanhua_00052.png</key>
            <dict>
                <key>frame</key>
                <string>{{437,2},{350,344}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,344}}</string>
                <key>sourceSize</key>
                <string>{350,344}</string>
            </dict>
            <key>tx_yanhua_00056.png</key>
            <dict>
                <key>frame</key>
                <string>{{789,2},{350,344}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,344}}</string>
                <key>sourceSize</key>
                <string>{350,344}</string>
            </dict>
            <key>tx_yanhua_00058.png</key>
            <dict>
                <key>frame</key>
                <string>{{1141,2},{350,344}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,344}}</string>
                <key>sourceSize</key>
                <string>{350,344}</string>
            </dict>
            <key>tx_yanhua_00062.png</key>
            <dict>
                <key>frame</key>
                <string>{{437,348},{350,344}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,344}}</string>
                <key>sourceSize</key>
                <string>{350,344}</string>
            </dict>
            <key>tx_yanhua_00064.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,424},{350,344}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,344}}</string>
                <key>sourceSize</key>
                <string>{350,344}</string>
            </dict>
            <key>tx_yanhua_00068.png</key>
            <dict>
                <key>frame</key>
                <string>{{789,348},{350,344}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{350,344}}</string>
                <key>sourceSize</key>
                <string>{350,344}</string>
            </dict>
            <key>tx_youxichang_wangzha_light03.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{420,433}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{420,433}}</string>
                <key>sourceSize</key>
                <string>{420,433}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>tx_zhuanpan_1.png</string>
            <key>size</key>
            <string>{1598,2036}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:65bee7432fefb823d66c3eb1223b8c32:6e8adba1163c5bc836aef54b9b97f181:ef4846b18e4da950fa77ba9e50036887$</string>
            <key>textureFileName</key>
            <string>tx_zhuanpan_1.png</string>
        </dict>
    </dict>
</plist>
