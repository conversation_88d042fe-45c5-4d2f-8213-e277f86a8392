<?xml version="1.0" encoding="utf-8"?>
<plist version="1.0"><dict><key>angle</key>
<real>30.000000</real>
<key>angleVariance</key>
<real>5.000000</real>
<key>duration</key>
<real>-1.000000</real>
<key>startParticleSize</key>
<real>20.000000</real>
<key>startParticleSizeVariance</key>
<real>50.000000</real>
<key>finishParticleSize</key>
<real>35.000000</real>
<key>finishParticleSizeVariance</key>
<real>100.000000</real>
<key>gravityx</key>
<real>0.000000</real>
<key>gravityy</key>
<real>-700.000000</real>
<key>maxParticles</key>
<real>90.000000</real>
<key>maxRadius</key>
<real>276.209991</real>
<key>maxRadiusVariance</key>
<real>154.949997</real>
<key>minRadius</key>
<real>0.000000</real>
<key>particleLifespan</key>
<real>0.300000</real>
<key>particleLifespanVariance</key>
<real>2.000000</real>
<key>rotatePerSecond</key>
<real>35.529999</real>
<key>rotatePerSecondVariance</key>
<real>0.000000</real>
<key>rotationEnd</key>
<real>360.000000</real>
<key>rotationEndVariance</key>
<real>360.000000</real>
<key>rotationStart</key>
<real>0.000000</real>
<key>rotationStartVariance</key>
<real>360.000000</real>
<key>sourcePositionVariancex</key>
<real>250.000000</real>
<key>sourcePositionVariancey</key>
<real>10.000000</real>
<key>sourcePositionx</key>
<real>142.000000</real>
<key>sourcePositiony</key>
<real>74.000000</real>
<key>speed</key>
<real>500.000000</real>
<key>speedVariance</key>
<real>300.000000</real>
<key>startColorAlpha</key>
<real>1.000000</real>
<key>startColorBlue</key>
<real>1.000000</real>
<key>startColorGreen</key>
<real>1.000000</real>
<key>startColorRed</key>
<real>1.000000</real>
<key>startColorVarianceAlpha</key>
<real>0.000000</real>
<key>startColorVarianceBlue</key>
<real>0.000000</real>
<key>startColorVarianceGreen</key>
<real>0.000000</real>
<key>startColorVarianceRed</key>
<real>0.000000</real>
<key>finishColorAlpha</key>
<real>0.501961</real>
<key>finishColorBlue</key>
<real>1.000000</real>
<key>finishColorGreen</key>
<real>1.000000</real>
<key>finishColorRed</key>
<real>1.000000</real>
<key>finishColorVarianceAlpha</key>
<real>1.000000</real>
<key>finishColorVarianceBlue</key>
<real>0.000000</real>
<key>finishColorVarianceGreen</key>
<real>0.000000</real>
<key>finishColorVarianceRed</key>
<real>0.000000</real>
<key>tangentialAccelVariance</key>
<real>30.000000</real>
<key>tangentialAcceleration</key>
<real>0.000000</real>
<key>radialAccelVariance</key>
<real>0.000000</real>
<key>radialAcceleration</key>
<real>0.000000</real>
<key>blendFuncSource</key>
<integer>770</integer>
<key>blendFuncDestination</key>
<integer>771</integer>
<key>emitterType</key>
<real>0</real>
<key>textureFileName</key>
<string>jinbi3.png</string>
</dict></plist>
