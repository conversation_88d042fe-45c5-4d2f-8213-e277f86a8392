<?xml version="1.0" encoding="utf-8"?>
<plist version="1.0"><dict><key>angle</key>
<real>90.000000</real>
<key>angleVariance</key>
<real>30.000000</real>
<key>duration</key>
<real>-1.000000</real>
<key>startParticleSize</key>
<real>20.000000</real>
<key>startParticleSizeVariance</key>
<real>50.000000</real>
<key>finishParticleSize</key>
<real>35.000000</real>
<key>finishParticleSizeVariance</key>
<real>100.000000</real>
<key>gravityx</key>
<real>0.000000</real>
<key>gravityy</key>
<real>-500.000000</real>
<key>maxParticles</key>
<real>180.000000</real>
<key>maxRadius</key>
<real>276.209991</real>
<key>maxRadiusVariance</key>
<real>154.949997</real>
<key>minRadius</key>
<real>0.000000</real>
<key>particleLifespan</key>
<real>0.500000</real>
<key>particleLifespanVariance</key>
<real>1.000000</real>
<key>rotatePerSecond</key>
<real>35.529999</real>
<key>rotatePerSecondVariance</key>
<real>0.000000</real>
<key>rotationEnd</key>
<real>360.000000</real>
<key>rotationEndVariance</key>
<real>360.000000</real>
<key>rotationStart</key>
<real>0.000000</real>
<key>rotationStartVariance</key>
<real>360.000000</real>
<key>sourcePositionVariancex</key>
<real>380.000000</real>
<key>sourcePositionVariancey</key>
<real>50.000000</real>
<key>sourcePositionx</key>
<real>163.000000</real>
<key>sourcePositiony</key>
<real>152.000000</real>
<key>speed</key>
<real>800.000000</real>
<key>speedVariance</key>
<real>300.000000</real>
<key>startColorAlpha</key>
<real>1.000000</real>
<key>startColorBlue</key>
<real>1.000000</real>
<key>startColorGreen</key>
<real>1.000000</real>
<key>startColorRed</key>
<real>1.000000</real>
<key>startColorVarianceAlpha</key>
<real>0.000000</real>
<key>startColorVarianceBlue</key>
<real>0.000000</real>
<key>startColorVarianceGreen</key>
<real>0.000000</real>
<key>startColorVarianceRed</key>
<real>0.000000</real>
<key>finishColorAlpha</key>
<real>0.831373</real>
<key>finishColorBlue</key>
<real>1.000000</real>
<key>finishColorGreen</key>
<real>1.000000</real>
<key>finishColorRed</key>
<real>1.000000</real>
<key>finishColorVarianceAlpha</key>
<real>0.721569</real>
<key>finishColorVarianceBlue</key>
<real>0.000000</real>
<key>finishColorVarianceGreen</key>
<real>0.000000</real>
<key>finishColorVarianceRed</key>
<real>0.000000</real>
<key>tangentialAccelVariance</key>
<real>30.000000</real>
<key>tangentialAcceleration</key>
<real>0.000000</real>
<key>radialAccelVariance</key>
<real>0.000000</real>
<key>radialAcceleration</key>
<real>0.000000</real>
<key>blendFuncSource</key>
<integer>770</integer>
<key>blendFuncDestination</key>
<integer>771</integer>
<key>emitterType</key>
<real>0</real>
<key>textureFileName</key>
<string>jinbi2.png</string>
<key>textureImageData</key>
<string>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</string>
</dict></plist>
