<?xml version="1.0" encoding="utf-8"?>
<plist version="1.0"><dict><key>angle</key>
<real>150.000000</real>
<key>angleVariance</key>
<real>0.000000</real>
<key>duration</key>
<real>-1.000000</real>
<key>startParticleSize</key>
<real>20.000000</real>
<key>startParticleSizeVariance</key>
<real>50.000000</real>
<key>finishParticleSize</key>
<real>35.000000</real>
<key>finishParticleSizeVariance</key>
<real>100.000000</real>
<key>gravityx</key>
<real>0.000000</real>
<key>gravityy</key>
<real>-700.000000</real>
<key>maxParticles</key>
<real>90.000000</real>
<key>maxRadius</key>
<real>276.209991</real>
<key>maxRadiusVariance</key>
<real>154.949997</real>
<key>minRadius</key>
<real>0.000000</real>
<key>particleLifespan</key>
<real>0.300000</real>
<key>particleLifespanVariance</key>
<real>2.000000</real>
<key>rotatePerSecond</key>
<real>35.529999</real>
<key>rotatePerSecondVariance</key>
<real>0.000000</real>
<key>rotationEnd</key>
<real>360.000000</real>
<key>rotationEndVariance</key>
<real>360.000000</real>
<key>rotationStart</key>
<real>0.000000</real>
<key>rotationStartVariance</key>
<real>360.000000</real>
<key>sourcePositionVariancex</key>
<real>250.000000</real>
<key>sourcePositionVariancey</key>
<real>10.000000</real>
<key>sourcePositionx</key>
<real>204.000000</real>
<key>sourcePositiony</key>
<real>266.000000</real>
<key>speed</key>
<real>500.000000</real>
<key>speedVariance</key>
<real>300.000000</real>
<key>startColorAlpha</key>
<real>1.000000</real>
<key>startColorBlue</key>
<real>1.000000</real>
<key>startColorGreen</key>
<real>1.000000</real>
<key>startColorRed</key>
<real>1.000000</real>
<key>startColorVarianceAlpha</key>
<real>0.000000</real>
<key>startColorVarianceBlue</key>
<real>0.000000</real>
<key>startColorVarianceGreen</key>
<real>0.000000</real>
<key>startColorVarianceRed</key>
<real>0.000000</real>
<key>finishColorAlpha</key>
<real>0.501961</real>
<key>finishColorBlue</key>
<real>1.000000</real>
<key>finishColorGreen</key>
<real>1.000000</real>
<key>finishColorRed</key>
<real>1.000000</real>
<key>finishColorVarianceAlpha</key>
<real>1.000000</real>
<key>finishColorVarianceBlue</key>
<real>0.000000</real>
<key>finishColorVarianceGreen</key>
<real>0.000000</real>
<key>finishColorVarianceRed</key>
<real>0.000000</real>
<key>tangentialAccelVariance</key>
<real>30.000000</real>
<key>tangentialAcceleration</key>
<real>0.000000</real>
<key>radialAccelVariance</key>
<real>0.000000</real>
<key>radialAcceleration</key>
<real>0.000000</real>
<key>blendFuncSource</key>
<integer>770</integer>
<key>blendFuncDestination</key>
<integer>771</integer>
<key>emitterType</key>
<real>0</real>
<key>textureFileName</key>
<string>jinbi.png</string>
<key>textureImageData</key>
<string>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</string>
</dict></plist>
