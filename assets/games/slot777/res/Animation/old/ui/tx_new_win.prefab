[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_rawFiles": null, "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}, {"__id__": 11}, {"__id__": 14}, {"__id__": 17}, {"__id__": 20}, {"__id__": 23}, {"__id__": 30}, {"__id__": 33}, {"__id__": 36}, {"__id__": 39}], "_tag": -1, "_active": true, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Panel_2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 7}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 540}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_id": "", "_opacity": 153, "_color": {"__type__": "cc.Color", "r": 150, "g": 200, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 540}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "darXJO55VMv4WKNT8dSTnv", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8bK5vBo65Jcrfp9acxamBt", "sync": false}, {"__type__": "cc.Node", "_name": "tx_chou<PERSON>_honbao_yuanguang_14_0", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 165, "b": 0, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 434, "height": 445}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4.5877, 75.7505, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_spriteFrame": {"__uuid__": "276ebf27-4b7b-45f7-a2b6-9246561e2d17"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "13ajc9JihE9LLt6msoTjdo", "sync": false}, {"__type__": "cc.Node", "_name": "tx_dz_zjd_digaung_12", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 12}], "_prefab": {"__id__": 13}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 665, "height": 665}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3.7698, 82.7456, 0, 0, 0, 0, 1, 0.6492, 0.6492, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_spriteFrame": {"__uuid__": "2cbe813b-fbfb-4836-a65e-7dc7f789e1ff"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6121l7GNpF7asbTsni0Iem", "sync": false}, {"__type__": "cc.Node", "_name": "Particle_1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 15}], "_prefab": {"__id__": 16}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8.0471, 47.1723, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_custom": false, "_file": {"__uuid__": "e39afe23-5667-4f48-9046-4b92087602f1"}, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "playOnLoad": true, "_autoRemoveOnFinish": false, "totalParticles": 150, "duration": -1, "emissionRate": 10, "life": 1, "lifeVar": 0, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "angle": 90, "angleVar": 20, "startSize": 50, "startSizeVar": 0, "endSize": 0, "endSizeVar": 0, "startSpin": 0, "startSpinVar": 0, "endSpin": 0, "endSpinVar": 0, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_positionType": 0, "_emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "speed": 180, "speedVar": 50, "tangentialAccel": 80, "tangentialAccelVar": 0, "radialAccel": 0, "radialAccelVar": 0, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27fGS6LsVFgJonO6Njg2BP", "sync": false}, {"__type__": "cc.Node", "_name": "JS_bg02_5", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 18}], "_prefab": {"__id__": 19}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 82}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-14.1018, 106.459, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_spriteFrame": {"__uuid__": "ea6d04df-f898-45c0-a58f-5ac6dd93a414"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3bZcUvvSNKjLBISmDVq944", "sync": false}, {"__type__": "cc.Node", "_name": "JS_bg03_7", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 21}], "_prefab": {"__id__": 22}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_contentSize": {"__type__": "cc.Size", "width": 402, "height": 101}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.0385, -3.7167, 0, 0, 0, 0, 1, 1, 0.1129, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "_spriteFrame": {"__uuid__": "e04ab96e-51f1-41a6-a485-45acf9fe5fc9"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98uoQrbDBP66/l8ijdA500", "sync": false}, {"__type__": "cc.Node", "_name": "JS_bg01_6", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 24}], "_tag": -1, "_active": true, "_components": [{"__id__": 28}], "_prefab": {"__id__": 29}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 430, "height": 111}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4.736, 18.8161, 0, 0, 0, 0, 1, 0.2488, 0.2488, 1]}}, {"__type__": "cc.Node", "_name": "BitmapFontLabel_1", "_objFlags": 0, "_parent": {"__id__": 23}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 25}, {"__id__": 26}], "_prefab": {"__id__": 27}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 241, "height": 52}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 11.099999999999994, 0, 0, 0, 0, 1, 1.2, 1.2, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.21975, "_right": 0, "_top": 0, "_bottom": 0.36574999999999996, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "6d116b40-fc50-4cd6-aa4b-409520e59f6f"}, "_isSystemFontUsed": false, "_spacingX": 0, "_N$string": "+6666666", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8lJNO2t1CvrcQtE2J89i2", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_spriteFrame": {"__uuid__": "595039bf-ca47-4350-b7c9-5cc9c637ff8c"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a9KZIvnaNCb6DUQUHJAUX/", "sync": false}, {"__type__": "cc.Node", "_name": "JS_win_9", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 31}], "_prefab": {"__id__": 32}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 234, "height": 106}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-13.9429, 99.2678, 0, 0, 0, 0, 1, 2.2182, 2.2182, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_spriteFrame": {"__uuid__": "b6a00686-ec99-480d-b823-d85118e7a2e2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "145UF27QlJSakucpLTKetF", "sync": false}, {"__type__": "cc.Node", "_name": "tx_<PERSON><PERSON>_jinji_lizi_00_10", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 34}], "_prefab": {"__id__": 35}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 197}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12.0471, 103.3778, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_spriteFrame": {"__uuid__": "5af74ed3-f61f-487f-b1b0-eebd9198a907"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3eZh+WERxKE4smqjBDsJWy", "sync": false}, {"__type__": "cc.Node", "_name": "tx_ch<PERSON><PERSON>_honbao_yuanguang_14", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 37}], "_prefab": {"__id__": 38}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 434, "height": 445}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4.5877, 75.7505, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "_spriteFrame": {"__uuid__": "276ebf27-4b7b-45f7-a2b6-9246561e2d17"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1bYKXYKiVEeYwzEvzubTxT", "sync": false}, {"__type__": "cc.Node", "_name": "tx_gxhd_lingqu_guangquan_15", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 40}], "_prefab": {"__id__": 41}, "_id": "", "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 634, "height": 634}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-11.4589, 70.5959, 0, 0, 0, 0, 1, 0.4634, 0.4634, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_spriteFrame": {"__uuid__": "1f22d212-df2e-4f6f-9a58-c3977bfb4afa"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eb8/NBpfZLMKuukq7CE/C9", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_defaultClip": null, "_clips": [{"__uuid__": "9b87dcd7-d429-499d-b066-efc557ea2f1e"}], "playOnLoad": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85xzySt+lPlL7sT86WLWD2", "sync": false}]