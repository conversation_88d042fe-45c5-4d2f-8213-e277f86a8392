<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>VS_bg_blue.png</key>
            <dict>
                <key>frame</key>
                <string>{{204,2},{610,200}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{610,200}}</string>
                <key>sourceSize</key>
                <string>{610,200}</string>
            </dict>
            <key>VS_bg_red.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{612,200}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{612,200}}</string>
                <key>sourceSize</key>
                <string>{612,200}</string>
            </dict>
            <key>VS_s.png</key>
            <dict>
                <key>frame</key>
                <string>{{1808,477},{186,126}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{186,126}}</string>
                <key>sourceSize</key>
                <string>{186,126}</string>
            </dict>
            <key>VS_v.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,616},{146,156}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{146,156}}</string>
                <key>sourceSize</key>
                <string>{146,156}</string>
            </dict>
            <key>bg.front.png</key>
            <dict>
                <key>frame</key>
                <string>{{1847,287},{126,174}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{126,174}}</string>
                <key>sourceSize</key>
                <string>{126,174}</string>
            </dict>
            <key>guangxiao_ty.png</key>
            <dict>
                <key>frame</key>
                <string>{{1808,605},{128,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{128,120}}</string>
                <key>sourceSize</key>
                <string>{128,120}</string>
            </dict>
            <key>liandui_5_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{406,2},{512,256}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{512,256}}</string>
                <key>sourceSize</key>
                <string>{512,256}</string>
            </dict>
            <key>touxiangkuang.png</key>
            <dict>
                <key>frame</key>
                <string>{{1636,149},{138,170}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{138,170}}</string>
                <key>sourceSize</key>
                <string>{138,170}</string>
            </dict>
            <key>tx_guangxiao2.png</key>
            <dict>
                <key>frame</key>
                <string>{{1032,511},{226,238}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{226,238}}</string>
                <key>sourceSize</key>
                <string>{226,238}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00002.png</key>
            <dict>
                <key>frame</key>
                <string>{{406,516},{87,106}}</string>
                <key>offset</key>
                <string>{99,4}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{221,46},{87,106}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00003.png</key>
            <dict>
                <key>frame</key>
                <string>{{1808,149},{139,136}}</string>
                <key>offset</key>
                <string>{84,-7}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{180,42},{139,136}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00004.png</key>
            <dict>
                <key>frame</key>
                <string>{{150,616},{141,156}}</string>
                <key>offset</key>
                <string>{83,6}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{178,19},{141,156}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00005.png</key>
            <dict>
                <key>frame</key>
                <string>{{293,614},{163,166}}</string>
                <key>offset</key>
                <string>{80,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{164,18},{163,166}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00006.png</key>
            <dict>
                <key>frame</key>
                <string>{{461,605},{193,172}}</string>
                <key>offset</key>
                <string>{68,5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{137,12},{193,172}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00007.png</key>
            <dict>
                <key>frame</key>
                <string>{{1454,506},{209,170}}</string>
                <key>offset</key>
                <string>{61,3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{122,15},{209,170}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00008.png</key>
            <dict>
                <key>frame</key>
                <string>{{846,516},{251,184}}</string>
                <key>offset</key>
                <string>{40,3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{80,8},{251,184}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00009.png</key>
            <dict>
                <key>frame</key>
                <string>{{1568,289},{277,186}}</string>
                <key>offset</key>
                <string>{25,5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{52,5},{277,186}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00010.png</key>
            <dict>
                <key>frame</key>
                <string>{{1626,477},{281,180}}</string>
                <key>offset</key>
                <string>{18,3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{43,10},{281,180}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00011.png</key>
            <dict>
                <key>frame</key>
                <string>{{1254,2},{295,188}}</string>
                <key>offset</key>
                <string>{14,3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{32,6},{295,188}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00012.png</key>
            <dict>
                <key>frame</key>
                <string>{{664,2},{313,196}}</string>
                <key>offset</key>
                <string>{2,5}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{11,0},{313,196}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00013.png</key>
            <dict>
                <key>frame</key>
                <string>{{862,2},{307,198}}</string>
                <key>offset</key>
                <string>{-8,4}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{4,0},{307,198}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00014.png</key>
            <dict>
                <key>frame</key>
                <string>{{854,317},{303,192}}</string>
                <key>offset</key>
                <string>{-12,7}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{2,0},{303,192}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00015.png</key>
            <dict>
                <key>frame</key>
                <string>{{1062,2},{301,190}}</string>
                <key>offset</key>
                <string>{-15,8}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{301,190}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00016.png</key>
            <dict>
                <key>frame</key>
                <string>{{1444,2},{285,190}}</string>
                <key>offset</key>
                <string>{-12,8}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{11,0},{285,190}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00017.png</key>
            <dict>
                <key>frame</key>
                <string>{{1260,507},{267,192}}</string>
                <key>offset</key>
                <string>{-12,7}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{20,0},{267,192}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00018.png</key>
            <dict>
                <key>frame</key>
                <string>{{656,516},{251,188}}</string>
                <key>offset</key>
                <string>{-18,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{22,7},{251,188}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00019.png</key>
            <dict>
                <key>frame</key>
                <string>{{664,317},{197,188}}</string>
                <key>offset</key>
                <string>{-20,4}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{47,5},{197,188}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00020.png</key>
            <dict>
                <key>frame</key>
                <string>{{1159,305},{213,200}}</string>
                <key>offset</key>
                <string>{-25,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{34,2},{213,200}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00021.png</key>
            <dict>
                <key>frame</key>
                <string>{{1374,299},{205,192}}</string>
                <key>offset</key>
                <string>{-38,1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{25,6},{205,192}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00022.png</key>
            <dict>
                <key>frame</key>
                <string>{{1847,2},{145,166}}</string>
                <key>offset</key>
                <string>{-31,9}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{62,11},{145,166}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00023.png</key>
            <dict>
                <key>frame</key>
                <string>{{1636,2},{143,150}}</string>
                <key>offset</key>
                <string>{-36,3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{58,25},{143,150}}</string>
                <key>sourceSize</key>
                <string>{331,206}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>tx_haiwai_VS.png</string>
            <key>size</key>
            <string>{2015,779}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:cd8f748cb8b70f1ac0cf858247ac1bfe:6cc908459531cbd225e20d0eb56223d6:c3337b23bacc3c4a3c74fa12e6f1f315$</string>
            <key>textureFileName</key>
            <string>tx_haiwai_VS.png</string>
        </dict>
    </dict>
</plist>
