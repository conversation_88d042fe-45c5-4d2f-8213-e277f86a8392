import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import TPPBGameView from "./TPPBGameView";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class TPPBCardLayer extends BaseLayer {

    private _isShowCard: boolean = false;
    private _gameView: TPPBGameView = null;
    // 是否显示牌数
    public get isShowCard(): boolean {
        return this._isShowCard;
    }

    onLoad() {
    }
    //////////////////////////////////////////////////////////////////////////////
    // 显示扑克数据
    public showPokerData(color: number, value: number) {
        // 判断扑克花色是否正确
        if (color < 3 || color > 6) {
            console.error("TPCardLayer.setPokerData: color is invalid. color: " + color);
            return;
        }
        value = value == 14 ? 1 : value;
        // 判断扑克数字是否正确
        if (value < 1 || value > 13) {
            console.error("TPCardLayer.setPokerData: value is invalid. value: " + value);
            return;
        }

        let back = this.node.getChildByName("img_back");
        let front = this.node.getChildByName("node_font");
        let imgfront = front.getChildByName("img_font");
        let frontShadow = front.getChildByName("img_shadow");
        let frontBack = front.getChildByName("img_bg");

        let frontNum = imgfront.getChildByName("img_num");
        let frontType = imgfront.getChildByName("img_type");
        let frontLogo = imgfront.getChildByName("img_logo");
        let frontLogoSpl = imgfront.getChildByName("img_logo_spl");
        let joker = imgfront.getChildByName("joker");
        let numStr = "num_";
        let typeStr = ""
        let logoStr = ""
        if (color == 3) {           // 方块
            typeStr = "logodiamond";
            numStr += "red_"
        }
        else if (color == 4) {      // 梅花
            typeStr = "logoclub";
            numStr += "black_"
        }
        else if (color == 5) {      // 红桃
            typeStr = "logoheart";
            numStr += "red_"
        }
        else if (color == 6) {      // 黑桃
            typeStr = "logospade";
            numStr += "black_"
        }

        if (value == 1) {
            numStr += "A"
            logoStr = typeStr;
        } else if (value == 11) {
            numStr += "J"
            logoStr = "logoJ";
        } else if (value == 12) {
            numStr += "Q"
            logoStr = "logoQ";
        } else if (value == 13) {
            numStr += "K"
            logoStr = "logoK";
        } else {
            numStr = numStr + value;
            logoStr = typeStr;
        }

        this.node.active = true;
        back.active = false;
        front.active = true;
        frontBack.active = false;
        frontShadow.active = false;
        joker.active = false;
        frontLogo.active = value < 11;
        frontLogoSpl.active = value >= 11;
        this.setSpriteFrame(frontNum.getComponent(cc.Sprite), "res/textures/card/" + numStr);
        this.setSpriteFrame(frontType.getComponent(cc.Sprite), "res/textures/card/" + typeStr);
        if (value < 11) {

            this.setSpriteFrame(frontLogo.getComponent(cc.Sprite), "res/textures/card/" + logoStr);
        } else {
            this.setSpriteFrame(frontLogoSpl.getComponent(cc.Sprite), "res/textures/card/" + logoStr);
        }
        this.setSpriteFrame(imgfront.getComponent(cc.Sprite), "res/textures/card/bgfront");
        this._isShowCard = true;
    }

    // 显示扑克背面
    public showPokerBack() {
        let back = this.node.getChildByName("img_back");
        let front = this.node.getChildByName("node_font");
        let imgfront = front.getChildByName("img_font");
        let lightshang = this.node.getChildByName("lightshang");
        let lightxia = imgfront.getChildByName("lightxia");
        back.active = true;
        front.active = false;
        this._isShowCard = false;

        if(lightshang){
            lightshang.active=false;
        }
        if(lightxia){
            lightxia.active=false;
        }
    }

    // 显示扑克背面
    public showBigPokerBack() {
        let back = this.node.getChildByName("img_back");
        let front = this.node.getChildByName("node_font");
        let frontBack = front.getChildByName("img_bg");
        front.active = true;
        frontBack.active = true;
        back.active = false;
        this._isShowCard = false;
    }

    // 显示扑克灰色
    public showPokerGray(show: boolean) {
        let back = this.node.getChildByName("img_back");
        let front = this.node.getChildByName("node_font");
        let backShadow = back.getChildByName("img_shadow");
        let frontShadow = front.getChildByName("img_shadow");
        backShadow.active = show;
        frontShadow.active = show;
    }

    public showPokerFront(small:boolean=false){
        let front = this.node.getChildByName("node_font");
        front.scale=small?0.68:1;
    }
   
    public showFrontLz(){
        let front = this.node.getChildByName("node_font");
        let imgfront = front.getChildByName("img_font");
        this.setSpriteFrame(imgfront.getComponent(cc.Sprite), "res/textures/card/bgfrontLz");
    }

    //////////////////////////////////////////////////////////////////////////////

}
