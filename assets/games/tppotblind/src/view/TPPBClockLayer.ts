import BaseLayer from "../../../../script/frame/component/BaseLayer";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TPPBClockLayer extends BaseLayer {

    progressBar: cc.ProgressBar = null;

    private _totalTime: number = 0;
    private _optsumtime: number = 0;
    private _show: boolean = false;

    onLoad() {
        this.progressBar = this.getComponent(cc.ProgressBar);
    }

    update(dt: number) {
        if (this._show) {
            if (this._optsumtime > 0) {
                this._optsumtime -= dt;
                this.updateUI();
            } else {
                this.stopTime();
            }
        }
    }

    /** 
     * 倒计时
     */
    public resetUI(optsumtime: number, totalTime: number) {
        if (totalTime <= 0 || optsumtime <= 0) {
            return;
        }
        this.node.active = true;
        this._totalTime = totalTime;
        this._optsumtime = optsumtime;
        this._show = true;
        this.updateUI();
    }

    private updateUI() {
        let progress = this._optsumtime / this._totalTime;;
        this.progressBar.progress = progress;
        if(progress<0.25){
            this.setSpriteAtlas(this.progressBar.barSprite,"res/textures/tpmain","linght_red");
        }else{
            this.setSpriteAtlas(this.progressBar.barSprite,"res/textures/tpmain","linght_yellow");
        }
    }

    /**
     * 停止倒计时
     */
    public stopTime() {
        this.node.active = false;
        this._show = false;
    }

}
