import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import { GameTextTips } from "../core/TPPBDefine";
import TPPBGameCore from "../core/TPPBGameCore";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TPPBTaskRewardLayer extends BaseLayer {

    @property(cc.Prefab)
    finger: cc.Prefab = null;

    @property(cc.Prefab)
    fanpaiAni: cc.Prefab = null;

    @property(cc.Prefab)
    fanpaiDongHua: cc.Prefab = null;


    private _allButton = [];
    private _allAni = [];
    private _allTxtReward = [];
    private _allIconReward = [];

    private _gameCore: TPPBGameCore = null;
    private _fetchid = 0;
    private _clickIndex = 0;
    private _fetching = false;
    private _finish = false;
    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent('TPPBGameCore');
    }

    public show(fetchid: number) {
        this._fetchid = fetchid;
        this._fetching = false;
        this._finish = false;
        let fanpaiDonghua = this.node.getChildByName('fanpaiDonghua');
        if (!fanpaiDonghua) {
            fanpaiDonghua = cc.instantiate(this.fanpaiDongHua);
            this.node.addChild(fanpaiDonghua);
            fanpaiDonghua.name = 'fanpaiDonghua';
            fanpaiDonghua.y = 198;

            this._allAni = [];
            this._allTxtReward = [];
            this._allIconReward = [];
            this._allButton = [];

            let imgBg = fanpaiDonghua.getChildByName('imgBg');
            let bgbutton = imgBg.getComponent(ButtonLayer);
            if (!bgbutton) {
                bgbutton = imgBg.addComponent(ButtonLayer);
                let eventHandler = new cc.Component.EventHandler();
                eventHandler.component = cc.js.getClassName(this);
                eventHandler.handler = 'onClickImgBg';
                eventHandler.target = this.node;
                eventHandler.customEventData = '';
                bgbutton.clickEvents.push(eventHandler);
            }

            for (let index = 0; index < 5; index++) {
                let sort = index + 1;
                const card = fanpaiDonghua.getChildByName('imgBg' + sort);
                let button = card.getComponent(ButtonLayer);
                if (!button) {
                    button = card.addComponent(ButtonLayer);
                    let eventHandler = new cc.Component.EventHandler();
                    eventHandler.component = cc.js.getClassName(this);
                    eventHandler.handler = 'onClickCardItem';
                    eventHandler.target = this.node;
                    eventHandler.customEventData = index + '';
                    button.clickEvents.push(eventHandler);
                }
                this._allAni.push(card.getChildByName('ani' + sort));
                this._allTxtReward.push(card.getChildByName('txtReward' + sort));
                this._allIconReward.push(card.getChildByName('imgReward' + sort));
                this._allButton.push(button);
            }

            let txtClickTips = fanpaiDonghua.getChildByName('txtClickTips').getComponent(cc.Label);
            txtClickTips.string=GameTextTips.UI_TXT_CLICK_CHOOSE_REWARD;
        }
        this._allTxtReward.forEach(item => {
            item.active = false;
        });
        this._allIconReward.forEach(item => {
            item.active = false;
        });
        this._allAni.forEach(item => {
            item.removeAllChildren();
        });
        this._allButton.forEach(item => {
            this.setSpriteFrame(item.getComponent(cc.Sprite),'res/effect/img/FP_beimian');
            item.enableTouch = true;
        });
        let pAni = fanpaiDonghua.getComponent(cc.Animation);
        pAni.play('tx_haiwai_fanpai_donghua');
        this.node.active = true;
    }

    public close() {
        this.node.stopAllActions();
        this.node.active = false;
    }
    private onClickImgBg() {
        if(this._finish){
            this.close();
        }
    }

    private onClickCardItem(target, eventData) {
        if (this._fetching) return;
        this._clickIndex = Common.toInt(eventData);
        this._gameCore.callFetchGameTask(this._fetchid);
        this._fetching = true;
        this._allButton.forEach(item => {
            item.enableTouch = false;
        });
    }

    public updateAwardInfo(info) {
        this._finish = true;
        let award = info.fetchaward;
        let index = this._clickIndex;

        let fanpaiDonghua = this.node.getChildByName('fanpaiDonghua');
        let txtClickTips = fanpaiDonghua.getChildByName('txtClickTips').getComponent(cc.Label);
        txtClickTips.string=GameTextTips.UI_TXT_CLICK_TO_CLOSE;

        let awardlist = []
        for (let key in info["awardlist"]) {
            awardlist.push(info["awardlist"][key]);
        }
        let awardIndex = 1;
        for (let index = 0; index < awardlist.length; index++) {
            if (award == awardlist[index].award) {
                awardIndex = index + 1;
                awardlist[index].used = true;
            }
        }

        this._allAni[index].active = true;
        this._allTxtReward[index].active = true;
        this._allIconReward[index].active = true;
        this._allTxtReward[index].getComponent(cc.Label).string = "₹" + award;
        this.setSpriteFrame(this._allIconReward[index].getComponent(cc.Sprite), 'res/effect/img/FP_icon0' + awardIndex)


        this._allAni.forEach(item => {
            item.removeAllChildren();
        });
        let fanpaiNode = cc.instantiate(this.fanpaiAni);
        this._allAni[index].addChild(fanpaiNode);
        let pAni = fanpaiNode.getComponent(cc.Animation);
        let animState = pAni.play('tx_haiwai_fanpai');
        animState.wrapMode = cc.WrapMode.Loop;

        let findAwardList = function () {
            for (let index = 0; index < awardlist.length; index++) {
                if (!awardlist[index].used) {
                    awardlist[index].used = true;
                    return awardlist[index].award;
                }

            }
        }

        cc.tween(this.node)
            .delay(1)
            .call(() => {
                for (let tindex = 0; tindex < this._allTxtReward.length; tindex++) {
                    if (tindex != index) {
                        let value = findAwardList() || 0;
                        this._allTxtReward[tindex].active = true;
                        this._allTxtReward[tindex].getComponent(cc.Label).string = "₹" + value;

                        this._allIconReward[tindex].active = true;
                        this.setSpriteFrame(this._allIconReward[tindex].getComponent(cc.Sprite), 'res/effect/img/FP_icon0' + (tindex + 1));
                        this.setSpriteAtlas(this._allButton[tindex].node.getComponent(cc.Sprite),'res/effect/img/tx_haiwai_fanpaixiaoguo','FP_zhengmian');
                    }
                }

            })
            .start()
    }
}


