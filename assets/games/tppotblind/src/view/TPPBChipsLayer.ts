import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import { BetType, Constant } from "../core/TPPBDefine";
import TPPBAudioMng from "../core/TPPBAudioMng";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TPPBChipsLayer extends BaseLayer {

    @property(cc.Prefab)
    chipLeft: cc.Prefab = null;

    @property(cc.Prefab)
    chipRight: cc.Prefab = null;

    @property(cc.Prefab)
    coinPrefab: cc.Prefab = null;

    // 声音对象
    private _audioMng: TPPBAudioMng = null;

    private _coins = [];
    private _curCoinIndex = 0;
    private _curZorder = 1;
    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this._audioMng = cc.Canvas.instance.getComponent(TPPBAudioMng);
    }

    /** 筹码重置 */
    public resetBet() {
        this.node.removeAllChildren();
    }

    /**
     * 筹码飞动效果
     */
    public chipMove(score: number, starPos: cc.Vec3, endPos: cc.Vec3, left: boolean, callback): any {
        let chipText = Common.moneyString(score);

        // let chipNode = cc.instantiate(left ? this.chipLeft : this.chipRight)
        let chipNode = cc.instantiate(this.chipRight);
        chipNode.setPosition(starPos);
        chipNode.opacity = 255;
        this.node.addChild(chipNode);

        let chipAni = chipNode.getComponent(cc.Animation);
        
        cc.tween(chipNode)
        .delay(0.02)
        .call(function () {
            chipAni.play();
        })
        .start();

        chipAni.on(cc.Animation.EventType.FINISHED, () => {
            cc.tween(chipNode)
                .to(0.5, { position: endPos })
                .to(0.1, { opacity: 0 })
                .call(function () {
                    if (callback) {
                        callback();
                    }
                })
                .removeSelf()
                .start();

        })
        let chipValNode = chipNode.getChildByName("img_betBg").getChildByName("txt_bet");
        let valLabel = chipValNode.getComponent(cc.Label);
        valLabel.string = chipText;

        this._audioMng.playFlyGold();

    }

    public chipMoveToUser(score: number, starPos: cc.Vec3, endPos: cc.Vec3, callback): any {
        let chipText = Common.moneyString(score);

        let chipNode = cc.instantiate(this.chipRight)
        chipNode.setPosition(starPos);
        chipNode.opacity = 255;
        this.node.addChild(chipNode);

        cc.tween(chipNode)
            .hide()
            .delay(1)
            .show()
            .to(0.5, { position: endPos })
            .to(0.1, { opacity: 0 })
            .call(function () {
                if (callback) {
                    callback();
                }
            })
            .removeSelf()
            .start();
        let chipValNode = chipNode.getChildByName("img_betBg").getChildByName("txt_bet");
        let valLabel = chipValNode.getComponent(cc.Label);
        valLabel.string = chipText;

    }

    public preloadCoins() {
        this._curCoinIndex = 0;
        let cacheCount = 300;
        for (let index = 0; index < cacheCount; index++) {
            let coinNode = cc.instantiate(this.coinPrefab);
            this.node.addChild(coinNode);
            coinNode.active = false;
            this._coins[index] = coinNode;
        }
    }

    public resetCoins() {
        this._curZorder = 1;
        for (let index = 0; index < this._coins.length; index++) {
            let coinNode = this._coins[index];
            coinNode.stopAllActions();
            coinNode.active = false;
        }
    }

    public recoveryCoins(coinCount){
        for (let index = 0; index < coinCount; index++) {
            let coinNode = this.createCoin();
            coinNode.active = true;
            coinNode.zIndex = this._curZorder + 1;

            let endX = Common.random(-160, 160);
            let endY = Common.random(15, 80);
            coinNode.x = endX;
            coinNode.y = endY;
        }
    }

    public moveCoinToArea(times: number, startPos: cc.Vec3) {
        let timesIndex = Constant.DEFAULT_MULTIS.findIndex((value) => { return value == times }) || 0;
        let flyCount = [2, 4, 8, 16];
        let coinCount = flyCount[timesIndex];
        for (let index = 0; index < coinCount; index++) {
            let coinNode = this.createCoin();
            coinNode.active = true;
            coinNode.position = startPos;
            coinNode.zIndex = this._curZorder + 1;

            let endX = Common.random(-160, 160);
            let endY = Common.random(15, 80);
            let delayTime = Common.random(1, 300) / 1000;

            cc.tween(coinNode)
                .hide()
                .delay(delayTime)
                .show()
                .to(0.2, { x: endX, y: endY })
                .start();
            this._curZorder++;

        }
        this._audioMng.playFlyCoin();

    }

    public moveCoinToUser(endPos: cc.Vec3, callback = null) {
        for (let index = 0; index < this._coins.length; index++) {
            let coinNode = this._coins[index];
            if (coinNode.active) {
                let time = Common.random(2, 6) * 0.1;
                cc.tween(coinNode)
                    .delay(1.2)
                    .to(time, { position: endPos })
                    .call(() => {
                        coinNode.active = false;
                        if (callback) {
                            callback();
                        }
                    }).start();
            }
        }

        cc.tween(this.node).delay(1.2).call(() => {
            this._audioMng.playFlyCoinUser();
        }).start();
    }

    private createCoin(): cc.Node {
        if (this._curCoinIndex >= this._coins.length) {
            this._curCoinIndex = 0;
        }
        let coinNode = this._coins[this._curCoinIndex];
        coinNode.stopAllActions();
        coinNode.active = false;
        this._curCoinIndex++;
        return coinNode;
    }
}


