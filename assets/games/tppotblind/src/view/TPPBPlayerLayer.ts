import Common from "../../../../script/frame/common/Common";
import { Direction, ZOrder } from "../../../../script/frame/common/Define";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import TPPBAudioMng from "../core/TPPBAudioMng";
import { BetType, CardType, Constant, GameTextTips } from "../core/TPPBDefine";
import TPPBGameCore from "../core/TPPBGameCore";
import TPPBGameView from "./TPPBGameView";
import TPPBGameLogic from "../core/TPPBGameLogic";
import TPPBCardLayer from "./TPPBCardLayer";
import TPPBClockLayer from "./TPPBClockLayer";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class TPPBPlayerLayer extends BaseLayer {
    //////////////////////////////////////////////////////////////////////////////
    @property({ type: cc.Enum(Direction) })
    userDirection: Direction = Direction.DOWN;

    @property(cc.Node)
    userHeadNode: cc.Node = null;

    @property(cc.Node)
    headInfoNode: cc.Node = null;

    @property(cc.Sprite)
    userHeadSprite: cc.Sprite = null;

    @property(cc.Label)
    textUserMoney: cc.Label = null;

    @property(cc.Label)
    textName: cc.Label = null;

    @property(cc.Node)
    bankerFlag: cc.Node = null;

    @property(cc.Node)
    cardInfoNode: cc.Node = null;

    @property([cc.Node])
    cardNodeArr: cc.Node[] = [];

    @property(cc.Node)
    cardTypeBgNode: cc.Node = null;

    @property(cc.Node)
    cardLiziBgNode: cc.Node = null;

    @property(cc.Node)
    headClockNode: cc.Node = null;

    @property(cc.Node)
    betBgNode: cc.Node = null;

    @property(cc.Label)
    labBet: cc.Label = null;

    @property(cc.Node)
    optAniNode: cc.Node = null;

    @property(cc.Node)
    optTipsNode: cc.Node = null;

    @property(cc.Node)
    packedTipsNode: cc.Node = null;

    @property(cc.Node)
    resultNode: cc.Node = null;

    @property(cc.Node)
    pkReplay: cc.Node = null;

    @property(cc.Node)
    loseNode: cc.Node = null;

    @property(cc.Node)
    smAvatarNode: cc.Node = null;

    @property(cc.Node)
    multiAniNode: cc.Node = null;


    // 开牌的默认位置
    @property(cc.Vec2)
    allCardOpenPos: cc.Vec2[] = [];
    // 开牌的角度
    @property([cc.Float])
    allCardOpenRotation = [];

    @property(cc.Boolean)
    isMePlayer: boolean = false;

    //////////////////////////////////////////////////////////////////////////////
    // 游戏逻辑对象
    public _gameLogic: TPPBGameLogic = null;
    /** 游戏核心 */
    private _gameCore: TPPBGameCore;
    /** 游戏界面 */
    private _gameView: TPPBGameView;
    // 声音对象
    private _audioMng: TPPBAudioMng = null;
    // 用户信息
    private _userInfo: any = { playerid: 0 };
    // 扑克数据
    private _cardDatas: any = [];
    // 牌型
    private _cardType: number = 0;
    // 用户头像节点默认位置
    private _userHeadPos: cc.Vec3 = cc.v3();

    // 手牌的默认位置
    private _allCardPos: cc.Vec2[] = [];
    // 手牌的角度
    private _allCardRotation = [];

    // 玩家是否在游戏中
    private _isGaming: boolean = false;
    //弃牌
    private _isFold: boolean = false;
    // 玩家所在的本地位置
    private _localSeatId: number = -1;

    /** 头像倒计时组件 */
    private _clockLayer: TPPBClockLayer;

    /** 用户是否等待中 */
    public waitinIng: boolean;

    /** 用户是否庄家 */
    public _isBanker: boolean = false;

    // 文字聊天
    private _chatNode: cc.Node = null;
    private _expNode: cc.Node = null;

    private _nickNameBg: cc.Node = null;

    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        // 游戏逻辑对象
        this._gameLogic = cc.Canvas.instance.getComponent('TPPBGameLogic');
        this._gameCore = cc.Canvas.instance.getComponent('TPPBGameCore');
        this._gameView = cc.Canvas.instance.getComponent('TPPBGameView');
        this._audioMng = cc.Canvas.instance.getComponent('TPPBAudioMng');

        this._nickNameBg = this.node.getChildByName('di');
        this._chatNode = this.node.getChildByName('node_chat');
        this._expNode = this.node.getChildByName('node_exp');
        this._expNode.stopAllActions()
        this._expNode.active = false;

        // 获取初始用户头像及牌的位置
        this.userHeadNode.getPosition(this._userHeadPos);
        for (let i = 0; i < this.cardNodeArr.length; ++i) {
            let currPos = new cc.Vec2(0, 0);
            let cardNode = this.cardNodeArr[i];
            cardNode.getPosition(currPos);
            this._allCardPos.push(currPos);
            this._allCardRotation.push(cardNode.angle);
        }

        this._clockLayer = this.headClockNode.getComponent(TPPBClockLayer);
        if (!this._clockLayer) {
            this._clockLayer = this.headClockNode.addComponent(TPPBClockLayer);
        }
        this.headClockNode.active = false;
        this.node.active = false;
        this.reset();
    }
    onDestroy() {
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
    }
    //////////////////////////////////////////////////////////////////////////////
    // 清理数据
    public clearData() {
        this._userInfo = { playerid: 0 };
        this._isGaming = false;
        this._cardDatas = [];
        this._cardType = 0;
        this._isBanker = false;
        this._isFold = false;
    }
    // 清理数据
    public clearDataInGame() {
        this._userInfo = { playerid: 0 };
        this._isGaming = false;
    }
    /**
     * 重置玩家基础UI
     */
    public resetBase() {
        if (this._clockLayer) {
            this._clockLayer.stopTime();
            this._clockLayer.node.stopAllActions();
        }
        this.setHeadGray(false);

        // 隐藏相关节点
        this.bankerFlag.active = false;
        this.optTipsNode.active = false;

        this.optAniNode.active = false;
        this.loseNode.active = false;
        this.smAvatarNode.active = false;

        this.headInfoNode.stopAllActions();
        this.headInfoNode.scale = 1;

        this.hideMultiAni();
        this.hidePKReplay();
        this.stopLightAni();
    }

    // 重置玩家的状态
    public reset() {
        this.resetBase();
        this.cardTypeBgNode.active = false;
        this.cardLiziBgNode.active = false;
        this.cardInfoNode.active = false;
        this.cardInfoNode.stopAllActions();

        // 扑克数据
        for (let i = 0; i < this.cardNodeArr.length; ++i) {
            let cardNode = this.cardNodeArr[i];
            let cardLayer = cardNode.getComponent(TPPBCardLayer);
            cardLayer.showPokerBack();
            cardLayer.showPokerGray(false);
            cardNode.stopAllActions();
            cardNode.setPosition(this._allCardPos[i]);
            cardNode.angle = this._allCardRotation[i];
            cardNode.active = false;
        }

        this.resultNode.stopAllActions();
        this.resultNode.active = false;

        if (this.packedTipsNode) {
            this.packedTipsNode.active = false;
        }

        this._chatNode.stopAllActions()
        this._chatNode.active = false;

        this.betBgNode.active = false;

        this.unscheduleAllCallbacks();

        this._isFold = false;
    }

    public exitInGaming() {
        this.resetBase();
        this.unscheduleAllCallbacks();
        if (!this.isMePlayer) {
            this.headInfoNode.active = false;
            this._nickNameBg.active = false;

            if (this._isFold && this.packedTipsNode.active) {
                this.optTipsNode.active = true;
                this.optTipsNode.getChildByName("txt").getComponent(cc.Label).string = GameTextTips.OPT_TXT_PACKED;
                this.packedTipsNode.active = false;
            }
        }
    }

    /**获取用户信息 */
    public get getUserInfo(): any {
        return this._userInfo;
    }
    /**获取用户牌型 */
    public get getCardTypeVal(): number {
        return this._cardType;
    }

    // 玩家是否在游戏中
    public get isGaming(): boolean {
        return (this.node.active && this._isGaming);
    }

    // 玩家ID
    public get playerid(): number {
        return this._userInfo.playerid;
    }

    // 设置玩家的本地位置
    public setLocalSeatId(seatId: number) {
        this._localSeatId = seatId;
    }

    public getLocalSeatId() {
        return this._localSeatId;
    }

    // 设置玩家为游戏中
    public setGameing() {
        this._isGaming = true;
        // if (this.isMePlayer) {
        //     this.showHuanZhuo(false);
        // }
    }

    /** 显示操作界面 */
    public showAwait(isShow: boolean = true) {
        if (this._clockLayer) {
            this._clockLayer.node.active = isShow;
        }
    }
    // 更新玩家的金币
    public updateMoney(money: number) {
        if (!this.textUserMoney) return;
        this.textUserMoney.string = Common.moneyString(money);
        this._userInfo.money = money;
    }

    // 设置用户信息
    public setUserInfo(userInfo: any) {
        if (this._nickNameBg) {
            this._nickNameBg.active = true;
        }
        this.textName.string = userInfo.nickname;
        this.headInfoNode.active = true;
        this.setPlayerHead(this.userHeadSprite, userInfo.headid, userInfo.wxheadurl);
        this.updateMoney(userInfo.money);

        this._isGaming = Common.toInt(userInfo["isgaming"]) == 1;
        this._userInfo = userInfo;
        this.node.active = true;
        this.switchToGaming();
    }

    /**
     * 更新下注数据 
     * @param curbet 当前下注金额
     */
    public updateBetMoney(curbet: number) {
        this.labBet.string = Common.moneyString(curbet);
        this.betBgNode.active = true;

        let multiBet = this._gameCore.difen * Constant.SHOW_MULTI_ANI;
        if (curbet >= multiBet) {
            if (!this.multiAniNode.active) {
                this.playMultiAni();
            }
        }
    }

    // 获取头像的世界坐标
    public headWorldPos(): cc.Vec2 {
        return this.userHeadNode.convertToWorldSpaceAR(this.userHeadNode.getAnchorPoint())
    }

    public switchToGaming() {
        // if (this._isGaming) {
        // this.setNodeDarken(this.userHeadNode, false, ["BankerFlagSprite", "TipsSprite"]);
        // this.textUserMoney.node.color = new cc.Color(246, 245, 90);
        // }
        // else {
        // this.setNodeDarken(this.userHeadNode, true, ["BankerFlagSprite", "TipsSprite"]);
        // }
        let show = !this._gameCore.meIsGaming || !this._isGaming;
        if (!this.isMePlayer) {
            if (this._nickNameBg) {
                this._nickNameBg.active = !show;
            }
            this.smAvatarNode.active = show;
        }
    }
    /**
     * 切换等待用户
     * @param isShow 是否显示
     * @param showallin 是否显示allin
     * @param waittime  等待时间
     * @param optsumtime  等待总时间
     */
    public switchToAwait(isShow: boolean, waittime: number, optsumtime: number) {
        this._clockLayer.node.stopAllActions();
        if (isShow && waittime > 0) {
            if (this._clockLayer) {
                this._clockLayer.stopTime();
                this._clockLayer.resetUI(waittime, optsumtime);
            }
            this.playLightAni();

            if(this.isMePlayer&&optsumtime>6){
                cc.tween(this._clockLayer.node).delay(optsumtime-6).call(()=>{
                    this._audioMng.playNoTime();
                }).delay(3).call(()=>{
                    this._audioMng.playNoTime();
                }).start();
            }
        } else {
            if (this._clockLayer) {
                this._clockLayer.stopTime();
            }
            this.stopLightAni();
        }
    }

    private setHeadGray(show) {
        this.setNodeDarken(this.userHeadNode, show);
        this.setNodeDarken(this.headInfoNode.getChildByName('kuang'), show);
    }
    // 显示或隐藏庄家标记
    public showBankerFlag(isShow: boolean) {
        this.bankerFlag.active = isShow;
        this._isBanker = isShow;
    }

    // 显示或隐藏提示
    public showBetOptTips(name: string, animation: boolean = true) {
        let opts = this.optAniNode.children;
        this.optAniNode.active = true;
        name = 'tx_haiwai_' + name;

        for (let index = 0; index < opts.length; index++) {
            const optItem = opts[index];

            if (optItem.name == name) {
                optItem.active = true;
                let itemAni = optItem.getComponent(cc.Animation);
                let itemAniState = itemAni.play(name);
                if (!animation) {
                    itemAni.setCurrentTime(itemAniState.duration);
                }
            } else {
                optItem.active = false;
            }
        }
    }

    public hideOptTips() {
        this.optAniNode.active = false;
    }

    public hideTips() {
        this.optAniNode.active = false;
        this.optTipsNode.active = false;
        this.betBgNode.active = false;
    }

    //看牌
    public showLookTips() {
        if (!this.isMePlayer) {
            this.optTipsNode.active = true;
            this.optTipsNode.getChildByName("txt").getComponent(cc.Label).string = GameTextTips.OPT_TXT_SEEN_B;
        }
    }

    //弃牌
    public showPackedTips() {
        this._isGaming = false;
        this._isFold = true;
        if (this.isMePlayer) {
            this.optTipsNode.active = true;
            this.optTipsNode.getChildByName("txt").getComponent(cc.Label).string = GameTextTips.OPT_TXT_PACKED;
        } else {
            this.packedTipsNode.active = true;
            cc.tween(this.headInfoNode).to(0.2, { scale: 0.85 }).start();
        }
        this.setHeadGray(true);
        this.showPokersGray();
        this.hideMultiAni();
    }

    public onCompareResult(lose) {
        if (lose) {
            this._isGaming = false;
        }
    }

    //是否同意pk
    public showPKReplay(agree: boolean) {
        this.pkReplay.active = true;
        this.pkReplay.getChildByName("agree").active = agree;
        this.pkReplay.getChildByName("refuse").active = !agree;
        cc.tween(this.pkReplay)
            .show()
            .delay(3)
            .hide()
            .start();
    }

    public hidePKReplay() {
        this.pkReplay.stopAllActions();
        this.pkReplay.active = false;
    }

    public showLose() {
        this.loseNode.active = true;
        this._isGaming = false;
        this.setHeadGray(true);
        this.showPokersGray();
        this.hideMultiAni();
        if (!this.isMePlayer) {
            cc.tween(this.headInfoNode).to(0.2, { scale: 0.85 }).start();
        }
    }
    //
    public showWaitTip(isShow: boolean) {
        this.waitinIng = isShow;
        // this.tipsSprite.node.active = isShow;
    }

    public playLightAni() {
        if (this.isMePlayer) {
            let light = this.node.getChildByName('bg').getChildByName('light');
            this.stopLightAni();
            cc.tween(light)
                .repeatForever(
                    cc.tween().to(1, { opacity: 255 })
                        .to(1, { opacity: 0 })
                )
                .start();
        }
    }

    public stopLightAni() {
        if (this.isMePlayer) {
            let light = this.node.getChildByName('bg').getChildByName('light');
            light.stopAllActions();
            light.opacity = 0;
        }
    }


    public showChat(type, content) {
        let self = this;
        if (type == 'text') {
            this._chatNode.active = true;
            this._chatNode.getChildByName('chat_text').getComponent(cc.Label).string = content;
            this._chatNode.stopAllActions();
            cc.tween(this._chatNode)
                .delay(3)
                .call(function () {
                    if (cc.isValid(self._chatNode)) {
                        self._chatNode.active = false;
                    }
                })
                .start();
        } else if (type == 'meexpression') {
            if (!content) return;
            this._expNode.active = true;
            this._expNode.stopAllActions();
            let spExp = this._expNode.getChildByName('bq').getComponent(sp.Skeleton);
            spExp.setAnimation(0, content, true);
            cc.tween(this._expNode)
                .delay(3)
                .call(function () {
                    if (cc.isValid(self._expNode)) {
                        self._expNode.active = false;
                    }
                })
                .start();
        }
    }

    // 设置扑克数据
    public setPlayerPokerData(cards: any, emtype: number) {
        this._cardDatas = [];
        if (cards ){
            for (let index=0;index<cards.length;index++) {
                this._cardDatas.push(cards[index]);
            }
        }

        // if (cards && typeof (cards) == "object") {
        //     for (let key in cards) {
        //         this._cardDatas.push(cards[key]);
        //     }
        // }
        // if (!this.isMePlayer) {
        //     this._gameLogic.sortCardData(this._cardDatas);
        // }
        this._cardType = emtype;
    }

    // 显示玩家扑克
    public showAllPokers() {
        let cards = this.cardNodeArr.length;
        this.cardInfoNode.active = true;
        for (let i = 0; i < cards; ++i) {
            let cardNode = this.cardNodeArr[i];
            let cardLayer = cardNode.getComponent(TPPBCardLayer);
            cardNode.active = true;
            cardLayer.showPokerBack();
            if (!cardLayer.isShowCard) {
                this.showPokerData(cardLayer, i);
                cardLayer.showPokerFront(!this.isMePlayer);
            }
        }
    }
    // 显示或隐藏提示
    public showCardType(isShow: boolean, isWin: boolean = false) {
        if (!isShow) {
            this.cardTypeBgNode.active = false;
            return;
        }
        let pointStr = this._gameCore.getCardtypeText(this._cardType);
        this.cardTypeBgNode.active = true;
        this.cardTypeBgNode.getChildByName("cardType").getComponent(cc.Label).string = pointStr;

        if (isWin) {
            this._audioMng.playCardType(this._cardType);
        }
    }


    public showPokerBack(index) {
        this.cardInfoNode.active = true;
        let cardNode = this.cardNodeArr[index];
        let cardLayer = cardNode.getComponent(TPPBCardLayer);
        if (cardLayer) {
            cardNode.active = true;
            cardLayer.showPokerBack();
        }
    }

    public showPokersGray() {
        let cards = this.cardNodeArr.length;
        for (let i = 0; i < cards; ++i) {
            let cardNode = this.cardNodeArr[i];
            let cardLayer = cardNode.getComponent(TPPBCardLayer);
            if (cardLayer) {
                cardNode.active = true;
                cardLayer.showPokerGray(true);
            }
        }
    }

    // 显示扑克数据
    private showPokerData(cardLayer: any, index: number) {
        let color = 0, value = 0;
        if (index < this._cardDatas.length) {
            let card = this._cardDatas[index];
            color = Common.toInt(card["color"]);
            value = Common.toInt(card["number"]);
        }
        if (color > 0 && value > 0) {
            cardLayer.showPokerData(color, value);
        }
    }


    //////////////////////////////////////////////////////////////////////////////

    // 播放翻牌动画
    public playOpenCardAction(animation:boolean=true) {
        let self = this;
        this.cardInfoNode.active = true;
        for (let index = 0; index <this._cardDatas.length; index++) {
            let card = this._cardDatas[index];
            if(card["number"]!=0){
                let cardNode = this.cardNodeArr[index];
                let cardLayer = cardNode.getComponent(TPPBCardLayer);
                cardNode.active = true;
                cardLayer.showPokerBack();
        
                if (animation) {
                    cardNode.is3DNode = true;
                    let angles1 = cardNode.eulerAngles.clone();
                    angles1.y = 90;
                    let angles2 = cardNode.eulerAngles.clone();
                    angles2.y = 0;
        
                    cc.tween(cardNode)
                        .call(() => { })
                        .to(0.2, { eulerAngles: angles1 })
                        .call(() => {
                            self.showPokerData(cardLayer, index);
                            cardLayer.showPokerFront(!this.isMePlayer);
                            if(!self.isGaming){
                                self.showPokersGray();
                            }
                        })
                        .to(0.2, { eulerAngles: angles2 })
                        .start();
                } else {
                    self.showPokerData(cardLayer, index);
                }
        
            }
            
        }

    }

    // 播放翻牌动画
    public playResultOpenCardAction(isWin,animation:boolean=true) {
        let self = this;
        let cards = this.cardNodeArr.length;
        this.cardInfoNode.active = true;

        for (let i = 0; i < cards; ++i) {
            let cardNode = this.cardNodeArr[i];
            let cardLayer = cardNode.getComponent(TPPBCardLayer);
            cardNode.active = true;
            cardNode.setPosition(this.allCardOpenPos[i]);
            cardNode.angle = this.allCardOpenRotation[i];
            cardLayer.showBigPokerBack();

            if (animation) {
                cardNode.is3DNode = true;
                let angles1 = cardNode.eulerAngles.clone();
                angles1.y = 90;
                let angles2 = cardNode.eulerAngles.clone();
                angles2.y = 0;

                cc.tween(cardNode)
                    .call(() => { })
                    .to(0.2, { eulerAngles: angles1 })
                    .call(() => {
                        self.showPokerData(cardLayer, i);
                        cardLayer.showPokerFront();
                        if(isWin){
                            cardLayer.showFrontLz();
                        }
                        if(!self.isGaming){
                            self.showPokersGray();
                        }
                    })
                    .to(0.2, { eulerAngles: angles2 })
                    .call(() => {
                        if (i >= cards - 1) {
                            self.showCardType(true,isWin);
                        }
                    })
                    .start();
            } else {
                self.showPokerData(cardLayer, i);
                cardLayer.showPokerFront();
                self.showCardType(true,isWin);
            }
        }

    }

    public playSelfOpenCardAction() {
        let self = this;
        let cards = this.cardNodeArr.length;
        this.cardInfoNode.active = true;
        for (let i = 0; i < cards; ++i) {
            let cardNode = this.cardNodeArr[i];
            cardNode.active = true;
            let cardLayer = cardNode.getComponent(TPPBCardLayer);
            if (!cardLayer.isShowCard) {
                cc.tween(cardNode)
                    .delay(0.1 * i)
                    .by(0.4, { angle: -360 })
                    .start();

                cc.tween(cardNode)
                    .delay(0.1 * i + 0.4)
                    .call(() => {
                        self.showPokerData(cardLayer, i);
                    })
                    .start();
            } else {
                self.showPokerData(cardLayer, i);
            }
        }
    }

    public playMultiAni() {
        if (this.multiAniNode.active) return;
        let self = this;
        this.multiAniNode.active = true;
        this.loadAsset("res/effect/tx_haiwai_touxiangkuang2", cc.Prefab, function (assets: cc.Prefab) {
            let fireNode = cc.instantiate(assets);
            self.multiAniNode.addChild(fireNode);

            let pAni = fireNode.getComponent(cc.Animation);
            let animState = pAni.play("tx_haiwai_touxiangkuang2");
            animState.wrapMode = cc.WrapMode.Loop;
        })
    }

    public hideMultiAni() {
        this.multiAniNode.removeAllChildren();
        this.multiAniNode.active = false;
    }
    //分数动画
    public playScoreChange(score: number) {
        let strMoney = Common.moneyString(score);
        if (score >= 0) {
            strMoney = "+" + strMoney;
        }
        let labScore = this.resultNode.getChildByName("num").getComponent(cc.Label);
        labScore.string = strMoney;

        let resultAni = this.resultNode.getComponent(cc.Animation);
        resultAni.play('tx_floating_num');
        this.resultNode.active = true;
    }

    //////////////////////////////////////////////////////////////////////////////
    // 点击玩家头像
    private onClickHead() {
        let self = this;
        if(!this.playerid)return;
        if (!this.isMePlayer) {
            let emojiNode = this._gameView.gameEmojiNode;
            let showPos = emojiNode.convertToNodeSpaceAR(this.headWorldPos());

            if (this.userDirection == Direction.LEFT) {
                showPos.x += 350;
            }
            else if (this.userDirection == Direction.RIGHT) {
                showPos.x -= 350;
            } else if (this.userDirection == Direction.UP) {
                showPos.y -= 100;
            } else if (this.userDirection == Direction.DOWN) {
                showPos.y += 150;
            }
            let playerInfo = { sendPlayerid: this._gameCore.playerid, receiverPlayerid: this._userInfo.playerid ,
                singlechatfee:this._gameCore.singlechatfee};
            UIHelper.showInteractExpression(emojiNode, showPos, playerInfo, (info: any) => {
                self._gameCore.sendChatMessage(info);
            });
        } else {
            this._gameView.openMeChat();
        }
    }

    //////////////////////////////////////////////////////////////////////////////

}

