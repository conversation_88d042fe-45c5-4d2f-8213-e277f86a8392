import { Constant, Sounds,  FREE_TYPE, GameTextTips, GameEvent, ICON_TYPE, JACKPOT_TYPE } from "../core/SLOTCATDefine";
import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import SLOTCATLotteryBox from "./SLOTCATLotteryBox";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import HallManager from "../../../../script/frame/manager/HallManager";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import SLOTCATGameCore, { DeskPlayersInfo, ScrollNumberParam, SpinStateFruitMachine, TaskInfo } from "../core/SLOTCATGameCore";
import Config from "../../../../script/frame/config/Config";
import SLOTCATGameLogic from "../core/SLOTCATGameLogic";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import EventManager from "../../../../script/frame/manager/EventManager";
import DataManager from "../../../../script/frame/manager/DataManager";
import { Direction, quickPayStyle } from "../../../../script/frame/common/Define";
import UIHelper from "../../../../script/frame/extentions/UIHelper";


//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slotcat/SLOTCATGameView')
export default class SLOTCATGameView extends BaseLayer {

    //规则帮助界面
    @property(cc.Prefab)
    helpPrefab: cc.Prefab = null;

    //彩金记录界面
    @property(cc.Prefab)
    jackpotRecordPrefab: cc.Prefab = null;

    //图标
    @property(cc.Prefab)
    item_icon: cc.Prefab = null;

    //龙
    @property(cc.Prefab)
    longPrafab: cc.Prefab = null;

    //免费次数界面
    @property(cc.Prefab)
    freetimesPrefab: cc.Prefab = null;

    //jackpot得分界面
    @property(cc.Prefab)
    jackpotPrefab: cc.Prefab = null;

    //任务界面
    @property(cc.Prefab)
    taskPrefab: cc.Prefab = null;

    //bigwin界面
    @property(cc.Prefab)
    bigwinPrefab: cc.Prefab = null;

    //epicwin界面
    @property(cc.Prefab)
    epicwinPrefab: cc.Prefab = null;

    //megawin界面
    @property(cc.Prefab)
    megawinPrefab: cc.Prefab = null;

    //superwin界面
    @property(cc.Prefab)
    superwinPrefab: cc.Prefab = null;

    //totalwin界面
    @property(cc.Prefab)
    totalwinPrefab: cc.Prefab = null;
    
     //桌上其它玩家win动画
     @property(cc.Prefab)
     otherWin_jackpot: cc.Prefab = null;
 
     //桌上其它玩家win动画
     @property(cc.Prefab)
     otherWin_mega: cc.Prefab = null;
 
     //桌上其它玩家win动画
     @property(cc.Prefab)
     otherWin_super: cc.Prefab = null;

    //主资源
    @property(cc.SpriteAtlas)
    mainUiAtlas: cc.SpriteAtlas = null;

    //jackpot奖池数组
    @property([cc.Node])
    jackpotNumList: cc.Node[] = [];

    //jackpot奖池其它玩家数组
    @property([cc.Node])
    jackpotNodeInfoList: cc.Node[] = [];

    // 游戏核心对象
    _gameCore: SLOTCATGameCore = null;
    //点击
    _bDoTouch: boolean = false;

    // lotterBox对象
    _lotteryBox: SLOTCATLotteryBox = null;
    _gameLogic: SLOTCATGameLogic = null;

    helpNode: cc.Node = null; //帮助界面
    jackpotRecordLayer : cc.Node = null;//jackpot记录
    jackpotHelpNode: cc.Node = null; //帮助界面

    //当前点击的头像
    _curClickHeadIndex: number = -1;
    
    onLoad() {

        //动态导入所有节点
        this.LoadAllLayerObject(this.node)

        this._gameCore = this.node.getComponent("SLOTCATGameCore");
        this._lotteryBox = this.node.getComponent("SLOTCATLotteryBox");
        this._gameLogic = this.node.getComponent("SLOTCATGameLogic");
        
        this.initSpinTouch();
        this.initSpinAnim();
        this.initDeskPlayersInfo();
        this.initTaskInfo();
    }

    //初始化任务信息
    initTaskInfo(){
        let taskInfo = <TaskInfo>{};

        taskInfo.node = this.LayerItems.Node_task_progress;
        taskInfo.loadingBar = this.LayerItems.LoadingBar_task;
        taskInfo.loadingBarDesc = this.LayerItems.Node_task_progress_mark;
        taskInfo.txt_progress = this.LayerItems.txt_progress.getComponent(cc.Label);
        taskInfo.infoNode = this.LayerItems.Node_task_ino;
        taskInfo.tipsAnimNode = this.LayerItems.FileNode_task_info;
        taskInfo.tipsCollectionNode = this.LayerItems.Node_task_open;
        taskInfo.tipsListviewNode = this.LayerItems.ListView_task;
        taskInfo.tipsItem = this.LayerItems.Panel_task_item;
        taskInfo.tipsIconSp = this.LayerItems.Image_task_icon.getComponent(cc.Sprite);
        taskInfo.tipsNum = this.LayerItems.Text_task_open_num.getComponent(cc.Label);
        taskInfo.tipsLockText = this.LayerItems.Text_task_lock_tip.getComponent(cc.Label);
        taskInfo.lightNode = this.LayerItems.Node_light;
        taskInfo.collectTextNode = taskInfo.infoNode.getChildByName('Node3'); 
        taskInfo.collectIconNode = this.LayerItems.Node_task_reward;
        taskInfo.collectIconSp = this.LayerItems.Image_task_reward.getComponent(cc.Sprite);
        taskInfo.collectMoney = this.LayerItems.Text_task.getComponent(cc.Label);
        taskInfo.nowNode = this.LayerItems.Node_task_now;
        taskInfo.nowNodeIcon = this.LayerItems.Image_task_now.getComponent(cc.Sprite);
        taskInfo.nowNodeNum = this.LayerItems.Text_task_now.getComponent(cc.Label);
        taskInfo.lockNode = this.LayerItems.Node_task_lock;
        taskInfo.lockSpNode = this.LayerItems.Image_task_lock;

        this._gameCore.taskInfo = taskInfo;
    }

    //添加计时 多少分钟不操作下注 则退出到大厅
    runNotBetToQuick() {
        let self = this;
        this.LayerItems.timeOutRunNode.stopAllActions();
        cc.tween(this.LayerItems.timeOutRunNode)
        .delay(60 * 10) //10分钟
        .call(()=>{
            self.LayerItems.timeOutRunNode.stopAllActions();
            let info = {reason:3};//超时未操作被踢
            self._gameCore.quitGame(info);
        })
        .start()
    }

    //加载场景动画
    loadingSceneAni(callback = null){
        let delayTime = 0.25;
        let bgList = []
        let self = this;
        this.LayerItems.loadLayer.active = true;
        this.LayerItems.loadLayer.children.forEach((item,index,arr)=>{
            let iSize = item.getContentSize();
            if(cc.winSize.height > iSize.height){
                iSize.width = cc.winSize.height / iSize.height * iSize.width;
                iSize.height = cc.winSize.height;
            }
            if(cc.winSize.width / arr.length > iSize.width){
                iSize.width = cc.winSize.width / arr.length;
            }
            bgList.push(item);
            item.active = true;
            item.setContentSize(iSize);
            item.x = -2 * (cc.winSize.width / arr.length)  + (cc.winSize.width / arr.length) * index;
            item.stopAllActions();
            let movePosY = cc.winSize.height - cc.winSize.height/4;
            index%2 == 0 && (movePosY = 0 - (cc.winSize.height - cc.winSize.height/4));
            item.y = movePosY;
            let delaytime1 = 0;
            if(index == arr.length - 1){
                delaytime1 = 1.25;
            }
            cc.tween(item)
            .to(delayTime,{position: cc.v2(item.x,0)})
            .delay(delaytime1)
            .call(()=>{
                if(delaytime1 > 0){
                    for(let i = 0;i < bgList.length;i++){
                        let item1 = bgList[i];
                        let movePosY1 = cc.winSize.height;
                        i%2 == 0 && (movePosY1 = 0 - cc.winSize.height);
                        item1.stopAllActions();
                        cc.tween(item1)
                        .to(delayTime,{position: cc.v2(item1.x,movePosY1)})
                        .call(()=>{
                            if(i == bgList.length - 1){
                                self.LayerItems.loadLayer.active = false;
                                callback && callback();
                            }
                        })
                        .start();
                    }
                }                
            })
           .start()  

        })
    }

    /**初始化按钮特效 */
    initSpinAnim() {
        let self = this;
        let bShowSpin = true;
        this.nodeRunActionRepeatForever(this.LayerItems.btn_spin, () => {
            self.LayerItems.btn_spin.getChildByName('Image_spin_txt_1').active = bShowSpin;
            self.LayerItems.btn_spin.getChildByName('Image_spin_txt_2').active = !bShowSpin;
            if (!bShowSpin) {
                let ani = self.LayerItems.btn_spin.getChildByName('Image_spin_txt_2').getComponent(cc.Animation);
                ani.play();
            }
            bShowSpin = !bShowSpin;
        }, 3.0, true)
    }

    start() {
        AudioHelper.instance.playMusic(Sounds.BG_MUSIC);
        EventManager.instance.on(GameEvent.POP_JACKPOT_RECORD_LAYER, this.onClickJackpotRecord, this);
        EventManager.instance.on(GameEvent.FREE_TOTAL_CONFIM_CLOSE, this.onClickTotalConfimBtn, this);
        EventManager.instance.on(GameEvent.FREE_CONFIM_CLOSE, this.onClickFreeConfimBtn, this);
        this.LayerItems.Image_coin_bg.x += (cc.winSize.width/cc.winSize.height >= 2) ? 90 : 0;
        this.loadingSceneAni();
    }

    //初始化桌上玩家信息
    initDeskPlayersInfo(){
        for(let i = 0;i < Constant.DESK_PLAYERS_MAX_NUM;i++){
            let deskPlayerInfo = <DeskPlayersInfo>{};
            deskPlayerInfo.node = this.LayerItems[`FileNode_player_${i}`];
            deskPlayerInfo.infoBgNode = deskPlayerInfo.node.getChildByName('Image_info_bg');
            deskPlayerInfo.nameNode = deskPlayerInfo.infoBgNode.getChildByName('Text_player_name');
            deskPlayerInfo.goldNode = deskPlayerInfo.infoBgNode.getChildByName('Text_player_coin');
            deskPlayerInfo.headBgNode = deskPlayerInfo.node.getChildByName('Image_head_bg'); 
            deskPlayerInfo.headNode = deskPlayerInfo.headBgNode.getChildByName('Image_head');
            deskPlayerInfo.headSpinAniNode = deskPlayerInfo.node.getChildByName('headSpinAni');
            deskPlayerInfo.aniNode = deskPlayerInfo.node.getChildByName('aniNode');
            deskPlayerInfo.winNode = deskPlayerInfo.node.getChildByName('Node_win');
            deskPlayerInfo.winBgNode = deskPlayerInfo.winNode.getChildByName('Image_win_bg');
            deskPlayerInfo.winOrLoseNumNode = deskPlayerInfo.winNode.getChildByName('winOrLoseNum');
            deskPlayerInfo.node.active = false;
            this._gameCore.deskPlayerInfoList.push(deskPlayerInfo);
        }
        
    }

    //对象运行动作单次
    nodeRunActionOncs(node: cc.Node, callback: Function, time: number, bImmediately?: boolean, bNotStopAction?: boolean) {
        if (!callback || !time) {
            return null;
        }

        if (Common.isNull(bNotStopAction) || !bNotStopAction) {
            node.stopAllActions();
        }
        //是否立即执行
        bImmediately && callback(bImmediately);
        if (!!node) {
            cc.tween(node)
                .delay(time)
                .call(() => {
                    !!node && (callback(false));
                })
                .start();
        }
    }

    //对象运行动作循环
    nodeRunActionRepeatForever(node: cc.Node, callback: Function, time: number, bImmediately?: boolean) {
        if (!callback || !time) {
            return null;
        }
        //是否立即执行
        bImmediately && callback(bImmediately);
        node.stopAllActions();
        if (!!node) {
            cc.tween(node)
                .repeatForever(
                    cc.tween()
                        .delay(time)
                        .call(() => {
                            !!node && (callback(false));
                        })
                )
                .start();
        }
    }

    /**Label滚动上涨 */
    scrollNumber(data: ScrollNumberParam) {
        if (data.began == data.end || data.end == 0) {
            if (data.format) {
                data.format(data.end, data);
            } else {
                data.txt.string = `${data.end}`;
            }
            data.callback && data.callback(data);
            return;
        }
        //刷新定时器
        let nCur = 0;
        let nMax = data.nCount ?? 10;
        let func = (nInterval: number) => {
            this.nodeRunActionOncs(data.txt.node, (bImmediately: boolean) => {
                let nValue = data.began + (data.end - data.began) * nCur / nMax;
                //避免精度问题
                nValue = Number((nValue + 0.000001).toFixed(2));
                if (data.format) {
                    data.format(nValue, data);
                } else {
                    data.txt.string = `${nValue}`;
                }
                if (++nCur > nMax) {
                    data.callback && data.callback(data);
                } else {
                    !bImmediately && func(nInterval);
                }
            }, nInterval, nCur == 0, data.bNotStopAction);
        }
        // data.txt.node.active = false;
        this.nodeRunActionOncs(data.txt.node, () => {
            // data.txt.node.active = true;
            if (!data.nInterval) {
                func((data.nTime ?? 1.0) / nMax);
            } else {
                func(data.nInterval);
            }
        }, data.nDelay ?? 0.001, null, data.bNotStopAction);
    }

    //检测余额是否够下注
    checkMoney() {
        
        if(DataManager.instance.isNeedPay(Config.GAME_LIST.slotcat.gameId)){//没有充值，提示充值
            HallManager.instance.openCharge({quickPayStyle:quickPayStyle.gameVipPay});
            this.setSpinAutoState(false);
            return;
        }

        if(this._gameCore.playScoreMoney != this._gameCore.myselfMoney){
            console.error('====当前余额不一致=======',this._gameCore.playScoreMoney,this._gameCore.myselfMoney)
            this._gameCore.playScoreMoney = this._gameCore.myselfMoney;
            this.showSelfMoney();
        }

        if (this._gameCore.playScoreMoney < this._gameCore.betMoney) {
            this.setSpinAutoState(false);
            // ToastHelper.show(GameTextTips.GOLD_BET_ERR);
            HallManager.instance.openCharge({quickPayStyle:quickPayStyle.gameQuickPay});
            return false;
        }
        return true;
    }

    /**赢金数值检测隐藏 */
    checkWinLabelHide() {
        //非free状态下才需处理
        if (this._gameCore.nSpinState != this._gameCore.SpinState.Free) {
            this.LayerItems.Node_win_normal.active = false;
            this.LayerItems.Node_win_good_luck.active = true;
        }
    }

    /**设置按钮自动模式状态 */
    setSpinAutoState(nState: boolean) {
        this._gameCore.bAutoSpin = nState;
        this.LayerItems.btn_spin.active = !this._gameCore.bAutoSpin;
        this.LayerItems.btn_auto.active = this._gameCore.bAutoSpin;
    }

    /**按钮触摸 */
    initSpinTouch() {
        let touchNode = this.LayerItems.Panel_spin_touch;
        touchNode.on(cc.Node.EventType.TOUCH_START, this.onTouchStart.bind(this));
        touchNode.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove.bind(this));
        touchNode.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd.bind(this));
        touchNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel.bind(this));
    }


    /**摇奖 */
    onClickSpin() {
        //音效
        AudioHelper.instance.playEffect(Sounds.SPIN);

        //游戏进行中
        if (!this._gameCore.bFruitIsStop) {
            return;
        }

        if (!this.checkMoney()) {
            return;
        }

        //发送开始
        this._gameCore.sendGameStart();
        //测试用
        // let info = {
        //     freetimes:0,
        //     jackpotnum: 1,
        //     iconresult: {
        //          '1':  4, '2': 6,  '3': 7,  '4': 0, '5':  9, 
        //          '6':  9,  '7': 1,  '8': 5,  '9': 10,  '10': 0, 
        //         '11':  9, '12': 4, '13': 2, '14': 0,  '15': 0},
        //     linecount:3,
        //     lineresult:[
        //         {line:7,num:4},
        //         {line:8,num:4},
        //         {line:9,num:5},
        //     ],
        //     winmoney: 0,
        //     totalmult: 18,
        //     changemoney:165,
        //     luckyjackpot: 0,
        // };
        
        // this._gameCore.onStartGame(info);            
        
    }


    /**触摸开始 */
    onTouchStart(touch: cc.Touch) {
        // if (!this._gameCore.bFruitIsStop) {
        //     return;
        // }
        this._bDoTouch = false;
        let touchNode = this.LayerItems.Panel_spin_touch;
        clearTimeout((<any>this.LayerItems.Panel_spin_touch).spineTouchTimer);
        (<any>touchNode).spineTouchTimer = setTimeout(() => {
            if (!this._bDoTouch && this._gameCore.nSpinState != this._gameCore.SpinState.Free) {//this._gameCore.bFruitIsStop && 
                this._bDoTouch = true;
                clearTimeout((<any>touchNode).spineTouchTimer);
                this.setSpinAutoState(!this._gameCore.bAutoSpin);
            }
        }, 500);

        touchNode.getParent().setScale(1.1);
        
    }
    /**触摸移动 */
    onTouchMove(touch: cc.Touch) {
        //TODO
    }
    /**触摸结束 */
    onTouchEnd(touch: cc.Touch) {
        clearTimeout((<any>this.LayerItems.Panel_spin_touch).spineTouchTimer);
        this.LayerItems.Panel_spin_touch.getParent().setScale(1);
        if (this._gameCore.nSpinState != this._gameCore.SpinState.Free) {
            if (this._bDoTouch) {
                this._bDoTouch = false;
            }
            else {
                this.setSpinAutoState(false);
            }

            if (this._gameCore.bFruitIsStop) {
                this.onClickSpin();
            }
        } 
    }
    /**触摸取消 */
    onTouchCancel(touch: cc.Touch) {
        if(this._bDoTouch){
            clearTimeout((<any>this.LayerItems.Panel_spin_touch).spineTouchTimer);
            this._bDoTouch = false;
            if (this._gameCore.bFruitIsStop) {
                this.onClickSpin();
            }
        } 
    }

    /**刷新按钮状态 */
    updateSpinState(nSpinState: number, bReplayBgMusic: boolean = true) {
        //状态相同不处理
        if (this._gameCore.nSpinState == nSpinState) {
            return;
        }

        //刷新按钮状态
        this._gameCore.nSpinState = nSpinState;
        
        this.LayerItems.btn_spin.active = false;
        this.LayerItems.btn_free.active = false;
        this.LayerItems.btn_auto.active = false;
        this.LayerItems.Panel_free.active = false;

        switch (this._gameCore.nSpinState) {
            case SpinStateFruitMachine.None:
                break;
            case SpinStateFruitMachine.Normal:
                this.changeAddOrSubBtnColor();
                this.LayerItems.freeAni.active = false;
                this.LayerItems.Image_bg_free.active = false;
                this.LayerItems.btn_spin.active = true;
                bReplayBgMusic && AudioHelper.instance.playMusic(Sounds.BG_MUSIC);
                this.LayerItems.blockInput.active = false;
                if (this._gameCore.lastAutoSpinState) {
                    this._gameCore.lastAutoSpinState = false;
                    this.setSpinAutoState(true);
                    let self = this;
                    this.scheduleOnce(() => {
                        self.onClickSpin();
                    }, 1.5)
                }
                break;
            case SpinStateFruitMachine.Free:
                this.changeAddOrSubBtnColor();
                bReplayBgMusic && AudioHelper.instance.playMusic("res/sounds/bg_spceail");
                this.LayerItems.btn_free.active = true;
                this.LayerItems.Free_Text.getComponent(cc.Label).string = this._gameCore.resultData.freetimes + '';
                this.LayerItems.blockInput.active = true;
                //添加免费特效
                this.addFreeAnim();
                break;
            default:
                break;
        }
    }

    /**刷新Free按钮次数 */
    updateFreeTimes() {
        this.LayerItems.Free_Text.getComponent(cc.Label).string = `${this._gameCore.nLastFreeTimes}`;
    }

    /**添加免费特效 */
    addFreeAnim() {
        this.LayerItems.Image_bg_free.active = true;
        this.LayerItems.freeAni.active = true;
    }

    onDestroy() {
        this.LayerItems.timeOutRunNode.stopAllActions();
        EventManager.instance.off(GameEvent.POP_JACKPOT_RECORD_LAYER, this.onClickJackpotRecord, this);
        EventManager.instance.off(GameEvent.FREE_TOTAL_CONFIM_CLOSE, this.onClickTotalConfimBtn, this);
        EventManager.instance.off(GameEvent.FREE_CONFIM_CLOSE, this.onClickFreeConfimBtn, this);
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
    }

    //显示当前选定下注金额
    showBetConfig(showTips: boolean = false) {
        this.LayerItems.bet_num.getComponent(cc.Label).string = `${this._gameCore.betMoney / Config.SCORE_RATE}`;
        this.LayerItems.betNumDesc.getComponent(cc.Label).string = `${(this._gameCore.betMoney / Config.SCORE_RATE / Constant.BET_RATE_NUM).toFixed(2)} x ${Constant.BET_RATE_NUM}`;
        // cc.sys.localStorage.setItem(Constant.LOCAL_STORAGE_BET_NUM_KEY, this._gameCore.betMoney / Config.SCORE_RATE); //保存到本地
        
        if(!showTips && this._gameCore.meAddBeiLv >= 3){
            this.LayerItems.jackpot_tips_bg.opacity = 0;
            this.LayerItems.jackpot_bet_tips1.active = false;
            this.LayerItems.jackpot_bet_tips2.active = true;
            this.LayerItems.Node_lock1.active = false;
            this.LayerItems.Node_lock2.active = false;
            this.LayerItems.Node_lock3.active = false;
            return;
        }

        //显示jackpot提示
        let self = this;
        if(self._gameCore.meAddBeiLv == 2 || self._gameCore.meAddBeiLv == 0 || self._gameCore.meAddBeiLv == 3 || self._gameCore.meAddBeiLv == self._gameCore.bets.length - 1){
            this.LayerItems.jackpot_tips_bg.stopAllActions();
            cc.tween(this.LayerItems.jackpot_tips_bg)
            .call(()=>{
                if((self._gameCore.meAddBeiLv == 2 || self._gameCore.meAddBeiLv == 0) && !self.LayerItems.jackpot_bet_tips1.active){
                    self.LayerItems.jackpot_tips_bg.opacity = 255;
                    self.LayerItems.jackpot_bet_tips1.active = true;
                    self.LayerItems.jackpot_bet_tips2.active = false;
                    self.LayerItems.Node_lock1.active = true;
                    self.LayerItems.Node_lock2.active = true;
                    self.LayerItems.Node_lock3.active = true;
                }    
                if((self._gameCore.meAddBeiLv == 3 || self._gameCore.meAddBeiLv == self._gameCore.bets.length - 1) && !self.LayerItems.jackpot_bet_tips2.active){
                    self.LayerItems.jackpot_tips_bg.opacity = 255;
                    self.LayerItems.jackpot_bet_tips1.active = false;
                    self.LayerItems.jackpot_bet_tips2.active = true;
                    self.LayerItems.Node_lock1.active = false;
                    self.LayerItems.Node_lock2.active = false;
                    self.LayerItems.Node_lock3.active = false;
                } 
            })
            .delay(5.0)
            .call(()=>{
                cc.tween(self.LayerItems.jackpot_tips_bg).to(1.0,{opacity:0}).start();
            })
            .start();
        }
    }

    //更新自己身上金额
    showSelfMoney() {
        this.LayerItems.scoreMoney.getComponent(cc.Label).string = this.moneyFormat(this._gameCore.playScoreMoney,2);
    }

    // 获取金币字符串 count: 0不加K; 1-n: 超过指定位数时加上K.  decimals: 显示小数位数
    moneyFormat(money: number, decimals: number = 0): string {
        return (money / Config.SCORE_RATE).toFixed(decimals);
    }

    //刷新当前奖池金额
    updateJackpotNum(jackpotnum?: number,jackpotType?: number) {

        if (Common.isNull(jackpotnum)) {
            return;
        }

        this._gameCore.jackpotnum[jackpotType] = jackpotnum;

        let self = this;
        let allJackpotNumLabel = self.jackpotNumList[jackpotType].getComponent(cc.Label)

        if (!this._gameCore.nOldJackpotNum[jackpotType]) {
            allJackpotNumLabel.string = `$${this.formatNumberForComma(this._gameCore.jackpotnum[jackpotType], true)}`;
            this._gameCore.nOldJackpotNum[jackpotType] = this._gameCore.jackpotnum[jackpotType];
            return;
        }

        this.scrollNumber({
            txt: allJackpotNumLabel,
            began: self._gameCore.nOldJackpotNum[jackpotType],
            end: self._gameCore.jackpotnum[jackpotType],
            format: (nValue) => {
                allJackpotNumLabel.string = `$${self.formatNumberForComma(nValue, true)}`;
            }
        });

        this._gameCore.nOldJackpotNum[jackpotType] = this._gameCore.jackpotnum[jackpotType];
    }

    /**金额用','隔开，例如：10000 => 10,000 千分  formatFlag 奖池指定格式 123456 => 1,23,456*/
    public formatNumberForComma(data: number | string, formatFlag: boolean = false,decimals: number = 2) {
        let list = [];
        let str = `${data}`;
        let decimalsStr = '';
        if(str.indexOf('.') != -1){
            let pos = str.indexOf('.');
            decimalsStr = str.substring(pos);
            str = str.substring(0,pos);
        }
        for (let i = str.length - 1, j = 1; i >= 0; --i, ++j) {
            list.unshift(str[i]);
            if (formatFlag) {
                j == 3 && list.unshift(`,`);
                j == 5 && list.unshift(`,`);
            }
            else {
                j % 3 == 0 && list.unshift(`,`);
            }
        }
        if (list[0] == `,`) {
            list.shift();
        }
        if(decimals == 2){//始终保留2位小数
            if(decimalsStr.length == 0){
                decimalsStr = decimalsStr.concat('.00');
            }
            if(decimalsStr.length == 1){
                decimalsStr = decimalsStr.concat('00');
            }
            else if(decimalsStr.length == 2){
                decimalsStr = decimalsStr.concat('0');
            }
            for(let i = 0; i < decimalsStr.length;i++){
                list.push(decimalsStr[i]);
            }
        }
        return list.join('');
    }

    //断线重连
    public onToOtherRoom() {
        this._gameCore.lastAutoSpinState = false;
        (<any>this.LayerItems.runAniNode1).bRunAct = false;
        (<any>this.LayerItems.runAniNode2).bRunAct = false;
        (<any>this.LayerItems.runAniNode3).bRunAct = false;
        this.setSpinAutoState(false);
        this.updateSpinState(SpinStateFruitMachine.Normal);
        this.LayerItems.Panel_win.active = false;
        this.runNotBetToQuick();
    }

    //字符格式化
    public stringFormat(strContent: string, params: any[]): string {
        var str = strContent;
        for (var i = 0; i < params.length; i++) {
            var reg = new RegExp("\\{" + i + "\\}", "gm");
            str = str.replace(reg, params[i]);
        }
        return str;
    }

    //播放其它玩家Jackpot中奖动画
    playerOhterJackpotAni(jackpotType?: number) {

        let self = this;
        let runAniNodeList = [self.LayerItems.runAniNode1,self.LayerItems.runAniNode2,self.LayerItems.runAniNode3];
        let FileNode_jackpot_list = [self.LayerItems.FileNode_jackpot_1,self.LayerItems.FileNode_jackpot_2,self.LayerItems.FileNode_jackpot_3];

        let type = jackpotType;
        
        if(!self || !runAniNodeList[type]){
            return;
        }

        if (this._gameCore.otherJackpotList[type].length == 0) {
            (<any>runAniNodeList[type]).bRunAct = false;
            runAniNodeList[type].stopAllActions();
            return;
        }

        if (!(<any>runAniNodeList[type]).bRunAct) {
            (<any>runAniNodeList[type]).bRunAct = true;
            cc.tween(runAniNodeList[type])
                .repeatForever(
                    cc.tween()
                        .call(() => {
                            self.sliceOtherJackpotList(type,FileNode_jackpot_list);
                        })
                        .delay(10)
                        .call(() => {
                            FileNode_jackpot_list[type].getComponent(cc.Animation).stop();
                            self.jackpotNumList[type].active = true;
                            self.jackpotNodeInfoList[type].active = false;
                            (<any>runAniNodeList[type]).bRunAct = false;
                            if(type == 0){//粒子缩放
                                FileNode_jackpot_list[type].getChildByName('Node1').scale = 0.01;
                            }
                            else if(type == 1){//粒子缩放
                                cc.find('Node1/Node2',FileNode_jackpot_list[type]).scale = 0.01;
                            }
                            else if(type == 2){
                                cc.find('Node1/Sprite1',FileNode_jackpot_list[type]).opacity = 0; 
                            }
                            runAniNodeList[type].stopAllActions();
                            if (self._gameCore.otherJackpotList[type].length > 0) {
                                self.playerOhterJackpotAni(type);
                            }
                        })
                )
                .start();
        }

    }

    //更新移除在线玩家列表
    sliceOtherJackpotList(type,FileNode_jackpot_list) {
        let self = this;
        if (this._gameCore.otherJackpotList[type].length > 0) {
            let info = this._gameCore.otherJackpotList[type][0];
            if (!!info) {
                //中Jackpot的玩家在桌上
                if(this.deskPlayerPlayJackpot(info.playerid,info.winscore)){
                    return;//中桌上 不播上面中奖
                }

                let headImg = cc.find('Node3/Image_head_bg/Image_head',this.jackpotNodeInfoList[type]).getComponent(cc.Sprite); 
                super.setPlayerHead(headImg, info.headid, info.wxheadurl ?? "");
                
                if(type == 0){//中间大的奖池
                    let txt_name = this.jackpotNodeInfoList[type].getChildByName('Text_name').getComponent(cc.Label);
                    txt_name.string = Common.textClamp(info.name??'', 10, "...");
                }

                this._gameCore.otherJackpotList[type].splice(0, 1);
                this.jackpotNumList[type].active = false;
                this.jackpotNodeInfoList[type].active = true;
                let ani: cc.Animation = FileNode_jackpot_list[type].getComponent(cc.Animation);
                ani.stop();
                let animState = ani.play('animation0');
                AudioHelper.instance.stopEffectByPath(`res/sounds/jackpot`)
                AudioHelper.instance.playEffect(`res/sounds/jackpot`);
                animState.wrapMode = cc.WrapMode.Normal;
                ani.on('finished', (() => {
                    let animState = ani.play('animation1');
                    animState.wrapMode = cc.WrapMode.Loop;
                }), this);

                let txt_get_jackpot = this.jackpotNodeInfoList[type].getChildByName('txt_get_jackpot').getComponent(cc.Label);
                txt_get_jackpot.string = '';
                self.scrollNumber({
                    txt: txt_get_jackpot,
                    began: 0,
                    end: info.winscore ?? 0,
                    format: (nValueEx) => {
                        txt_get_jackpot.string = '$' + self.moneyFormat(nValueEx);
                    },
                    callback: () => {
                    }
                });
}
        }
    }
    
    //中jackpot的玩家在桌上
    deskPlayerPlayJackpot(playerid,winscore){
        if(cc.winSize.width/cc.winSize.height < 2){
            return false;
        }

        let bReturn = false;
        for(let i = 0; i < this._gameCore.deskPlayerInfoList.length;i++){
            let playerNodeInfo = this._gameCore.deskPlayerInfoList[i];
            playerNodeInfo.node.active = true;
            if(!Common.isNull(playerid) && playerNodeInfo.playerid == playerid){
                bReturn = true;//在桌上的不播上面中奖
                playerNodeInfo.playingAni = true;
                this.deskPlayerPlayWin(0,playerNodeInfo,{pmoney:playerNodeInfo.curMoney,winscore:winscore},true);
                break;
            }
        }
        return bReturn;
    }

    //更新桌上玩家信息
    onUpdateDeskPlayersInfo(info){
        for(let i = 0; i < this._gameCore.deskPlayerInfoList.length;i++){
            let playerNodeInfo = this._gameCore.deskPlayerInfoList[i];
            playerNodeInfo.node.active = true;
            let headSp = playerNodeInfo.headNode.getComponent(cc.Sprite);
            let player = info[(i + 1) + ''];
            if(!!player && !Common.isNull(player.playerid)){
                if(player.playerid == playerNodeInfo.playerid){//同个玩家
                    if(!!playerNodeInfo.playingAni){//是否在播动画
                        //播动画暂时不管 下次再更新
                    }
                    else{
                        playerNodeInfo.curMoney = player.pmoney;
                        playerNodeInfo.goldNode.getComponent(cc.Label).string = this.moneyFormat(player.pmoney ?? 0,2);
                    }
                }
                else{
                    playerNodeInfo.headSpinAniNode.active = false;
                    playerNodeInfo.winNode.active = false;
                    playerNodeInfo.infoBgNode.active = true;
                    this.setPlayerHead(headSp, player.headid, player.wxheadurl ?? "");
                    playerNodeInfo.curMoney = player.pmoney;
                    playerNodeInfo.nameNode.getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");
                    playerNodeInfo.goldNode.getComponent(cc.Label).string = this.moneyFormat(player.pmoney ?? 0,2);
                    playerNodeInfo.playerid = player.playerid;
                }
            }
            else{
                playerNodeInfo.infoBgNode.active = false;
                playerNodeInfo.headSpinAniNode.active = false;
                playerNodeInfo.winNode.active = false;
                headSp.spriteFrame = this.mainUiAtlas.getSpriteFrame('slotcat_ui_player_dengdai');
                playerNodeInfo.playerid = null;
                playerNodeInfo.curMoney = 0;
            }
        }
    }


    //更新桌上玩家游戏结果
    onUpdateDeskPlayersResultInfo(info){
        for(let i = 0; i < this._gameCore.deskPlayerInfoList.length;i++){
            let playerNodeInfo = this._gameCore.deskPlayerInfoList[i];
            playerNodeInfo.node.active = true;
            if(!Common.isNull(info.playerid) && playerNodeInfo.playerid == info.playerid){
                this.playDeskPlayerAni(playerNodeInfo,info);
                break;
            }
        }
    }

    //播放桌上玩家动画
    playDeskPlayerAni(playerNodeInfo: DeskPlayersInfo,info){
        playerNodeInfo.playingAni = true;
        playerNodeInfo.aniNode.removeAllChildren();
        playerNodeInfo.aniNode.active = true;
        playerNodeInfo.headSpinAniNode.active = true;
        playerNodeInfo.headSpinAniNode.getComponent(cc.Animation).play();
        playerNodeInfo.headSpinAniNode.opacity = 255;
        playerNodeInfo.headSpinAniNode.stopAllActions();

        cc.tween(playerNodeInfo.headSpinAniNode)
        .delay(1.0)
        .to(0.5,{opacity:0})
        .call(()=>{
            if(info.winscore <= 0){
                playerNodeInfo.playingAni = false;
            }
            playerNodeInfo.headSpinAniNode.active = false;
        })
        .start();

        if(info.winscore <= 0){
            this.scorllToGold(playerNodeInfo,info,false);
            return;
        }

        let mult = info.mult / 10;//倍数
        if(mult < 4){//4倍以下不播弹出界面动画 只播数字滚动
            this.scorllToGold(playerNodeInfo,info,false);
            return;
        }
        this.deskPlayerPlayWin(mult,playerNodeInfo,info);
    }

    //桌上玩家赢弹窗
    deskPlayerPlayWin(mult,playerNodeInfo,info,bJackpot: boolean = false){
        let playNode = this.otherWin_mega;
        let name = 'otherWin_mega';
        let soundName = 'res/sounds/midWin_other';
        if(bJackpot){
            playNode = this.otherWin_jackpot;
            name = 'otherWin_jackpot'
            soundName = '';
        }
        else{
            if(mult >= 8){
                playNode = this.otherWin_super;
                name = 'otherWin_super'
                soundName = 'res/sounds/bigWin_other';
            }
        }
        
        if(soundName.length > 0){
            this.scheduleOnce(() => {
                AudioHelper.instance.playEffect(soundName);
            }, 0.02)
        }
        playerNodeInfo.aniNode.children.forEach(item=>item.active = false);
        let ani = playerNodeInfo.aniNode.getChildByName(name);
        if(!ani){
            ani = cc.instantiate(playNode);
            playerNodeInfo.aniNode.addChild(ani);
            ani.name = name;
        }       
        ani.active = true;
        ani.getComponent(cc.Animation).play();
        let self = this;
        ani.getComponent(cc.Animation).on('finished', (() => {
            self.deskPlayerFlyScore(playerNodeInfo,info,ani);
        }), this);
    }

    //桌上玩家飘分
    deskPlayerFlyScore(playerNodeInfo,info,ani){
        // playerNodeInfo.aniNode.removeAllChildren();
        ani.active = false;
        playerNodeInfo.winOrLoseNumNode.getComponent(cc.Label).string = '+' + this.moneyFormat(info.winscore ?? 0,2);
        playerNodeInfo.winOrLoseNumNode.setContentSize(playerNodeInfo.winOrLoseNumNode.getContentSize());//重新读取size大小
        playerNodeInfo.winBgNode.setContentSize(cc.size(playerNodeInfo.winOrLoseNumNode.getContentSize().width + 60,playerNodeInfo.winBgNode.getContentSize().height));
        playerNodeInfo.winNode.stopAllActions();
        playerNodeInfo.winNode.setPosition(cc.v2(0,0));
        playerNodeInfo.winNode.opacity = 255;
        playerNodeInfo.winNode.active = true;
        cc.tween(playerNodeInfo.winNode)
        .delay(1.0)
        .by(1.0,{position:cc.v3(0,10)})
        .parallel(
            cc.tween().to(1.0,{opacity:0}),
            cc.tween().by(1.0,{position:cc.v3(0,10)})
        )
        .call(()=>{
            playerNodeInfo.winNode.opacity = 255;
            playerNodeInfo.winNode.active = false;
            playerNodeInfo.playingAni = false;
        })
        .start();

        this.scorllToGold(playerNodeInfo,info,true);
    }

    //滚动身上金币
    scorllToGold(playerNodeInfo,info,playingAni: boolean = true){
        //身上余额滚动
        let nScrollTime = 5;
        let nScrollIndex = 0;

        playerNodeInfo.goldNode.stopAllActions();
        cc.tween(playerNodeInfo.goldNode)
        .repeat(
            nScrollTime,
            cc.tween()
            .delay(1.0 / nScrollTime)
            .call(()=>{
                nScrollIndex += 1;
                let nScore = info.pmoney - info.winscore * (1 - nScrollIndex / nScrollTime);
                playerNodeInfo.goldNode.getComponent(cc.Label).string = this.moneyFormat(nScore ?? 0,2);
                if(!playingAni){
                    playerNodeInfo.playingAni = false;
                }
            })
        )
        .start();
    }

     //道具特效运行
     public doRoomIntertChat(sendPlayerPos, receiverPlayerPos, content) {

        let sendPos = this.getHeadNodePositon(sendPlayerPos);
        let receiverPos = this.getHeadNodePositon(receiverPlayerPos);

        if(Common.isNull(sendPos) || Common.isNull(receiverPos)){
            return;
        }

        let startPos = cc.v3(this.LayerItems.gameEmojiNode.convertToNodeSpaceAR(sendPos));
        let endPos = cc.v3(this.LayerItems.gameEmojiNode.convertToNodeSpaceAR(receiverPos));
        let Direction1 = Direction.LEFT;
        if(receiverPlayerPos%2 == 1){
            Direction1 = Direction.RIGHT;
        }

        UIHelper.playInteractExpression(this.LayerItems.gameEmojiNode, startPos, endPos, content, Direction1 == Direction.LEFT);
    }

    // 点击玩家头像
    private onClickHead(target: any, customEventData: any) {

        let self = this;
        let headPos = Common.toInt(customEventData);

        let playerid = this.getPlayerIdAtHeadPos(headPos);
        if(!playerid){//座上有效玩家 
            this._curClickHeadIndex = -1;
            return;
        } 

        let pos = this.getHeadNodePositon(headPos);
        if(Common.isNull(pos)){
            this._curClickHeadIndex = -1;
            return;
        }
        
        let showPos = this.LayerItems.gameEmojiNode.convertToNodeSpaceAR(pos);
        headPos%2 == 0 ? showPos.x += 350 : showPos.x -= 350;
       
        this._curClickHeadIndex = headPos;
        
        let playerInfo = { sendPlayerid: self._gameCore.playerid, receiverPlayerid: playerid,
            singlechatfee:this._gameCore.singlechatfee};
        UIHelper.showInteractExpression(this.LayerItems.gameEmojiNode, showPos, playerInfo, (info: any) => {
            self._gameCore.sendChatMessage(info);
        });
    }

    //玩家头像位置
    getHeadNodePositon(pos: number){
        let newPos: cc.Vec2 = null;
        let userHeadNode: cc.Node = null;

        if(Common.isNull(pos)){
            return newPos;
        }

        if(pos == Constant.customMyselfPos){
            userHeadNode = this.LayerItems.myselfNode;
        }
        else{
            userHeadNode = this._gameCore.deskPlayerInfoList[pos].headBgNode ;
        }
        
        if(Common.isNull(userHeadNode)){
            return newPos;
        }

        newPos = userHeadNode.convertToWorldSpaceAR(userHeadNode.getAnchorPoint())

        return newPos;
    }

    //根据玩家id获取座上玩家位置 包含自己  bReceiverPlayerid:是否道具接收者 如果是 优先从座上玩家列表取位置 （主要处理自己同时坐在桌上六个玩家位置时）
    getHeadPosAtPlayerId(playerId: number,bReceiverPlayerid: boolean = false){
        let pos = null;

        if(Common.isNull(playerId)){
            return pos;
        }
        if(!!bReceiverPlayerid){
            if(this._curClickHeadIndex == Constant.customMyselfPos && playerId == this._gameCore.playerid){//自定义自己的一个位置
                pos = Constant.customMyselfPos;
            }
            else{
                for (let i = 0; i < this._gameCore.deskPlayerInfoList.length; i++) {
                    if (!!playerId && playerId > 0 && playerId == this._gameCore.deskPlayerInfoList[i].playerid) {
                        pos = i;
                        break;
                    }
                }
    
                if(Common.isNull(pos) && playerId == this._gameCore.playerid){//自定义自己的一个位置
                    pos = Constant.customMyselfPos;
                }
            }  
        }
        else{//发送者 优先从自己位置
            if(playerId == this._gameCore.playerid){//自定义自己的一个位置
                pos = Constant.customMyselfPos;
            }
            else{
                for (let i = 0; i < this._gameCore.deskPlayerInfoList.length; i++) {
                    if (!!playerId && playerId > 0 && playerId == this._gameCore.deskPlayerInfoList[i].playerid) {
                        pos = i;
                        break;
                    }
                }    
            }
        }
        
        return pos;
    }

    //根据玩家位置获取座上玩家id 包含自己
    getPlayerIdAtHeadPos(pos: number){
        let playerid = null;
        
        for (let i = 0; i < this._gameCore.deskPlayerInfoList.length; i++) {
            if (pos != null && pos >= 0 && i == pos && this._gameCore.deskPlayerInfoList[i].playerid > 0) {
                playerid = this._gameCore.deskPlayerInfoList[i].playerid;
                break;
            }
        }
        
        if(!playerid && pos == Constant.customMyselfPos){//自定义自己的一个位置 
            playerid = this._gameCore.playerid;
        }
                       
        return playerid;
    }

    //刷新任务
    updateTask(info){

        if(!info){
            this._gameCore.taskInfo.node.active = false;
            this._gameCore.bClickSendGetTaskFlag = false;
            return;
        }

        let taskicon = info.taskicon??0;
        if(taskicon == 0){//0代表没有任务
            this._gameCore.taskInfo.node.active = false;
            this._gameCore.bClickSendGetTaskFlag = false;
            return;
        }
        //当前任务所需最小下注
        let block = false;
        let taskminbet = info.taskminbet??0;
        let taskmoney = info.taskmoney??0;
        if(this._gameCore.betMoney < taskminbet){
            block = true;
        }
        let taskcurnum = info.taskcurnum??0;//当前进度
        taskcurnum = taskcurnum < 0 ? 0 : taskcurnum;
        let tasktotalnum = info.tasktotalnum??0;//总需进度

        this._gameCore.taskCollectState = taskcurnum == tasktotalnum && taskcurnum > 0;//是否可领取
        
        //进度相关
        let progress = Number((taskcurnum / tasktotalnum).toFixed(1));
        this._gameCore.taskInfo.node.active = true;
        let pSize = this._gameCore.taskInfo.loadingBar.getContentSize();
        this._gameCore.taskInfo.loadingBar.getComponent(cc.ProgressBar).progress = progress;
        this._gameCore.taskInfo.txt_progress.string = `${progress * 100}%`;
        this._gameCore.taskInfo.loadingBarDesc.x = -(pSize.width/2) + (pSize.width * progress);
        
        //提示相关
        this._gameCore.taskInfo.tipsIconSp.spriteFrame = this.mainUiAtlas.getSpriteFrame(`slotcat_ui_icon${taskicon}`);
        this._gameCore.taskInfo.tipsNum.string = 'x' + tasktotalnum;
        
        //可领取相关
        this._gameCore.taskInfo.collectMoney.string = '₹' + this.moneyFormat(taskmoney);
        this._gameCore.taskInfo.nowNodeIcon.spriteFrame = this.mainUiAtlas.getSpriteFrame(`slotcat_ui_icon${taskicon}`);
        this._gameCore.taskInfo.nowNodeNum.string = taskcurnum + '/' + tasktotalnum;

        this._gameCore.taskInfo.lockSpNode.active = block;
        if(this._gameCore.taskInfo.lockSpNode.active){
            this._gameCore.taskInfo.tipsCollectionNode.active = false;
            this._gameCore.taskInfo.tipsLockText.string = this.stringFormat(GameTextTips.Text_task_tip,[this.moneyFormat(taskminbet)]);
            this._gameCore.taskInfo.tipsLockText.node.active = true;
        }
        else{
            this._gameCore.taskInfo.tipsCollectionNode.active = true;
            this._gameCore.taskInfo.tipsLockText.node.active = false;
        }
        
        if(this._gameCore.taskCollectState){//可领取
            this.LayerItems.task_run_anim_node.stopAllActions();
            this._gameCore.taskInfo.collectIconNode.active = true;
            this._gameCore.taskInfo.nowNode.active = false;
            this._gameCore.taskInfo.collectTextNode.active = true;
            this._gameCore.taskInfo.lightNode.active = true;
            this._gameCore.taskInfo.infoNode.getComponent(cc.Animation).play();
        }
        else{
            this.onShowTaskTips(null,null);
        }
    }

    //显示或隐藏任务提示框
    onShowTaskTips(target: any, customEventData: any){
        if(this._gameCore.taskCollectState){
            if(!Common.isNull(customEventData) && !this._gameCore.bClickSendGetTaskFlag){//主动点击了领取
                this._gameCore.sendCollectTask();
                let self = this;
                this._gameCore.bClickSendGetTaskFlag = true;
                this.scheduleOnce(()=>{
                    self && (self._gameCore.bClickSendGetTaskFlag = false);
                },3)
            }
            return;
        }

        if(Common.isNull(customEventData) && this._gameCore.isshowTaskTipsDialog){//非点击时，已经打开的弹窗，不做动画
            return;
        }

        this._gameCore.taskInfo.collectTextNode.active = false;
        this._gameCore.taskInfo.lightNode.active = false;

        let anim = this._gameCore.taskInfo.tipsAnimNode.getComponent(cc.Animation);
        let self = this;
        this.LayerItems.task_run_anim_node.stopAllActions();
        cc.tween(this.LayerItems.task_run_anim_node)
        .call(()=>{
            if(self._gameCore.isshowTaskTipsDialog){
                anim.play('shouhui');
                if(!self._gameCore.taskInfo.lockSpNode.active){//锁定状态 不改变图标
                    self._gameCore.taskInfo.collectIconNode.active = !self._gameCore.taskInfo.collectIconNode.active;
                    self._gameCore.taskInfo.nowNode.active = !self._gameCore.taskInfo.nowNode.active;
                }
                
                self._gameCore.isshowTaskTipsDialog = false;
            }
            else{
                anim.play('dianji');
                self._gameCore.isshowTaskTipsDialog = true;
                if(!self._gameCore.taskInfo.lockSpNode.active){//锁定状态 不改变图标
                    if(self._gameCore.taskInfo.nowNode.active){
                        self._gameCore.taskInfo.collectIconNode.active = true;
                        self._gameCore.taskInfo.nowNode.active = false;
                    }
                }
            }
        })
        .delay(5.0)
        .call(()=>{
            if(self._gameCore.isshowTaskTipsDialog){
                anim.play('shouhui');
                self._gameCore.isshowTaskTipsDialog = false;
                if(!self._gameCore.taskInfo.lockSpNode.active){//锁定状态 不改变图标
                    self._gameCore.taskInfo.collectIconNode.active = !self._gameCore.taskInfo.collectIconNode.active;
                    self._gameCore.taskInfo.nowNode.active = !self._gameCore.taskInfo.nowNode.active;
                }
            }
        })
        .start()   
    }

    /**领取任务金币动画*/
    playCollectTaskAnim(taskMoney: number,playScoreMoney: number) {
        taskMoney = taskMoney??0;
        if(taskMoney <= 0){
            return;
        }
        let self = this;        
        let endNode = this.LayerItems.Image_coin_bg;
        let endPos = endNode.convertToWorldSpaceAR(endNode.getAnchorPoint())
        endPos = cc.v2(this.LayerItems.Node_coins.convertToNodeSpaceAR(endPos));
        let tcoinNumArr = [1, 2, 3, 4, 6, 8, 10];//随机飞金币数量
        let floorNum = Math.floor(taskMoney/this._gameCore.betMoney);
        let beishu = 0;
        for(let i = tcoinNumArr.length - 1; i >= 0;i--){
            if(floorNum >= tcoinNumArr[i]){
                beishu = i;
                break;
            }
        }
        let tcoinsNum = tcoinNumArr[beishu];
        let startNode = this._gameCore.taskInfo.infoNode;
        let startPos = startNode.convertToWorldSpaceAR(startNode.getAnchorPoint())
        startPos = cc.v2(this.LayerItems.Node_coins.convertToNodeSpaceAR(startPos));
        for (let tcoinsIndex = 0; tcoinsIndex < tcoinsNum; tcoinsIndex++) {
            let tcoins = cc.instantiate(self.LayerItems.item_coins);
            self.LayerItems.Node_coins.addChild(tcoins);
            tcoins.position = startPos;
            tcoins.active = false;
            cc.tween(tcoins)
                .delay(tcoinsIndex * 0.06)
                .call(() => {
                    tcoins.active = true;
                })
                .call(()=>{
                    if(tcoinsIndex == tcoinsNum - 1){
                        if(self._gameCore.taskCollectState){
                            self._gameCore.taskCollectState = false;
                            self.updateTask(null);
                        }
                        self.taskCollectScorllMoney(playScoreMoney);
                    }
                })
                .to(0.4, { position: cc.v3(endPos.x + 120, endPos.y)})
                .to(0.55,{scale:0.5})
                .call(() => {
                    if (!!tcoins) {
                        tcoins.removeFromParent();
                    }
                })
                .start()
        }
    }

    //任务时自己余额滚动
    taskCollectScorllMoney(playScoreMoney: number){
        let txt = this.LayerItems.scoreMoney.getComponent(cc.Label)
        let self = this;
        this.scrollNumber({
            nTime:0.5,
            txt: txt,
            began: playScoreMoney,
            end: self._gameCore.playScoreMoney,
            bNotStopAction: true,
            format: (nValue) => {
                txt.string = self.moneyFormat(nValue,2);
            },
        });
    }
    
    
    //////////////////////////////////////////////////////////////////////////////
    // 重置所有界面数据
    public resetAllUI() {
        // 重置玩家界面
    }

    //////////////////////////////////////////////////////////////////////////////
    // 返回大厅
    public onClickBack() {
        this._gameCore.quitGame();
    }

    // 点击充值
    public onClickCharge() {
         HallManager.instance.openCharge();
    }

    //加注
    onClickAdd() {
        if(!this._gameCore.bFruitIsStop){
            return;
        }
        AudioHelper.instance.playEffect(`res/sounds/jetton`);
        this._gameCore.onBetAdd();
        this.showBetConfig(true)
    }

    //减注
    onClickSub() {
        if(!this._gameCore.bFruitIsStop){
            return;
        }
        AudioHelper.instance.playEffect(`res/sounds/jetton`);
        this._gameCore.onBetSub();
        this.showBetConfig(true)
    }

    //按钮是否允许点击，不允许时变色
    changeAddOrSubBtnColor(){
        let bEnabled = true;
        if(!this._gameCore.bFruitIsStop || this._gameCore.nSpinState == SpinStateFruitMachine.Free ){
                bEnabled = false;
        }

        let color = bEnabled ? cc.color(255,255,255) : cc.color(128,128,128);
        this.LayerItems.btn_jian.color = color;
        this.LayerItems.btn_jia.color = color;
        this.LayerItems.btn_jian.getComponent(ButtonLayer).enableTouch = bEnabled
        this.LayerItems.btn_jia.getComponent(ButtonLayer).enableTouch = bEnabled
    }

    //点击开始免费游戏
    onClickStartFreeGame() {

        this.updateSpinState(SpinStateFruitMachine.Free);

        this.autoStartFreeGame();
    }

    //自动开始免费游戏
    autoStartFreeGame() {
        AudioHelper.instance.playEffect(Sounds.SPIN);

        this._gameCore.resultData = this._gameCore.resultFreeData[(this._gameCore.nLastMaxFreeTimes - this._gameCore.nLastFreeTimes + 1).toString()];
        this._gameCore.freeWinTotalNum += this._gameCore.resultData.winmoney;
        this._gameCore.freeChangeTotalNum += this._gameCore.resultData.changemoney;

        this._gameCore.nLastFreeTimes -= 1;
        this.updateFreeTimes();//刷新Free按钮次数

        this._gameCore.freeTimesEndFlag = FREE_TYPE.playing;

        if (this._gameCore.nLastFreeTimes == 0) {
            this._gameCore.freeTimesEndFlag = FREE_TYPE.end;
        }

        if (!!this._gameCore.resultData) {
            this._lotteryBox.startGame();
        }
    }


    //帮助
    public onClickHelp() {
        if(!this.helpNode){
            this.helpNode = cc.instantiate(this.helpPrefab);
            SceneManager.instance.addChildNode(this.helpNode);
        }
        
        this._gameLogic.popUpEffect(this.helpNode, true);
    }

    //jackpot记录
    public onClickJackpotRecord() {
        if(!this.jackpotRecordLayer){
            this.jackpotRecordLayer = cc.instantiate(this.jackpotRecordPrefab);
            SceneManager.instance.addChildNode(this.jackpotRecordLayer);
        }

        let self = this;
        this._gameLogic.popUpEffect(this.jackpotRecordLayer, true,(()=>{
            self.jackpotRecordLayer.getComponent("SLOTCATJackpotRecordLayer").setData(null);
        }));
    }

    //免费total界面confim按钮
    onClickTotalConfimBtn(){
        let self = this;
        let animPrefab = this.LayerItems.animNode.getChildByName('totalwinPrefab');
        if(!!animPrefab && animPrefab.active){
            let win_num = cc.find('Node1/Sprite10/win_num_prefab',animPrefab);
            if(!!win_num){
                win_num.stopAllActions();
                self.LayerItems.Panel_win.active = false;
                animPrefab.active = false;
                self._gameCore.btnTotalConfimCallback && self._gameCore.btnTotalConfimCallback();
            }
        }
    }

    //免费界面confim按钮
    onClickFreeConfimBtn(){
        let self = this;
        let animPrefab = this.LayerItems.Panel_free.getChildByName('freetimesPrefab');
        if(!!animPrefab && animPrefab.active){
            self.LayerItems.Panel_free.stopAllActions();
            self.LayerItems.Panel_free.active = false
            self.onClickStartFreeGame(); 
        }
    }
    

    //////////////////////////////////////////////////////////////////////////////

}
