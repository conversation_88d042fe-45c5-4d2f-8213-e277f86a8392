import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import { GameTextTips } from "../core/SLOTCATDefine";
import SLOTCATGameLogic from "../core/SLOTCATGameLogic";
import SLOTCATGameView from "./SLOTCATGameView";

const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slotcat/SLOTCATHelpLayer')
export default class SLOTCATHelpLayer extends BaseLayer {

    @property(cc.Node)
    ListView_menu: cc.Node = null;

    @property(cc.Node)
    node_content: cc.Node = null;
    
    _gameLogic :SLOTCATGameLogic = null;
    mCurPage = 0;
    
    onLoad() {
       this._gameLogic = cc.Canvas.instance.getComponent("SLOTCATGameLogic"); 
    }

    start() {
        
    }

    public onClickClose() {
        let self = this;
        this._gameLogic.popUpEffect(this.node,false,(()=>{
            if(self.mCurPage != 0){
                self.onClickChangePage(null,0);
            }
        }));
    }

    public onClickChangePage(target: any, customEventData: any) {
        let index = Common.toNumber(customEventData);

        if(this.mCurPage == index){
            return
        }
        this.mCurPage = index;

        this.ListView_menu.children.forEach((item,itemIndex)=>{
            item.getChildByName('Image_btn_normal').active = !(itemIndex == index);
            item.getChildByName('Image_btn_select').active = itemIndex == index; 
        })

        this.node_content.children.forEach((item,itemIndex)=>{
            item.active = itemIndex == index;
        })
    }

}
