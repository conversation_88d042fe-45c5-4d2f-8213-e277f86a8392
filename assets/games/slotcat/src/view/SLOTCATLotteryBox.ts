import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import { Constant, FREE_TYPE, ICON_TYPE, Sounds } from "../core/SLOTCATDefine";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import SLOTCATGameCore, { FruitMachineBaseMachineElement, SpinStateFruitMachine, WinLinesInfo } from "../core/SLOTCATGameCore";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import SLOTCATGameView from "./SLOTCATGameView";
import Config from "../../../../script/frame/config/Config";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";

const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slotcat/SLOTCATLotteryBox')
export default class SLOTCATLotteryBox extends BaseLayer {

    // 游戏核心对象
    private _gameCore: SLOTCATGameCore = null;
    private _gameView: SLOTCATGameView = null;
    schedule_updatePos = null;

    onLoad() {
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)

        this._gameCore = cc.Canvas.instance.getComponent("SLOTCATGameCore");
        this._gameView = cc.Canvas.instance.getComponent("SLOTCATGameView");

        //初始化水果机元素
        this.initMachineElement();
    }

    start() {

    }

    onDestroy() {
        // if(this.schedule_updatePos){
        //     this.unschedule(this.schedule_updatePos);
        //     this.schedule_updatePos = null;
        // }
    }

   

    /**初始化水果机元素 */
    initMachineElement() {
        //初始化水果
        this._gameCore.machineElement = [];
        let cSize = this.LayerItems.Panel_container.getContentSize();
        let count = 0;
        for (let nRow = 0, nRowMax = Constant.ROW_MAX; nRow < nRowMax; ++nRow) {
            this._gameCore.machineElement[nRow] = [];
            for (let nCol = 0, nColMax = Constant.COL_SHOW; nCol < nColMax; ++nCol) {
                count += 1;
                let node = cc.instantiate(this._gameView.item_icon);
                node.name = 'icon' + count;
                this.LayerItems.Panel_container.addChild(node);
                node.active = true;
               
                let nType = Common.random(0, Constant.ELEMENT_MAX_NUM - 1);
                let machineElement: FruitMachineBaseMachineElement = {
                    node: node,
                    nRow: nRow,
                    nCol: nCol,
                    nScale: node.scale
                };

                this._gameCore.machineElement[nRow][nCol] = machineElement;
                //保存引用
                this._gameCore.machineElementMap.set(node, machineElement);
                //刷新显示
                this.updateMachineElement(node, nType);
                //调整位置
                node.setPosition(cc.v2(
                    - 2 * (cSize.width / nColMax) + (nCol * (cSize.width / nColMax)),
                    cSize.height - (nRow * (cSize.height / Constant.ROW_SHOW))
                ));
            }
        }  
    }

    /**刷新水果机元素 */
    updateMachineElement(node: cc.Node, nType: number) {
        let info = this._gameCore.machineElementMap.get(node);
        info && (this._gameCore.machineElement[info.nRow][info.nCol].nType = nType);
        !!node && (node.getChildByName('icon').getComponent(cc.Sprite).spriteFrame = this._gameView.mainUiAtlas.getSpriteFrame(`slotcat_ui_icon${nType}`));
    }

    //开始游戏 
    startGame() {
        this._gameCore.setResultDataInfo();
        this.doDrawLottery();
    }

    //重置初始数据
    resetData() {
        this.hideAllPrizeIconAni(true);
        this._gameView.checkWinLabelHide();
        this._gameCore.resetDataValue();

        for (let i = 0; i < this._gameCore.machineElement.length; i++) {
            for (let j = 0; j < this._gameCore.machineElement[i].length; j++) {
                let machineElement = this._gameCore.machineElement[i][j];
                machineElement.nStopRow = null;
                machineElement.nMoveIndex = null;
                machineElement.isOK = false;
                machineElement.isShowLongAnim = false;
                machineElement.isPlayAnim = false;
            }
        }
    }

    update(dt: number) {
        if (this._gameCore.bFruitIsStop || this._gameCore.m_updateIndex > 0) {
            return;
        }
        //水果机滑动界面大小
        let cSize = this.LayerItems.Panel_container.getContentSize();
        let nMaxRollNum = 30;
        let self = this;
        for (let nCol = 0; nCol < Constant.COL_SHOW; nCol++) {  //3
            this._gameCore.m_RollNum[nCol] += 1
            let nRollNum = this._gameCore.m_RollNum[nCol]
            nMaxRollNum = nMaxRollNum +  this._gameCore.m_nDoLen[nCol]
            if (nRollNum <= nMaxRollNum) {
                let nImageIndex = null
                for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) { //5
                    let rollItem = this._gameCore.machineElement[nRow][nCol];
                    rollItem.nStopRow = rollItem.nStopRow || rollItem.nRow;
                    rollItem.nStopRow = rollItem.nStopRow + this._gameCore.m_nOnceMoveLen[nCol]
                    //超出底部时
                    rollItem.nMoveIndex = (rollItem.nMoveIndex || 0) + 1
                    if (rollItem.nStopRow >= Constant.ROW_MAX) {
                        //回到顶部F
                        rollItem.nStopRow = rollItem.nStopRow - Constant.ROW_MAX; 
                        //重置图标，只有2-4的是服务器数据图标，修改需要调整下面的赋值
                        if (nRow >= 2 && nRow <= 4) {
                            //更换图标
                            if (!rollItem.isOK) {
                                //当剩余步数不足一次循环时更变样式
                                if (nMaxRollNum - rollItem.nMoveIndex <= Constant.ROW_MAX / this._gameCore.m_nOnceMoveLen[nCol]) {
                                    //重置图标，只有2-4的是服务器数据图标，修改需要调整下面的赋值
                                    nImageIndex = this._gameCore.fruitData[nRow - 1 - 1][nCol]
                                    rollItem.isOK = true;
                                    rollItem.nType = nImageIndex;
                                    this.updateMachineElement(rollItem.node,nImageIndex);
                                    if(nImageIndex == ICON_TYPE.NUM_9){//猫
                                        self.scheduleOnce(()=>{
                                            rollItem.node.getChildByName('icon').active = false;
                                            let aniNode = rollItem.node.getChildByName('prize_mao');
                                            let ani = aniNode.getComponent(cc.Animation);
                                            ani.stop();
                                            aniNode.active = true;  
                                            let animState = ani.play('animation0');
                                            animState.wrapMode = cc.WrapMode.Normal;
                                        },0.5)
                                    }
                                } else{
                                    let rand = Common.random(0, Constant.ELEMENT_MAX_NUM - 1)
                                    this.updateMachineElement(rollItem.node, rand);
                                }
                            }
                        } else {
                            let rand = Common.random(0, Constant.ELEMENT_MAX_NUM - 1)
                            this.updateMachineElement(rollItem.node, rand);
                        }
                    }
                    rollItem.node.y = cSize.height - (rollItem.nStopRow * (cSize.height / Constant.ROW_SHOW));               
                }

                //滚动结束
                if (nRollNum == nMaxRollNum) {
                    this.playColScrollEndAudio(nCol);
                    //如果有龙，显示龙动画
                    this.showLongAni(nCol);
                }
            }
            else {
                let springback = this._gameCore.m_springback[nCol];
                if (springback < Constant.ROW_MAX * Constant.ROW_SHOW) {//界面3*5 最后一个显示的元素
                    for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                        let posY = cSize.height - (nRow * (cSize.height / Constant.ROW_SHOW));
                        let rollItem = this._gameCore.machineElement[nRow][nCol];
                        rollItem.nMoveIndex = 0;
                        rollItem.isOK = false;
                        // 反弹
                        if (nRow >= 2 && nRow <= 4) {
                            if (springback < 7) {
                                rollItem.node.y = (posY - (springback + 1) * 5);
                            } else {
                                rollItem.node.y = (posY - (70 - springback * 5));
                            }
                            //特效
                            if (springback == Constant.ROW_MAX * Constant.ROW_SHOW - 2) { //倒数第二个元素
                                //播放中牛或鹿的声音
                                // this.playNiuOrLuPrizeSound();
                            }
                        } else {
                            //校准位置
                            rollItem.node.y = (posY)
                        }
                    }
                    springback = springback + 1
                    this._gameCore.m_springback[nCol] = springback
                    if (springback == Constant.ROW_MAX * Constant.ROW_SHOW && nCol == Constant.COL_SHOW - 1) {
                        this._gameCore.m_updateIndex = 1; 

                        //收集金币动画
                        // this.playCollectCowAnim();
                        //更新任务
                        if(this._gameCore.taskData){
                            this._gameView.updateTask(this._gameCore.taskData);
                            this._gameCore.taskData = null;
                        }
                        
                        this.scheduleOnce(()=>{
                            //显示线
                            this.showWinLine();
                        },0.5)

                        break;
                    }
                }
            }
        }
    }
    

    update2(dt: number) {
        if (this._gameCore.bFruitIsStop || this._gameCore.m_updateIndex > 0) {
            return;
        }

        let t_ptPos = [
            194.5,
            194.5,
            142.3,    
            129.7,
            91.27,
            71.11,
            97.5,
            33.94,
            17.56,
            1.18,
            -15.2,
            -6.5,
            -47.96,
            -64.34,
            -80.72,
            -97.1,
            -114.5,
            -133.64,
            -151.28,
            -169.55,
            -182.15,
            -227.5,
            -227.5,
            -227.5,
            -227.5,
        ]

        //水果机滑动界面大小
        let cSize = this.LayerItems.Panel_container.getContentSize();
        let nMaxRollNum = 25;

        for (let nCol = 0; nCol < Constant.COL_SHOW; nCol++) {
            this._gameCore.m_RollNum[nCol] += 1
            
            let nRollNum = this._gameCore.m_RollNum[nCol]
            let maxRollNum = nCol * this._gameCore.m_nDoLen[nCol] + nMaxRollNum  
            
            if (nRollNum <= maxRollNum) {
                let nImageIndex = null
                for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                    let rollItem = this._gameCore.machineElement[nRow][nCol];
                    this._gameCore.m_indexMovePosList[nCol][nRow] += 1;
                    if(this._gameCore.m_indexMovePosList[nCol][nRow] > nMaxRollNum){
                        //回到顶部F
                        this._gameCore.m_indexMovePosList[nCol][nRow] = 0;
                        //重置图标，只有2-4的是服务器数据图标，修改需要调整下面的赋值
                        if (nRow >= 2 && nRow <= 4) {
                            //重置图标，只有2-4的是服务器数据图标，修改需要调整下面的赋值
                            nImageIndex = this._gameCore.fruitData[nRow - 2][nCol];

                            rollItem.isOK = true;
                            rollItem.nType = nImageIndex;
                            this.updateMachineElement(rollItem.node,nImageIndex);
                        } else {
                            let rand = Common.random(0, Constant.ELEMENT_MAX_NUM - 1)
                            this.updateMachineElement(rollItem.node, rand);
                        }
                    }

                    rollItem.node.y = t_ptPos[this._gameCore.m_indexMovePosList[nCol][nRow]];                 
                }

                //滚动结束
                if (nRollNum == maxRollNum) {
                    this.playColScrollEndAudio(nCol);
                }
            }
            else {
                let springback = this._gameCore.m_springback[nCol];
                if (springback < Constant.ROW_MAX * Constant.ROW_SHOW) {//界面3*5 最后一个显示的元素
                    for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                        let posY = cSize.height / Constant.ROW_SHOW * (Constant.ROW_MAX / 2 - (nRow + 1) + 1 + 0.5);
                        let rollItem = this._gameCore.machineElement[nRow][nCol];
                        if (nRow >= 2 && nRow <= 4) {
                            if (springback < 7) {
                                rollItem.node.y = (posY - (springback + 1) * 5);
                            } else {
                                rollItem.node.y = (posY - (65 - springback * 5 + (nRow - 2) * 7));
                            }
                        }
                        else{
                            rollItem.node.y = posY;
                        }
                        springback = springback + 1
                        this._gameCore.m_springback[nCol] = springback
                    }
                }

                if (springback == Constant.ROW_MAX * Constant.ROW_SHOW && nCol == Constant.COL_SHOW - 1) {
                    // if(this.schedule_updatePos){
                    //     this.unschedule(this.schedule_updatePos);
                    //     this.schedule_updatePos = null;
                    // }
                    this._gameCore.m_updateIndex = 1; 

                    this.scheduleOnce(()=>{
                        //显示线
                        this.showWinLine();
                    },0.35)
                    
                    break;
                }
            }
        }
    }

    //显示龙动画 同一列中2个或3个才播
    showLongAni(nCol: number){
        let fruitData = this._gameCore.fruitData;
        let count = 0;
        for(let nRow = 0;nRow < fruitData.length;nRow++){
            let nType = fruitData[nRow][nCol];
            if(nType == ICON_TYPE.NUM_8){//龙
                count += 1;
            }
        }

        if(count <= 1 || fruitData[1][nCol] != ICON_TYPE.NUM_8){//3个中至少要中2个 中间一个是必中
            return;
        }

        //mask截取size高  
        let showLongIndex = 0;
        let sizeHeight = 500;//3条龙
        if(count == 2 && fruitData[0][nCol] == fruitData[1][nCol]){//2条龙 最上面2个元素是龙
            sizeHeight = 322;
            showLongIndex = 1;
        }
        else if(count == 2 && fruitData[1][nCol] == fruitData[2][nCol]){//2条龙 最下面2个元素是龙
            sizeHeight = 500;
            showLongIndex = 2;
        }
        
        let longNode = this.LayerItems[`prize_long${nCol}`].getChildByName('longNode');
        if(!longNode){
            longNode = cc.instantiate(this._gameView.longPrafab);
            this.LayerItems[`prize_long${nCol}`].addChild(longNode);
            longNode.name = 'longNode';
        }
        longNode.active = true;
        let mask = longNode.getChildByName('mask');
        let longItemList = [null,null,null];
        longItemList[0] = mask.getChildByName('prize_long_anim_3');
        longItemList[1] = mask.getChildByName('prize_long_anim_2_up');
        longItemList[2] = mask.getChildByName('prize_long_anim_2_down');
        mask.setContentSize(cc.size(mask.getContentSize().width,0));

        longItemList.forEach((item,index)=>{
            item.active = index == showLongIndex;
        })
        let element1 = this._gameCore.machineElement[2][nCol]
        let element2 = this._gameCore.machineElement[3][nCol]
        let element3 = this._gameCore.machineElement[4][nCol]

        let maxTimeCount = 15;
        let drawSize = Math.ceil(sizeHeight/maxTimeCount);
        let addSize = 0;
        longNode.stopAllActions();
        for(let i = 0; i < maxTimeCount;i++){
            cc.tween(longNode)
            .delay(0.5 + 0.075 * i)
            .call(()=>{
                addSize += drawSize;
                if(i == 3 && showLongIndex <= 1){
                    element1.node.getChildByName('icon').active = false;
                    element1.isShowLongAnim = true;
                }
                else if(i == 5){
                    AudioHelper.instance.stopEffectByPath(`res/sounds/long_win`);
                    AudioHelper.instance.playEffect(`res/sounds/long_win`);
                }
                else if(i == 8){
                    element2.node.getChildByName('icon').active = false;
                    element2.isShowLongAnim = true;
                }
                else if(i == 12 && showLongIndex != 1){
                    element3.node.getChildByName('icon').active = false;
                    element3.isShowLongAnim = true;
                }
                mask.setContentSize(cc.size(mask.getContentSize().width,addSize));
            })
            .start();
        }
    }

    /**开始摇奖 */
    doDrawLottery() {
        //隐藏所有线
        this.hideAllLine();
        this._gameCore.bFruitIsStop = false;
        this._gameView.changeAddOrSubBtnColor();
        //音效
        AudioHelper.instance.playEffect(Sounds.ScrollStart);
        let self = this;
        // if(self.schedule_updatePos){
        //     self.unschedule(self.schedule_updatePos);
        //     self.schedule_updatePos = null;
        // }
        // let time = 0;
        // self.schedule_updatePos = (dt)=>{
        //     // if(!self){
        //     //     return;
        //     // }
        //     // time = time + 1
        //     self.update1(dt)
        //     // if(time == 2){
        //     //     time = 0;
        //     //     self.update2(dt)
        //     // }
        // }
        // self.schedule(self.schedule_updatePos,0.5)
    }

    /**显示中奖线 */
    showWinLine() {
        let resultData = this._gameCore.resultData;
        let winLinesInfo = resultData.winLinesInfo;

        let self = this;
        if (winLinesInfo && winLinesInfo.length > 0) {
            //先播所有中奖线及图标动画，然后轮播单条线
            self.repeatPlayMoreLineAni(winLinesInfo);
            if (self._gameCore.nLastFreeTimes <= 0 && self._gameCore.nSpinState == SpinStateFruitMachine.Free) {//免费最后一次 //先更新赢金再弹界面
                self.updateWinNum(() => { self.playWinAnim(() => { self.doFruitMachineEnd() }) }); //刷新赢金
            }
            else {
                self.playWinAnim(() => { self.updateWinNum(() => { self.doFruitMachineEnd() }) }); //刷新赢金
            }
        }
        else {
            if (this._gameCore.nLastFreeTimes <= 0 && this._gameCore.nSpinState == SpinStateFruitMachine.Free) {
                this.updateWinNum(() => { this.playWinAnim(() => { this.doFruitMachineEnd() }) }); //刷新赢金
            }
            else {
                if (resultData.multgoldnum >= 2 || resultData.jackpotcash > 0) {
                    this.playWinAnim(() => { this.updateWinNum(() => { this.doFruitMachineEnd() }) }); //刷新赢金
                }
                else {
                    // //刷新赢金
                    this.updateWinNum();

                    this.doFruitMachineEnd();
                }
            }
        }
    }

    //repeat播放多种线
    repeatPlayMoreLineAni(winLinesInfo: WinLinesInfo[]) {

        if (!winLinesInfo || winLinesInfo?.length == 0) {
            return;
        }
        let self = this;

        AudioHelper.instance.playEffect(`res/sounds/lianxian`);
        //显示所有线
        winLinesInfo.forEach(element => {
            self.setOneLineVisile(element, true, false, true);
        });

        //所有线显示一段时间后，切换至每隔1.5秒轮换每条线
        this._gameCore.curLineIndex = 0;

        (<any>this.LayerItems.Node_Lines_ex).playLineSoundList = [];

        let func = () => {
            self.hideAllLine(false,false);
            if (self._gameCore.curLineIndex >= 0) {
                if (!(<any>this.LayerItems.Node_Lines_ex).playLineSoundList[self._gameCore.curLineIndex]) {
                    self.playLineSound();
                }

                self.setOneLineVisile(winLinesInfo[self._gameCore.curLineIndex], true, false);
            }
            if (++self._gameCore.curLineIndex >= winLinesInfo.length) {
                self._gameCore.curLineIndex = 0;
            }
        }
        //轮流播放每条线
        this.LayerItems.Node_Lines_ex.stopAllActions();
        if (winLinesInfo.length == 1) {
            func();
        }
        else {
            this._gameView.nodeRunActionRepeatForever(self.LayerItems.Node_Lines_ex, func, 1.5);
        }
    }

    //播放连线声音
    playLineSound() {
        let multipleMusic = [4, 15, 25, 35];
        for (let i = 0; i < multipleMusic.length; i++) {//根据倍率播放
            if (this._gameCore.resultData.totalmult < multipleMusic[i]) {
                AudioHelper.instance.playEffect(`res/sounds/line${i + 1}`);
                (<any>this.LayerItems.Node_Lines_ex).playLineSoundList[this._gameCore.curLineIndex] = true;
                break;
            }
            if (i == multipleMusic.length - 1) {
                AudioHelper.instance.playEffect(`res/sounds/lineMore`);
                (<any>this.LayerItems.Node_Lines_ex).playLineSoundList[this._gameCore.curLineIndex] = true;
            }
        }
    }

    /**隐藏所有线 */
    hideAllLine(bStop: boolean = true,bStopOld: boolean = true) {
        this.hideAllPrizeIconAni();
        for (let i = 1; i <= Constant.MAX_LINES; ++i) {
            let winLinesInfo = <WinLinesInfo>{};
            winLinesInfo.nLine = i;
            this.setOneLineVisile(winLinesInfo, false, true,false,bStopOld);
        }
        if (bStop) {
            this.LayerItems.Node_Lines_ex.stopAllActions();
        }
    }

    /**设置单线显隐 */
    setOneLineVisile(data: WinLinesInfo, bVisible: boolean, hideAllPrizeIconAni: boolean = true, playAllPrizeAni: boolean = false,bStopOld: boolean = true) {
        let nViewLine = data.nLine;

        if (nViewLine && nViewLine <= Constant.MAX_LINES) {
            let lineNumNode = this.LayerItems.Node_Lines_ex.getChildByName(`Reward_Line_${nViewLine}`);
            if (lineNumNode) {
                lineNumNode.active = bVisible;
            }
        }

        //切换多条线时，隐藏当前所有中奖后重新显示对应中奖图标动画
        if (hideAllPrizeIconAni) {
            this.hideAllPrizeIconAni();
        }
        if (data.prizeIconList && data.prizeIconList.length > 0) {
            let count = 0;
            let prizeIndexCount = 0;//中奖图标索引累加
            for (let nRow = 0; nRow < Constant.ROW_MAX; nRow++) {
                for (let nCol = 0; nCol < Constant.COL_SHOW; nCol++) {
                    count += 1;
                    if (nRow > 0 && nRow < Constant.ROW_MAX - 1) {
                        let subitem = this._gameCore.machineElement[nRow + 1][nCol];
                        let iconNode = subitem.node.getChildByName('icon');
                        let nType = subitem.nType;
                        //Constant.COL_SHOW + data.prizeIconList[prizeIndexCount] 第一行隐藏5个图标 中奖索引要加上之前图标
                        if (prizeIndexCount < data.prizeIconList.length && data.prizeIconList[prizeIndexCount] && count == Constant.COL_SHOW + data.prizeIconList[prizeIndexCount]) {
                            !subitem.isShowLongAnim && (subitem.node.getChildByName('prizeLightSpin').active = true);
                            iconNode.active = false;
                            if (!Common.isNull(nType) && !subitem.isShowLongAnim) { //显示了龙动画时，不播龙所在对应图标中奖动画
                                if(nType == ICON_TYPE.NUM_9 || nType == ICON_TYPE.NUM_6){ //猫 乌鸦
                                    let name = 'prize_mao';
                                    nType == ICON_TYPE.NUM_6 && (name = 'prize_waya');
                                    let ani = subitem.node.getChildByName(name).getComponent(cc.Animation); 
                                    ani.node.active = true;
                                    ani.stop();
                                    let animState = ani.play();
                                    animState.wrapMode = cc.WrapMode.Normal;
                                    ani.on('finished', (() => {
                                        animState = ani.play('animation2');
                                        animState.wrapMode = cc.WrapMode.Loop;
                                    }))
                                }
                                else if(nType == ICON_TYPE.NUM_7 || nType == ICON_TYPE.NUM_WILD || nType == ICON_TYPE.NUM_10){//宝石 宝箱 wild
                                    let list = {7:'baoshi',0:'yaoshi',10:'baoxiang'};
                                    let ani_spin = subitem.node.getChildByName('icon_spin').getComponent(sp.Skeleton); 
                                    ani_spin.node.active = true;
                                    ani_spin.setAnimation(0,list[nType],true);
                                }
                                else{//其它图标中奖
                                    let ani = subitem.node.getChildByName('prize_other'); 
                                    cc.find('Node_anim/Sprite_icon',ani).getComponent(cc.Sprite).spriteFrame = this._gameView.mainUiAtlas.getSpriteFrame(`slotcat_ui_icon${nType}`);
                                    ani.active = true;
                                }
                            }
                            prizeIndexCount += 1
                        }
                        else {
                            if (!playAllPrizeAni) { //playAllPrizeAni 播放多组线时，不停其它线动画
                                !subitem.isShowLongAnim && (iconNode.active = true);
                                subitem.node.getChildByName('prize_mao').getComponent(cc.Animation).pause(); 
                                subitem.node.getChildByName('prize_mao').active = false;
                                subitem.node.getChildByName('prize_waya').getComponent(cc.Animation).pause(); 
                                subitem.node.getChildByName('prize_waya').active = false;
                                subitem.node.getChildByName('icon_spin').active = false;
                                subitem.node.getChildByName('prize_other').active = false;
                            }
                        }
                    }
                }
            }
        }
    }

    //隐藏所有图标中奖动画
    hideAllPrizeIconAni(bHidePrize7Ani: boolean = false) {
        this._gameCore.machineElement.forEach((item) => {
            item.forEach((subitem) => {
                subitem.node.getChildByName('prizeLightSpin').active = false;
                if(bHidePrize7Ani){
                    subitem.node.getChildByName('prize_mao').getComponent(cc.Animation).pause(); 
                    subitem.node.getChildByName('prize_mao').active = false;
                    subitem.node.getChildByName('prize_waya').getComponent(cc.Animation).pause(); 
                    subitem.node.getChildByName('prize_waya').active = false;
                    subitem.node.getChildByName('prize_other').active = false;
                    subitem.node.getChildByName('icon_spin').active = false;
                    subitem.node.getChildByName('icon').active = true;
                }
            })
        })
        if(bHidePrize7Ani){
            for(let i = 0;i < 5;i++){
                let longNode = this.LayerItems[`prize_long${i}`].getChildByName('longNode');
                if(!!longNode){
                    longNode.stopAllActions();
                    longNode.active = false;
                }
            }
        }                    
    }

    /**播放赢金动画 */
    playWinAnim(callback: Function = null) {
        let anim: cc.Prefab;
        let animName: string = '';
        let resultData = this._gameCore.resultData;
        let goldNum = resultData.winmoney;
        let soundName = '';
        let delayTime = 2;
        this._gameCore.btnTotalConfimCallback = null;
        
        if(this._gameCore.nLastFreeTimes == 0 && this._gameCore.nSpinState == SpinStateFruitMachine.Free) {
            anim = this._gameView.totalwinPrefab;
            goldNum = this._gameCore.freeWinTotalNum;
            soundName = 'res/sounds/freeTotalWin'
            animName = 'totalwinPrefab'
            delayTime = 5;
            if(goldNum <= 0){
                callback && callback();
                return;
            }
        }
        else {
            if(this._gameCore.resultData.jackpotcash > 0){//有jackpot中奖
                anim = this._gameView.jackpotPrefab;
                soundName = `res/sounds/jackpot`;
                animName = 'jackpotPrefab';
                delayTime = 3;
                AudioHelper.instance.stopEffectByPath(`res/sounds/jackpot`)
            }
            else{
                if (resultData.multgoldnum < 2 || this._gameCore.nLastFreeTimes > 0) {
                    callback && callback();
                    return;
                }
                AudioHelper.instance.playEffect(`res/sounds/win`);
                if (resultData.multgoldnum < 5) {
                    anim = this._gameView.bigwinPrefab;
                    soundName = `res/sounds/smallWin`;
                    animName = 'bigwinPrefab';
                } else if (resultData.multgoldnum < 20) {
                    anim = this._gameView.megawinPrefab;
                    animName = 'megawinPrefab';
                    soundName = `res/sounds/midWin`
                } else if (resultData.multgoldnum < 40) {
                    anim = this._gameView.epicwinPrefab;
                    animName = 'epicwinPrefab';
                    soundName = `res/sounds/bigWin`
                } else {
                    anim = this._gameView.superwinPrefab;
                    animName = 'superwinPrefab';
                    soundName = `res/sounds/superWin`
                }
            }
            
        }
        this.scheduleOnce(() => {
            AudioHelper.instance.playEffect(soundName);
        }, 0.02)

        this.LayerItems.animNode.children.forEach(item=>item.active = false);
        this.LayerItems.Panel_win.active = true;
        let animPrefab = this.LayerItems.animNode.getChildByName(animName);
        if(!animPrefab){
            animPrefab = cc.instantiate(anim);
            this.LayerItems.animNode.addChild(animPrefab);
            animPrefab.name = animName;
        }
        animName == 'totalwinPrefab' && (this._gameCore.btnTotalConfimCallback = callback);//免费点击确认

        animPrefab.active = true;
        
        let win_num = null;
        if(this._gameCore.resultData.jackpotcash > 0){//有jackpot中奖
            win_num = cc.find('Sprite62/win_num_prefab',animPrefab).getComponent(cc.Label);
            let ani =  animPrefab.getComponent(cc.Animation);
            ani.stop();
            let animState = ani.play('animation0');
            animState.wrapMode = cc.WrapMode.Normal;
            ani.on('finished',(()=>{
                let animState1 = ani.play('animation1');
                animState1.wrapMode = cc.WrapMode.Loop;
            }), this);
        }
        else{
            win_num = cc.find('Node1/Sprite10/win_num_prefab',animPrefab).getComponent(cc.Label);
            animPrefab.getComponent(cc.Animation).play(); 
        }
        
        win_num.string = '0.00';
  
        let self = this;
        this.scheduleOnce(() => {
            self._gameView.scrollNumber({
                txt: win_num,
                began: 0,
                end: goldNum,
                format: (nValue, dataEx) => {
                    win_num.string = `${self._gameView.moneyFormat(nValue, 2)}`
                },
                callback: () => {
                    win_num.node.stopAllActions();
                    self._gameView.nodeRunActionOncs(win_num.node, () => {
                        let show = animPrefab.active;
                        self.LayerItems.Panel_win.active = false;
                        animPrefab.active = false;
                        show && callback && callback(); //total界面时，主动点击关闭了 不在此回调
                    }, delayTime);
                },
            });
        }, 0.5);
    }   
    
    /**刷新赢金数字 */
    updateWinNum(callback: Function = null) {
        let self = this;
        //先隐藏所有
        this.LayerItems.Node_win_good_luck.active = false;
        this.LayerItems.Node_win_normal.active = false;
        this.LayerItems.Node_win_free_total.active = false;

        //依据结算信息刷新
        let resultData = this._gameCore.resultData;
        if (!resultData) {
            this.LayerItems.Node_win_good_luck.active = true;
        } else {
            //免费
            if (this._gameCore.nSpinState == SpinStateFruitMachine.Free) {
                this.LayerItems.Node_win_free_total.active = true;
                let winNum = this.LayerItems.Node_win_free_total.getChildByName('win_num').getComponent(cc.Label);
                this._gameView.scrollNumber({
                    txt: winNum,
                    began: this._gameCore.lastFreeWinNum,
                    end: this._gameCore.freeWinTotalNum,
                    format: (nValueEx) => {
                        winNum.string = self._gameView.moneyFormat(nValueEx, 2);
                    },
                    callback: () => {
                        callback && callback();
                    }
                });
                this._gameCore.lastFreeWinNum = this._gameCore.freeWinTotalNum;
            }
            //正常
            else {
                if (resultData.winmoney > 0) {
                    this.LayerItems.Node_win_normal.active = true;
                    let winNum = this.LayerItems.Node_win_normal.getChildByName('win_num').getComponent(cc.Label);
                    winNum.string = '0.00';
                    this._gameView.scrollNumber({
                        txt: winNum,
                        nTime: resultData.multgoldnum < 2 ? 0.35 : 1.0,
                        began: 0,
                        end: resultData.winmoney,
                        format: (nValueEx) => {
                            winNum.string = self._gameView.moneyFormat(nValueEx, 2);
                        },
                        callback: () => {
                            this.LayerItems.Node_win_normal.stopAllActions();
                            callback && callback();
                        }
                    });
                } else {
                    this.LayerItems.Node_win_good_luck.active = true;
                    callback && callback();
                }
            }
        }
    }
   
    //动画播放结束
    doFruitMachineEnd() {
        //更新自己余额
        if (this._gameCore.freeTimesEndFlag == FREE_TYPE.end) {//免费完成时累加的总赢金
            this._gameCore.playScoreMoney = this._gameCore.myselfMoney;
            this._gameView.showSelfMoney();
            this._gameCore.freeTimesEndFlag = FREE_TYPE.non;
        }
        else {
            if (this._gameCore.nLastFreeTimes <= 0) {//免费播放时，不直接更新余额
                this._gameCore.playScoreMoney = this._gameCore.myselfMoney;
                this._gameView.showSelfMoney();
            }
        }

        //水果机停止
        this._gameCore.bFruitIsStop = true;
        this._gameView.changeAddOrSubBtnColor();
               
        this.onFruitMachineEnd();
    }

    /**水果机动画完成后回调 */
    onFruitMachineEnd() {
        let resultData = this._gameCore.resultData;

        let self = this;
        let onMachineEnd = () => {
            switch (this._gameCore.nSpinState) {
                case SpinStateFruitMachine.Free: {
                    if (self._gameCore.nLastFreeTimes > 0) {
                        self.scheduleOnce(() => {
                            self._gameView.autoStartFreeGame();
                        }, 1.5)
                    } else {
                        self._gameView.updateSpinState(SpinStateFruitMachine.Normal);
                    }
                    break;
                }
                case SpinStateFruitMachine.Normal: {
                   if (resultData.freetimes > 0) {//是否中了免费
                        self._gameCore.lastAutoSpinState = self._gameCore.bAutoSpin;
                        self._gameView.setSpinAutoState(false);
                        self._gameView.loadingSceneAni(self.playFreeAnim.bind(self))//弹免费次数
                    } else {
                        if (self._gameCore.bAutoSpin) {
                            self._gameView.setSpinAutoState(true);
                            self.scheduleOnce(() => {
                                self._gameView.onClickSpin();
                            }, 1.5)
                        }
                    }
                    break;
                }
            }
        }
        
        onMachineEnd(); 
    }

    //播放免费动画
    playFreeAnim() {
        let self = this;
        AudioHelper.instance.playEffect(`res/sounds/pop_free`);
        self.LayerItems.Panel_free.active = true;
        self.LayerItems.freeAni.active = true;

        let ani = self.LayerItems.Panel_free.getChildByName('freetimesPrefab');
        if(!ani){
            ani = cc.instantiate(self._gameView.freetimesPrefab);
            self.LayerItems.Panel_free.addChild(ani);
            ani.name = 'freetimesPrefab';
        }
        ani.getComponent(cc.Animation).play();

        let numText = cc.find('Node/BitmapFontLabel',ani).getComponent(cc.Label);
        self._gameView.scrollNumber({
            txt: numText,
            began: 0,
            end: self._gameCore.resultData.freetimes,
            nTime: 0.7,
            format: (nValueEx) => {
                numText.string = Common.toInt(nValueEx) + '';
            },
            callback: () => {
                if(!self || !self.LayerItems.Panel_free.active){
                    return;
                }
                self.LayerItems.Panel_free.stopAllActions();
                cc.tween(self.LayerItems.Panel_free)
                    // .call(()=>{
                    //     self.LayerItems.btn_free.active = true;
                    //     self.LayerItems.Free_Text.getComponent(cc.Label).string = self._gameCore.resultData.freetimes + '';
                    // })
                    .delay(2.5)
                    .call(() => {
                        if(!self || !self.LayerItems.Panel_free.active){
                            return;
                        }
                        self._gameView.onClickStartFreeGame(); 
                        self.LayerItems.Panel_free.active = false;
                    })
                    .start()
            }
        });
    }


    /**播放每列结束音效 */
    playColScrollEndAudio(nCol: number) {
        AudioHelper.instance.stopEffectByPath(Sounds.ScrollStart)
        AudioHelper.instance.playEffect(`res/sounds/slotFruitStop${nCol + 1}`);
    }
    
    //拆分相同元素
    splitSameElementCol(arr_, val_, count_, index_) {
        let tdata = []
        for (let i = 0; i < 5; i++) {
            tdata[i] = { val: -1 };
        }
        for (let i = 0; i < arr_.length; i++) {
            for (let j = 0; j < arr_[i].length; j++) {
                if (val_ == arr_[i][j] && 0 > tdata[j].val) {
                    tdata[j].val = val_
                    tdata[j].pos = { row: i, col: j }
                }
            }
        }
        let tret = []
        let tsameArrr = []
        for (let i = 0; i < 5; i++) {
            tsameArrr[i] = []
            for (let j = i; j >= 0; j--) {
                if (0 < tdata[j].val) {
                    if (!index_ || index_ == i) {
                        tsameArrr[i].push(tdata[j])
                    }
                } else {
                    break
                }
            }
            if (count_ <= tsameArrr[i].length) {
                tret.push(tsameArrr[i])
            }
        }
        return tret
    }
}