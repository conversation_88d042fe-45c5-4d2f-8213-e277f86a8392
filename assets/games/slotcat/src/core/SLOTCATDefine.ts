import { LanguageType } from "../../../../script/frame/common/Define";

//////////////////////////////////////////////////////////////////////////////////
// 基础数据
export let Constant = {
    ROW_MAX: 5, //最大显示行数
    ROW_SHOW: 3,//实际可见行数
    COL_SHOW: 5, //实际可见列数
    ELEMENT_MAX_NUM: 11,//11种图标
    MAX_LINES : 9 ,   /**最大线数 */
    BET_RATE_NUM: 9, //下注固定倍率
    customMyselfPos: 4, //处理道具位置是自己时，自定义的一个位置      
    BET_NUM_CONFIG_LIST: [1.8,9,18,90,180,900,1800],//下注金额配置
    DESK_PLAYERS_MAX_NUM : 4,//桌上最大玩家数
    LOCAL_STORAGE_BET_NUM_KEY: 'SLOTCAT_BET_NUM_KEY',//下注本地缓存值
    ICON_IDLE_NAME_LIST : {0:'yaoshi',7:'baoshi',10:'baoxiang'},
}

/**每条线上的水果对应的Y轴位置 */
export const LINE_POS = [
    [2, 2, 2, 2, 2],
    [1, 1, 1, 1, 1],
    [3, 3, 3, 3, 3],
    [1, 2, 3, 2, 1],
    [3, 2, 1, 2, 3],
    [1, 1, 2, 3, 3],
    [3, 3, 2, 1, 1],
    [2, 3, 2, 1, 2],
    [2, 1, 2, 3, 2],
    [1, 2, 2, 2, 1],
    [2, 1, 1, 1, 2],
    [2, 3, 3, 3, 2],
    [3, 2, 2, 2, 3],
    [1, 3, 1, 3, 1],
    [3, 1, 3, 1, 3],
]

/**每条中奖线上的水果对应的Y轴位置  只按中间3行5列显示的 索引按 1~15 */ 
export const LINE_POS_EX = [
    [6, 7, 8, 9, 10],
    [1, 2, 3, 4, 5],
    [11, 12, 13, 14, 15],
    [1, 7, 13, 9, 5],
    [11, 7, 3, 9, 15],
    [1, 2, 8, 14, 15],
    [11, 12, 8, 4, 5],
    [6, 12, 8, 4, 10],
    [6, 2, 8, 14, 10],
]


//服务器图标对应值
//  11种图案类型
export const ICON_TYPE = {
    NUM_WILD:     0,   //WILD字
    NUM_1:       1,   
    NUM_2:          2,   
    NUM_3:          3,  
    NUM_4:           4,  
    NUM_5:      5,   
    NUM_6:      6,   
    NUM_7:      7,   
    NUM_8:       8,   
    NUM_9:     9,  
    NUM_10:        10,  
}


//免费游戏状态
export const FREE_TYPE = {
    non:-1,//非免费
    playing:1, //进行中
    end:2, //已结束
}


// 游戏协议
export let Protos = {
    CS_SLOTCAT_GAMESTART_P : 1000,		//游戏开始
	SC_SLOTCAT_GAMESTART_P : 1001,				//游戏开始结果
	SC_SLOTCAT_FREEGAME_P : 1002,				//免费游戏结果
	CS_SLOTCAT_JPLIST_P: 1003,				//Jackpot中奖列表
	SC_SLOTCAT_JPLIST_P: 1004,				//Jackpot中奖列表
	SC_SLOTCAT_JACKPOT_P: 1005,				//同步Jackpot分数
	SC_SLOTCAT_JPAWARD_P: 1006,				//Jackpot中奖通知
    SC_SLOTCAT_PLAYERLIST_P:1007,          //广播桌面玩家列表
    SC_SLOTCAT_GAMERESULT_P:1008,         //广播桌面玩家游戏结果
    SC_SLOTCAT_TASKINFO_P:1009,					//通知任务信息
	CS_SLOTCAT_GETTASK_P:1010,					//领取任务奖励
	SC_SLOTCAT_GETTASK_P:1011,					//领取任务奖励
}

let GameTextEnglish = {
    GOLD_BET_ERR:"Insufficient coins, bet failed",
    QUIT_TIPS:"You have started the game. If you exit the game, the system will automatically help you manage it. It will not affect the gold coin settlement. Are you sure to exit the game?",

    Text_task_open: "cumulative collection",
    Text_task_tip: "Total bet {0} unlock",

    Multiples:"Multiples",
    Lines:"Lines",
    Rules:"Rules",
    Jackpot:"Jackpot",
    Text_multiples_wild:"WILD symbol can substitute for any\nsymbols except free and jackpot",
    Text_multiples_free:"3 same for 5 free spins\n4 same for 10 free spins\n5 same for 15 free spins",
    Text_multiples_jackpot:"You can get a different percenttage of the JACKPOT",
    Text_lines:"1. Bet on 1 line and only get 1 line reward" + "\n" +
        "2. Starting from the staring point, the same symbol can be awarded successively",
    Text_rule:"In each payline, the symbols crossing from the first row to the third row are same, then you can win. If the same symbol appears on the fourth row or even fifth row, you can win more.",
    Text_rule_1:"WILD symbol can substitute for any symbols",
    Text_rule_2:"Same symbols",
    Text_rule_3:"Different symbols",
    Text_rule_4:"Different symbols",
    Text_jackpot:"The single bet reaches ₹10 and the same symbol is the jackpot \n You get the JACKPOT cash",
    NumberOfSymbols:"Number of\nsymbols",
    SingleLineBet:"Single line\nbet",
    Text_get:"Get",
    Rewards:"Rewards",
    SingleBet:"SingleBet",
    Type:"Type",
    Winner:"Winner",
    Time:"Time",
    RulesOfTheJackpot:"Rules of the jackpot",
}

let GameTextIndia = {
    GOLD_BET_ERR:"सिक्के कम हैं, दांव असफल रहा",
    QUIT_TIPS:"आपने खेल शुरू कर दिया है. यदि आप गेम से बाहर निकलते हैं, तो सिस्टम स्वचालित रूप से इसे प्रबंधित करने में आपकी सहायता करेगा। इसका सोने के सिक्कों के निपटान पर कोई असर नहीं पड़ेगा. क्या आप निश्चित रूप से खेल से बाहर निकलेंगे?",

    Text_task_open: "संचयी संग्रह",
    Text_task_tip: "कुल निवेश की आवश्यकता है {0}",

    Multiples:"विभिन्न",
    Lines:"पंक्तियां",
    Rules:"नियमों",
    Jackpot:"जैकपोट",
    Text_multiples_wild:"जंगली प्रतीक मुफ्त और जैकपॉट को छोड़कर किसी भी प्रतीक के लिए स्थानापन्न कर सकते हैं",
    Text_multiples_free:"5 मुफ़्त स्पिन के लिए 3 समान\n10 मुफ़्त स्पिन के लिए 4 समान\n15 मुफ़्त स्पिन के लिए 5 समान",
    Text_multiples_jackpot:"आप जैकपॉट का एक अलग प्रतिशत प्राप्त कर सकते हैं",
    Text_lines:"1. 1 लाइन पर बेट लगाएं और केवल 1 लाइन इनाम पाएं" + "\n" +
        "2. घूरने के बिंदु से शुरू होकर, एक ही प्रतीक को क्रमिक रूप से दिया जा सकता है",
    Text_rule:"प्रत्येक पेलाइन में, पहली पंक्ति से तीसरी पंक्ति तक जाने वाले प्रतीक समान हैं, तो आप जीत सकते हैं। यदि वही प्रतीक चौथी पंक्ति या पाँचवीं पंक्ति पर भी दिखाई देता है, तो आप अधिक जीत सकते हैं।",
    Text_rule_1:"जंगली प्रतीक किसी भी प्रतीक के लिए स्थानापन्न कर सकता है",
    Text_rule_2:"वही प्रतीक",
    Text_rule_3:"विभिन्न प्रतीक",
    Text_rule_4:"विभिन्न प्रतीक",
    Text_jackpot:"सिंगल बेट ₹10 तक पहुंचती है और जैकपॉट एक ही प्रतीक है \n आपको जैकपॉट नकद मिलता है",
    NumberOfSymbols:"प्रतीकों की\nसंख्या",
    SingleLineBet:"सिंगल लाइन\nबेटिंग",
    Text_get:"प्राप्त",
    Rewards:"बक्शीश",
    SingleBet:"सिंगल बेट",
    Type:"प्रकार",
    Winner:"विजेता",
    Time:"समय",
    RulesOfTheJackpot:"जैकपॉट के नियम",
}

// 文本内容
export let GameTextTips = GameTextEnglish

export let updateGameTextTips = () => {
    let curLanguage = cc.sys.localStorage.getItem('curLanguage') || 1;
	if (curLanguage == LanguageType.CHINESE) {
	//   GameTextTips = GameTextCH;
	} else if (curLanguage == LanguageType.ENGLISH) {
		GameTextTips = GameTextEnglish;
	} else if (curLanguage == LanguageType.INDIA) {
		GameTextTips = GameTextIndia;
	}
}


// 背景、音效文件名
export let Sounds = {
    BG_MUSIC: "res/sounds/bg",

    Jetton:  "res/sounds/jetton",
    ScrollStart:  "res/sounds/scrollStart",
    Lianxian:  "res/sounds/lianxian",
    SPIN: "res/sounds/spin", 
    GET_FREE: "res/sounds/get_free",
    GET_WILD: "res/sounds/get_wild",
    JACKPOT: "res/sounds/jackpot",
}

export const GameEvent = {
    JACKPOT_RECORD_INFO:"JACKPOT_RECORD_INFO",//jackpot记录
    HALL_SWITCH_LANGUAGE: "HALL_SWITCH_LANGUAGE",//切换多语言
    POP_JACKPOT_RECORD_LAYER:"POP_JACKPOT_RECORD_LAYER",//关闭jackpot帮助后重新弹出jackpotRecord界面
    JACKPOT_RECORD: "JACKPOT_RECORD",//record界面jackpot
    ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE: "ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE", //充值到账领取
    FREE_TOTAL_CONFIM_CLOSE: 'FREE_TOTAL_CONFIM_CLOSE',//免费total界面关闭
    FREE_CONFIM_CLOSE: 'FREE_CONFIM_CLOSE',//免费界面关闭
} 

//jackpot奖池
export const JACKPOT_TYPE = {
    LEFT:2, //最左边奖池
    RIGHT:1,//最右边奖池
    CENTER:0,//中间奖池
} 

//////////////////////////////////////////////////////////////////////////////////
