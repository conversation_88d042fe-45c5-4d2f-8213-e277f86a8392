import Common from "../../../../script/frame/common/Common";
import EventManager from "../../../../script/frame/manager/EventManager";
import { GameEvent } from "./SLOTCATDefine";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slotcat/SLOTCATGameLogic')
export default class SLOTCATGameLogic extends cc.Component {
    //////////////////////////////////////////////////////////////////////////////
   
    //弹出效果
    popUpEffect(node: cc.Node,isOpen: boolean = false,callback: Function = null){

        if(!node){
            return;
        }
        let maskLayer = node.getChildByName('mask');
        node.stopAllActions();
        if(isOpen){
            if(!!maskLayer){
                maskLayer.opacity = 0;
            }
            node.active = true;
            node.scale = 0;  
            node.opacity = 0;
            cc.tween(node)
            .parallel(
                cc.tween().to(0.15,{scale: 0.75},{ easing: 'backIn' }),
                cc.tween().to(0.15,{opacity: 30})
            )
            .parallel(
                cc.tween().by(0.05,{opacity: 225}),
                cc.tween().by(0.05,{scale: 0.25},{ easing: 'backOut' })
            )
            .call(()=>{
                if(!!maskLayer){
                    cc.tween(maskLayer)
                    .to(0.2,{opacity: 153})
                    .start()
                }
                callback && callback();
            })
            .start();
        }
        else{
            if(!!maskLayer){
                cc.tween(maskLayer)
                .to(0.05,{opacity: 0})
                .start()
            }

            cc.tween(node)
            .to(0.2,{scale: 0},{ easing: 'backIn' })
            .call(()=>{
                callback && callback();
                node.active = false;
                // node.removeFromParent();
            })
            .start();
        }
    }

    //total界面点击confim关闭
    onClickTotalConfimBtn(){
        EventManager.instance.emit(GameEvent.FREE_TOTAL_CONFIM_CLOSE); 
    }

    //free界面点击关闭
    onClickFreeConfimBtn(){
        EventManager.instance.emit(GameEvent.FREE_CONFIM_CLOSE); 
    }

    //////////////////////////////////////////////////////////////////////////////

}
