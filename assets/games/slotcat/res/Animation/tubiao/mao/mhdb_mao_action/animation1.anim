{"__type__": "cc.AnimationClip", "_name": "animation1", "_objFlags": 0, "_rawFiles": null, "_duration": 0.21666666666666667, "sample": 60, "speed": 0.4167, "wrapMode": 1, "curveData": {"paths": {"Node1/Sprite1": {"props": {"position": []}}, "Node1/Sprite2": {"props": {"position": [], "scaleX": [], "scaleY": [], "anchorX": [], "anchorY": [], "angle": []}}, "Node1/Sprite3": {"props": {"position": [], "scaleX": [], "scaleY": [], "anchorX": [], "anchorY": [], "angle": []}}, "Node1/Sprite4": {}, "Node1/Node2/Sprite5": {"props": {"position": []}}, "Node1/Node2/Sprite6": {"props": {"position": [], "scaleX": [], "scaleY": []}}, "Node1/Node2": {"props": {"position": [], "opacity": [{"frame": 0.21666666666666667, "value": 0}]}}, "Node1/Sprite20/Sprite7": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [{"frame": 0.21666666666666667, "value": 0}]}}, "Node1/Sprite20/Sprite8": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [{"frame": 0.21666666666666667, "value": 0}], "angle": []}}, "Node1/Sprite20": {"props": {"position": [], "scaleX": [], "scaleY": [], "anchorX": [], "anchorY": [], "angle": []}}, "Node1/Node3/Sprite9": {"props": {"position": [{"frame": 0.05, "value": [35.2615, -45.2307]}, {"frame": 0.06666666666666667, "value": [55.4798, -39.6785]}, {"frame": 0.21666666666666667, "value": [55.4798, -39.6785]}], "scaleX": [{"frame": 0.05, "value": 1}, {"frame": 0.06666666666666667, "value": 1}, {"frame": 0.21666666666666667, "value": 1}], "scaleY": [{"frame": 0.05, "value": 1}, {"frame": 0.06666666666666667, "value": 1}, {"frame": 0.21666666666666667, "value": 1}], "opacity": [{"frame": 0.05, "value": 255}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.21666666666666667, "value": 255}], "angle": [{"frame": 0.05, "value": 0}, {"frame": 0.06666666666666667, "value": 0}, {"frame": 0.21666666666666667, "value": 0}]}}, "Node1/Node3/Sprite10": {"props": {"position": [{"frame": 0.05, "value": [9.7767, -45.2307]}, {"frame": 0.06666666666666667, "value": [19.0926, -39.6783]}, {"frame": 0.21666666666666667, "value": [19.0926, -39.6783]}], "scaleX": [{"frame": 0.05, "value": 1}, {"frame": 0.21666666666666667, "value": 1}], "scaleY": [{"frame": 0.05, "value": 1}, {"frame": 0.21666666666666667, "value": 1}], "opacity": [{"frame": 0.05, "value": 255}, {"frame": 0.21666666666666667, "value": 255}], "angle": [{"frame": 0.05, "value": 0}, {"frame": 0.21666666666666667, "value": 0}]}}, "Node1/Node3/Sprite11": {"props": {"position": [{"frame": 0.05, "value": [-15.9286, -45.2307]}, {"frame": 0.06666666666666667, "value": [-20.8402, -39.6781]}, {"frame": 0.21666666666666667, "value": [-20.8402, -39.6781]}], "scaleX": [{"frame": 0.05, "value": 1}, {"frame": 0.06666666666666667, "value": 1}, {"frame": 0.21666666666666667, "value": 1}], "scaleY": [{"frame": 0.05, "value": 1}, {"frame": 0.06666666666666667, "value": 1}, {"frame": 0.21666666666666667, "value": 1}], "opacity": [{"frame": 0.05, "value": 255}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.21666666666666667, "value": 255}], "angle": [{"frame": 0.05, "value": 0}, {"frame": 0.06666666666666667, "value": 0}, {"frame": 0.21666666666666667, "value": 0}]}}, "Node1/Node3/Sprite12": {"props": {"position": [{"frame": 0.05, "value": [-39.1231, -45.2307]}, {"frame": 0.06666666666666667, "value": [-53.7734, -39.6785]}, {"frame": 0.21666666666666667, "value": [-53.7734, -39.6785]}], "scaleX": [{"frame": 0.05, "value": 1}, {"frame": 0.21666666666666667, "value": 1}], "scaleY": [{"frame": 0.05, "value": 1}, {"frame": 0.21666666666666667, "value": 1}], "opacity": [{"frame": 0.05, "value": 255}, {"frame": 0.21666666666666667, "value": 255}], "angle": [{"frame": 0.05, "value": 0}, {"frame": 0.21666666666666667, "value": 0}]}}, "Node1/Node3/Sprite13": {"props": {"position": [{"frame": 0.06666666666666667, "value": [-53.611, -35.0364]}, {"frame": 0.21666666666666667, "value": [-53.611, -35.0364]}], "scaleX": [{"frame": 0.21666666666666667, "value": 1}], "scaleY": [{"frame": 0.21666666666666667, "value": 1}], "opacity": [{"frame": 0.05, "value": 0}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.21666666666666667, "value": 0}], "angle": [{"frame": 0.21666666666666667, "value": 0}]}}, "Node1/Node3/Sprite14": {"props": {"position": [{"frame": 0.06666666666666667, "value": [-21.0991, -39.3522]}, {"frame": 0.21666666666666667, "value": [-21.0991, -39.3522]}], "scaleX": [{"frame": 0.21666666666666667, "value": 1}], "scaleY": [{"frame": 0.21666666666666667, "value": 1}], "opacity": [{"frame": 0.05, "value": 0}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.21666666666666667, "value": 0}], "angle": [{"frame": 0.21666666666666667, "value": 0}]}}, "Node1/Node3/Sprite15": {"props": {"position": [{"frame": 0.06666666666666667, "value": [19.7951, -39.3215]}], "scaleX": [], "scaleY": [], "opacity": [{"frame": 0.05, "value": 0}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.21666666666666667, "value": 0}], "angle": []}}, "Node1/Node3/Sprite16": {"props": {"position": [{"frame": 0.06666666666666667, "value": [55.1005, -39.1414]}], "scaleX": [], "scaleY": [], "opacity": [{"frame": 0.05, "value": 0}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.21666666666666667, "value": 0}], "angle": []}}, "Node1/Node3/Sprite17": {"props": {"position": [{"frame": 0.06666666666666667, "value": [-0.081, -31.4873]}], "scaleX": [{"frame": 0.06666666666666667, "value": 0.3277}, {"frame": 0.21666666666666667, "value": 0.3524}], "scaleY": [{"frame": 0.06666666666666667, "value": 0.3277}, {"frame": 0.21666666666666667, "value": 0.3277}], "opacity": [{"frame": 0.05, "value": 0}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.21666666666666667, "value": 0}]}}, "Node1/Node3": {"props": {"position": [{"frame": 0.03333333333333333, "value": [0.0001, -7.6589]}, {"frame": 0.06666666666666667, "value": [1.1757, 7.9416]}], "scaleX": [{"frame": 0.03333333333333333, "value": 0.7}, {"frame": 0.06666666666666667, "value": 1.3}], "scaleY": [{"frame": 0.03333333333333333, "value": 0.7}, {"frame": 0.06666666666666667, "value": 1.3}], "angle": [{"frame": 0.03333333333333333, "value": 0}]}}, "Node1/Sprite18": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": []}}, "Node1/Sprite19": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": []}}, "Node1": {"props": {"scaleX": [], "scaleY": [], "angle": []}}}}, "events": []}