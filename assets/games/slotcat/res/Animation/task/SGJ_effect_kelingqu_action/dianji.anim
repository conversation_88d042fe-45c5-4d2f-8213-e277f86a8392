{"__type__": "cc.AnimationClip", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_rawFiles": null, "_duration": 0.2, "sample": 60, "speed": 0.3333, "wrapMode": 1, "curveData": {"paths": {"Sprite_task_bg/Node_light/Node1/Sprite1": {"props": {"position": [], "scaleX": [], "scaleY": [], "angle": []}}, "Sprite_task_bg/Node_light/Node1": {"props": {"position": [], "scaleX": [], "scaleY": [], "angle": []}}, "Sprite_task_bg/Node_light/Node2/Sprite2": {"props": {"position": [], "scaleX": [], "scaleY": [], "angle": []}}, "Sprite_task_bg/Node_light/Node2": {"props": {"position": [], "scaleX": [], "scaleY": []}}, "Sprite_task_bg/Node_task_reward/Image_task_reward": {"props": {"position": [], "scaleX": [], "scaleY": [], "angle": []}}, "Sprite_task_bg": {"props": {"active": [{"frame": 0, "value": true}]}}, "Node3/Sprite_task_collect": {"props": {"active": [], "position": [], "scaleX": [], "scaleY": [], "angle": []}, "comps": {"cc.Sprite": {"spriteFrame": [{"frame": 0.03333333333333333, "value": {"__uuid__": "9fcaf332-c64b-49b5-bccc-b7bd00d49bf3"}}]}}}}}, "events": []}