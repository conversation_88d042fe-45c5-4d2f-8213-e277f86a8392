{"__type__": "cc.AnimationClip", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_native": "", "_duration": 0.23333333333333334, "sample": 60, "speed": 0.3333, "wrapMode": 1, "curveData": {"paths": {"Sprite_task_bg/Node_light/Node1/Sprite1": {"props": {"position": [], "scaleX": [], "scaleY": [], "angle": []}}, "Sprite_task_bg/Node_light/Node1": {"props": {"position": [], "scaleX": [], "scaleY": [], "angle": []}}, "Sprite_task_bg/Node_light/Node2/Sprite2": {"props": {"position": [], "scaleX": [], "scaleY": [], "angle": []}}, "Sprite_task_bg/Node_light/Node2": {"props": {"position": [], "scaleX": [], "scaleY": []}}, "Sprite_task_bg/Node_task_reward/Image_task_reward": {"props": {"position": [], "scaleX": [], "scaleY": [], "angle": []}}, "Sprite_task_bg": {"props": {"active": [{"frame": 0, "value": true}]}}, "Node3/Sprite_task_collect": {"props": {"active": [{"frame": 0.23333333333333334, "value": true}], "position": [], "scaleX": [], "scaleY": [], "angle": [{"frame": 0.23333333333333334, "value": 0}]}}}}, "events": []}