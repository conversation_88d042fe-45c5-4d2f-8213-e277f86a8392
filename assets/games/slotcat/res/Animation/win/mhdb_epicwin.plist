<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>Aperture.png</key>
            <dict>
                <key>frame</key>
                <string>{{570,712},{216,214}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{216,214}}</string>
                <key>sourceSize</key>
                <string>{216,214}</string>
            </dict>
            <key>mgdb_win.png</key>
            <dict>
                <key>frame</key>
                <string>{{604,2},{450,350}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{450,350}}</string>
                <key>sourceSize</key>
                <string>{450,350}</string>
            </dict>
            <key>mhdb_epicwin1.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1062},{168,60}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{168,60}}</string>
                <key>sourceSize</key>
                <string>{168,60}</string>
            </dict>
            <key>mhdb_epicwin2.png</key>
            <dict>
                <key>frame</key>
                <string>{{570,928},{183,140}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{183,140}}</string>
                <key>sourceSize</key>
                <string>{183,140}</string>
            </dict>
            <key>mhdb_epicwin3.png</key>
            <dict>
                <key>frame</key>
                <string>{{788,773},{170,199}}</string>
                <key>offset</key>
                <string>{1,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{5,3},{170,199}}</string>
                <key>sourceSize</key>
                <string>{178,209}</string>
            </dict>
            <key>mhdb_epicwin4.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,826},{418,234}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{418,234}}</string>
                <key>sourceSize</key>
                <string>{418,234}</string>
            </dict>
            <key>mhdb_epicwin5.png</key>
            <dict>
                <key>frame</key>
                <string>{{828,454},{317,177}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{317,177}}</string>
                <key>sourceSize</key>
                <string>{317,177}</string>
            </dict>
            <key>mhdb_epicwin6.png</key>
            <dict>
                <key>frame</key>
                <string>{{437,404},{686,131}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{686,131}}</string>
                <key>sourceSize</key>
                <string>{686,131}</string>
            </dict>
            <key>mhdb_epicwin7_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{600,400}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{600,400}}</string>
                <key>sourceSize</key>
                <string>{600,400}</string>
            </dict>
            <key>tx_new_duanwei_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{570,454},{256,256}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{256,256}}</string>
                <key>sourceSize</key>
                <string>{256,256}</string>
            </dict>
            <key>tx_puyu_guangquan4_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{846,945},{126,126}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{126,126}}</string>
                <key>sourceSize</key>
                <string>{126,126}</string>
            </dict>
            <key>tx_ty_caiguang_00024_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{712,945},{132,190}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{132,190}}</string>
                <key>sourceSize</key>
                <string>{132,190}</string>
            </dict>
            <key>tx_youxichang_wangzha_light03.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,404},{420,433}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{420,433}}</string>
                <key>sourceSize</key>
                <string>{420,433}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>mhdb_epicwin.png</string>
            <key>size</key>
            <string>{1007,1137}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:d1c02d068ed1994e2f5972f8b2b652e6:9f0e88d39bbdad2a1becdb96b8013903:750f7c99facd51ee014eb4514ea2eaf8$</string>
            <key>textureFileName</key>
            <string>mhdb_epicwin.png</string>
        </dict>
    </dict>
</plist>
