<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>btn_stop.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{279,91}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{279,91}}</string>
                <key>sourceSize</key>
                <string>{279,91}</string>
            </dict>
            <key>mhdb_stop1_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,283},{110,44}}</string>
                <key>offset</key>
                <string>{-1,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{9,4},{110,44}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,781},{26,40}}</string>
                <key>offset</key>
                <string>{-43,-2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{9,7},{26,40}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00001.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,685},{38,44}}</string>
                <key>offset</key>
                <string>{-36,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{10,3},{38,44}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00002.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,731},{48,44}}</string>
                <key>offset</key>
                <string>{-31,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{10,3},{48,44}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00003.png</key>
            <dict>
                <key>frame</key>
                <string>{{46,679},{50,44}}</string>
                <key>offset</key>
                <string>{-31,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{9,4},{50,44}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00004.png</key>
            <dict>
                <key>frame</key>
                <string>{{46,547},{70,44}}</string>
                <key>offset</key>
                <string>{-20,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{10,4},{70,44}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00005.png</key>
            <dict>
                <key>frame</key>
                <string>{{48,467},{78,44}}</string>
                <key>offset</key>
                <string>{-16,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{10,4},{78,44}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00006.png</key>
            <dict>
                <key>frame</key>
                <string>{{48,283},{90,44}}</string>
                <key>offset</key>
                <string>{-9,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{11,4},{90,44}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00007.png</key>
            <dict>
                <key>frame</key>
                <string>{{48,375},{90,44}}</string>
                <key>offset</key>
                <string>{-3,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{17,4},{90,44}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00008.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,395},{84,44}}</string>
                <key>offset</key>
                <string>{6,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{29,3},{84,44}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00009.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,481},{80,42}}</string>
                <key>offset</key>
                <string>{13,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{38,4},{80,42}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00010.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,563},{70,42}}</string>
                <key>offset</key>
                <string>{19,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{49,4},{70,42}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00011.png</key>
            <dict>
                <key>frame</key>
                <string>{{46,619},{58,42}}</string>
                <key>offset</key>
                <string>{25,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{61,4},{58,42}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00012.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,635},{48,42}}</string>
                <key>offset</key>
                <string>{30,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{71,4},{48,42}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00013.png</key>
            <dict>
                <key>frame</key>
                <string>{{48,731},{38,42}}</string>
                <key>offset</key>
                <string>{35,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{81,4},{38,42}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
            <key>mhdb_stop_00014.png</key>
            <dict>
                <key>frame</key>
                <string>{{44,781},{26,26}}</string>
                <key>offset</key>
                <string>{41,8}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{93,4},{26,26}}</string>
                <key>sourceSize</key>
                <string>{130,50}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>mhdb_stop.png</string>
            <key>size</key>
            <string>{95,809}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:24d9e82c0a3ef28946a932df81f62601:d06c704b8146f80d4f6ae53f1b0fc7f3:26196ea83e66dca00638579a9dd82402$</string>
            <key>textureFileName</key>
            <string>mhdb_stop.png</string>
        </dict>
    </dict>
</plist>
