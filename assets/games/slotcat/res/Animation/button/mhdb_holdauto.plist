<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>icon_holdauto.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{219,43}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{219,43}}</string>
                <key>sourceSize</key>
                <string>{219,43}</string>
            </dict>
            <key>mhdb_holdauto1_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,2},{216,40}}</string>
                <key>offset</key>
                <string>{-1,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,13},{216,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{762,86},{38,40}}</string>
                <key>offset</key>
                <string>{-90,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,13},{38,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00001.png</key>
            <dict>
                <key>frame</key>
                <string>{{702,86},{58,40}}</string>
                <key>offset</key>
                <string>{-80,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,13},{58,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00002.png</key>
            <dict>
                <key>frame</key>
                <string>{{162,47},{78,40}}</string>
                <key>offset</key>
                <string>{-70,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{6,13},{78,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00003.png</key>
            <dict>
                <key>frame</key>
                <string>{{673,44},{96,40}}</string>
                <key>offset</key>
                <string>{-61,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,13},{96,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00004.png</key>
            <dict>
                <key>frame</key>
                <string>{{338,86},{96,40}}</string>
                <key>offset</key>
                <string>{-60,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{7,13},{96,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00005.png</key>
            <dict>
                <key>frame</key>
                <string>{{533,44},{138,40}}</string>
                <key>offset</key>
                <string>{-39,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{7,13},{138,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00006.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,47},{158,40}}</string>
                <key>offset</key>
                <string>{-29,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{7,13},{158,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00007.png</key>
            <dict>
                <key>frame</key>
                <string>{{441,2},{176,40}}</string>
                <key>offset</key>
                <string>{-18,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{9,12},{176,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00008.png</key>
            <dict>
                <key>frame</key>
                <string>{{619,2},{164,40}}</string>
                <key>offset</key>
                <string>{-3,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{30,12},{164,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00009.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,44},{164,40}}</string>
                <key>offset</key>
                <string>{24,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{57,12},{164,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00010.png</key>
            <dict>
                <key>frame</key>
                <string>{{389,44},{142,40}}</string>
                <key>offset</key>
                <string>{36,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{80,12},{142,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00011.png</key>
            <dict>
                <key>frame</key>
                <string>{{204,86},{132,40}}</string>
                <key>offset</key>
                <string>{41,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{90,13},{132,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00012.png</key>
            <dict>
                <key>frame</key>
                <string>{{436,86},{96,40}}</string>
                <key>offset</key>
                <string>{59,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{126,13},{96,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00013.png</key>
            <dict>
                <key>frame</key>
                <string>{{534,86},{92,40}}</string>
                <key>offset</key>
                <string>{61,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{130,12},{92,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
            <key>mhdb_holdauto_00014.png</key>
            <dict>
                <key>frame</key>
                <string>{{628,86},{72,40}}</string>
                <key>offset</key>
                <string>{71,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{150,12},{72,40}}</string>
                <key>sourceSize</key>
                <string>{230,70}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>mhdb_holdauto.png</string>
            <key>size</key>
            <string>{802,128}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:6fb905113abe8ee3ad3ffa1d84181934:28473fad71f4d634514b5b0f421cc725:d9bf3b32683a3332a0682293f72dea10$</string>
            <key>textureFileName</key>
            <string>mhdb_holdauto.png</string>
        </dict>
    </dict>
</plist>
