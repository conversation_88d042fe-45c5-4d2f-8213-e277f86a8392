import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import { CAR } from "../core/CARDefine";
import CARGameCore from "../core/CARGameCore";
import CARGameViewLayer from "./CARGameViewLayer";

export interface playInfo {
    node?: cc.Node,       // 所在节点
    pos?: cc.Vec2,         // 所在坐标, 不传默认为cc.v2(0, 0) 索引0~5:在座玩家 6:自己 7:其它玩家
    head?: cc.Sprite,     //头像图片
    hotSpinNode?: cc.Node,  //头像火焰spin节点
    nickName?: cc.Label,  //玩家昵称
    goldNum?: cc.Label,  //玩家金币
    winFlyGoldNode?: cc.Node, //玩家飘分节点
    winFlyGold?: cc.Label, //玩家飘分
    winFlyOrgPosY?: number, //飘分原始Y位置
    chairid?: number,   //本地座位号 
    playerid?: number,    //玩家Id
    money?: number,     //身上金币
    isMove?: boolean,    //头像是否移动
    node_chat?: cc.Node, //聊天
    node_exp?: cc.Node, //表情
}

const { ccclass, property, disallowMultiple, menu } = cc._decorator;

@ccclass
@disallowMultiple()
@menu('car/CARPlayerInfoLayer')
export default class CARPlayerInfoLayer extends BaseLayer {

    //在座玩家信息
    @property(cc.Node)
    node_player: cc.Node = null;

    //自己信息
    @property(cc.Node)
    self_player: cc.Node = null;

    //其它玩家信息
    @property(cc.Node)
    other_player: cc.Node = null;

    //在线其它玩家数
    @property(cc.Label)
    other_players_num: cc.Label = null;

    //玩家飘分节点
    @property(cc.Node)
    player_win_num_node: cc.Node = null;

    //聊天表情
    @property(cc.Node)
    chatNode: cc.Node = null;
    

    /** 游戏视图对象 */
    _gameView: CARGameViewLayer = null;
    /** 游戏核心对象 */
    _gameCore: CARGameCore = null;
    //玩家信息
    _playerInfo: Array<playInfo> = [];
    //玩家列表
    _allUsers = [];
    //自家信息
    _MyInfo: playInfo = null;
    //在线玩家信息
    _onLineInfo: playInfo = null;

    onLoad() {
        this._gameCore = this.node.getComponent("CARGameCore");
        this._gameView = this.node.getComponent("CARGameViewLayer");
        this.initPlayerNode();
    }

    //初始化头像信息
    initPlayerNode() {
        //在座6个玩家
        this.node_player.children.forEach((item,index) => {
            this.fillPlayerInfo(item,index,);
        });
        //自己
        this.fillPlayerInfo(this.self_player,CAR.CUSTOM_MYSELF_POS);
       
        //在线其它玩家
        this.fillPlayerInfo(this.other_player,CAR.CUSTOM_OTHER_POS, true);
    }

    //填充玩家信息
    fillPlayerInfo(playerNode: cc.Node,chairid: number,otherPlayerFlag: boolean = false) {
        let playInfo = <playInfo>{};
        playInfo.node = playerNode;
        playInfo.pos = cc.v2(playerNode.x, playerNode.y)
        if (!otherPlayerFlag) {//其它玩家没有这些信息
            playInfo.head = playerNode.getChildByName('nodeMask').getChildByName('head_icon').getComponent(cc.Sprite);
            playInfo.hotSpinNode = playerNode.getChildByName('hot_ani');
            playInfo.nickName = playerNode.getChildByName('player_name').getComponent(cc.Label);
            playInfo.goldNum = playerNode.getChildByName('player_gold_num').getComponent(cc.Label);
            let player_win_num = this.player_win_num_node.getChildByName(`player_win_num${chairid}`);
            playInfo.winFlyGold = player_win_num.getChildByName('Node_1').getChildByName('num').getComponent(cc.Label);
            playInfo.winFlyGoldNode = player_win_num;
            playInfo.winFlyOrgPosY = player_win_num.y;
            playInfo.chairid = chairid;
            playInfo.isMove = false;
            playInfo.node_chat = this.chatNode.getChildByName("chatNode" + chairid).getChildByName('node_chat');
            playInfo.node_exp = this.chatNode.getChildByName("chatNode" + chairid).getChildByName('node_exp');
        }
        this._playerInfo.push(playInfo);
        if(chairid == CAR.CUSTOM_MYSELF_POS){
            this._MyInfo = playInfo;
        }
        if(chairid == CAR.CUSTOM_OTHER_POS){
            this._onLineInfo = playInfo;
        }
    }

    start() {
        this.resetData();
        this.initSelfInfo();
    }

    //重置数据
    resetData() {
        this.setPlayerNodeToOrg();
    }

    //初始化自己信息
    initSelfInfo() {
        let userInfo = this._gameCore.userInfo;
        if(userInfo){
            this._MyInfo.money = userInfo.money;
            super.setPlayerHead(this._MyInfo.head, userInfo.headid, userInfo.wxheadurl);
            this._MyInfo.nickName.getComponent(cc.Label).string = Common.textClamp(userInfo.nickname);
            this._MyInfo.goldNum.string = this._gameView.moneyFormat(this._MyInfo.money,2);
            this._MyInfo.playerid = userInfo.playerid;
        }
    }

    //设置在线其它玩家数
    setOtherPlayerNum(num: number) {
        this.other_players_num.string = num.toString();
    }

    //设置玩家原始状态
    setPlayerNodeToOrg() {
        this._playerInfo.forEach(item => {
            if (item.winFlyGoldNode) {
                item.winFlyGoldNode.stopAllActions();
                item.winFlyGoldNode.active = false;
                item.winFlyGoldNode.y = item.winFlyOrgPosY;
            }
            item.hotSpinNode && (item.hotSpinNode.active = false);
            item.node_chat && (item.node_chat.stopAllActions());
            item.node_chat && (item.node_chat.active = false);
        });
    }

    //显示火焰动画
    showPlayerHotAni(playerIndex: number, show: boolean = true) {
        if (this._playerInfo[playerIndex] && this._playerInfo[playerIndex].hotSpinNode) {
            this._playerInfo[playerIndex].hotSpinNode.active = show;
        }
    }

    //更新玩家信息
    updatePlayerInfo(playerIndex: number, nickName: string, goldNum: number, playerId: number, headid?: number, headUrl?: string) {
        if (this._playerInfo[playerIndex]) {
            super.setPlayerHead(this._playerInfo[playerIndex].head, headid, headUrl);
            this._playerInfo[playerIndex].nickName.string = Common.textClamp(nickName, 10, "...");
            this._playerInfo[playerIndex].goldNum.string = `/${this._gameView.moneyFormat(goldNum)}`;
            this._playerInfo[playerIndex].playerid = playerId;
        }
    }

    //更新玩家金币
    updatePlayerMoney(playerIndex: number, goldNum: number) {
        if (this._playerInfo[playerIndex]) {
            this._playerInfo[playerIndex].goldNum.string = `/${this._gameView.moneyFormat(goldNum)}`;
        }
    }

    //赢家飘分
    flyWinGoldAni(playerIndex: number, winGold: number) {
        if (!this._playerInfo[playerIndex].winFlyGoldNode) {
            return;
        }
        let player = this._playerInfo[playerIndex];
        let node = player.winFlyGoldNode;
        node.active = true;
        node.opacity = 255;
        player.winFlyGold.string = `+${this._gameView.moneyFormat(winGold)}`;
        node.getComponent(cc.Animation).play();
        node.on('finished',(()=>{
            node.active = false;
        }), this);
        // node.y = this._playerInfo[playerIndex].winFlyOrgPosY;
        // node.stopAllActions();
        // node.getChildByName('player_num_text').getComponent(cc.Label).string = `+${winGold}`;
        // cc.tween(node)
        //     .to(0.4, { position: cc.v3(node.x, this._playerInfo[playerIndex].winFlyOrgPosY + CAR.FLY_WIN_GOLD_POS_ADD_Y) })
        //     .delay(2).call(function () {
        //         node.active = false;
        //     })
        //     .start();
    }

    onSaveUserInfo(playerlist: any) {
        for (let i in playerlist) {
            this._allUsers[playerlist[i].playerid] = playerlist[i];
        }
        //--请求玩家列表
        this._gameCore.requestAllPlayerList();
    }
    //玩家进入
    onUserEnter(info: any) {
        !this._allUsers[info.playerid] && (this._allUsers[info.playerid] = info);
    }
    //玩家离开
    onUserLeave(info: any) {
        this._allUsers[info.playerid] && (this._allUsers[info.playerid] == null);
    }

    //玩家下注同步玩家数据
    onUpdateUserBetUserInfo(msg: any) {
        let val = this._allUsers[msg.playerid];
        if (val) {
            val.money = msg.chouma;
        }
    }

    //更新玩家金币
    updateUserScore(msg: any) {
        let userInfo = this._allUsers[msg.playerid];
        if (userInfo) {
            userInfo.coin = msg.coin;
            userInfo.money = msg.coin;
        }
    }

    //修改在线用户数据
    onUpdateUserData(msg: any) {
        if (!this._gameView._clickOtherLinePlayerLayer && !!msg && !!msg.betrank) {
            for (let key in msg.betrank) {
                if (msg.betrank[key]) {
                    let player = msg.betrank[key];
                    let user = this._allUsers[player.playerid];
                    if (user) {
                        user.money = player.coin && player.coin || player.playercoin;
                        user.headid = player.headid && player.headid || user.headid;
                        user.nickname = player.name && player.name || user.nickname;
                    }
                }
            }
        }
    }
    
    //初始化在线玩家数据
    initPlayerListData(info) {
        //--保存前六个玩家显示到桌子上
        let deskPlayerList = [];
        for (let i = 0; i < 6; i++) {
            if(!!info.betrank){
                deskPlayerList.push(info.betrank[i + 1])
            }
        }

        if (this._gameCore._isGameEnd || !this._gameView._clickOtherLinePlayerLayer) {
            this.onUpdateDeskUserInfo(deskPlayerList);
            this._gameCore._isGameEnd && (this._gameCore._isGameEnd = false);
            return;
        }
        
        this._gameCore._playerListData = info;
    }

    //修改桌位玩家数据
    onUpdateDeskUserInfo(info: any) {
        for (let i = 0; i < CAR.CUSTOM_MYSELF_POS; i++) {//只处理桌上玩家数据
            let playerNode = this._playerInfo[i];
            let key = i;
            let player = info[key];
            let userInfo = this._allUsers[player?.playerid];
            if (playerNode && userInfo && player && player.playerid > 0) {
                super.setPlayerHead(playerNode.head, player.headid, userInfo.wxheadurl || "");
                let decimals = 0;
                if(player.playerid == this._MyInfo.playerid){
                    decimals = 2;
                }
                playerNode.goldNum.string = '₹ ' + this._gameView.moneyFormat(player.coin,decimals);
                playerNode.playerid = player.playerid;
                playerNode.nickName.getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");
            } else {
                if(playerNode){
                    playerNode.nickName.string = ''
                    playerNode.goldNum.string = ''
                    playerNode.head.spriteFrame = null;
                    playerNode.playerid = null;
                    playerNode.money = 0;
                    playerNode.hotSpinNode.active = false;
                    playerNode.winFlyGoldNode.active = false;
                } 
            }
        }
    }

    //更新余额
    updateMyScore(score: number,isUpdateUserInfo: boolean = false,initFlag: boolean = false) {
        this._MyInfo.money = score;
        isUpdateUserInfo && (this._gameCore.userInfo.money = score);
        this._MyInfo.goldNum.string = this._gameView.moneyFormat(score,2);
        if (!initFlag && this._gameCore._gameState == CAR.GameState.Bet) {
            this._gameView.autoSelectChip();
        }
    }

    showChat(type, content,sendLocalSeatId) {
        let self = this;

        if (type == 'text') {
            this._playerInfo[sendLocalSeatId].node_chat.active = true;
            this._playerInfo[sendLocalSeatId].node_chat.getChildByName('chat_text').getComponent(cc.Label).string = content;
            this._playerInfo[sendLocalSeatId].node_chat.stopAllActions();
            cc.tween(this._playerInfo[sendLocalSeatId].node_chat)
                .delay(3)
                .call(function () {
                    if (cc.isValid(self._playerInfo[sendLocalSeatId].node_chat)) {
                        self._playerInfo[sendLocalSeatId].node_chat.active = false;
                    }
                })
                .start();
        } else if (type == 'meexpression') {
            if (!content) return;
            this._playerInfo[sendLocalSeatId].node_exp.active = true;
            this._playerInfo[sendLocalSeatId].node_exp.stopAllActions();
            let spExp = this._playerInfo[sendLocalSeatId].node_exp.getChildByName('bq').getComponent(sp.Skeleton);
            spExp.setAnimation(0, content, true);
            cc.tween(this._playerInfo[sendLocalSeatId].node_exp)
                .delay(3)
                .call(function () {
                    if (cc.isValid(self._playerInfo[sendLocalSeatId].node_exp)) {
                        self._playerInfo[sendLocalSeatId].node_exp.active = false;
                    }
                })
                .start();
        }
    }
}
