import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import PopupLayer from "../../../../script/frame/component/PopupLayer";
import Config from "../../../../script/frame/config/Config";
import EventManager from "../../../../script/frame/manager/EventManager";
import CARGameLogic from "../core/CARGameLogic";


const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('car/CARHelpLayer')
export default class CARHelpLayer extends BaseLayer {
    
    _gameLogic :CARGameLogic = null;
    
    onLoad() {
       this._gameLogic = cc.Canvas.instance.getComponent("CARGameLogic"); 
    }

    start() {
        
    }

    public onClickClose() {
        this._gameLogic.popUpEffect(this.node,false);
    }

}
