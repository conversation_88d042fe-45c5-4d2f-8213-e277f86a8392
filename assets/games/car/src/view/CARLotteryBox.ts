import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import { AREA_BEISHU, CAR, CAR_ID_TO_AREA } from "../core/CARDefine";
import CARGameCore, { RESULT_TYPE, resultDatas } from "../core/CARGameCore";
import CARGameViewLayer from "./CARGameViewLayer";


const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('car/CARLotteryBox')
export default class CARLotteryBox extends BaseLayer {

    @property(cc.Node)
    turntableAni: cc.Node = null;

    @property(cc.Node)
    lucky_ani: cc.Node = null;
    

    // 游戏核心对象
    _gameCore: CARGameCore = null;
    _gameView: CARGameViewLayer = null;
    _gameAudio: any = null;
    bgAnim: cc.Animation;
    dialAnim: cc.Animation;
    node3: cc.Node;

    onLoad() {
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)

        this._gameCore = cc.Canvas.instance.getComponent("CARGameCore");
        this._gameView = cc.Canvas.instance.getComponent("CARGameViewLayer");
        this._gameAudio = cc.Canvas.instance.getComponent("CARAudioMng");

        //初始化转盘
        this.initTurntableInfo();

    }

    start() {
        this.bgAnim = this.LayerItems.tx_haiwai_benchibaoma3.getComponent(cc.Animation);
        this.dialAnim = this.LayerItems.tx_haiwai_benchibaoma1.getComponent(cc.Animation);
        this.node3 = this.LayerItems.tx_haiwai_benchibaoma3.getChildByName('Node_3');
        this.stopAnim();
    }

    onDestroy() {

    }

    //重置初始数据
    resetData() {
        this.setBadLuckyVisible(false)
        this.stopResultAnim();
    }

    //初始化转盘
    initTurntableInfo() {
        for (let i = 1; i <= CAR.CAR_COUNT; i++) {
            let item = this.LayerItems.turntable.getChildByName('turntable' + i);
            let dengNode = item.getChildByName('deng'); 
            this._gameCore._dengNodeList.push(dengNode)
            this._gameCore.turntableItemList.push(item);
        }
    }

    //开始游戏
    onGameStart(callback = null) {
        let data = {
            endIndexList: [0,2,10,15],
            // endIndexList: [0,1,2,3,4,5],
            resultType: RESULT_TYPE.ResultType_MultiThree
            // resultType: RESULT_TYPE.ResultType_TranFive
        }
        this.playResultAnim(data,callback);
    }

    playBgAnim() {
        let animState = this.bgAnim.play();
        animState.wrapMode = cc.WrapMode.Loop;
        this.node3.active = true;
    }

    pauseBgAnim() {
        let animState = this.bgAnim.play();
        animState.wrapMode = cc.WrapMode.Normal;
        this.node3.active = false;
    }

    stopBgAnim() {
        this.bgAnim.pause();
        this.node3.active = false;
    }

    playWaitAnim() {
        let animState = this.dialAnim.play("animation0");
        animState.wrapMode = cc.WrapMode.Loop;
    }

    playRunAnim() {
        this.dialAnim.play("animation1");
        this.dialAnim.off('finished');
        let self = this;
        this.dialAnim.on('finished',(()=>{
            let animState =  self.dialAnim.play("animation2");
            animState.wrapMode = cc.WrapMode.Loop;
        }), this);
    }

    playEndAnim() {
        let animState = this.dialAnim.play("animation3");
        animState.wrapMode = cc.WrapMode.Normal;
        let self = this;
        this.dialAnim.off('finished');
        this.dialAnim.on('finished', (()=>{
            self.stopBgAnim();
            self.stopAnim();
        }))
    }

    stopAnim() {
        let animState =this.dialAnim.play("animation0");
        animState.wrapMode = cc.WrapMode.Normal;
        let self = this;
        this.scheduleOnce(()=>{
            self.dialAnim.pause();
        },0.02)
    }

    //跑动动画
    playResultAnim(data: resultDatas,callback = null) {
        this.playBgAnim();
        this.playRunAnim();
        let anim = cc.tween();
        let delayTime = 0;
        let endIndex = this._gameCore._startMoveIndex;
        let info = this.getNormalAnim(endIndex, data.endIndexList[0]);
        anim.then(info.anim);
        delayTime += info.delayTime;
        endIndex = info.endIndex;
        let self = this;
        anim.call(() => {
            self.pauseBgAnim();
            self.playEndAnim();
        })

        let resultAnim = this[`playResultAnim${data.resultType}`];
        if (resultAnim) {
            let resultInfo = resultAnim.call(self, Object.assign({}, info, data))
            anim.then(resultInfo.anim);
            delayTime += resultInfo.delayTime;
            endIndex = resultInfo.endIndex;
        }
        anim.delay(1);
        delayTime += 1;

        anim.tag(100);
        anim.target(this);

        this._gameCore._startMoveIndex = endIndex;

        anim.call(()=>{
            callback && callback();
        });
        anim.start();
        this._gameAudio._loadPlaySFX(`res/audio/carStart`);
    }

    //指定亮灯
    setTurntableItemLight(index, visible, isAnim = false) {
        let dengNode = this._gameCore._dengNodeList[index];
        dengNode.stopAllActions();
        dengNode.opacity = 255;
        if (isAnim) {
            dengNode.active = true;
            if (visible) {
                dengNode.opacity = 0;
                cc.tween(dengNode)
                    .to(0.2, { opacity: 255 })
                    .start()
            } else {
                dengNode.opacity = 255;
                cc.tween(dengNode)
                    .to(0.2, { opacity: 0 })
                    .call(() => {
                        dengNode.active = false;
                    })
                    .start()
            }
        } else {
            dengNode.opacity = 255;
            dengNode.active = visible == true;
        }
    }

    //下一个位置
    nextIndex(curIndex: number, isReverse: boolean = false, offsetIndex: number = 1) {
        if (isReverse) {
            return curIndex - offsetIndex < 0 ? CAR.CAR_COUNT + curIndex - offsetIndex : curIndex - offsetIndex;
        }
        return curIndex + offsetIndex >= CAR.CAR_COUNT ? curIndex + offsetIndex - CAR.CAR_COUNT : curIndex + offsetIndex
    }

    //当前位置
    offsetIndex(curIndex: number, endIndex: number) {
        let offsetIndex = endIndex - curIndex;
        if (offsetIndex < 0) {
            offsetIndex += CAR.CAR_COUNT;
        }
        return offsetIndex;
    }

    /**
     * 缓慢移动
     * @param curIndex 当前位置
     * @param countMove 总共移动距离
     * @param totalTime 耗时
     * @param moveToSlow 是否减速
     */
    getSlowMoveAnim(curIndex: number, countMove: number, totalTime: number, moveToSlow: boolean = false){
        let anim = cc.tween();
        let delayTime = 0;
        let endIndex = curIndex;
        // offsetD 等差时间值
        // 等差数列求等差值 (offsetD + offsetD * countMove) * countMove / 2 = totalTime
        let self = this;
        let offsetD = totalTime * 2 / (1 + countMove) / countMove;
        for (let i = 1; i <= countMove; i++) {
            let animDelayTime = moveToSlow ? offsetD * i : offsetD * (countMove - i + 1)
            anim.delay(animDelayTime);
            anim.call(() => {
                self.setTurntableItemLight(curIndex, false);
                curIndex = self.nextIndex(curIndex);
                self.setTurntableItemLight(curIndex, true);
            })
            delayTime += animDelayTime;
            endIndex = self.nextIndex(endIndex);
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: endIndex,
        }
    }
    /**
     * 快速移动
     * @param curIndex 当前位置
     * @param countMove 总共移动距离
     * @param totalTime 耗时
     * @param isReverse 是否逆时针
     */
    getQuickMoveAnim(curIndex: number, countMove: number, totalTime: number, isReverse: boolean = false) {
        let anim = cc.tween();
        let delayTime = 0;
        let endIndex = curIndex;
        let animDelayTime = totalTime / countMove;
        let self = this;
        for (let i = 1; i <= countMove; i++) {
            anim.delay(animDelayTime);
            anim.call(() => {
                self.setTurntableItemLight(curIndex, false, true);
                curIndex = self.nextIndex(curIndex, isReverse);
                self.setTurntableItemLight(curIndex, true);
            })
            delayTime += animDelayTime;
            endIndex = self.nextIndex(endIndex, isReverse);
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: endIndex,
        }
    }
    /**
     * 正常位移动画
     * @param curIndex 开始位置
     * @param endIndex 结束位置
     * @returns 
     */
    getNormalAnim(curIndex: number, endIndex: number) {
        this.setTurntableItemLight(curIndex, true);
        let anim = cc.tween();
        let delayTime = 0;
        {
            let animInfo = this.getSlowMoveAnim(curIndex, 6, 2, false)
            anim.then(animInfo.anim);
            delayTime += animInfo.delayTime;
            curIndex = animInfo.endIndex;
        }

        let endMoveCount = 10;
        {
            let offsetIndex = this.offsetIndex(curIndex, endIndex);
            let countMove = offsetIndex - endMoveCount;
            countMove += offsetIndex < 12 ? CAR.CAR_COUNT * 5 : CAR.CAR_COUNT * 4
            let animInfo = this.getQuickMoveAnim(curIndex, countMove, 5);
            anim.then(animInfo.anim);
            delayTime += animInfo.delayTime;
            curIndex = animInfo.endIndex;
        }

        {
            let animInfo = this.getSlowMoveAnim(curIndex, endMoveCount, 2.5, true)
            anim.then(animInfo.anim);
            delayTime += animInfo.delayTime;
            curIndex = animInfo.endIndex;
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: endIndex,
        }
    }

    /**
     * 闪烁动画
     * @param index 位置
     * @returns 
     */
    getBlinkAnim(index: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let visible = true;
        let self = this;
        for (let i = 0; i < 6; i++) {
            anim.delay(0.1)
            delayTime += 0.1;
            anim.call(() => {
                visible = !visible;
                self.setTurntableItemLight(index, visible);
            })
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: index,
        }
    }
    /**
     * 打枪动画
     * @param curIndex 
     * @param endIndex 
     */
    getBangAnim(curIndex: number, endIndex: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let offsetIndex = this.offsetIndex(curIndex, endIndex);
        let isReverse = false;
        if (offsetIndex < 12) {
            isReverse = true;
            offsetIndex = CAR.CAR_COUNT - offsetIndex;
        }

        let self = this;
        anim.call(() => {
            self._gameAudio._loadPlaySFX(`res/audio/bang`);
        })

        let animInfo = this.getQuickMoveAnim(curIndex, offsetIndex, 1, isReverse)
        anim.then(animInfo.anim);
        delayTime += animInfo.delayTime;
        curIndex = animInfo.endIndex;

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: endIndex,
        }
    }
    /**
     * 播放中奖特效
     * @param index 位置
     */
    playJiangAnim(index: number) {
        let node = cc.instantiate(this.turntableAni);
        this.LayerItems.turntable.addChild(node);
        let positon = this._gameCore.turntableItemList[`${index}`].position;
        node.position = cc.v3(positon.x + 3,positon.y);
        node.active = true;
        node.getComponent(cc.Animation).play();
        this._gameCore._turntableJiangAnim.push(node);
    }

    /**
     * 播放Lucky中奖特效
     * @param index 位置
     */
    playLuckAnim(index: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let curIndex = index;

        let self = this;
        anim.call(() => {
            let node = cc.instantiate(self.lucky_ani);
            self.LayerItems.turntable.addChild(node);
            let positon = self._gameCore.turntableItemList[`${index}`].position;
            node.position = cc.v3(positon.x,positon.y);
            node.active = true;
            node.getComponent(cc.Animation).play();
            node.getComponent(cc.Animation).off('finished');
            node.getComponent(cc.Animation).on('finished', () => {
                node.active = false;
            }, self);
            self._gameCore._turntableJiangAnim.push(node);
            self._gameAudio._loadPlaySFX(`res/audio/luckStart`);
        })
        anim.delay(1.25);
        delayTime += 1.25;

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: curIndex,
        }
    }

    //停止动画
    stopResultAnim() {
        cc.Tween.stopAllByTag(100);
        this.stopBgAnim();
        this.stopAnim();
        for (let i = 0; i < CAR.CAR_COUNT; i++) {
            this.setTurntableItemLight(i, false)
        }
        this.setTurntableItemLight(this._gameCore._startMoveIndex, true)

        this._gameCore._turntableJiangAnim.forEach(v => {
            v.removeFromParent(true);
        })
        this._gameCore._turntableJiangAnim = [];
    }

    //赢的区域灯光及中间区域倍数
    playAreaWinAnim(areaIndex, addNum) {
        if(Common.isNull(areaIndex)){
            return;
        }
        this._gameCore._areaInfoList[areaIndex].winSameMulti += addNum * this._gameCore._areaInfoList[areaIndex].multi;
        this._gameView.setAreaWinLight(areaIndex);
        this._gameView.setCenterWinBeiShuVisible(areaIndex,true,AREA_BEISHU[areaIndex] * this._gameCore._areaInfoList[areaIndex].winSameMulti);
    }

    //赢的区域右上角翻倍倍数
    playAreaMultiAnim(areaIndex, multi) {
        if(Common.isNull(areaIndex)){
            return;
        }
        this._gameCore._areaInfoList[areaIndex].multi = multi;
        this._gameView.setRightTopWinBeiShuVisible(areaIndex,true,AREA_BEISHU[areaIndex] * multi);
    }
    /**
     * 普遍开奖
     * @param data 
     * @returns 
     */
    playResultAnim0(data) {
        return this.playReward(data.endIndex)
    }
    /**
     * 播放luck 打枪动画
     * @param data 
     * @param bangCount 开枪次数
     * @returns 
     */
    playBangAnim(data, bangCount: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let curIndex = data.endIndex;
        let info = this.playLuckAnim(curIndex);
        anim.then(info.anim);
        delayTime += info.delayTime;
        curIndex = info.endIndex;

        for (let i = 0; i < bangCount; i++) {
            let bangAniminfo = this.getBangAnim(curIndex, data.endIndexList[1 + i])
            anim.then(bangAniminfo.anim);
            delayTime += bangAniminfo.delayTime;
            curIndex = bangAniminfo.endIndex;

            // 打中lucky 吞奖
            if (bangAniminfo.endIndex == 0 && bangCount == 1) {
                let info = this.playBadLuck(curIndex)
                anim.then(info.anim);
                delayTime += info.delayTime;
                curIndex = info.endIndex;
            } else {
                if (i != bangCount) {
                    let self = this;
                    anim.call(() => {
                        self._gameAudio._loadPlaySFX(`res/audio/luckStart`);
                    })
                    let blinkAniminfo = this.getBlinkAnim(curIndex)
                    anim.then(blinkAniminfo.anim);
                    delayTime += blinkAniminfo.delayTime;
                    curIndex = blinkAniminfo.endIndex;
                }
                let info = this.playReward(curIndex)
                anim.then(info.anim);
                delayTime += info.delayTime;
                curIndex = info.endIndex;
            }
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: curIndex,
        }
    }
    //打枪1 具体参照RESULT_TYPE里面的值
    playResultAnim1(data) {
        return this.playBangAnim(data, 1)
    }

    playResultAnim2(data) {
        return this.playBangAnim(data, 2)
    }

    playResultAnim3(data) {
        return this.playBangAnim(data, 3)
    }

    //火车开动
    getTrainStartMoveAnim(curIndex: number, countMove: number, totalTime: number, trainCount: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let endIndex = curIndex;
        let offsetD = totalTime / countMove;
        let self = this;
        for (let i = 1; i <= countMove; i++) {
            let offset = offsetD;
            anim.delay(offset)
            anim.call(() => {
                self.setTurntableItemLight(self.nextIndex(curIndex, true, trainCount - 1), false);
                curIndex = self.nextIndex(curIndex);
                self.setTurntableItemLight(curIndex, true);
                self._gameAudio._loadPlaySFX(`res/audio/trainStart`);
            })
            delayTime += offset;
            endIndex = this.nextIndex(endIndex);
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: endIndex,
        }
    }

    //火车缓速移动
    getTrainSlowMoveAnim(curIndex: number, countMove: number, totalTime: number, trainCount: number, moveToSlow: boolean = false) {
        let anim = cc.tween();
        let delayTime = 0;
        let endIndex = curIndex;
        let offsetD = totalTime * 2 / (1 + countMove) / countMove;
        let self = this;
        for (let i = 1; i <= countMove; i++) {
            let offset = moveToSlow ? offsetD * i : offsetD * (countMove - i + 1)
            anim.delay(offset)
            anim.call(() => {
                self.setTurntableItemLight(self.nextIndex(curIndex, true, trainCount - 1), false);
                curIndex = self.nextIndex(curIndex);
                self.setTurntableItemLight(curIndex, true);
                self._gameAudio._loadPlaySFX(`res/audio/trainStart`);
            })
            delayTime += offset;
            endIndex = this.nextIndex(endIndex);
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: endIndex,
        }
    }

     //火车快速移动
    getTrainQuickMoveAnim(curIndex: number, countMove: number, totalTime: number, trainCount: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let endIndex = curIndex;
        let offsetT = totalTime / countMove;
        let self = this;
        for (let i = 1; i <= countMove; i++) {
            anim.delay(offsetT)
            anim.call(() => {
                self.setTurntableItemLight(self.nextIndex(curIndex, true, trainCount - 1), false, false);
                curIndex = self.nextIndex(curIndex);
                self.setTurntableItemLight(curIndex, true);
                self._gameAudio._loadPlaySFX(`res/audio/trainStart`);
            })
            delayTime += offsetT;
            endIndex = this.nextIndex(endIndex);
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: endIndex,
        }
    }

    //获取火车动画
    getTrainAnim(curIndex: number, endIndex: number, trainCount: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let info = this.getTrainSlowMoveAnim(curIndex, 6, 2, trainCount, false);
        anim.then(info.anim);
        delayTime += info.delayTime;
        curIndex = info.endIndex;

        let endMoveCount = 10;
        let offsetIndex = this.offsetIndex(curIndex, endIndex)
        let countMove = offsetIndex - endMoveCount + (offsetIndex < 12 ? CAR.CAR_COUNT * 2 : CAR.CAR_COUNT * 1);
        {
            let info = this.getTrainQuickMoveAnim(curIndex, countMove, 5, trainCount);
            anim.then(info.anim);
            delayTime += info.delayTime;
            curIndex = info.endIndex;
        }
        {
            let info = this.getTrainSlowMoveAnim(curIndex, endMoveCount, 2.5, trainCount, true);
            anim.then(info.anim);
            delayTime += info.delayTime;
            curIndex = info.endIndex;
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: curIndex,
        }
    }

    //播放火车动画
    playTrainAnim(data, trainCount: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let curIndex = data.endIndex;
        
        {
            let info = this.playLuckAnim(curIndex);
            anim.then(info.anim);
            delayTime += info.delayTime;
            curIndex = info.endIndex;
        }

        {
            let info = this.getTrainStartMoveAnim(curIndex, trainCount, trainCount * 0.5, trainCount);
            anim.then(info.anim);
            delayTime += info.delayTime;
            curIndex = info.endIndex;
        }

        anim.delay(0.5);
        delayTime += 0.5;
        let self = this;
        anim.call(() => {
            self._gameAudio._loadPlaySFX(`res/audio/train`);
        })

        {
            let info = this.getTrainAnim(curIndex, data.endIndexList[1], trainCount);
            anim.then(info.anim);
            delayTime += info.delayTime;
            curIndex = info.endIndex;
        }

        anim.delay(0.4);
        delayTime += 0.4;
        anim.call(() => {
            for (let i = 0; i < trainCount; i++) {
                let index = self.nextIndex(curIndex, true, i);
                self.playJiangAnim(index);
            }
        })

        anim.delay(0.4);
        delayTime += 0.4;
        anim.call(() => {
            for (let i = 0; i < trainCount; i++) {
                let index = self.nextIndex(curIndex, true, i);
                let areaIndex = CAR_ID_TO_AREA[index];
                self.playAreaWinAnim(areaIndex, 1);
            }
        })

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: curIndex,
        }
    }

    playResultAnim4(data) {
        return this.playTrainAnim(data, 2)
    }

    playResultAnim5(data) {
        return this.playTrainAnim(data, 3)
    }

    playResultAnim6(data) {
        return this.playTrainAnim(data, 4)
    }

    playResultAnim7(data) {
        return this.playTrainAnim(data, 5)
    }

    //播放翻倍动画
    playMultiAnim(data, multi: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let curIndex = data.endIndex;

        {
            let info = this.playLuckAnim(curIndex);
            anim.then(info.anim);
            delayTime += info.delayTime;
            curIndex = info.endIndex;
        }
        let self = this;
        anim.call(() => {
            for (let i = 0; i < CAR.CAR_COUNT; i++) {
                let areaIndex = CAR_ID_TO_AREA[i];
                self.playAreaMultiAnim(areaIndex, multi);
            }
            self._gameAudio._loadPlaySFX(`res/audio/fire`);
        })
        anim.delay(1);
        delayTime += 1;

        anim.call(() => {
            self._gameAudio._loadPlaySFX(`res/audio/luckStart`);
        })

        {
            let blinkAniminfo = this.getBlinkAnim(curIndex)
            anim.then(blinkAniminfo.anim);
            delayTime += blinkAniminfo.delayTime;
            curIndex = blinkAniminfo.endIndex;
        }

        {
            let bangAniminfo = this.getBangAnim(curIndex, data.endIndexList[1])
            anim.then(bangAniminfo.anim);
            delayTime += bangAniminfo.delayTime;
            curIndex = bangAniminfo.endIndex;

            // 打中lucky 吞奖
            if (bangAniminfo.endIndex == 0) {
                let info = this.playBadLuck(curIndex)
                anim.then(info.anim);
                delayTime += info.delayTime;
                curIndex = info.endIndex;
            } else {
                anim.delay(0.4);
                delayTime += 0.4;
                anim.call(() => {
                    self.playJiangAnim(bangAniminfo.endIndex);
                });
                anim.delay(0.4);
                delayTime += 0.4;
                anim.call(() => {
                    let areaIndex = CAR_ID_TO_AREA[bangAniminfo.endIndex];
                    self.playAreaWinAnim(areaIndex, 1);
                });
            }
        }

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: curIndex,
        }
    }

    playResultAnim8(data) {
        return this.playMultiAnim(data, 2)
    }

    playResultAnim9(data) {
        return this.playMultiAnim(data, 3)
    }

    /**
     * 播放吞奖
     * @param curIndex 
     * @returns 
     */
    playBadLuck(curIndex: number) {
        let anim = cc.tween();
        let delayTime = 0;
        let self = this;
        anim.call(() => {
            self._gameAudio._loadPlaySFX(`res/audio/luckStart`);
        })
        let blinkAniminfo = this.getBlinkAnim(curIndex)
        anim.then(blinkAniminfo.anim);
        delayTime += blinkAniminfo.delayTime;
        curIndex = blinkAniminfo.endIndex;
        anim.call(() => {
            self._gameAudio._loadPlaySFX(`res/audio/luckEnd`);
        });
        anim.delay(0.4);
        delayTime += 0.4;
        anim.call(() => {
            self.playJiangAnim(curIndex);
        });

        anim.call(() => {
            self.setBadLuckyVisible(true);
            self._gameAudio._loadPlaySFX(`res/audio/badLuck`);
        })
        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: curIndex,
        }
    }
    /**
     * 播放中奖
     * @param curIndex 
     * @returns 
     */
    playReward(curIndex: number) {
        let anim = cc.tween();
        let delayTime = 0;
        anim.delay(0.4);
        delayTime += 0.4;
        let self = this;
        anim.call(() => {
            self.playJiangAnim(curIndex);
        })
        anim.delay(0.4);
        delayTime += 0.4;
        anim.call(() => {
            let areaIndex = CAR_ID_TO_AREA[curIndex];
            self.playAreaWinAnim(areaIndex, 1);
        })

        return {
            anim: anim,
            delayTime: delayTime,
            endIndex: curIndex,
        }
    }

    //bad_lucky图片是否显示
    setBadLuckyVisible(show: boolean) {
        this.LayerItems.bad_lucky.active = show;
    }
}