const { ccclass, property, disallowMultiple, menu } = cc._decorator;
import SceneManager from "../../../../script/frame/manager/SceneManager";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import Common from "../../../../script/frame/common/Common";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import { Direction, ZOrder, quickPayStyle } from "../../../../script/frame/common/Define";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import CARModel from "../core/CARModel";
import AlertLayer from "../../../../script/frame/component/AlertLayer";
import CARHistoryLayer from "./CARHistoryLayer";
import CARGameCore, { betAreaInfo } from "../core/CARGameCore";
import CARGameLogic from "../core/CARGameLogic";
import DataManager from "../../../../script/frame/manager/DataManager";
import CARPlayerInfoLayer, { playInfo } from "./CARPlayerInfoLayer";
import CARLotteryBox from "./CARLotteryBox";
import Config from "../../../../script/frame/config/Config";
import CARAudioMng from "../core/CARAudioMng";
import HallManager from "../../../../script/frame/manager/HallManager";
import { AREA_BEISHU, CAR, GameTextTips } from "../core/CARDefine";
import CARChipPoolAssisCom from "../core/CARChipPoolAssisCom";

@ccclass
@disallowMultiple()
@menu('car/CARGameViewLayer')
export default class CARGameViewLayer extends BaseLayer {

    @property(cc.Prefab)
    beishu_center: cc.Prefab = null;

    @property(cc.Prefab)
    beishu_right: cc.Prefab = null;

    //规则帮助界面
    @property(cc.Prefab)
    helpPrefab: cc.Prefab = null;

    //在线其它玩家列表
    @property(cc.Prefab)
    onLinePlayersLayer: cc.Prefab = null;

    //历史记录
    @property(cc.Prefab)
    trendLayer: cc.Prefab = null;

    //筹码预制体
    @property(cc.Prefab)
    chipNode: cc.Prefab = null;

    @property(cc.SpriteAtlas)
    mainUiAtlas: cc.SpriteAtlas = null;




    // @property(cc.Node)
    // topLayer: cc.Node = null;

    // @property(cc.Node)
    // mainLayer: cc.Node = null;

    // @property(cc.Node)
    // btnBanker: cc.Node = null;

    // @property(cc.Node)
    // bottomLayer: cc.Node = null;

    // @property(cc.Node)
    // alterLayer: cc.Node = null;

    // @property(cc.Node)
    // menuButton: cc.Node = null;

    // //用户主体预制体
    // @property(cc.Prefab)
    // userPrefab: cc.Prefab = null;

    // //历史走势图标预制体
    // @property(cc.Prefab)
    // recordItem: cc.Prefab = null;

    // //头像赢钱动效
    // @property(cc.Prefab)
    // winFrameEffect: cc.Prefab = null;

    // //头像赢钱动效
    // @property(cc.Prefab)
    // winStarEffect: cc.Prefab = null;

    // //在线玩家预制体
    // @property(cc.Prefab)
    // onLineItemPrefab: cc.Prefab = null;

    // //筹码预制体
    // @property(cc.Prefab)
    // chipNode: cc.Prefab = null;

    // @property(cc.RichText)
    // btnBankerLimit: cc.RichText = null;

    // @property(cc.Node)
    // btnMyApplyBanker: cc.Node = null;

    // @property(cc.Node)
    // historyList: cc.Node = null;

    // @property(cc.Node)
    // chargeNode: cc.Node = null;

    // @property(cc.SpriteAtlas)
    // gameAtlas: cc.SpriteAtlas = null;

    // @property(cc.SpriteAtlas)
    // cardAtlas: cc.SpriteAtlas = null;

    // @property(cc.SpriteAtlas)
    // userinfoAtlas: cc.SpriteAtlas = null;

    // @property(cc.SpriteAtlas)
    // bankerAtlas: cc.SpriteAtlas = null;

    // @property(cc.SpriteAtlas)
    // historyAtlas: cc.SpriteAtlas = null;

    // @property(cc.SpriteAtlas)
    // playerListAtlas: cc.SpriteAtlas = null;

    // @property(cc.SpriteAtlas)
    // commonAtlas: cc.SpriteAtlas = null;

    //玩家对象
    _playerView: CARPlayerInfoLayer = null;
    /** 逻辑相关 */
    _gameLogic: CARGameLogic;
    /** 游戏核心对象 */
    _gameCore: CARGameCore = null;
    //
    _lotteryBox: CARLotteryBox = null;
    /** 声音播放 */
    _gameAudio: CARAudioMng = null;
    //帮助界面
    _helpNode: cc.Node = null;
    //在线玩家列表层是否初始化创建过
    _onlinePanelLayer: cc.Node = null;
    //历史走势界面
    _historyLayer: cc.Node = null;
    //是否点击弹开了在线玩家信息列表
    _clickOtherLinePlayerLayer: boolean = false;
    //下注计时器
    _betScheduler: any = null;
    //等待下局计时器
    _waitNextScheduler: any = null;

    _test = false;
    // playerNode: any;
    // chipsNode: cc.Node;

    // totalplayernum: any;
    // userBetMsgSch: any;
    // updateScheduler: any;
    // allTime: number = 0;
    // movingOnlineUserHead: any;
    // btnOnline: cc.Node;
    // isPlayingBet1Sound: boolean;
    // isPlayingBetSound: boolean;
    // lastRoundArea: number;

    // // carBetScript: CARCenterLayer;


    // bankerListMsg: any;
    // trackSprite: any;
    // isSyscnBet: boolean = true; //是否同步筹码，5秒后同步筹码
    // //神算子 幸运星 残影动效
    // ghostCanvasList: cc.Sprite[] = [];
    // roleCamera: cc.Camera = null;
    // role: cc.Node;
    // betMsg: any;

    // historyBox: cc.Node;
    // historyNode: cc.Node;
    // carResultPanel: any;

    // _centerLayer: cc.Node = null;
    // _currentTime: number[] = [0, 0];
    // _timeLabel: cc.Label = null;
    // _onlineCountLabel: cc.Label = null;

    // Pokers: CARModel.Card[] = [new CARModel.Card, new CARModel.Card]; //牌数据
    // compareTb: CARModel.DeskCard[] = [new CARModel.DeskCard, new CARModel.DeskCard, new CARModel.DeskCard]; //牌数据
    // TouchBet: CARModel.DesktopBet[] = [new CARModel.DesktopBet, new CARModel.DesktopBet, new CARModel.DesktopBet];//桌面对象
    // MyInfo: CARModel.MyInfo = new CARModel.MyInfo(); //我的对象
    // Players: CARModel.Player[] = [new CARModel.Player, new CARModel.Player, new CARModel.Player, new CARModel.Player, new CARModel.Player, new CARModel.Player]; //玩家对象 0 神算子 1 大富豪

    // BankerInfo: CARModel.MyInfo = new CARModel.MyInfo();
    // BankerSystemInfo: CARModel.MyInfo = new CARModel.MyInfo();
    // ChipBtn = {};
    // chipPos = {};
    // resultTb = {};

    // userWinChip = {};
    // userInfo = {};
    // historyListTb: any[] = [];
    // userBetMsgTemp: any[] = [];
    // selectScore = -1;
    // bankerState = 0;     // 0 没上庄，1 在上庄列表，2 已上庄
    // selectChipIndex = null;
    // isSelectChip = null;    //是否手动选择筹码
    // starArea = -1;        //神算子下注区域
    // allUsers = {};
    // flyingBets: any[] = [];
    // cardletPlayerList = {};
    // flyingBets_he: any[] = [];
    // isChangeBanker = false;

    // CHIP_DATA_CONFIG: CARModel.ChipConfig[] = [];
    // SELECT_CHIP_DATA_CONFIG: CARModel.ChipConfig[] = [];
    // SELECT_CHOUMA_CONFIG: string[] = ["0", "1", "2", "3", "5"]; //选中筹码索引
    // // 配置数据存储的键值
    // storeChoumaIndexKey: string = "CARDChoumaIndexKey";
    // //游戏状态动效
    // statePanel: cc.Node = null;
    // //龙筹码
    // longPanel: cc.Node = null;
    // //和筹码
    // hePanel: cc.Node = null;
    // //虎筹码
    // huPanel: cc.Node = null;

    // //上庄弹窗
    // _mainUserLayer: cc.Node = null;
    // //在线玩家列表
    // _onlineUserLayer: cc.Node = null;
    // //用户信息
    // // _userLayer:cc.Node = null;
    // //历史走势
    // _historyLayer: cc.Node = null;
    // //等待下注
    // _tipWaitLayer: cc.Node = null;
    // //提示确认框
    // _messageBoxLayer: cc.Node = null;
    // //提示确认返回事件
    // _callBackMessage: any = null;
    // //  当前是否打开菜单
    // private _isOpenMenu: boolean;






    // // -----------------生命周期函数--------------------//
    onLoad() {
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)

        this._gameCore = this.node.getComponent("CARGameCore");
        this._lotteryBox = this.node.getComponent("CARLotteryBox");
        this._gameLogic = this.node.getComponent("CARGameLogic");
        this._gameAudio = this.node.getComponent("CARAudioMng");
        this._playerView = this.node.getComponent("CARPlayerInfoLayer");
        
        let self = this;
        cc.game.on(cc.game.EVENT_HIDE, this.onEnterBackground, self);
        cc.game.on(cc.game.EVENT_SHOW, this.onEnterForgeground, self);

        this.initBetInfo();



        //     let chipValData = [1000, 5000, 10000, 50000, 100000, 500000, 1000000, 5000000, 100000000, 50000000, 100000000, 500000000, 1000000000];
        //     let chipValNameData = ["1K", "5K", "10K", "50K", "100K", "500K", "1M", "5M", "10M", "50M", "100M", "500M", "1000M"];
        //     for (let i = 0; i < chipValData.length; i++) {
        //         let item: CARModel.ChipConfig = new CARModel.ChipConfig();
        //         item.valName = chipValNameData[i];
        //         item.bgKey = "mortgage_" + (i + 1);
        //         item.betBgKey = "mortgage_fei_" + (i + 1);
        //         item.bgSelectKey = "mortgage_xuan";
        //         this.CHIP_DATA_CONFIG.push(item);
        //     }
        //     this.initChouMaData();

        //     //定义界面控件
        //     this._mainUserLayer = this.alterLayer.getChildByName("setMainPanel");
        //     this._onlineUserLayer = this.alterLayer.getChildByName("onlineUserPanel");
        //     this._historyLayer = this.alterLayer.getChildByName("historyPanel");
        //     this._tipWaitLayer = this.alterLayer.getChildByName("tipWaitPanel");
        //     this._messageBoxLayer = this.alterLayer.getChildByName("alertPanel");
        //     this._centerLayer = this.mainLayer.getChildByName("centerLayer");
        //     this.chargeNode.active = DataManager.instance.chargeStatus == 1;

        //     self.statePanel = self.mainLayer.getChildByName("statePanel");
        //     self.statePanel.active = false;
        //     let gameTimePanel: cc.Node = this._centerLayer.getChildByName("gameTime");
        //     gameTimePanel.active = false;
        //     //初始牌
        //     this.initCardUI();
        //     this.initHistory();

        //     // 改变节点的层级
        //     this.alterLayer.zIndex = ZOrder.UI + 6;
        //     this.topLayer.zIndex = ZOrder.UI + 5
        //     // this.carBetScript = this.bottomLayer.getChildByName("betPanel").getComponent(CARCenterLayer);
    }
    start() {
        this._gameAudio.playMusic();
    }

    onDestroy() {
        let self = this;
        cc.game.off(cc.game.EVENT_HIDE, this.onEnterBackground, self);
        cc.game.off(cc.game.EVENT_SHOW, this.onEnterForgeground, self);
    }

    //初始下注信息
    initBetInfo() {
        this.LayerItems.game_bets.children.forEach(item => this._gameCore._betChipsNodeList.push(item));
        for (let i = 0; i < CAR.AREA_MAX_NUM; i++) {
            let areaInfo = <betAreaInfo>{};
            let areaNode = this.LayerItems[`btn_area${i}`];
            areaInfo.node = areaNode;
            areaInfo.betFlag = areaNode.getChildByName('betFlag');
            areaInfo.icon = areaNode.getChildByName('icon').getComponent(cc.Sprite);
            areaInfo.beishu = areaNode.getChildByName('beishu').getComponent(cc.Label);
            areaInfo.num = areaNode.getChildByName('num').getComponent(cc.RichText);
            areaInfo.img_star = areaNode.getChildByName('img_star');
            areaInfo.img_star_fill = areaInfo.img_star.getChildByName('img_star_fill').getComponent(cc.ProgressBar);
            areaInfo.win_beishu_center = this.LayerItems.winBeishu_center.getChildByName(`beishu_${i}`);
            areaInfo.win_beishu_right = this.LayerItems.winBeishu_right.getChildByName(`beishu_${i}`);
            areaInfo.win_light = areaNode.getChildByName('win_light');
            areaInfo.area = i;
            areaInfo.myself_betsNum = 0;
            areaInfo.areaTotalNum = 0;
            areaInfo.winSameMulti = 0;
            areaInfo.betAreaScoreSumList = [0, 0, 0, 0, 0, 0, 0, 0];
            areaInfo.betAreaDelScoreSumList = [0, 0, 0, 0, 0, 0, 0, 0];
            areaInfo.multi = 1;
            this._gameCore._areaInfoList.push(areaInfo)
        }
    }

    //是否播放停止动画
    setStopAniPlay(play: boolean, callback = null) {
        let self = this;
        this.LayerItems.spineStop.active = play;
        if (play) {
            this._gameAudio._loadPlaySFX("res/audio/betend");
            this.LayerItems.spineStop.getComponent(sp.Skeleton).setAnimation(0, "animation", false);
            this.LayerItems.spineStop.getComponent(sp.Skeleton).setCompleteListener(() => {
                self.LayerItems.spineStop.active = false;
                callback && callback();
            })
        }
    }

    //设置等待倒计时
    setWaitCountTime(show: boolean) {
        this.LayerItems.img_gametip_start.active = show;
        if (show && !Common.isNull(this._gameCore._betTime)) {
            let self = this;
            if (this._waitNextScheduler) {
                this.unschedule(this._waitNextScheduler);
                this._waitNextScheduler = null;
            }
            this._gameCore._currentTimeOut = this._gameCore._betTime;
            this.LayerItems.lable_wait_time.getComponent(cc.Label).string = this._gameCore._currentTimeOut + '';

            self._waitNextScheduler = (() => {
                self._gameCore._currentTimeOut -= 1;
                self.LayerItems.lable_wait_time.getComponent(cc.Label).string = (self._gameCore._currentTimeOut < 0 ? 0 : self._gameCore._currentTimeOut) + '';

                if (self._gameCore._currentTimeOut < 0) {
                    self.LayerItems.img_gametip_start.active = false;
                    if (self._waitNextScheduler) {
                        self.unschedule(self._waitNextScheduler);
                        self._waitNextScheduler = null;
                    }
                }
            })
            this.schedule(self._waitNextScheduler, 1);
        }
        else {
            if (this._waitNextScheduler) {
                this.unschedule(this._waitNextScheduler);
                this._waitNextScheduler = null;
                this.LayerItems.img_gametip_start.active = false;
            }
        }
    }

    //设置倒计时
    setCountTime(show: boolean, time?: number) {
        if (time) {
            this.LayerItems.lb_game_time.getComponent(cc.Label).string = time.toString();
        }
        this.LayerItems.lb_game_time.active = show && time > 0;
    }


    //设置筹码图片
    setBetChipsImg(atlas: cc.SpriteAtlas, chipNumList: number[]) {
        this._gameCore._chipNumList = chipNumList;
        if (atlas && chipNumList) {
            this._gameCore._betChipsNodeList.forEach((item, index) => {
                let sp = item.getComponent(cc.Sprite);
                if (chipNumList[index]) {
                    sp.spriteFrame = atlas.getSpriteFrame(`car_chip_${chipNumList[index]}`);
                }
            });
        }
    }

    //设置当前筹码是否选中 -1都不选中
    setSelectBetChipLight(selectIndex: number) {
        this._gameCore._betChipsNodeList.forEach((item, index) => {
            item.getChildByName('img_bet_choose').active = index == selectIndex;
            if (index == selectIndex) {
                item.getComponent(cc.Button).interactable = true;
                item.getComponent(cc.Button).enabled = true;
            }
            item.y = index == selectIndex ? CAR.CHIP_MOVE_POS_Y_NUM : 0;
            this._gameCore._selectIndex = selectIndex;
        });
    }

    //设置所有按钮是否禁用
    setAllBetChipDisable(show: boolean) {
        this._gameCore._betChipsNodeList.forEach((item, index) => { this.setBetChipDisable(index, show) });
    }

    //设置按钮是否禁用
    setBetChipDisable(Index: number, show: boolean) {
        this._gameCore._betChipsNodeList[Index].getChildByName('img_un_bet').active = show;
        this._gameCore._betChipsNodeList[Index].getComponent(cc.Button).interactable = !show;
        this._gameCore._betChipsNodeList[Index].getComponent(cc.Button).enabled = !show;
        if (show) {
            this._gameCore._betChipsNodeList[Index].y = 0;
        };
    }

    //自动选择筹码
    autoSelectChip(initFlag: boolean = false) {
        let mySelfGold = this._playerView._MyInfo.money;

        //初始化
        if (initFlag) {
            this._gameCore._selectIndex = cc.sys.localStorage.getItem(CAR.LOCAL_STORAGE_BET_NUM_KEY);
            if (Common.isNull(this._gameCore._selectIndex) || this._gameCore._selectIndex < 0 || this._gameCore._selectIndex > this._gameCore._chipNumList.length) {
                this._gameCore._selectIndex = 0;
            }
        }
        let curSelectValue = this._gameCore._chipNumList[this._gameCore._selectIndex];

        if (initFlag) {//初始进来 不自动选择，让玩家点击时，提示充值
            if (mySelfGold < curSelectValue) {//初始进来钱不够上次选择的按钮 默认为第一个
                this._gameCore._selectIndex = 0;
            }
            else {
                for (let i = this._gameCore._chipNumList.length - 1; i >= 0; i--) {
                    if (i == this._gameCore._selectIndex) {
                        this.setSelectBetChipLight(i);
                        break;
                    }
                }
            }
        }
        else {//根据钱自动选择筹码
            let isSel: boolean = false;
            if (mySelfGold < this._gameCore._chipNumList[this._gameCore._selectIndex]) {
                for (let i = this._gameCore._chipNumList.length - 1; i >= 0; i--) {
                    this.setBetChipDisable(i, this._gameCore._chipNumList[i] > mySelfGold);
                    if (this._gameCore._chipNumList[i] <= mySelfGold && !isSel) {
                        isSel = true;
                        this.setSelectBetChipLight(i);
                    }
                }
            }
        }
    }

    //设置中间区域赢的倍数是否显示 
    setCenterWinBeiShuVisible(areaIndex: number, show: boolean = false, beishuNum: number = 1) {
        let node = this._gameCore._areaInfoList[areaIndex].win_beishu_center;
        node.active = show;
        let beishuNode = node.getChildByName('YXX_eff_beishu');
        if (!beishuNode) {
            beishuNode = cc.instantiate(this.beishu_center);
            beishuNode.name = 'YXX_eff_beishu';
            node.addChild(beishuNode);
        }
        beishuNode.active = show;
        if (show) {
            beishuNode.getComponent(cc.Animation).stop();
            let beishu = cc.find('BitmapFontLabel_1/win_beishu_big', beishuNode);
            if (beishu) {
                beishu.getComponent(cc.Label).string = 'x' + beishuNum;
            }
            beishuNode.getComponent(cc.Animation).play();
        }
    }

    //设置右上角域赢的倍数是否显示
    setRightTopWinBeiShuVisible(areaIndex: number, show: boolean = false, beishuNum: number = 1) {
        if (!this._gameCore._areaInfoList[areaIndex]) {
            console.error('===')
        }
        let node = this._gameCore._areaInfoList[areaIndex].win_beishu_right;
        node.active = show;
        let beishuNode = node.getChildByName('BR_effect_beishu');
        if (!beishuNode) {
            beishuNode = cc.instantiate(this.beishu_right);
            beishuNode.name = 'BR_effect_beishu';
            node.addChild(beishuNode);
        }
        beishuNode.position = cc.v3(70, 40);
        beishuNode.active = show;
        if (show) {
            let beishu = beishuNode.getChildByName('BitmapFontLabel');
            if (beishu) {
                beishu.getComponent(cc.Label).string = beishuNum + 'x';
            }
            let ani = beishuNode.getComponent(cc.Animation);
            let animState = ani.play('chuxian');
            animState.wrapMode = cc.WrapMode.Normal;
            ani.off('finished');
            ani.on('finished', (() => {
                let animState = ani.play('xunhuan');
                animState.wrapMode = cc.WrapMode.Loop;
            }), this);
        }
    }

    //设置当前区域图标
    setAreaIconsSp(atlas: cc.SpriteAtlas, iconIndexList: number[]) {
        this._gameCore._areaInfoList.forEach((item, index) => item.icon.spriteFrame = atlas.getSpriteFrame(`CB_chebiao0${iconIndexList[index]}`));
    }

    //设置自己下注区域是否高亮
    setAreaLightVisible(areaIndex: number, show?: boolean) {
        this._gameCore._areaInfoList[areaIndex].betFlag.active = !!show;
    }

    //设置下注数量
    setAreaBetNum(areaIndex: number, totalBet: number = 0, mySelfBet: number = 0) {
        let str = `<color=#A9D5E7>${this.moneyFormat(totalBet)}</c><color=#FAEB44>/${this.moneyFormat(mySelfBet)}</color>`
        this._gameCore._areaInfoList[areaIndex].num.string = str;
    }

    //设置当前区域底图倍数
    setAreaBgBeiShu(areaBgBeiShuList: number[]) {
        this._gameCore._areaInfoList.forEach((item, index) => item.beishu.string = `${areaBgBeiShuList[index]}x`);
    }

    //设置当前区域赢灯光
    setAreaWinLight(areaIndex: number) {
        if (this._gameCore._areaInfoList[areaIndex].win_light.active) {
            return;
        }
        this._gameCore._areaInfoList[areaIndex].win_light.active = true;
        cc.tween(this._gameCore._areaInfoList[areaIndex].win_light)
            .then(cc.fadeTo(0.5, 0))
            .then(cc.fadeTo(0.5, 255))
            .union()
            .repeatForever()
            .start();
    }

    //设置区域神算子底图是否显示 
    setStarBgVisible(areaIndex: number, show?: boolean) {
        this._gameCore._areaInfoList[areaIndex].img_star.active = !!show;
    }

    //设置区域神算子进度
    setStarProgressBar(areaIndex: number, progress: number) {
        this._gameCore._areaInfoList[areaIndex].img_star_fill.progress = progress;
    }

    //更新桌面在线人数
    onUpdateOnlinePlayersNum(info) {
        if (!!info && !!info.totalplayernum) {
            this.LayerItems.lbOnlinePlayers.getComponent(cc.Label).string = info.totalplayernum + '';
        }
    }


    //点击下注区域
    private onClickBetAreaBtn(target: any, customEventData: any) {

        if (DataManager.instance.isNeedPay(Config.GAME_LIST.longhu.gameId)) {//没有充值，提示充值
            HallManager.instance.openCharge({ quickPayStyle: quickPayStyle.gameVipPay });
            return;
        }

        let myMoney = this._playerView._MyInfo.money;
        if (myMoney < this._gameCore._betNeedNum) {//余额不足
            HallManager.instance.openCharge({ quickPayStyle: quickPayStyle.gameQuickPay });
            return;
        }

        if (!this._gameCore._isStartBet || this._gameCore._gameState != CAR.GameState.Bet) {//非下注阶段
            return;
        }

        let selectScore = this._gameCore._chipNumList[this._gameCore._selectIndex];
        if (Common.isNull(selectScore) || selectScore <= 0) {
            return;
        }

        if (myMoney < selectScore) {
            return;
        }

        let myAllBet = 0;
        this._gameCore._areaInfoList.forEach(item => { myAllBet += item.myself_betsNum });
        let MaxBet = this._gameCore._BetMax ?? 0;
        if (myAllBet + selectScore >= MaxBet) {//超过最大可下注值
            this.show(GameTextTips.GAME_BET_MONEY_MAX_TIP);
            return;
        }
        let areaIndex = Common.toNumber(customEventData)
        this._gameCore.sendBet(areaIndex + 1, selectScore);//发送下注消息

        // self.userMyselfBetHeadMove(self._playerView._MyInfo); //自家头像移动

        //提前扣钱及下注动作
        let areaInfo = this._gameCore._areaInfoList[areaIndex];
        this._playerView._MyInfo.money -= selectScore;
        areaInfo.areaTotalNum += selectScore;
        areaInfo.myself_betsNum += selectScore;
        this.setAreaBetNum(areaIndex, areaInfo.areaTotalNum, areaInfo.myself_betsNum);
        this._playerView.updateMyScore(this._playerView._MyInfo.money)
        this.setAreaLightVisible(areaIndex, true);
        this.deskChipMove(selectScore, this._gameCore._playerView._MyInfo.node, areaIndex, null, CAR.CUSTOM_MYSELF_POS);
    }


    //点击筹码
    private onClickBetBtn(target: any, customEventData: any) {
        let index = Common.toNumber(customEventData);
        if (index == this._gameCore._selectIndex) {
            return
        }
        this._gameCore._selectIndex = index;
        this.setSelectBetChipLight(this._gameCore._selectIndex);
    }


    //点击返回大厅
    private onClickReturnBtn(target: any, customEventData: any) {
        cc.sys.localStorage.setItem(CAR.LOCAL_STORAGE_BET_NUM_KEY, this._gameCore._selectIndex);
        this._gameCore.quitGame();
    }


    //点击帮助
    private onClickHelpBtn(target: any, customEventData: any) {
        if (!this._helpNode) {
            this._helpNode = cc.instantiate(this.helpPrefab);
            SceneManager.instance.addChildNode(this._helpNode);
        }

        this._gameLogic.popUpEffect(this._helpNode, true);
    }

    //点击充值
    private onClickRechargeBtn(target: any, customEventData: any) {
        HallManager.instance.openCharge();
    }

    //点击历史记录
    private onClickHistoryBtn(target: any, customEventData: any) {
        if (!this._historyLayer) {
            this._historyLayer = cc.instantiate(this.trendLayer);
            SceneManager.instance.addChildNode(this._historyLayer);
        }

        this._gameLogic.popUpEffect(this._historyLayer, true);
    }

    //点击聊天
    private onClickMsgBtn(target: any, customEventData: any) {
        let self = this;
        let playerInfo = { sendPlayerid: this._gameCore.playerid };
        UIHelper.showGameChatLayer(this.node, playerInfo, (info: any) => {
            self._gameCore.sendChatMessage(info);
        },true);
    }

    //显示在线玩家
    public onClickShowOnlineUser() {
        let self = this;
        if (self._clickOtherLinePlayerLayer) {//防止重复点击
            self.scheduleOnce(() => {
                !!self && !!self._clickOtherLinePlayerLayer && (self._clickOtherLinePlayerLayer = false);
            }, 2)
            return;
        }
        self._clickOtherLinePlayerLayer = true;
        self._gameCore.requestAllPlayerList();
    }

    //接收玩家信息后显示界面
    showOnlineUserEnd() {
        let self = this;
        if (!self._clickOtherLinePlayerLayer) {
            return;
        }
        self._clickOtherLinePlayerLayer = false;

        let bShow: boolean = this._onlinePanelLayer && this._onlinePanelLayer.active;

        if (!this._onlinePanelLayer) {//未创建过
            this._onlinePanelLayer = cc.instantiate(this.onLinePlayersLayer);
            SceneManager.instance.addChildNode(this._onlinePanelLayer);
        }
        if (bShow) {//已经显示的界面 点击下一页时，不用弹出效果
            self._onlinePanelLayer.getComponent('CAROnlinePlayersLayer').setData(self, self._gameCore, self._gameCore._playerListData, true, self.hideOnlineUser.bind(self));
        }
        else {
            this._gameLogic.popUpEffect(this._onlinePanelLayer, true, (() => {
                self._onlinePanelLayer.getComponent('CAROnlinePlayersLayer').setData(self, self._gameCore, self._gameCore._playerListData, true, self.hideOnlineUser.bind(self));
            }));
        }
    }

    //更新点击在线其它玩家界面状态
    updateOnlineLayerState() {
        let self = this;
        self._clickOtherLinePlayerLayer = true;
    }

    //隐藏在线玩家
    public hideOnlineUser() {
        this._clickOtherLinePlayerLayer = false;
        this._gameLogic.popUpEffect(this._onlinePanelLayer, false);
    }

    // update(dt) {
    //     let currentTime = new Date();
    //     let hours = currentTime.getHours();
    //     let minutes = currentTime.getMinutes();
    //     //设置当前时间
    //     if (this._timeLabel && (hours > this._currentTime[0] || minutes > this._currentTime[1])) {
    //         this._timeLabel.string = (hours < 9 ? "0" + hours : hours) + ":" + (minutes < 9 ? "0" + minutes : minutes);
    //         this._currentTime[0] = hours;
    //         this._currentTime[1] = minutes;
    //     }
    // }
    // //------------------界面方法-------------------//
    // public initChouMaData() {

    //     this.SELECT_CHIP_DATA_CONFIG = [];
    //     for (let i = 0; i < 5; i++) {
    //         let item: CARModel.ChipConfig = this.CHIP_DATA_CONFIG[i];

    //         this.SELECT_CHIP_DATA_CONFIG.push(item);
    //     }

    // }
    // //初始化牌
    // private initCardUI() {
    //     let self = this;
    //     let cardName = ["longCard", "huCard", "heCard"];
    //     let compareNode = ["leftPanel", "rightPanel"];
    //     let startPos = [0, 0]
    //     let lookStartPos = [-108, 112]
    //     for (let i = 0; i < 2; i++) {
    //         let cardNode: cc.Node = self.mainLayer.getChildByName("cardNode").getChildByName(cardName[i]);
    //         self.compareTb[i].card = cardNode;
    //         self.compareTb[i].valueCard = cardNode.getChildByName("valueCard");
    //         self.compareTb[i].valueCard.active = false;
    //         self.compareTb[i].winnerIcon = cardNode.getChildByName("winIcon");
    //         self.compareTb[i].startPos = cc.v2(lookStartPos[i], -21);
    //         self.compareTb[i].node = self.mainLayer.getChildByName(compareNode[i]);
    //         self.compareTb[i].node.active = false;
    //         self.Pokers[i].card = cardNode;
    //         self.Pokers[i].valueCard = cardNode.getChildByName("valueCard");
    //         let movePos = cardNode.getPosition();
    //         self.Pokers[i].startPos = cc.v2(movePos.x - startPos[i], movePos.y);
    //         self.Pokers[i].movePos = movePos;
    //         self.compareTb[i].winnerIcon.y = -15;
    //         self.compareTb[i].winnerIcon.active = false;

    //     }
    //     self.compareTb[2].card = self.mainLayer.getChildByName("cardNode").getChildByName(cardName[2]);
    //     self.compareTb[2].winnerIcon = self.compareTb[2].card.getChildByName("winIcon");
    //     self.compareTb[2].winnerIcon.y = -15;
    //     self.compareTb[2].winnerIcon.active = false;

    //     //初始化牌桌数据
    //     let desktopName = ["longPanel", "huPanel", "hePanel"]
    //     let desktopStarName = ["longStarImg", "huStarImg", "heStarImg"]
    //     this.LayerItems.chipsNode = self.mainLayer.getChildByName("chipsNode");
    //     for (let i = 0; i < self.TouchBet.length; i++) {
    //         let baseNode = self._centerLayer.getChildByName(desktopName[i]);
    //         self.TouchBet[i].node = baseNode;


    //         self.TouchBet[i].star = baseNode.getChildByName(desktopStarName[i]);
    //         self.TouchBet[i].total_label = baseNode.getChildByName("totalLabel").getComponent(cc.Label);
    //         self.TouchBet[i].self_label = baseNode.getChildByName("selfLabel").getComponent(cc.Label);

    //         self.TouchBet[i].star.active = false;
    //         self.TouchBet[i].total_label.string = "0";
    //         self.TouchBet[i].self_label.string = "0";
    //         self.TouchBet[i].total_number = 0;
    //         self.TouchBet[i].self_number = 0;

    //     }
    //     self.btnOnline = self.bottomLayer.getChildByName("rightPanel").getChildByName("onlineButton");
    // }

    // //设置下注筹码值
    // private betString(betNum: number) {
    //     return this.moneyFormat(betNum) + "";
    // }
    // public onBackHall() {
    //     if (this._isOpenMenu == true) {
    //         this._isOpenMenu = false;
    //         UIHelper.clearAll();
    //         return;
    //     }
    //     let self = this;
    //     let text = CAR.LangData.GAME_BET_EXIT_ERR;
    //     if (self._playerView._MyInfo.playerid == self.BankerInfo.playerid) {
    //         AlertHelper.show(CAR.LangData.GAME_BANKER_EXIT_ERR)
    //         return;
    //     }
    //     if (self._playerView._MyInfo._myself_isbet) {
    //         AlertHelper.confirm(text, function () {
    //             this.LayerItems.chipsNode.stopAllActions();
    //             self._centerLayer.stopAllActions();

    //             self._gameCore.quitGame();
    //         }, null);
    //         return;
    //     }
    //     this.chipsNode.stopAllActions();
    //     this._centerLayer.stopAllActions();

    //     self._gameCore.quitGame();
    // }
    // public openUserInfo(user: any, player: cc.Node) {
    //     ////throw new Error("Method not implemented.");
    //     let self = this;
    //     if (player.active == false) {
    //         self._gameLogic.showLayer(player);
    //         cc.tween(player)
    //             .delay(3)
    //             .call(function () {
    //                 user.isOpenUserInfo = false;
    //                 self._gameLogic.hideLayer(player);
    //             }).start();
    //     }
    // }
    // public getUserInfo(playerid: any) {
    //     return this.allUsers[playerid];
    // }

    //显示提示框
    public show(text: string, time: number = 2) {
        ToastHelper.instance.showToast(text, time);
    }


    // //显示历史记录
    // public showHistory() {
    //     let self = this;
    //     self._gameLogic.showLayer(this._historyLayer, function () {
    //         // self._history.show();
    //     });
    // }
    // //隐藏历史记录
    // public hideHistory() {
    //     this._gameLogic.hideLayer(this._historyLayer);
    // }
    // //显示在线玩家
    // public showOnlineUser() {

    //     this._gameLogic.showLayer(this._onlineUserLayer);
    //     this.loadPlayerList();
    // }
    // //隐藏在线玩家
    // public hideOnlineUser() {
    //     this._gameLogic.hideLayer(this._onlineUserLayer);
    // }

    // public getOnlineCount() {
    //     // let playerCount:number = self._gameCore.getPlayerCount();
    //     // if(this._onlineCountLabel) this._onlineCountLabel.string = CAR.LangData.ONLINE_COUNT+ playerCount;

    // }
    // public hideMessageBox() {
    //     this._gameLogic.hideLayer(this._messageBoxLayer);
    // }
    // public okMessageBox() {
    //     this._gameLogic.hideLayer(this._messageBoxLayer);
    //     if (this._callBackMessage) {
    //         this._callBackMessage();
    //     }
    // }

    // //---------------------网络事件回调---------------------//
    //进入后台
    onEnterBackground() {
        let self = this;
        self._gameAudio.setOnEnterBackgroundFlag(true);
        self.LayerItems.chipsNode.stopAllActions();
        self.LayerItems.game_center.stopAllActions();
        self.node.stopAllActions();
    }
    //切换前台
    onEnterForgeground() {
        let self = this;
        self._gameAudio.setOnEnterBackgroundFlag(false);
    }

    // updateUserScore(msg: any) {
    //     let userInfo = this.allUsers[msg.playerid];
    //     if (userInfo) {
    //         userInfo.coin = msg.coin;
    //         userInfo.money = msg.coin;
    //     }
    // }
    // updateOtherScore(playerid: number, score: number) {
    //     for (let i = 0; i < this.Players.length; i++) {
    //         if (playerid == this.Players[i].playerid) {
    //             let scoreLabel: cc.Label = this.Players[i].score.getComponent(cc.Label);
    //             scoreLabel.string = this.moneyFormat(score) + "";
    //         }

    //     }
    // }

    // //下注成功
    // myBetSuccess(msg: any) {
    //     let self = this;
    //     self.TouchBet[0].self_number = msg.long;
    //     self.TouchBet[1].self_number = msg.hu;
    //     self.TouchBet[2].self_number = msg.he;
    //     self.TouchBet[0].self_label.string = this.betString(msg.long);
    //     self.TouchBet[1].self_label.string = this.betString(msg.hu);
    //     self.TouchBet[2].self_label.string = this.betString(msg.he);
    //     if (msg.long > 0) {
    //         self.TouchBet[0].self_label.node.active = true;
    //     }
    //     if (msg.hu > 0) {
    //         self.TouchBet[1].self_label.node.active = true;
    //     }
    //     if (msg.he > 0) {
    //         self.TouchBet[2].self_label.node.active = true;
    //     }
    //     let all_long = self.TouchBet[0].total_number;
    //     let all_hu = self.TouchBet[1].total_number;
    //     let all_he = self.TouchBet[2].total_number;

    //     if (all_long < msg.long_total) {
    //         self.TouchBet[0].total_label.string = this.betString(msg.long_total);
    //         self.TouchBet[0].total_number = msg.long_total;
    //     }
    //     if (all_hu < msg.hu_total) {
    //         self.TouchBet[1].total_label.string = this.betString(msg.hu_total);
    //         self.TouchBet[1].total_number = msg.hu_total;
    //     }
    //     if (all_he < msg.he_total) {
    //         self.TouchBet[2].total_label.string = this.betString(msg.he_total);
    //         self.TouchBet[2].total_number = msg.he_total;
    //     }
    //     self._playerView._MyInfo.score.getComponent(cc.Label).string = this.moneyFormat(msg.chouma) + "";
    //     this._gameCore.userInfo.money = msg.chouma;
    //     self._playerView._MyInfo.money = msg.chouma;
    //     self._playerView._MyInfo._myself_isbet = true;
    //     self.autoSelectChip();
    // }



    // //结算特效
    // resultAct(player) {

    //     let spineNode: cc.Node = player.fnt_WinAnimation;
    //     // let startNode = cc.instantiate(this.winStarEffect);
    //     // startNode.getComponent(sp.Skeleton).setAnimation(0,"animation",false);

    //     spineNode.removeAllChildren();
    //     spineNode.addChild(cc.instantiate(this.winFrameEffect));
    //     // spineNode.addChild(startNode);
    //     spineNode.active = true;
    // }
    // //数字飞动
    // flyNumber(node: cc.Node) {
    //     //--node:setScale(0.3);
    //     let oldPos = node.getPosition();

    //     let move = cc.tween().to(0.4, { position: cc.v2(oldPos.x, oldPos.y + 40) })
    //     let delay = cc.tween().delay(2);
    //     node.stopAllActions();
    //     cc.tween(node)
    //         .to(0.4, { position: cc.v3(oldPos.x, oldPos.y + 40) })
    //         .delay(2).call(function () {
    //             node.active = false;
    //         })
    //         .start();
    // }

    //结算数字飘动
    resultFlyNumber() {
        let nChangeList = this.playerWinOrLose();
        for (let i = 0; i < nChangeList.length; i++) {
            if (nChangeList[i] > 0) {
                if(i == CAR.CUSTOM_MYSELF_POS){
                    this._gameAudio._loadPlaySFX("res/audio/winner");
                }
                this._playerView.flyWinGoldAni(i, nChangeList[i]);
            }
        }
    }
    // }
    //开始下注
    onStartBet() {
        //print("\n开始下注============================\n");
        this.setWaitCountTime(false);
        this.cleanGameData();
        this._gameCore._isStartBet = true;
        this.betTimeOut();
    }

    //停止下注
    onStopBet() {
        this._test = true;
        let self = this;
        this.LayerItems.node_player_other.stopAllActions();
        if (this._betScheduler) {
            this.unschedule(this._betScheduler);
            this._betScheduler = null;
        }
        this.setCountTime(false);
        this._gameCore._isStartBet = false;
        this.setStopAniPlay(true, (async () => {
            self._lotteryBox.onGameStart(await self.onGameResult.bind(self));
        }));
    }

    //下注倒计时
    betTimeOut() {
        let self = this;
        if (this._betScheduler) {
            this.unschedule(this._betScheduler);
            this._betScheduler = null;
        }

        this._gameCore._currentTimeOut = this._gameCore._betTime;
        this.setCountTime(true, this._gameCore._currentTimeOut);

        self._betScheduler = (() => {
            //--yg:调整读秒显示问题
            self._gameCore._currentTimeOut -= 1;

            self.setCountTime(true, self._gameCore._currentTimeOut < 0 ? 0 : self._gameCore._currentTimeOut);

            //if (0 < self._gameCore._currentTimeOut && self._gameCore._currentTimeOut <= 3) {
            if (self._gameCore._gameState == CAR.GameState.Bet && self._gameCore._currentTimeOut == 3) {
                self._gameAudio._loadPlaySFX("res/audio/timeDown");
            }
            if (self._gameCore._currentTimeOut <= 1 && self._gameCore._gameState == CAR.GameState.Bet) {//下注倒计时快结束时
                // self._gameCore._isStartBet = false;
            }
            if (self._gameCore._currentTimeOut < 0) {
                self.setCountTime(false);
                if (self._betScheduler) {
                    self.unschedule(self._betScheduler);
                    self._betScheduler = null;
                }
            }
        })
        this.schedule(self._betScheduler, 1);
    }

    //运行神算子动作
    playLuckStarAction(areaIndex) {
        if (!this._gameCore._isStartBet) {
            this.LayerItems.star_fly_1_plist.opacity = 0;
            this.LayerItems.otherLayer.stopAllActions();
            this.LayerItems.otherLayer.removeAllChildren();
            return;
        }
        if (this._gameCore._playedLuckStarAction[areaIndex]) {
            this._gameCore._playedLuckStarAreaList[areaIndex].push(areaIndex);
            return
        }
        this._gameCore._playedLuckStarProcess[areaIndex] += 0.1;
        if (this._gameCore._playedLuckStarProcess[areaIndex] > 1) {
            this.LayerItems.star_fly_1_plist.opacity = 0;
            this._gameCore._playedLuckStarProcess[areaIndex] = 1;
            this._gameCore._playedLuckStarAreaList[areaIndex] = [];
            this.LayerItems.otherLayer.stopAllActions();
            this.LayerItems.otherLayer.removeAllChildren();
            return;
        }
        let endNode = this._gameCore._areaInfoList[areaIndex].img_star;
        let endPos = endNode.convertToWorldSpaceAR(endNode.getAnchorPoint())
        endPos = cc.v2(this.LayerItems.otherLayer.convertToNodeSpaceAR(endPos));
        this._gameCore._playedLuckStarAction[areaIndex] = true;
        let self = this;
        this.LayerItems.star_fly_1_plist.opacity = 255;
        this.luckStartTailAnimation(this.LayerItems.flyBetStar.position, endPos, this.LayerItems.otherLayer, this.LayerItems.flyBetStar, () => {
            self.setStarBgVisible(areaIndex, true);
            self.setStarProgressBar(areaIndex, self._gameCore._playedLuckStarProcess[areaIndex]);
            self.LayerItems.star_fly_1_plist.opacity = 0;
            self._gameCore._playedLuckStarAction[areaIndex] = false;
            if (self._gameCore._playedLuckStarAreaList[areaIndex].length > 0) {
                self._gameCore._playedLuckStarAreaList[areaIndex].slice(0, 1);
                self.playLuckStarAction(areaIndex);//循环读取直到所有播完
            }
        })
    }

    //神算子动画
    luckStartTailAnimation(startPos: cc.Vec3, endPos: cc.Vec2, parent: cc.Node, starNode: cc.Node, callfunc: Function = null) {
        let starNodeList: cc.Node[] = [];
        let tmpStarNdList: cc.Node[] = [];
        let generateStarNdFunc = (pos: cc.Vec3) => {
            let tailNd = cc.instantiate(starNode);
            tailNd.position = pos;
            tailNd.name = 'starTail';
            tailNd.active = true;
            parent.addChild(tailNd);
            starNodeList.push(tailNd);
            tmpStarNdList.push(tailNd);
            return tailNd;
        };
        let tempStarNd = generateStarNdFunc(startPos);
        let moveTime = 0.6;
        let dismisTime = 0.4;
        let stepTime = 0.02;
        let tailMaxNum = 100;

        cc.tween(tempStarNd)
            .parallel(
                cc.tween().sequence(
                    cc.jumpTo(moveTime, cc.v2(endPos.x, endPos.y), 150, 1),
                    cc.callFunc(() => {
                        if (!!callfunc) {
                            callfunc(tempStarNd);
                            tmpStarNdList.forEach(tailNd => tailNd.removeFromParent());
                        }
                    })
                ),
                cc.tween().repeat(
                    tailMaxNum,
                    cc.tween().sequence(
                        cc.delayTime(stepTime),
                        cc.callFunc(() => {
                            let tailNd = generateStarNdFunc(tempStarNd.position);
                            cc.tween(tailNd)
                                .parallel(
                                    cc.tween().to(dismisTime, { scale: 0.2 }),
                                    cc.tween().to(dismisTime, { opacity: 0 }, { easing: 'sineOut' })
                                )
                                .call(() => {
                                    //从tmpStarNdList删除
                                    let idx = tmpStarNdList.indexOf(tailNd);
                                    if (idx != -1) {
                                        tailNd.removeFromParent();
                                        tmpStarNdList.splice(idx, 1);
                                    }
                                    //从_starNdsList删除
                                    idx = starNodeList.indexOf(tailNd);
                                    if (idx != -1) {
                                        tailNd.removeFromParent();
                                        starNodeList.splice(idx, 1);
                                    }
                                })
                                .start();
                        })
                    )
                )
            )
            .start()
    }


    // //休息时间
    // onSleep() {
    //     let self = this;
    //     let delayTime = 2.8;
    //     cc.tween(this.node).delay(delayTime)
    //         .call(function () {
    //             if (self.isChangeBanker) {
    //                 cc.tween(self.node)
    //                     .delay(2)
    //                     .call(function () {
    //                         self.sendCard();
    //                         self.hideTipWait();
    //                     })
    //                     .start();

    //                 self.isChangeBanker = false;
    //             } else {
    //                 self.sendCard();
    //                 self.hideTipWait();
    //             }
    //         })
    //         .start();

    // }




    //字符格式化
    public stringFormat(strContent: string, params: any[]): string {
        var str = strContent;
        for (var i = 0; i < params.length; i++) {
            var reg = new RegExp("\\{" + i + "\\}", "gm");
            str = str.replace(reg, params[i]);
        }
        return str;
    }

    // 获取金币字符串 count: 0不加K; 1-n: 超过指定位数时加上K.  decimals: 显示小数位数
    moneyFormat(money: number, decimals: number = 0): string {
        return (money / Config.SCORE_RATE).toFixed(decimals);
    }
    // //重置自己的下注
    // betFaild() {
    //     let self = this;
    //     if (self._gameCore.model.isBetMax && self.betMsg) {
    //         //print("重置自己的下注, ", self.betMsg.long_self, self.betMsg.hu_self, self.betMsg.he_self);
    //         self.TouchBet[0].self_label.getComponent(cc.Label).string = self.moneyFormat(self.betMsg.long_self)
    //         self.TouchBet[1].self_label.getComponent(cc.Label).string = self.moneyFormat(self.betMsg.long_self)
    //         self.TouchBet[2].self_label.getComponent(cc.Label).string = self.moneyFormat(self.betMsg.long_self)
    //         self.autoSelectChip();
    //     }
    // }
    // //初始化历史记录
    // private initHistory() {
    //     let self = this;
    //     //--历史记录
    //     self.historyNode = this.alterLayer.getChildByName("historyPanel").getChildByName("mainBg");
    //     self.historyBox = self.historyNode.getChildByName("historyBox");
    //     self.carResultPanel = {};
    //     // this._history.initzoushiUI(self);
    // }
    // //--结算更新走势
    // updateResultTrend(data) {
    //     // this._history.updateData(data, 3);
    // }
    // //--更新走势
    // updateTrendData(data: any) {
    //     // this._history.initzoushi(data);

    // }
    // initTrendData(msg: any) {
    //     let self = this;
    //     self.lastRoundArea = 0;
    //     for (let i = 0; i < msg.data.length; i++) {
    //         if (self.historyListTb.length < 18) {
    //             self.historyListTb.push(msg.data[i]);
    //         }
    //         if (self.historyListTb.length >= 18) {
    //             self.historyListTb.splice(0, 1);
    //             self.historyListTb.push(msg.data[i]);
    //         }
    //     }

    // }
    // //更新游戏走势小图
    // onUpdateHistoryIcon(msg: any) {
    //     this.historyList.removeAllChildren();
    //     if (!this.historyList) {
    //         return
    //     }
    //     //计算只显示最后17局数据
    //     let keys = [];
    //     for (let key in msg) {
    //         if (Number(key) > 0) {
    //             keys.push(key);
    //         }
    //     }
    //     if (keys.length > 17) {
    //         keys.splice(0, keys.length - 17);
    //     }

    //     for (let i = 0; i < 17; i++) {

    //     }
    //     for (let i = 0; i < keys.length; i++) {
    //         this.addHistoryItem(msg[keys[i]].win - 1);
    //     }

    // }

    // //--结算更新历史记录
    // addHistoryItem(winArea: number) {
    //     let self = this;
    //     let imagArr = ["img_dot_item_long", "img_dot_item_hu", "img_dot_item_he"]
    //     if (this.historyList.childrenCount >= 17) {
    //         this.historyList.children[0].removeFromParent();
    //     }
    //     let item: cc.Node = cc.instantiate(this.recordItem); //self.historyList.children[index];
    //     let img_res = imagArr[winArea];
    //     item.getComponent(cc.Sprite).spriteFrame = self.historyAtlas.getSpriteFrame(img_res);
    //     self.historyList.addChild(item);
    //     item.active = true;
    // }


    //     let tempPlayerScroll: cc.Node = self._onlineUserLayer
    //         .getChildByName("mainBg")
    //         .getChildByName("scrollView");
    //     let tempPlayerList: cc.Node = tempPlayerScroll
    //         .getChildByName("view")
    //         .getChildByName("content");
    //     // let eventHandler = new cc.Component.EventHandler();
    //     // eventHandler.component = cc.js.getClassName(this);
    //     // eventHandler.handler = 'refreshGoldItem';
    //     // eventHandler.target = this.node;
    //     // this.tempPlayerScroll.refreshScrollView(this._goldData, eventHandler);
    //     //return;
    //     tempPlayerList.active = false;
    //     for (let i = 0; i < sortList.length; i++) {
    //         let user = self.cardletPlayerList[sortList[i]];
    //         let item: cc.Node;
    //         if (i < tempPlayerList.childrenCount) {
    //             self.set_playerListData(tempPlayerList.children[i], user);
    //         } else {
    //             item = cc.instantiate(self.onLineItemPrefab);
    //             let rankImg: cc.Node = item.getChildByName("rankBox").getChildByName("rankImg");
    //             let rankLabel: cc.Node = item.getChildByName("rankBox").getChildByName("rankLabel");
    //             //item.playerid = sortList[i];
    //             if (i < 9) {
    //                 self.setSpriteFrame(rankImg.getComponent(cc.Sprite), "res/images/playerList/rich_" + (i) + "")
    //                 rankImg.active = true;
    //                 rankLabel.active = false;
    //             } else {
    //                 rankLabel.getComponent(cc.Label).string = "No." + i;
    //                 rankImg.active = false;
    //                 rankLabel.active = true;
    //             }

    //             if (i < 2) {
    //                 self.setSpriteFrame(item.getComponent(cc.Sprite), "res/images/playerList/rank_cell_l");
    //                 //self.setSpriteAtlas(item.getComponent(cc.Sprite),"res/images/playerList","rank_cell_l")
    //             } else {
    //                 self.setSpriteFrame(item.getComponent(cc.Sprite), "res/images/playerList/rank_cell_d");
    //                 // self.setSpriteAtlas(item.getComponent(cc.Sprite),"res/images/playerList","rank_cell_d")
    //             }
    //             self.set_playerListData(item, user);
    //             tempPlayerList.addChild(item);

    //         }
    //     }

    //     let len = tempPlayerList.childrenCount - sortList.length;
    //     let childCount = tempPlayerList.childrenCount;
    //     if (len > 0) {
    //         for (let i = 0; i < len; i++) {
    //             tempPlayerList.removeChild(tempPlayerList.children[childCount - i]);
    //         }
    //     }
    //     childCount = tempPlayerList.childrenCount;
    //     let itemHeight: number = childCount > 0 ? tempPlayerList.children[0].height : 0;
    //     itemHeight += 5;
    //     tempPlayerList.height = itemHeight * childCount - 5;
    //     if (isTop) {
    //         tempPlayerScroll.getComponent(cc.ScrollView).scrollToPercentVertical(100);
    //     }
    //     tempPlayerList.active = true;
    // }
    // /**
    //  * 设置玩家列表数据
    //  * @param item 
    //  * @param usermsg 
    //  * @param index 
    //  */
    // set_playerListData(item: cc.Node, usermsg: any, index?: number) {
    //     let self = this;
    //     let userinfo = self.getUserInfo(usermsg.playerid);
    //     super.setPlayerHead(item.getChildByName("headBox").getChildByName("headImg").getComponent(cc.Sprite), usermsg.headid, (userinfo && userinfo.wxheadurl));
    //     item.getChildByName("goldBox").getChildByName("goldLabel").getComponent(cc.Label).string = this.moneyFormat(usermsg.coin);//utils:moneyString(usermsg.coin)
    //     item.getChildByName("goldBox").getChildByName("gameLabel").getComponent(cc.Label).string = Common.textClamp(usermsg.name, 20, "...");//utils:nameStandardString(tostring(usermsg.name), 24, 180)
    //     item.getChildByName("roundBox").getChildByName("betLabel").getComponent(cc.Label).string = this.moneyFormat(usermsg.bet);//utils:moneyString(usermsg.bet) 
    //     item.getChildByName("roundBox").getChildByName("winBox").getChildByName("winLabel").getComponent(cc.Label).string = this.moneyFormat(usermsg.winnum);
    // }
    //结算
    onResult(msg: any) {
        let self = this;
        let resultMsg: any = {};
        resultMsg._playerid = msg._playerid;
        resultMsg.other = {};
        resultMsg.resultPos = msg.resultPos - 1;
        resultMsg.win_value = msg.win_value;
        resultMsg.others = { betrank: {} };
        resultMsg.winList = [0, 0, 0, 0, 0, 0, 1, 0]
        if (msg.other) {
            let count = 0;
            for (let key in msg.other) {
                let item = msg.other[key];
                let tmp = item.split(",");
                let userData: any = {};
                userData.playercoin = parseInt(tmp[0]);
                userData.nChange = parseInt(tmp[1]);
                userData.playerid = key;
                count += 1;
                resultMsg.others.betrank[count.toString()] = userData;
            }
        }
        this._gameCore._settleData = resultMsg;
        // self.onGameResult(resultMsg);
    }

    //筹码飞向庄家
    chipToBanker() {
        let endNode = this.LayerItems.node_betFlyZhuang;
        let endPos = endNode.convertToWorldSpaceAR(endNode.getAnchorPoint())
        endPos = cc.v2(this.LayerItems.chipsNode.convertToNodeSpaceAR(endPos));
        let self = this;
        for (let areaIndex = 0; areaIndex < CAR.AREA_MAX_NUM; areaIndex++) {
            if (this._gameCore._settleData.winList[areaIndex] <= 0 && this._gameCore._chips[areaIndex]) {
                this._gameCore._chips[areaIndex].forEach((item, index, listLen) => {
                    let moveTime = Common.getTwoPointDistance(item.node.position, endPos) / 2800;
                    let delayTime = Common.random(1, Math.min(500, index * 100)) / 1000;
                    cc.tween(item.node)
                        .delay(delayTime)
                        .to(moveTime, { position: endPos }, { easing: "cubicOut" })
                        .call(() => {
                            CARChipPoolAssisCom.instance.put(item.node);
                            item.node = null;
                            if (index == listLen.length - 1) {
                                self._gameCore._chips[areaIndex] = [];
                                self._gameAudio._loadPlaySFX("res/audio/bet");
                            }
                        })
                        .start()
                })
            }
        }
    }

    //自己及桌上玩家自定义位置及总输赢
    playerWinOrLose() {
        let nChangeList = [0, 0, 0, 0, 0, 0, 0, 0];//0~5 在座玩家 6 自己 7 在线其它玩家
        let result = this._gameCore._settleData;
        for (let key in result.others.betrank) {
            let player = result.others.betrank[key];
            if (!!player) {
                if (player.playerid == this._playerView._MyInfo.playerid) {//自己
                    nChangeList[CAR.CUSTOM_MYSELF_POS] = player.nChange;
                }
                else {
                    let deskPos = this.getDeskPlayerPos(player.playerid);
                    if (deskPos != CAR.SEAT_POS_INDEX_OTHER) {//桌上玩家
                        nChangeList[deskPos] = player.nChange;
                    }
                    else {//其它玩家 无论输赢 都飞币 
                        nChangeList[deskPos] = 1;
                    }
                }
            }
        }
        return nChangeList;
    }

    //筹码飞向赢的区域
    chipToWinArea() {
        let self = this;
        let result = this._gameCore._settleData;
        let nChangeList = this.playerWinOrLose();
        let newCreateChipList = [[], [], [], [], [], [], [], []];//区域所需绘制筹码数
        for (let areaIndex = 0; areaIndex < CAR.AREA_MAX_NUM; areaIndex++) {
            if (result.winList[areaIndex] > 0) {
                this._gameCore._chips[areaIndex].forEach((item) => {
                    let playerPos = item.node.getComponent('CARChipItem').betPlayerPos;
                    (Common.isNull(playerPos) || playerPos < 0 || playerPos > CAR.CUSTOM_OTHER_POS) && (playerPos = CAR.CUSTOM_OTHER_POS);
                    if (nChangeList[playerPos] > 0) {
                    }
                    else {//当前区域玩家是赢 但总分是输 将原筹码飞向其它玩家
                        item.node.getComponent('CARChipItem').betPlayerPos = CAR.CUSTOM_OTHER_POS;
                        self._gameCore._areaInfoList[areaIndex].betAreaScoreSumList[playerPos] = 0;
                        self._gameCore._areaInfoList[areaIndex].betAreaDelScoreSumList[playerPos] = 0;
                    }
                });
                let betAreaScoreSumList = this._gameCore._areaInfoList[areaIndex].betAreaScoreSumList;
                for (let i = 0; i < betAreaScoreSumList.length; i++) {
                    if (this._gameCore._areaInfoList[areaIndex].betAreaScoreSumList[i] > 0) {
                        //赢家当前区域总下注值 * 乘以当前区域中奖倍数 + 已经移除的筹码值
                        let score = betAreaScoreSumList[i] * this._gameCore._areaInfoList[areaIndex].winSameMulti * AREA_BEISHU[areaIndex]
                            + this._gameCore._areaInfoList[areaIndex].betAreaDelScoreSumList[i];
                        let winBet = [];
                        if (i == CAR.CUSTOM_MYSELF_POS) {//真实数量
                            winBet = this.chipInValueSplit(score);// this.chipInValueSplit(score,i != CAR.CUSTOM_OTHER_POS);//自己及桌上六个玩家 拆分为真实筹码 其它在线玩家 拆分时一半为假筹码
                        }
                        else {
                            winBet = this.splitUserBetChipScore(score, 5)
                        }

                        for (let j = 0; j < winBet.length; j++) {
                            let chipRestule = this.onlinePlayersChipCreate(winBet[i], this.LayerItems.node_betFlyZhuang, areaIndex, null, i);
                            if (!!chipRestule) {
                                newCreateChipList[areaIndex].push(chipRestule);
                                this._gameCore._chips[areaIndex].push({ node: chipRestule.node, score: chipRestule.score, playerPos: i });
                            }
                        }
                    }
                }
            }
        }

        let startNode = this.LayerItems.node_betFlyZhuang;
        let startPos = startNode.convertToWorldSpaceAR(startNode.getAnchorPoint())
        startPos = cc.v2(this.LayerItems.chipsNode.convertToNodeSpaceAR(startPos));
        for (let areaIndex = 0; areaIndex < CAR.AREA_MAX_NUM; areaIndex++) {
            if (this._gameCore._settleData.winList[areaIndex] > 0 && newCreateChipList[areaIndex].length > 0) {
                let chipPosNode = this._gameCore._areaInfoList[areaIndex].node;
                let endPos = chipPosNode.convertToWorldSpaceAR(chipPosNode.getAnchorPoint())
                endPos = cc.v2(this.LayerItems.chipsNode.convertToNodeSpaceAR(endPos));
                newCreateChipList[areaIndex].forEach((item, index, listLen) => {
                    let addOrFalse = (Common.random(1, 10)) % 2 == 1 ? 1 : -1  // 左或者右
                    let addOrFalse2 = (Common.random(1, 20)) % 2 == 1 ? 1 : -1
                    let endPos1 = cc.v2(endPos.x + addOrFalse * Math.random() * (chipPosNode.width / 2 - 30), endPos.y - 15 + addOrFalse2 * Math.random() * (chipPosNode.height / 2 - 40))
                    let moveTime = Common.getTwoPointDistance(startPos, endPos1) / 2800;
                    let delayTime = Common.random(1, Math.min(500, index * 100)) / 1000;
                    item.node.active = true;
                    item.node.opacity = 255;
                    cc.tween(item.node)
                        .delay(delayTime)
                        .to(moveTime, { position: endPos1 }, { easing: "cubicOut" })
                        .call(() => {
                            if (index == listLen.length - 1) {
                                self._gameAudio._loadPlaySFX("res/audio/bet");
                            }
                        })
                        .start()
                })
            }
        }
    }

    //筹码飞向赢家
    chipToWinUser() {
        let self = this;
        for (let areaIndex = 0; areaIndex < CAR.AREA_MAX_NUM; areaIndex++) {
            if (this._gameCore._settleData.winList[areaIndex] > 0 && this._gameCore._chips[areaIndex]) {
                this._gameCore._chips[areaIndex].forEach((item, index, listLen) => {
                    let betPlayerPos = item.node.getComponent('CARChipItem').betPlayerPos;
                    let endNode = self._playerView._playerInfo[betPlayerPos].node;
                    let endPos = endNode.convertToWorldSpaceAR(endNode.getAnchorPoint())
                    endPos = cc.v2(self.LayerItems.chipsNode.convertToNodeSpaceAR(endPos));
                    let moveTime = Common.getTwoPointDistance(item.node.position, endPos) / 2800;
                    let delayTime = Common.random(1, Math.min(500, index * 100)) / 1000;
                    cc.tween(item.node)
                        .delay(delayTime)
                        .to(moveTime, { position: endPos }, { easing: "cubicOut" })
                        .call(() => {
                            CARChipPoolAssisCom.instance.put(item.node);
                            item.node = null;
                            if (index == listLen.length - 1) {
                                self._gameCore._chips[areaIndex] = [];
                                self._gameAudio._loadPlaySFX("res/audio/bet");
                            }
                        })
                        .start()
                })
            }
        }
    }

    //游戏结算
    onGameResult(msg: any) {
        let self = this;
        new Promise((resolve) => {
            this.LayerItems.settleRunAniNode.stopAllActions();
            let recoveryChipTween = cc.tween(this.LayerItems.settleRunAniNode)
                .call(() => {
                    self.chipToBanker();
                })
                .delay(1)

            let isWin: boolean = false;
            self._gameCore._settleData.winList.forEach(value => { value > 0 && (isWin = true) });
            if (isWin) {
                recoveryChipTween
                    .call(() => {
                        self.chipToWinArea();
                    })
                    .delay(1)
                    .call(() => {
                        self.chipToWinUser();
                    })
                    .delay(1)
            }
            recoveryChipTween.call(() => {
                self.resultFlyNumber();
                self._playerView.onUpdateUserData(self._gameCore._settleData.others);
            });
            recoveryChipTween.delay(3)
            recoveryChipTween.call(() => {
                self._gameCore._isGameEnd = true;
                self._test = false;
                self._gameCore.requestAllPlayerList();
                // self.cleanGameData();
                self.onStartBet()
                resolve(true);
            });
            recoveryChipTween.start();
        })
        // cc.tween(this.LayerItems.settleRunAniNode)
        //     .call(function () {
        //         self.flyResultNumber(msg);
        //         // self.addHistoryItem(msg.resultPos);
        //         // self.updateResultTrend(msg.resultPos + 1);
        //         cc.tween(self.LayerItems.settleRunAniNode)
        //             .delay(1.0)
        //             .call(function () {
        //                 let data: any = {};
        //                 data.win = msg.resultPos;
        //                 data.Value = msg.win_value;
        //                 // self.historyListTb.unshift();
        //                 // self.historyListTb.push(data);

        //                 if (self.BankerInfo.playerid != self._playerView._MyInfo.playerid) {
        //                     self.autoSelectChip();
        //                 }

        //                 self.onUpdateUserData(msg.others);
        //                 // console.error('清理时间111..........' + Common.formatDateTimeNormal())

        //                 cc.tween(self.LayerItems.settleRunAniNode)
        //                     .delay(0.6)
        //                     .call(function () {
        //                         self.LayerItems.winSoundNode.removeAllChildren();
        //                         self._gameCore.requestAllPlayerList();
        //                         self.isGameEnd = true;
        //                         // console.error('清理时间222..........' + Common.formatDateTimeNormal())
        //                         cc.tween(self._centerLayer).delay(1).call(function () {
        //                             // console.error('清理时间333..........' + Common.formatDateTimeNormal())
        //                             self.cleanGameData();
        //                         }).start();
        //                     }).start();
        //                 self._playerView._MyInfo.isBet = false;
        //             }).start();
        //     }).start();

    }

    resetData() {

        this.LayerItems.node_player_other.stopAllActions();
        this.LayerItems.otherLayer.stopAllActions();
        this.LayerItems.otherLayer.removeAllChildren();
        this.LayerItems.chipsNode.removeAllChildren();
        this.LayerItems.star_fly_1_plist.opacity = 0;

        if (this._betScheduler) {
            this.unschedule(this._betScheduler);
            this._betScheduler = null;
            this.setCountTime(false);
        }

        if (this._waitNextScheduler) {
            this.unschedule(this._waitNextScheduler);
            this._waitNextScheduler = null;
            this.LayerItems.img_gametip_start.active = false;
        }

        this.setStopAniPlay(false);
    }

    //清除游戏数据
    cleanGameData() {
        //console.log("===================清除游戏数据==================");
        this._gameCore.resetData();
        this.resetData();
        this._lotteryBox.resetData();
        this._playerView.resetData();
    }

    //其它玩家金币定时一起飞
    onlineBetAct() {
        let self = this;
        this.LayerItems.node_player_other.stopAllActions();
        let startTime = 1.2
        let randIndex = 1;
        let subTime = 0.01;
        cc.tween(this.LayerItems.node_player_other)
            .repeatForever(
                cc.tween()
                    .call(() => {
                        self.onlineChipsAct(startTime, randIndex, 1);
                        if (startTime < 0.3) {
                            startTime = 0.3;
                        }
                        else {
                            startTime -= subTime
                        }
                    })
                    .delay(0.4)
            )
            .start();

        let startTime1 = 0.8
        let bRessetFlag1 = false;
        cc.tween(this.LayerItems.node_player_other)
            .delay(1.2)
            .repeatForever(
                cc.tween()
                    .call(() => {
                        self.onlineChipsAct(startTime1, 2, 2);
                        randIndex = 2;
                        subTime = 0.02
                        if (startTime1 < 0.4) {
                            startTime1 = 1;
                            bRessetFlag1 = true;
                        }
                        else {
                            if (bRessetFlag1) {
                                startTime1 -= Common.random(15, 40) * 0.001
                            }
                            else {
                                startTime1 -= 0.02
                            }
                        }
                    })
                    .delay(0.2)
            )
            .start();
    }

    //其它在线玩家飞筹码
    private onlineChipsAct(startTime: number, betNumIndex: number, operListIndex: number) {
        let flyList = this._gameCore._flyingOnlineBets1;
        let list = [Common.random(8, 20), Common.random(10, 35), Common.random(40, 60)]
        if (operListIndex == 2) {
            flyList = this._gameCore._flyingOnlineBets2;
            list = [Common.random(8, 15), Common.random(15, 30), Common.random(30, 50)]
        }

        let newCreateChipList = [[], [], [], [], [], [], [], []];//区域所需绘制筹码数
        let maxDrawNum: number = list[betNumIndex]// Common.random(30,50);//每次最多只用个数
        let len = flyList.length > maxDrawNum ? maxDrawNum : flyList.length;
        for (let i = len - 1; i >= 0; i--) {
            let val = flyList[i];
            let chipRestule = this.onlinePlayersChipCreate(val.score, val.player, val.area, val.dirctionall, this.getDeskPlayerPos(val.playerid));
            newCreateChipList[val.area].push(chipRestule);
            flyList.splice(i, 1);
        }

        for (let i = 0; i < newCreateChipList.length; i++) {
            let areaChipList = newCreateChipList[i];
            this.removeAreaMoreChips(i);
            for (let j = 0; j < areaChipList.length; j++) {
                if (!this._gameCore._isStartBet) {
                    break;
                }
                let chip = areaChipList[j];
                if (!!chip) {
                    this.onlineChipsMove(chip, startTime);
                }
            }
        }
    }

    //其它玩家飞币 先创建筹码
    private onlinePlayersChipCreate(score: any, player: any, area: any, dirctionall: any, playerPos: number = CAR.SEAT_POS_INDEX_OTHER): any {
        let chipValue = 0;
        let chipIndex = this._gameCore._chipNumList.findIndex(item => item == score);
        if (chipIndex > -1 && chipIndex < this._gameCore._chipNumList.length) {
            chipValue = this._gameCore._chipNumList[chipIndex] / Config.SCORE_RATE;
        } else {
            //没有找到筹码
            return null;
        }
        let moveChipImg: cc.SpriteFrame = this.mainUiAtlas.getSpriteFrame(`car_chip_fly_${chipValue}`)
        if (!moveChipImg) {
            this.show(this.stringFormat(GameTextTips.GAME_BET_SCORE_ERR, [this.moneyFormat(score)]));
            return null;
        }

        let chip: cc.Node = CARChipPoolAssisCom.instance.get();
        if (!chip) {
            return null;
        }
        this._gameCore._chipPoolList.push(chip);
        this._gameCore._areaInfoList[area].betAreaScoreSumList[playerPos] += score;
        chip.opacity = 255;
        chip.getComponent('CARChipItem').betPlayerPos = playerPos;
        chip.getComponent(cc.Sprite).spriteFrame = moveChipImg;
        let nodePos = cc.v2(this.LayerItems.chipsNode.convertToNodeSpaceAR(player.convertToWorldSpaceAR(player.getAnchorPoint())));
        chip.setPosition(nodePos.x, nodePos.y);
        chip.angle = this.getRandom(-50, 50);
        this.LayerItems.chipsNode.addChild(chip);
        chip.active = false;

        return { node: chip, score: score, player: player, area: area, dirctionall: dirctionall, playerPos: playerPos };
    }

    //在线其它玩家筹码飞动
    onlineChipsMove(chip: any, betTime: number) {
        let self = this;
        let chipPosNode = this._gameCore._areaInfoList[chip.area].node;
        let endPos = chipPosNode.convertToWorldSpaceAR(chipPosNode.getAnchorPoint())
        endPos = cc.v2(this.LayerItems.chipsNode.convertToNodeSpaceAR(endPos));
        let addOrFalse = (Common.random(1, 10)) % 2 == 1 ? 1 : -1  // 左或者右
        let addOrFalse2 = (Common.random(1, 20)) % 2 == 1 ? 1 : -1
        let eP = cc.v2(endPos.x + addOrFalse * Math.random() * (chipPosNode.width / 2 - 30), endPos.y - 15 + addOrFalse2 * Math.random() * (chipPosNode.height / 2 - 40))
        chip.node.active = true;
        cc.tween(chip.node)
            .to(betTime, { position: eP }, { easing: 'expoOut' })
            .call(() => {
                self.updateAreaTotal(chip.area, chip.dirctionall);
            })
            .start();
        if (!self._gameCore._isPlayingBetSound) {
            self._gameCore._isPlayingBetSound = true
            self._gameAudio._loadPlaySFX("res/audio/bet");
            self.scheduleOnce(function () {
                self._gameCore._isPlayingBetSound = false;
            }, 0.1)
        }
        this._gameCore._chips[chip.area].push({ node: chip.node, score: chip.score, playerPos: chip.playerPos });
    }

    //删除过多的筹码
    removeAreaMoreChips(area: number) {
        if (this._gameCore._chips[area].length > CAR.areaShowMaxChipsNum) {
            for (let key = 0; key < this._gameCore._chips[area].length; key++) {
                if (this._gameCore._chips[area].length < CAR.areaShowMaxChipsNum) {
                    break;
                }
                let val = this._gameCore._chips[area][key];
                if (val) {
                    this._gameCore._areaInfoList[area].betAreaDelScoreSumList[val.playerPos] += val.score;//桌上被移走的筹码数
                    CARChipPoolAssisCom.instance.put(val.node);
                    val = null;
                    this._gameCore._chips[area].splice(key, 1);
                    key--;
                }
            }
        }
    }


    //同步玩家下注筹码
    updateChipMsg(playerid: any, count: number, chipNum: number, betinfo: any, area: number, total: {}) {

        let gameMyInfo = this._gameCore.userInfo;
        if (!this._gameCore._runPlayerBetAnimFlag) {
            this._gameCore._runPlayerBetAnimFlag = true;
            this._gameCore._chipsCount = 0;
            this.onlineBetAct();
        }

        let msg: any = {};
        msg.playerid = playerid;
        msg.chouma = betinfo.chouma;
        msg.odds = chipNum;
        msg.direction = area;
        msg.dirctionall = total[area];
        this._playerView.onUpdateUserBetUserInfo(msg);

        let playerNode = null;
        if (playerid == gameMyInfo.playerid) {
            return;
        }

        //桌上玩家
        for (let userindex = 0; userindex < CAR.CUSTOM_MYSELF_POS; userindex++) {
            if (msg.playerid == this._playerView._playerInfo[userindex].playerid) {
                playerNode = this._playerView._playerInfo[userindex];
                this.userBetHeadMove(playerNode);
                this._playerView.updatePlayerMoney(userindex, msg.chouma);
                break;
            }
        }

        //在线其它玩家
        if (Common.isNull(playerNode)) {
            this._gameCore._chipsCount += 1;
            let operListIndex = Math.ceil(this._gameCore._chipsCount / 50) % 2 == 1 ? 1 : 2;//插入不同数量到列表
            this.addUserBetMsgInfo(operListIndex, msg);
            return;
        }

        let self = this;
        if (count > 0) {
            this.deskChipMove(msg.odds, playerNode.node, msg.direction, function () {
                self.updateAreaTotal(msg.direction, msg.dirctionall);
            }, this.getDeskPlayerPos(msg.playerid));
        }
        this.updateAreaTotal(msg.direction, msg.dirctionall);
        if (msg.playerid == this._playerView._playerInfo[CAR.LUCKY_PLAYER_POS].playerid) {//神算子位置
            this.playLuckStarAction(msg.direction);
        }
    }

    //桌上玩家索引
    getDeskPlayerPos(playerid: number) {
        let deskPlayerPos: number = CAR.SEAT_POS_INDEX_OTHER;
        for (let userindex = 0; userindex < CAR.CUSTOM_MYSELF_POS; userindex++) {
            if (playerid == this._playerView._playerInfo[userindex].playerid) {
                deskPlayerPos = userindex;
                break;
            }
        }
        return deskPlayerPos;
    }

    //更新总下注金额
    updateAreaTotal(areaIndex: number, areaTotalBet: number) {
        let areaAll = areaTotalBet;
        let areaInfo = this._gameCore._areaInfoList[areaIndex];
        if (Common.toNumber(areaAll) > Common.toNumber(areaInfo.areaTotalNum)) {
            areaInfo.areaTotalNum = areaAll;
            this.setAreaBetNum(areaIndex, areaAll, areaInfo.myself_betsNum);
        }
    }


    //添加下注信息到列表
    addUserBetMsgInfo(operListIndex: number, msg) {
        let list = this._gameCore._userBetMsgTemp1;
        if (operListIndex == 2) {
            list = this._gameCore._userBetMsgTemp2;
        }
        list.push(msg);
        this.onOtherUserBet(operListIndex);
    }

    //玩家下注
    onOtherUserBet(operListIndex: number) {
        let list = this._gameCore._userBetMsgTemp1;
        let flyList = this._gameCore._flyingOnlineBets1;
        if (operListIndex == 2) {
            list = this._gameCore._userBetMsgTemp2;
            flyList = this._gameCore._flyingOnlineBets2;
        }

        for (let i = 0; i < list.length; i++) {
            let key = i;
            let val = list[i];
            if (val) {
                if (val.odds > this._gameCore._chipNumList[this._gameCore._chipNumList.length - 1]) {
                    let userBet = this.splitUserBetChipScore(val.odds, 3);
                    for (let j = 0; j < userBet.length; j++) {
                        flyList.push({ score: userBet[j], player: this.LayerItems.node_player_other, area: val.direction, dirctionall: val.dirctionall })
                    }

                } else {
                    flyList.push({ score: val.odds, player: this.LayerItems.node_player_other, area: val.direction, dirctionall: val.dirctionall })
                }
            }
            list.splice(key, 1);
            i--;
        }
    }

    //数组随机，改变原数组
    arrayRandom(array) {
        let len = array.length;
        while (len > 1) {
            let index = Math.floor(Math.random() * len--);
            [this[len], this[index]] = [this[index], this[len]]
        }
    }

    // 根据传入总值拆分成筹码数组（筹码太多，回收的时候效率不高，只显示百分之五十的筹码和一部分假筹码） unFake 如果true就不做假筹码;
    chipInValueSplit(value: number, unFake: boolean = true): Array<number> {
        let chips: number[] = this._gameCore._chipNumList;
        let _chipFakeCount = [2, 1, 1, 2, 2, 2];
        if (value <= 0) return [0];
        try {
            if (value <= chips[chips.length - 1] * 0.5) unFake = true;
        } catch (error) {
            return;
        }
        let values = new Array();
        let index = chips.length;
        // 值对应筹码值不拆分
        for (let i = 0; i < chips.length; ++i) {
            if (chips[i] == value) {
                values.push(value);
                return values;
            }
        }
        for (index; index >= 0; --index) {
            while (value > chips[index]) {
                values.push(chips[index])
                value = value - chips[index];
            }
        }
        if (value > 0) {
            for (value; value > 0;) {
                value = value - chips[index + 1];
                values.push(chips[index + 1]);
            }
        }
        if (!unFake) {
            this.arrayRandom(values)
            values = values.slice(0, Math.ceil(values.length * 0.5));  // 乱序后取长度的50% 做假筹码
            for (let i = 0; i < chips.length - 1; ++i) {
                let min = 0;
                let max = _chipFakeCount[i];
                let times = Math.floor(Math.random() * (max - min + 1)) + min;
                for (times; times > 0;) {
                    let tValue = chips[i];
                    values.push(tValue);
                    times--;
                }
            }
        }
        return values.reverse();
    }

    //--根据下注总分数 拆分筹码分数
    splitUserBetChipScore(score: any, count: number): number[] {
        let publicScore = this._gameCore._chipNumList;
        let chipScore = score;
        let tempCount = 0;
        let userScore: number[] = [];
        for (let i = 0; i < 5; i++) {
            tempCount = Math.floor(chipScore / publicScore[i]);
            chipScore = publicScore[i];
            //--print("拆分筹码分数：", tempCount, i);
            if (tempCount > count) {
                tempCount = Math.floor(count / (i + 1));
            }
            //--print("拆分筹码循环：", tempCount, i);
            for (let j = 0; j < tempCount; j++) {
                userScore.push(chipScore);
            }

            chipScore = score - (userScore.length * 100);
        }
        return userScore;
    }

    //玩家头像移动
    userBetHeadMove(player: playInfo) {
        if (!this._gameCore._isStartBet) {
            return;
        }
        if (!player.isMove) {
            player.isMove = true;
            let movePos = 20;
            if (player.chairid % 2 == 1) {
                movePos = -20;
            }

            let oldSize = player.node.getPosition();
            cc.tween(player.node)
                .to(0.08, { position: cc.v3(player.node.x + movePos, player.node.y) })//, { easing: 'sineInOut' }
                .to(0.08, { position: cc.v3(oldSize.x, oldSize.y) })//, { easing: 'sineInOut' }
                .call(function () {
                    player.isMove = false;
                }).start();
        }
    }
    //自家头像移动
    userMyselfBetHeadMove(player: playInfo) {
        if (!player.isMove) {
            player.isMove = true;
            let movePosX = 210;
            let movePosY = 20;

            let oldSize = player.node.getPosition();
            cc.tween(player.node)
                .to(0.05, { position: cc.v3(player.node.x + movePosX, player.node.y + movePosY) })
                .to(0.05, { position: cc.v3(oldSize.x, oldSize.y) })
                .call(function () {
                    player.isMove = false;
                }).start();
        }
    }

    //结算 玩家赢的分数 计算筹码拆分
    splitChipScore(score: any, selectChipTb?: number[]): number[] {
        let self = this;
        if (score < 1) {
            return [0];
        }
        let publicScore = selectChipTb && selectChipTb || self._gameCore._chipNumList;
        let chipScore = score;
        let tempCount = 0;
        let userScore: number[] = [];
        //每个筹码最大显示数量
        let maxChipCount = [10, 8, 6, 4, 100];
        for (let i = 0; i < publicScore.length; i++) {
            // tempCount = Math.floor(chipScore / publicScore[publicScore.length-i-1]);
            // chipScore = publicScore[publicScore.length-1-i];
            //调整结算筹码 分拆大量
            tempCount = Math.floor(chipScore / publicScore[i]);
            tempCount = maxChipCount[i] < tempCount ? maxChipCount[i] : tempCount;
            let itemChipScore = publicScore[i];

            //--print("拆分筹码循环：", tempCount, i);
            for (let j = 0; j < tempCount; j++) {
                userScore.push(itemChipScore);
                chipScore -= itemChipScore;
            }

            //chipScore = score % publicScore[publicScore.length-1-i];

        }
        return userScore;

    }

    //获取指定范围的随机数
    private getRandom(minNum: number, maxNum?: number): number {
        return Common.random(minNum, maxNum);
    }

    //筹码飞动
    deskChipMove(score: any, player: any, area: any, call: () => void, playerPos: number = CAR.SEAT_POS_INDEX_OTHER) {
        if (!this._gameCore._isStartBet) {
            return;
        }
        let moveResult = this.chipMoveAct(score, player, area, call, playerPos);
        if (moveResult) {
            if (!this._gameCore._chips[area]) {
                this._gameCore._chips[area] = [];
            }
            this._gameCore._chips[area].push(moveResult);
        }
    }
    //筹码飞动效果
    private chipMoveAct(score: any, player: any, area: any, call: () => void, playerPos: number = CAR.SEAT_POS_INDEX_OTHER): any {

        let chipValue = 0;
        let chipIndex = this._gameCore._chipNumList.findIndex(item => item == score);
        if (chipIndex > -1 && chipIndex < this._gameCore._chipNumList.length) {
            chipValue = this._gameCore._chipNumList[chipIndex] / Config.SCORE_RATE;
        } else {
            //没有找到筹码
            return null;
        }
        let moveChipImg: cc.SpriteFrame = this.mainUiAtlas.getSpriteFrame(`car_chip_fly_${chipValue}`)
        if (!moveChipImg) {
            this.show(this.stringFormat(GameTextTips.GAME_BET_SCORE_ERR, [this.moneyFormat(score)]));
            return null;
        }
        let chip: cc.Node = CARChipPoolAssisCom.instance.get();
        if (!chip) {
            return null;
        }
        this._gameCore._chipPoolList.push(chip);
        this._gameCore._areaInfoList[area].betAreaScoreSumList[playerPos] += score;
        chip.opacity = 255;
        chip.getComponent('CARChipItem').betPlayerPos = playerPos;
        chip.getComponent(cc.Sprite).spriteFrame = moveChipImg;
        let nodePos = cc.v2(this.LayerItems.chipsNode.convertToNodeSpaceAR(player.convertToWorldSpaceAR(player.getAnchorPoint())));
        chip.setPosition(nodePos.x, nodePos.y);
        chip.active = true;
        let rotation = this.getRandom(-50, 50);

        chip.angle = rotation;
        let chipPosNode = this._gameCore._areaInfoList[area].node;
        let endPos = chipPosNode.convertToWorldSpaceAR(chipPosNode.getAnchorPoint())
        endPos = cc.v2(this.LayerItems.chipsNode.convertToNodeSpaceAR(endPos));
        this.LayerItems.chipsNode.addChild(chip);

        let addOrFalse = (Common.random(1, 10)) % 2 == 1 ? 1 : -1  // 左或者右
        let addOrFalse2 = (Common.random(1, 20)) % 2 == 1 ? 1 : -1
        let eP = cc.v2(endPos.x + addOrFalse * Math.random() * (chipPosNode.width / 2 - 30), endPos.y - 15 + addOrFalse2 * Math.random() * (chipPosNode.height / 2 - 40))

        let distance = nodePos.sub(eP).mag();
        let distanceTime = 2000;
        if (playerPos != CAR.CUSTOM_MYSELF_POS) {
            distanceTime = 2500;//桌上其它玩家 速率调快
        }

        let self = this;
        let time = distance / distanceTime
        chip.stopAllActions();
        cc.tween(chip)
            .to(time, { position: cc.v3(eP) }, { easing: 'sineInOut' })
            .start();

        if (!this._gameCore._isPlayingBetSound) {
            this._gameCore._isPlayingBetSound = true
            this._gameAudio._loadPlaySFX("res/audio/bet");
            this.scheduleOnce(function () {
                self._gameCore._isPlayingBetSound = false;
            }, 0.2)
        }

        return { node: chip, score: score, playerPos: playerPos };
    }


    //聊天
    public doRoomChat(sendLocalSeatId, type, content) {
        this._playerView.showChat(type, content,sendLocalSeatId);
    }

    //道具特效运行
    public doRoomIntertChat(sendPlayerPos, receiverPlayerPos, content) {

        let sendPos = this.getHeadNodePositon(sendPlayerPos);
        let receiverPos = this.getHeadNodePositon(receiverPlayerPos);

        if (Common.isNull(sendPos) || Common.isNull(receiverPos)) {
            return;
        }

        let startPos = cc.v3(this.LayerItems.gameEmojiNode.convertToNodeSpaceAR(sendPos));
        let endPos = cc.v3(this.LayerItems.gameEmojiNode.convertToNodeSpaceAR(receiverPos));
        let Direction1 = Direction.LEFT;
        if (receiverPlayerPos % 2 == 1) {
            Direction1 = Direction.RIGHT;
        }

        UIHelper.playInteractExpression(this.LayerItems.gameEmojiNode, startPos, endPos, content, Direction1 == Direction.LEFT);
    }

    // 点击玩家头像
    private onClickHead(target: any, customEventData: any) {

        let self = this;
        let headPos = Common.toInt(customEventData);

        let playerid = this.getPlayerIdAtHeadPos(headPos);
        if (!playerid) {//座上有效玩家 
            this._gameCore._curClickHeadIndex = -1;
            return;
        }

        let pos = this.getHeadNodePositon(headPos);
        if (Common.isNull(pos)) {
            this._gameCore._curClickHeadIndex = -1;
            return;
        }
        let showPos = this.LayerItems.gameEmojiNode.convertToNodeSpaceAR(pos);
        headPos % 2 == 0 ? showPos.x += 350 : showPos.x -= 350;

        this._gameCore._curClickHeadIndex = headPos;

        let playerInfo = { sendPlayerid: this._playerView._MyInfo.playerid, receiverPlayerid: playerid };
        UIHelper.showInteractExpression(this.LayerItems.gameEmojiNode, showPos, playerInfo, (info: any) => {
            self._gameCore.sendChatMessage(info);
        });
    }

    //玩家头像位置
    getHeadNodePositon(pos: number) {
        let newPos: cc.Vec2 = null;
        let userHeadNode: cc.Node = null;

        if (Common.isNull(pos)) {
            return newPos;
        }

        userHeadNode = this._playerView._playerInfo[pos].node;

        if (Common.isNull(userHeadNode)) {
            return newPos;
        }

        newPos = userHeadNode.convertToWorldSpaceAR(userHeadNode.getAnchorPoint())

        return newPos;
    }

    //根据玩家id获取座上玩家位置 包含自己  bReceiverPlayerid:是否道具接收者 如果是 优先从座上玩家列表取位置 （主要处理自己同时坐在桌上六个玩家位置时）
    getHeadPosAtPlayerId(playerId: number, bReceiverPlayerid: boolean = false) {
        let pos = null;

        if (Common.isNull(playerId)) {
            return pos;
        }
        if (!!bReceiverPlayerid) {
            if (this._gameCore._curClickHeadIndex == CAR.SEAT_POS_INDEX_MY && playerId == this._playerView._MyInfo.playerid) {//自定义自己的一个位置
                pos = CAR.SEAT_POS_INDEX_MY;
            }
            else {
                for (let i = 0; i < this._playerView._playerInfo.length; i++) {
                    if (!!playerId && playerId > 0 && playerId == this._playerView._playerInfo[i].playerid) {
                        pos = i;
                        break;
                    }
                }
            }
        }
        else {//发送者 优先从自己位置
            if (playerId == this._playerView._MyInfo.playerid) {//自定义自己的一个位置
                pos = CAR.SEAT_POS_INDEX_MY;
            }
            else {
                for (let i = 0; i < this._playerView._playerInfo.length; i++) {
                    if (!!playerId && playerId > 0 && playerId == this._playerView._playerInfo[i].playerid) {
                        pos = i;
                        break;
                    }
                }
                if (pos == null) {
                    pos = CAR.SEAT_POS_INDEX_OTHER;
                }
            }
        }

        return pos;
    }

    //根据玩家位置获取座上玩家id 包含自己
    getPlayerIdAtHeadPos(pos: number) {
        let playerid = null;

        for (let i = 0; i < this._playerView._playerInfo.length; i++) {
            if (pos != null && pos >= 0 && i == pos && this._playerView._playerInfo[i].playerid > 0) {
                playerid = this._playerView._playerInfo[i].playerid;
                break;
            }
        }

        return playerid;
    }

    // //--根据分数,创建筹码
    // createChips(score: any, area: number, count: number) {
    //     let self = this;
    //     //this._gameCore._chips[area] = checktable(this._gameCore._chips[area]);
    //     let chipScore = self.splitUserBetChipScore(score, count);

    //     if (!chipScore || chipScore.length <= 0) {
    //         return;
    //     }

    //     if (this._gameCore._chips[area]) {
    //         if (this._gameCore._chips[area]) this._gameCore._chips[area] = [];
    //     }
    //     for (let i = 0; i < chipScore.length; i++) {

    //         let chip = self.createChouMa(chipScore[i], area);
    //         this._gameCore._chips[area].push({ node: chip, score: score })
    //     }
    // }
    // //创建单个筹码节点
    // createChouMa(score: number, area: number): cc.Node {
    //     let self = this;
    //     let chouMaIndex = self._gameCore._chipNumList.findIndex(item => item == score);
    //     let choumaVal: CARModel.ChipConfig;
    //     if (chouMaIndex > -1 && chouMaIndex < self.SELECT_CHIP_DATA_CONFIG.length) {
    //         choumaVal = self.SELECT_CHIP_DATA_CONFIG[chouMaIndex];
    //     } else {
    //         return;
    //     }


    //     let moveChipImg: cc.SpriteFrame = self.gameAtlas.getSpriteFrame(choumaVal.betBgKey)
    //     if (!moveChipImg) {
    //         self.show(self.stringFormat(CAR.LangData.GAME_BET_SCORE_ERR, [self.moneyFormat(score)]));//utils:moneyString(score)]);
    //         return null;
    //     }


    //     let chip: cc.Node = cc.instantiate(self.chipNode);



    //     chip.getComponent(cc.Sprite).spriteFrame = moveChipImg;

    //     let rotation = self.getRandom(-50, 50);
    //     chip.angle = rotation;

    //     let chipPosNode = self.TouchBet[area].node;
    //     let offsetX = chipPosNode.x;
    //     let offsetY = chipPosNode.y;
    //     let moveToPos = cc.v2(Common.random(offsetX - 70, offsetX + 70), Common.random(offsetY - 55, offsetY + 55));
    //     chip.setPosition(moveToPos);
    //     this.LayerItems.chipsNode.addChild(chip);
    //     return chip;
    // }
    // //断线重连
    // offLineToOnline(msg: any) {
    //     let self = this;
    //     //self.cardCompareNode:hide();
    //     let config = msg.config;
    //     if (config && config.Odds) {
    //         if (config.Odds.he) {
    //             //self.betArea_heRule:setString("3.和：1:"..config.Odds.he);
    //             self.TouchBet[2].node.getComponent(cc.Sprite).spriteFrame = this.gameAtlas.getSpriteFrame("bg_he_" + config.Odds.he);
    //         }
    //     }


    //     this.cleanGameData();
    //     self.setChipBtnEnabled(false);
    //     self.autoSelectChip();
    //     //设置上庄条件
    //     if (self.btnBankerLimit && self._gameCore.model.bankerNeed) {
    //         // self.btnBankerLimit.maxWidth=320;
    //         // self.btnBankerLimit.lineHeight=20;
    //         // self.btnBankerLimit.string="<color=#A4895D>Nếu số dư ít hon <color=#A4895D>"+self.moneyFormat(self._gameCore.model.bankerNeed) +"</color> sẽ mất quyền làm cái</color>";
    //     }

    //     if (msg.state == CAR.GameState.Bet) {
    //         self._gameCore._isStartBet = true;
    //         let chipsNum = 150000;
    //         let count = 15;
    //         if (Common.toNumber(msg.nextat) > 10) {
    //             count = 5
    //         } else if (Common.toNumber(msg.nextat) > 5) {
    //             count = 10;
    //         }
    //         if (msg.TableBetInfo) {
    //             self.restoreDeskAllInfo(msg.TableBetInfo, count);
    //         }
    //     }
    //     self._playerView._MyInfo.money = msg.chouma;
    //     self.restoreDeskMyInfo(msg.betinfo);

    // }
    // //--恢复桌子上自己的数据
    // restoreDeskMyInfo(msg: any) {
    //     let temp = {};
    //     let self = this;
    //     temp[0] = msg.long;
    //     temp[1] = msg.hu;
    //     temp[2] = msg.he;
    //     let myMoney = self._playerView._MyInfo.money;
    //     for (let i = 0; i < self.TouchBet.length; i++) {
    //         self.TouchBet[i].self_label.string = self.moneyFormat(temp[i]) + "";//setString(utils:moneyString(temp[i], 0));
    //         if (temp[i] > 0) {
    //             self._playerView._MyInfo._myself_isbet = true;
    //             self.TouchBet[i].self_label.node.active = true;
    //         }
    //         //myMoney = myMoney - temp[i];
    //     }
    //     self._playerView._MyInfo.money = myMoney;

    //     self._playerView._MyInfo.score.getComponent(cc.Label).string = this.moneyFormat(myMoney);//:setString(utils:moneyString(myMoney));
    //     self.autoSelectChip();
    // }
    // //--恢复桌子上的数据
    // restoreDeskAllInfo(msg: any, count: number) {
    //     let self = this;
    //     let temp = {};
    //     temp[0] = msg.long;
    //     temp[1] = msg.hu;
    //     temp[2] = msg.he;
    //     for (let i = 0; i < self.TouchBet.length; i++) {
    //         self.TouchBet[i].total_label.getComponent(cc.Label).string = this.moneyFormat(temp[i]);// :setString(utils:moneyString(temp[i], 0));
    //         self.TouchBet[i].total_number = temp[i];
    //         if (temp[i] > 0) {
    //             if (i == 2) {
    //                 count = 5;
    //             }
    //             self.createChips(temp[i], i, count);
    //         }
    //     }
    // }

    //结算金币飞动
    //  flyResultNumber(result: any) {
    //     let self = this;
    //     if (!result.others || !result.others.betrank) {
    //         return;
    //     }
    //     let winUserPos: cc.Vec2[] = [];

    //     //在线玩家赢的次数
    //     let isOnlineCount = 0;        
    //     for (let key in result.others.betrank) {           
    //         if(!!result.others.betrank[key]){ 
    //             let player = result.others.betrank[key];
    //             if (player.playerid == self._playerView._MyInfo.playerid) {
    //                 if (player.nChange > 0) {
    //                     let pos = cc.v2(self.LayerItems.chipsNode.convertToNodeSpaceAR(self._playerView._MyInfo.node.convertToWorldSpaceAR(self._playerView._MyInfo.node.getAnchorPoint())));
    //                     winUserPos.push(pos);
    //                 }
    //                 self.chipToUser(player.playerid, player.nChange, result.resultPos, self._playerView._MyInfo);
    //                 self._playerView._MyInfo.money = player.playercoin;
    //                 this.scheduleOnce(function () {
    //                     if (player.nChange > 0) {
    //                         self.resultAct(self._playerView._MyInfo);
    //                     }
    //                     self.resultFlyNumber(self._playerView._MyInfo, self._playerView._MyInfo.playerid, player.nChange, player.playercoin);
    //                 }, 0.9);

    //             } else {
    //                 let hasExists = false;
    //                 for (let j = 0; j < CAR.CUSTOM_MYSELF_POS; j++) {
    //                     let playerModel = self._playerView._playerInfo[j];
    //                     if (player.playerid == playerModel.playerid) {
    //                         if (player.nChange > 0) {
    //                             let pos = cc.v2(self.LayerItems.chipsNode.convertToNodeSpaceAR(playerModel.node.convertToWorldSpaceAR(playerModel.node.getAnchorPoint())));
    //                             winUserPos.push(pos);
    //                         }
    //                         self.chipToUser(player.playerid, player.nChange, result.resultPos, playerModel);
    //                         this.scheduleOnce(function () {
    //                             if (player.nChange > 0) {
    //                                 self.resultAct(playerModel);
    //                             }
    //                             self.resultFlyNumber(playerModel, playerModel.playerid, player.nChange, player.playercoin,true);
    //                         }, 0.9);
    //                         hasExists = true;
    //                     }
    //                 }
    //                 isOnlineCount += hasExists == false ? 1 : 0
    //             }
    //         }
    //     }

    //     //--庄家
    //     if (result.zhuang.nChange > 0) {
    //         let bankerNode = this.LayerItems.node_betFlyZhuang;
    //         let pos = cc.v2(self.LayerItems.chipsNode.convertToNodeSpaceAR(bankerNode.convertToWorldSpaceAR(bankerNode.getAnchorPoint())));
    //         winUserPos.push(pos);
    //         for (let i = 0; i < 3; i++) {
    //             //有下注的区域，筹码飞动到庄家
    //             if (self._gameCore._chips[i].length > 0) {
    //                 self.findChip(result.zhuang.playerid, result.zhuang.nChange, i);
    //                 let pos = cc.v2(self.LayerItems.chipsNode.convertToNodeSpaceAR(bankerNode.convertToWorldSpaceAR(bankerNode.getAnchorPoint())));
    //                 self.chipMoveToWinUser(self.userWinChip[result.zhuang.playerid], i, pos, 0.3,LHD.Const.customBankerPos);
    //             }
    //         }

    //         this.scheduleOnce(function () {
    //             if (result.zhuang.playerid > 0) {
    //                 self.resultAct(self.BankerInfo);
    //             }
    //             self.bankerResultFly(result.zhuang.playerid, result.zhuang.nChange, "+");
    //         }, 0.9);

    //     } else if (result.zhuang.nChange < 0) {
    //         this.scheduleOnce(function () {
    //             self.bankerResultFly(result.zhuang.playerid, result.zhuang.nChange);
    //         }, 0.9);

    //     }
    //     let decimals = 0
    //     if (result.zhuang.playerid == self._playerView._MyInfo.playerid) {//庄家是自己，保留2位小数
    //         decimals = 2;
    //     }

    //     self.BankerInfo.score.getComponent(cc.Label).string = self.moneyFormat(result.zhuang.playercoin,decimals);//utils:moneyString(result.zhuang.playercoin);
    //     if (isOnlineCount > 0) {
    //         let pos = cc.v2(self.LayerItems.chipsNode.convertToNodeSpaceAR(self.btnOnline.convertToWorldSpaceAR(self.btnOnline.getAnchorPoint())));
    //         winUserPos.push(pos);
    //     }
    //     // console.error('飞币时间222..........' + Common.formatDateTimeNormal())   
    //     self.chipMoveToPlayerList();
    //     // self.flyMoveChips(winUserPos, function (osTime: number) { });
    //     if (result.zhuang.playerid == self._playerView._MyInfo.playerid) {
    //         self._playerView._MyInfo.score.getComponent(cc.Label).string = self.moneyFormat(result.zhuang.playercoin,2);//utils:moneyString(result.zhuang.playercoin);
    //     }

    //     // this._gameAudio.playWinBet();
    //     // this._gameAudio.playWinBet1();
    // }
    // //玩家赢的筹码拆分
    // findChip(playerid: any, score: any, areaIndex: number) {
    //     let self = this;
    //     if (areaIndex < 0 || !score) {
    //         return;
    //     }
    //     self.userWinChip[playerid] = {}; //--checktable(self.userWinChip[playerid]);
    //     self.userWinChip[playerid][areaIndex] = [];//--checktable(self.userWinChip[playerid][areaIndex]);
    //     let chipScore = self.splitChipScore(score);
    //     console.log("玩家赢的筹码分数拆分：" + chipScore.length)
    //     //--dump(chipScore, "玩家赢的筹码分数拆分：");
    //     for (let i = 0; i < chipScore.length; i++) {
    //         if (self.Chips[areaIndex]) {
    //             for (let j = 0; j < self.Chips[areaIndex].length; j++) {
    //                 let val = self.Chips[areaIndex][j];
    //                 if (val.score = chipScore[i]) {
    //                     if (chipScore.length >= self.userWinChip[playerid][areaIndex].length) {
    //                         if (val) {
    //                             self.userWinChip[playerid][areaIndex].push(val);
    //                             self.Chips[areaIndex].splice(j, 1);
    //                             j--;
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     }

    //     if (self.userWinChip[playerid][areaIndex].length <= 0) {
    //         for (let i = 0; i < chipScore.length; i++) {

    //             let chip = self.createChouMa(chipScore[i], areaIndex);
    //             self.userWinChip[playerid][areaIndex].push({ node: chip, score: chipScore[i] });
    //         }
    //     }
    // }

    // //--回收筹码
    // chipToUser(playerid: any, nChange: any, resultPos: any, player: any) {
    //     let self = this;
    //     if (nChange > 0) {
    //         self.findChip(playerid, nChange, resultPos);
    //         let pos = cc.v2(self.LayerItems.chipsNode.convertToNodeSpaceAR(player.head.convertToWorldSpaceAR(player.head.getAnchorPoint())));
    //         self.chipMoveToWinUser(self.userWinChip[playerid], resultPos, pos, 0.3,LHD.Const.customDeskPlayer);
    //     }
    // }

    // //筹码飞到胜利一方
    // chipMoveToWinUser(chipTab: any, AreaIndex: number, pos: cc.Vec2, delay: number, flyPos: number = -1) {
    //     let self = this;

    //     if (!chipTab || !chipTab[AreaIndex]) {
    //         return;
    //     }
    //     if (chipTab[AreaIndex].length > 0) {
    //         let countOnline = [0,0,0];
    //         let countBank = [0,0,0];
    //         let countDeskPlayer = [0,0,0];
    //         let act = cc.tween(self.LayerItems.chipsNode)
    //             .delay(0.01)
    //             .call(function () {
    //                 let index = 0;
    //                 let frames = Math.ceil(chipTab[AreaIndex].length)
    //                 let len = chipTab[AreaIndex].length;

    //                 for (let i = 0; i < frames; i++) {
    //                     if (chipTab[AreaIndex].length < index || chipTab[AreaIndex].length <= 0 || index >= chipTab[AreaIndex].length) {
    //                         //console.log("总数："+chipTab[AreaIndex].length+"未找到筹码对象"+index);
    //                         self.LayerItems.winSoundNode.removeAllChildren();
    //                         return;
    //                     }
    //                     let item: cc.Node = chipTab[AreaIndex][index].node;
    //                     if (!item) {
    //                         self.LayerItems.winSoundNode.removeAllChildren();
    //                         return;
    //                     }
    //                     let startPos: cc.Vec2 = item.getPosition();
    //                     let delayGap = 0.015
    //                     let speed = 1700
    //                     let time = pos.sub(startPos).mag() / speed

    //                     let mvPos = cc.v2(0, 0)
    //                     if (startPos.x > pos.x) {
    //                         mvPos.x = 20
    //                     } else {
    //                         mvPos.x = -20
    //                     }

    //                     if (startPos.y > pos.y) {
    //                         mvPos.y = 20
    //                     } else {
    //                         mvPos.y = -20
    //                     }
    //                     //暂时不用这个拖尾回收
    //                     let delay_time = delayGap * index
    //                     if (delay_time > 0.6 && index != len) {
    //                         delay_time = (Math.random() * (50 - 10) + 10) / 100
    //                     } else if (delay_time > 0.6 && index == len) {
    //                         delay_time = 0.6
    //                     }

    //                     //固定时间回收筹码
    //                     if(flyPos == LHD.Const.customOnLine){//在线其它玩家飞币改一下
    //                         countOnline[AreaIndex] += 1;
    //                         delay_time = 0.015 * Math.ceil(countOnline[AreaIndex]/10);
    //                         if(AreaIndex == 0){
    //                             time = 0.6
    //                         }
    //                         if(AreaIndex == 1){
    //                             time = 0.3
    //                         }
    //                         if(AreaIndex == 2){
    //                             time = 0.45
    //                         }
    //                     }
    //                     if(flyPos == LHD.Const.customBankerPos){
    //                         countBank[AreaIndex] += 1;
    //                         delay_time = 0.025 * Math.ceil(countBank[AreaIndex]/5);
    //                         // if(AreaIndex == 0){
    //                         //     time = 0.35
    //                         // }
    //                         // if(AreaIndex == 1){
    //                         //     time = 0.3
    //                         // }
    //                         // if(AreaIndex == 2){
    //                         //     time = 0.35
    //                         // }
    //                     }

    //                     if(flyPos == LHD.Const.customDeskPlayer){

    //                         countDeskPlayer[AreaIndex] += 1;
    //                         delay_time = 0.03 * Math.ceil(countDeskPlayer[AreaIndex]/2);
    //                     //     if(AreaIndex == 0){
    //                     //         time = 0.5
    //                     //     }
    //                     //     if(AreaIndex == 1){
    //                     //         time = 0.6
    //                     //     }
    //                     //     if(AreaIndex == 2){
    //                     //         time = 0.5
    //                     //     }
    //                     }

    //                     cc.tween(item)
    //                         .delay(delay_time)
    //                         .by(0.2, { position: cc.v3(mvPos.x, mvPos.y) })
    //                         .to(time, { position: cc.v3(pos.x, pos.y) },{easing : 'sineOut'})//,{easing : 'sineInOut'}
    //                         .to(0.5,{opacity: 0})
    //                         // .call(function () {
    //                             // item.active = false;
    //                         // })
    //                         .delay(0.7)
    //                         .call(function () {
    //                             if (index == len) {
    //                                 chipTab[AreaIndex].forEach(function (chipItem, k,nodes) {
    //                                     // chipItem.node.removeFromParent()
    //                                     LHChipPoolAssisCom.instance.put(chipItem.node);
    //                                     // console.error('===个数' + nodes.length)
    //                                     if(k == nodes.length - 1){
    //                                         self.LayerItems?.winSoundNode?.removeAllChildren();
    //                                     }
    //                                 });
    //                                 chipTab[AreaIndex] = [];
    //                             }
    //                         }).start();


    //                     if (delay < 0.8) {
    //                         delay = delay + 0.1;
    //                         self.allTime = delay;
    //                     }
    //                     index = index + 1;
    //                 }
    //             }).start();


    //     }
    // }

    // chipMoveToPlayerList() {
    //     let self = this;
    //     // let pos = self.btnOnline.getPosition();
    //     this.addWinSoundAct();
    //     let pos = cc.v2(self.node.convertToNodeSpaceAR(self.btnOnline.convertToWorldSpaceAR(self.btnOnline.getAnchorPoint())));
    //     for (let i = 0; i < 3; i++) {
    //         self.chipMoveToWinUser(self.Chips, i, pos, 0.3,LHD.Const.customOnLine);
    //     }
    // }

    //增加赢音效
    // addWinSoundAct(){
    //     let self = this;
    //     self.LayerItems.winSoundNode.removeAllChildren();
    //     let node1 = new cc.Node();
    //     self.LayerItems.winSoundNode.addChild(node1);
    //     cc.tween(node1)
    //     .repeatForever(
    //         cc.tween()
    //         .delay(0.25)
    //         .call(()=>{
    //             self._gameAudio.playWinBet()
    //         })
    //     )
    //     .start();

    //     let node2 = new cc.Node();
    //     self.LayerItems.winSoundNode.addChild(node2);

    //     cc.tween(node2)
    //     .repeatForever(
    //         cc.tween()
    //         .delay(0.4)
    //         .call(()=>{
    //             self._gameAudio.playWinBet()
    //         })
    //     )
    //     .start();
    // }

}
