import BaseLayer from "../../../../script/frame/component/BaseLayer";
import CARGameLogic from "../core/CARGameLogic";


const { ccclass, property, disallowMultiple, menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('car/CARHistoryLayer')
export default class CARHistoryLayer extends BaseLayer {
    
    _gameLogic :CARGameLogic = null;
    
    onLoad() {
       this._gameLogic = cc.Canvas.instance.getComponent("CARGameLogic"); 
    }

    start() {
        
    }

    public onClickClose() {
        this._gameLogic.popUpEffect(this.node,false);
    }

}
