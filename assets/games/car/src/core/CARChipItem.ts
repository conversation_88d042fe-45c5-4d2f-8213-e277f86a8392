import { CAR } from "./CARDefine";

const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('car/CARChipItem')
export default class CARChipItem extends cc.Component {


    private _release: boolean;
    betPlayerPos: number = CAR.SEAT_POS_INDEX_OTHER;//谁下的注
        
    public reuse() {
        this._release = false;
    }

    public unuse() {
        this._release = true;
    }

    public set isRelease(release: boolean) {
        this._release = release;
    }
    public get isRelease(): boolean {
        return this._release;
    }
    
    public resetUI() {
       this.node.active = false;
       this.node.x = 0;
       this.node.y = 0;
       this.node.angle = 0;
       this.node.opacity = 255;
       this.betPlayerPos = CAR.SEAT_POS_INDEX_OTHER;
       this.node.removeFromParent();
    }
}
