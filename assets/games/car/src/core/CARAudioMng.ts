const {ccclass, property} = cc._decorator;
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
@ccclass
export default class CARAudioMng extends cc.Component {
   
    bEnterBackgroundFlag: Boolean = false;

    init(){
    }
    playMusic () {
        let path = "res/audio/gameBg";
        AudioHelper.instance.playMusic(path,true);
        
    }
    pauseMusic() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        AudioHelper.instance.pauseMusic();
    }

    resumeMusic() {
        AudioHelper.instance.resumeMusic();
    }
  
    _loadPlaySFX (path) {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        AudioHelper.instance.playEffect(path);
    }    

    setOnEnterBackgroundFlag(bEnterBackgroundFlag: boolean){
        this.bEnterBackgroundFlag = bEnterBackgroundFlag;
    }
}

