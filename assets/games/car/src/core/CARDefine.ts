import { LanguageType } from "../../../../script/frame/common/Define"

export const CAR =
{
	CAR_COUNT: 24,                      //车图标总数目
	CHIP_MOVE_POS_Y_NUM: 20,            //筹码上移位置 
	ICON_INDEX_LIST: [1, 2, 3, 4, 5, 6, 7, 8], //车子图标对应SpriteAtlas中索引号
	CENTER_WIN_BEISHU_ANI_NAME_LIST: ['kuang', 'win_x5', 'win_x10', 'win_x15', 'win_x25', 'win_x40'], //中间区域下注赢倍数
	SEAT_POS_INDEX_MY: 6, //自己固定在6号位
	SEAT_POS_INDEX_OTHER: 7,//其它玩家固定在7号位 
	FLY_WIN_GOLD_POS_ADD_Y: 40,//玩家飘分上移Y位置
	CUSTOM_OTHER_POS: 7, //自定义其它玩家位置
	CUSTOM_MYSELF_POS: 6, //自定义自己位置
	LOCAL_STORAGE_BET_NUM_KEY: 'LOCAL_STORAGE_BET_NUM_KEY',//上次选择的下注键值
	AREA_MAX_NUM: 8,//最多8个区域
	areaShowMaxChipsNum: 120,//每个区域最多显示筹码数量
	LUCKY_PLAYER_POS: 1,//神算子位置

	//协议
	CMD:
	{
		SC_CAR_CONFIG_P: 3900,				//发送配置
		SC_CAR_GAMESTATE_P: 3901,				//游戏状态切换
		SC_CAR_FIRST_P: 3902,				//开始发牌动画
		SC_CAR_OPTTIME_P: 3903,				//下注亮牌的[[可操作？]]时间
		CS_CAR_BUYHORSE_P: 3904,				//请求下注
		SC_CAR_BUYHORSE_P: 3905,				//下注 发送倍数_不用
		CS_CAR_REQUEST_ZHUANG_P: 3906,				//请求上庄
		CS_CAR_REQUEST_NOT_ZHUANG_P: 3907,			    //请求取消上庄
		SC_CAR_SHOWCARD_P: 3912,				//通知亮牌操作
		SC_CAR_SETTLEMENT_P: 3913,				//比牌结果&结算
		SC_CAR_OPER_ERROR_P: 3914,				//服务端返回操作错误码
		CS_CAR_HISTORY_P: 3915,				//请求历史信息
		SC_CAR_HISTORY_P: 3916,				//返回历史信息 
		CS_CAR_ALLLIST_P: 3920,                //请求玩家列表
		SC_CAR_ALLLIST_P: 3921,                //返回玩家列表
		SC_CAR_BETINFO: 3922,                //下注信息
		SC_CAR_SYNC_BET: 3923,                //下注同步
		CS_CAR_REQUEST_ZHUANG_LIST_P: 3924,                //请求上庄列表
		SC_CAR_BET_SUCCESS: 3926,				//下注成功
		SC_CAR_BET_SYNC: 3927,				//筹码增量信息
		SC_CAR_SETTLEMENT_P_NEW: 3928,
		SC_CAR_ONLINENUM_P: 3929,               //桌面上在线玩家数		
	},

	GameState:
	{
		None: 0,	            //无状态
		Bet: 1,	            //下注阶段
		ShowResult: 2,	            //亮牌阶段	
		GameEnd: 3,	            //游戏休息,等下一局开始
	},

	GAME_ERROR:				                    //返回错误提示
	{
		GAME_ERROR_NOT_MONEY: 0,	        // not enough money
		GAME_ERROR_BUY_LIMIT: 1,	        // get the limit
		GAME_ERROR_NOT_ROUND: 2,           // can not off zhuang.. round does not reach the min..
		GAME_ERROR_OZ_STATE: 3,	        // 
		GAME_ERROR_ZHUANG_NO_MONEY: 4,	        // 上庄金钱不足
		GAME_ERROR_NEXT_ROUND: 5,	        // 下轮下庄
		GAME_ERROR_OFFZHUANG_WUNIU: 6,	        // 无牛下庄
		GAME_ERROR_APPLYZHUANG_OK: 7,	        // 申请上庄成功
		GAME_ERROR_NOT_MONEY_TO_BET: 8,           // 金钱不足不能下注
		GAME_ERROR_FOLLOW_TO_BET: 9,	        // 没有续投的记录
		GAME_ERROR_FOLLOW_LIMIT: 10,	        //续投超出房间限制
		GAME_ERROR_FOLLOW_NOT_MONEY: 11,          //续投个人金钱不足
		GAME_ERROR_SLIENTCE_TOMANNY: 12,	        //沉默次数太多
		GAME_ERROR_BET_TOOMORE: 13,          //下注超出限额
		GAME_ERROR_STATE_ERROR: 14,          //下注失败
		GAME_ERROR_BUY_POS_ERROR: 15,	        //下注区域错误
		GAME_ERROR_BANKER_BET: 16,		    //庄家下注
		GAME_ERROR_ILLEGAL_BET: 17,		    //非法筹码
		GAME_ERROR_BET_TOOFAST: 18,
	},

	// 游戏事件
	GameEvent: {
		ONLINE_PLAYER_CHANGE_PAGE: "ONLINE_PLAYER_CHANGE_PAGE",//在线玩家界面切换页时更新数据

	},

}

let GameTextCH = {

}

let GameTextEnglish = {
	lh_seat_empty: "Empty",
	lh_time: "Time",
	lh_rewards: "Rewards",
	lh_bet: "Bet",
	lh_type: "Type",
	lh_bigWinner: "Big Winner",
	lh_win: "Win",
	lh_total_onlieNum: "Total online players:",
	lh_prev: "Prev",
	lh_prev_line: "_____",
	lh_next: "Next",
	lh_next_line: "_____",
	lh_title_help: "Help",
	lh_Tiger: "Tiger",
	lh_Dragon: "Dragon",
	lh_Tie: "Tie",
	lh_Rounds: "Rounds",
	lh_NextDragon: "Next: Dragon",
	lh_NextTiger: "Next: Tiger",
	lh_bankerTips1: "<color=#B8A886>Conditions: {0}</color>",
	lh_bankerTips2: "<color=#C36C14>{0} in queue (top 10 shown)</color>",
	lh_bankerTips3: "<color=#B8A886>Dealer can hold position for max 3 rounds, auto exit if below {0}</color>",
	lh_tips: "TIPS",
	lh_confirm: "CONFIRM",

	//还没给对应英文及印地语翻译
	GOLD_BET_ERR: "Insufficient coins, bet failed",
	GOLD_BET_MAX_ERR: "Reached current betting limit, unable to continue betting",
	GAME_MAIN_MIN_ERR: "You cannot step down as the dealer due to insufficient number of rounds served",
	GAME_STATE_NOT_XZ_ERR: "You cannot step down as the dealer due to your current game status",
	GAME_XZ_JU_TIP: "You can step down as the dealer after {0} rounds",
	GAME_SZ_MONEY_TIP: "You can only become the dealer when your balance has at least {0}, please recharge",
	GAME_XZ_NEXT_TIP: "You will step down as the dealer in the next round",
	GAME_APPLY_SZ_TIP: "Application to be dealer successful, added to dealer list",
	GAME_EXIT_MIN_JU_TIP: "You have not participated in the game for {0} rounds, thank you for your attention",
	GAME_BET_ADD_ERR: "Bet failed",
	GAME_ZHUANG_NOT_XZ_TIP: "Dealer, please do not place bets",
	GAME_BET_ING_TIP: "Currently placing bets",
	GAME_BANKER_JU_TEXT: "Current game {0}",//当前第几局
	GAME_SERVER_CONFIG_ERR: "Failed to retrieve server configuration",
	GAME_PLEASE_SELECT_GOLD_TIP: "Please select betting chips",
	GAME_ZHUANG_XIA_TIP: "You are requesting to leave the bank, and you will no longer be in the bank after this process is completed. Are you sure?",//"您正在请求下庄，本局完成后，\n   您将不再坐庄，确认吗？",//
	GAME_BANKER_EXIT_ERR: "You are currently the dealer and cannot exit the game!",
	GAME_BET_EXIT_ERR: "You have already placed a bet. Exiting the game will trigger automatic management by the system, which will not affect the coin settlement. Are you sure you want to exit the game?",
	GAME_APPLY_EXIT_ERR: "You are applying to be the dealer, do you confirm leaving the room?",
	GAME_BET_MONEY_MAX_TIP: "The betting amount has reached the upper limit",

	GAME_GOLD_BET_MIN_TIP: "金币大于{0}元才可以下注，请充值",
	GAME_BET_XT_ERR: "续投失败,没有投注记录",
	GAME_BET_XT_MIN_ERR: "金币不足，续投失败",
	GAME_BET_SCORE_ERR: "下注数据错误：{0}",
	GAME_BET_MIN_ERR: "剩余金币不足{0}元，无法下注",
}


let GameTextIndia = {
	lh_seat_empty: "खाली",
	lh_time: "समय",
	lh_rewards: "पुरस्कार",
	lh_bet: "शर्त",
	lh_type: "प्रकार",
	lh_bigWinner: "बड़ा विजेता",
	lh_win: "जीत",
	lh_total_onlieNum: "कुल ऑनलाइन खिलाड़ी:",
	lh_prev: "पिछला पृष्ठ",
	lh_prev_line: "_________",
	lh_next: "अगला पृष्ठ",
	lh_next_line: "_________",
	lh_title_help: "मदद",
	lh_Tiger: "टाइगर",
	lh_Dragon: "ड्रैगन",
	lh_Tie: "गुलोबन्द",
	lh_Rounds: "दौर",
	lh_NextDragon: "अगला: ड्रैगन",
	lh_NextTiger: "अगला: टाइगर",
	lh_bankerTips1: "<color=#B8A886>शर्तें: {0}</color>",
	lh_bankerTips2: "<color=#C36C14>कतार में {0} (शीर्ष 10 दिखाए गए)</color>",
	lh_bankerTips3: "<color=#B8A886>डीलर अधिकतम {0} दौर तक रह सकते हैं, 2000 से कम होने पर स्वत: बाहर</color>",
	lh_tips: "टिप्स",
	lh_confirm: "पुष्टि करें",

	GOLD_BET_ERR: "सिक्के कम हैं, दांव असफल रहा",
	GOLD_BET_MAX_ERR: "वर्तमान दांव की सीमा तक पहुंच गए हैं, दांव लगाना जारी नहीं रख सकते",
	GAME_MAIN_MIN_ERR: "वर्तमान में आपके डीलर के रूप में पूर्ण नहीं किए गए पर्याप्त दौर के कारण आप डीलर से कदम नहीं हटा सकते",
	GAME_STATE_NOT_XZ_ERR: "आपकी वर्तमान खेल स्थिति के कारण आप डीलर से कदम नहीं हटा सकते",
	GAME_XZ_JU_TIP: "आप {0} दौर के बाद डीलर से कदम नीचे उतर सकते हैं",
	GAME_SZ_MONEY_TIP: "जब आपका शेष राशि कम से कम {0} हो तब ही आप डीलर बन सकते हैं, कृपया रिचार्ज करें",
	GAME_XZ_NEXT_TIP: "आप अगले दौर में डीलर के रूप में कदम नीचे उतरेंगे",
	GAME_APPLY_SZ_TIP: "डीलर बनने का आवेदन सफल, सूची में जोड़ा गया",
	GAME_EXIT_MIN_JU_TIP: "आप {0} दौर से खेल में भाग नहीं ले रहे हैं, आपका ध्यान देने के लिए धन्यवाद",
	GAME_BET_ADD_ERR: "दांव असफल हुआ",
	GAME_ZHUANG_NOT_XZ_TIP: "डीलर, कृपया दांव न लगाएं",
	GAME_BET_ING_TIP: "वर्तमान में दांव लगा रहे हैं",
	GAME_BANKER_JU_TEXT: "वर्तमान खेल {0}",//当前第几局
	GAME_SERVER_CONFIG_ERR: "सर्वर कॉन्फ़िगरेशन प्राप्त करने में विफल",
	GAME_PLEASE_SELECT_GOLD_TIP: "कृपया दांव चिप्स चुनें",
	GAME_ZHUANG_XIA_TIP: "आप बैंक छोड़ने का अनुरोध कर रहे हैं, और इस प्रक्रिया के पूरा होने के बाद आप बैंक में नहीं होंगे। क्या आप सुनिश्चित हैं?",//"您正在请求下庄，本局完成后，\n   您将不再坐庄，确认吗？",//
	GAME_BANKER_EXIT_ERR: "आप वर्तमान में डीलर हैं और खेल से बाहर नहीं जा सकते!",
	GAME_BET_EXIT_ERR: "आपने पहले ही दांव लगा दिया है। खेल से बाहर जाने पर सिस्टम द्वारा आपकी स्वतः संचालन की जाएगी, जो सिक्कों के निपटान को प्रभावित नहीं करेगा। क्या आप वाकई खेल से बाहर जाना चाहते हैं?",
	GAME_APPLY_EXIT_ERR: "आप डीलर बनने के लिए आवेदन कर रहे हैं, क्या आप कमरा छोड़ने की पुष्टि करते हैं?",
	GAME_BET_MONEY_MAX_TIP: "सट्टेबाजी की रकम ऊपरी सीमा तक पहुंच गई है",
	

	GAME_GOLD_BET_MIN_TIP: "金币大于{0}元才可以下注，请充值",//暂时没用到
	GAME_BET_XT_ERR: "续投失败,没有投注记录",//暂时没用到
	GAME_BET_XT_MIN_ERR: "金币不足，续投失败",//暂时没用到
	GAME_BET_SCORE_ERR: "下注数据错误：{0}",//暂时没用到
	GAME_BET_MIN_ERR: "剩余金币不足{0}元，无法下注",//暂时没用到
}

// 文本内容
export let GameTextTips = GameTextEnglish

export let updateGameTextTips = () => {
	let curLanguage = cc.sys.localStorage.getItem('curLanguage') || 1;
	if (curLanguage == LanguageType.CHINESE) {
		//   GameTextTips = GameTextCH;
	} else if (curLanguage == LanguageType.ENGLISH) {
		GameTextTips = GameTextEnglish;
	} else if (curLanguage == LanguageType.INDIA) {
		GameTextTips = GameTextIndia;
	}
}

//图标对应区域
export const CAR_ID_TO_AREA = {
	0 : null, 
	1 : 4, 
	2 : 6,
	3 : 5,
	4 : 7,
	5 : 3,
	6 : 1,
	7 : 2,
	8 : 7,
	9 : 5,
	10 : 6,
	11 : 4,
	12 : 3,
	13 : 7,
	14 : 5,
	15 : 6,
	16 : 4,
	17 : 3,
	18 : 0,
	19 : 2,
	20 : 4,
	21 : 6,
	22 : 5,
	23 : 7,
}

//倍数
export const AREA_BEISHU = {
	0:40,
	1:25,
	2:15,
	3:10,
	4:5,
	5:5,
	6:5,
	7:5
}

