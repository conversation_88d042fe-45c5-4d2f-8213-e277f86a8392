import GameCore from "../../../../script/frame/model/GameCore";
import CARGameViewLayer from "../view/CARGameViewLayer";
import Common from "../../../../script/frame/common/Common";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import EventManager from "../../../../script/frame/manager/EventManager";
import CARPlayerInfoLayer from "../view/CARPlayerInfoLayer";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import { CAR, GameTextTips, updateGameTextTips } from "./CARDefine";
import { GameEvent } from "../../../../script/frame/common/Define";
import { TextTips } from "../../../../script/frame/common/Language";
import Config from "../../../../script/frame/config/Config";
import CARChipPoolAssisCom from "./CARChipPoolAssisCom";
const { ccclass, property, disallowMultiple, menu } = cc._decorator;

export enum RESULT_TYPE {
    ResultType_Normal = 0, //正常结果
    ResultType_FireOne = 1, //打枪1个
    ResultType_FireTwo = 2, //打枪2个
    ResultType_FireThree = 3, //打枪3个
    ResultType_TranTwo = 4, //火车2
    ResultType_TranThree = 5, //火车3
    ResultType_TranFour = 6, //火车4
    ResultType_TranFive = 7, //火车5
    ResultType_MultiTwo = 8, //翻倍2
    ResultType_MultiThree = 9, //翻倍3
    ResultType_MAX = 10, // 
}

export type resultDatas = {
    endIndexList: number[];
    resultType: RESULT_TYPE;
}


export interface betAreaInfo {
    node?: cc.Node,       // 当前节点
    area?: number, //当前区域值
    betFlag?: cc.Node,    //下注亮灯
    icon?: cc.Sprite,       //图标
    beishu?: cc.Label,    //底图倍数
    num?: cc.RichText,   //下注分数
    img_star?: cc.Node,  //神算子图标底图
    img_star_fill?: cc.ProgressBar, //神算子进度
    win_beishu_center?: cc.Node, //赢的倍数（中间大的win倍数）
    win_beishu_right?: cc.Node, //赢的倍数(右上角带火焰效果的倍数)
    win_light?: cc.Node, //赢的区域灯光
    myself_betsNum?: number, //当前区域自己下注总数
    areaTotalNum?: number,//当前区域总下注
    winSameMulti?: number, //中相同的图标累加倍数
    multi?: number, //是否翻倍
    betAreaScoreSumList?: number[];//0~5 在座玩家 6 自己 7 在线其它玩家
    betAreaDelScoreSumList?: number[];//0~5 在座玩家 6 自己 7 在线其它玩家 //已从桌上移走的筹码
}


@ccclass
@disallowMultiple()
@menu('car/CARGameCore')
export default class CARGameCore extends GameCore {
    /** 游戏视图对象 */
    _gameView: CARGameViewLayer = null;
    //玩家对象
    _playerView: CARPlayerInfoLayer = null;
    //图标转动灯列表
    _dengNodeList: cc.Node[] = [];
    //图标列表
    turntableItemList: cc.Node[] = [];
    //筹码列表
    _betChipsNodeList: cc.Node[] = [];
    //当前选中筹码索引
    _selectIndex: number = 0;
    //筹码值列表
    _chipNumList: number[] = [1000, 5000, 10000, 100000, 500000, 1000000];
    //中奖光圈
    _turntableJiangAnim: cc.Node[] = [];
    //在线玩家列表
    _playerListData: any[];
    //游戏是否结束
    _isGameEnd = false;
    //多少局未下注
    _notBetCount: number = 0;
    //最大下注额
    _BetMax: number = 0;
    //下注倒计时
    _betTime: number = 15;
    //最低下注额
    _betNeedNum: number = 0;
    //当前游戏状态
    _gameState: number = CAR.GameState.None;
    //是否下注重连
    _isReconectGame: boolean = false;
    //断线重连时余额
    _reconnectMoney: number = 0;
    //当前倒计时
    _currentTimeOut: number = 0;
    //游戏是否开始下注
    _isStartBet: boolean = false;
    //区域信息
    _areaInfoList: betAreaInfo[] = [];
    //筹码列表
    _chips: any[] = [[], [], [], [], [], [], [], []];
    //筹码缓存池列表
    _chipPoolList = [];
    //是否正在播神算子动画
    _playedLuckStarAction = [false, false, false, false, false, false, false, false];
    //神算子进度
    _playedLuckStarProcess = [0, 0, 0, 0, 0, 0, 0, 0];
    //神算子当前区域列表
    _playedLuckStarAreaList = [[], [], [], [], [], [], [], []];
    //当前是否正在播下注声
    _isPlayingBetSound: boolean = false;
    //自己是否下注
    _myself_isbet: boolean = false;
    //其它玩家是否开始下注
    _runPlayerBetAnimFlag: boolean = false;
    //其它玩家下注数
    _chipsCount: number = 0;
    //其它玩家下注列表1 
    _userBetMsgTemp1 = [];
    //其它玩家下注列表2
    _userBetMsgTemp2 = [];
    //其它玩家下注飞币列表1
    _flyingOnlineBets1 = [];
    //其它玩家下注飞币列表2
    _flyingOnlineBets2 = [];
    //当前点击的头像
    _curClickHeadIndex: number = -1;
    //结算数据
    _settleData: any = null;

    //当前开始位置 
    _startMoveIndex: number = 0;


    // model = {
    //     isBetMax:false,
    //     bankerNeed:2000,
    //     betMoneyValue:10,
    //     NotBetCount:0,
    //     lastRefreshHistoryTime:0,
    //     lastHadBet:false,
    //     BET_TIME:15,
    //     betNeed:0,
    //     BetMax:0,
    //     bankerMaxTurn:0,
    //     bankerMinTurn:0,
    //     TYPE_BEI_CONFIGS:{
    //         he:13,
    //     },
    // };
    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this._gameView = this.node.getComponent("CARGameViewLayer");
        this._playerView = this.node.getComponent("CARPlayerInfoLayer");
        this.updateGameLan();
        super.onLoad();
    }

    //////////////////////////////////////////////////////////////////////////////

    start() {
        let self = this;
        this.bindGameMessage(CAR.CMD.SC_CAR_CONFIG_P, self.onConfigs, self);               //配置
        this.bindGameMessage(CAR.CMD.SC_CAR_GAMESTATE_P, self.onGameStatus, self);        //游戏状态
        this.bindGameMessage(CAR.CMD.SC_CAR_OPER_ERROR_P, self.onError, self);              //错误消息
        // this.bindGameMessage(CAR.CMD.SC_CAR_HISTORY_P,self.onHistory, self);               //历史记录
        this.bindGameMessage(CAR.CMD.SC_CAR_ALLLIST_P, self.onAllPlayerList, self);        //玩家列表
        this.bindGameMessage(CAR.CMD.SC_CAR_BET_SUCCESS, self.onBetSuccess, self);
        this.bindGameMessage(CAR.CMD.SC_CAR_BET_SYNC, self.onBetSYNC, self);
        this.bindGameMessage(CAR.CMD.SC_CAR_SETTLEMENT_P_NEW, self.onGameResultNew, self);
        this.bindGameMessage(CAR.CMD.SC_CAR_ONLINENUM_P, self.onUpdateOnlinePlayersNum, self); //更新桌面上玩家在线人数

        CARChipPoolAssisCom.instance.init(this._gameView.chipNode);
        EventManager.instance.on(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);

        super.start()
    }

    onBetSuccess(msg) {
        //console.log(msg, "下注成功：");
        if (msg.chouma) {
            super.updatePlayerMoney({ coin: msg.chouma, playerid: msg._playerid });
        }
        //  this._gameView.myBetSuccess(msg);
    }

    private updateGameLan() {
        updateGameTextTips();
        TextTips["GameTextTips"] = GameTextTips;
    }

    onBetSYNC(msg) {
        //console.log(msg.total, "筹码增量信息：");
        let tabArr: number[] = [0, 4, 8, 12, 16];
        let total = {};
        total[0] = msg.total.long;
        total[1] = msg.total.hu;
        total[2] = msg.total.he;
        total[3] = msg.total.long;
        total[5] = msg.total.long;
        total[4] = msg.total.hu;
        total[6] = msg.total.hu;
        total[7] = msg.total.he;
        let selectConfig = this._chipNumList;
        for (let key in msg) {
            if (key != "_playerid" && key != "total" && key != "roundid") {
                let val = msg[key];

                for (let i = 0; i <= tabArr.length; i++) {

                    let _rshift_long = (val.long >> tabArr[i]);
                    let _band_long = (_rshift_long & 0x0000000f);
                    if (_band_long > 0) {
                        this._gameView.updateChipMsg(key, _band_long, selectConfig[i], val, 0, total);
                        this._gameView.updateChipMsg(key, _band_long, selectConfig[i], val, 3, total);
                        this._gameView.updateChipMsg(key, _band_long, selectConfig[i], val, 5, total);
                    }

                    let _rshift_hu = (val.hu >> tabArr[i]);
                    let _band_hu = (_rshift_hu & 0x0000000f);
                    if (_band_hu > 0) {
                        this._gameView.updateChipMsg(key, _band_hu, selectConfig[i], val, 1, total);
                        this._gameView.updateChipMsg(key, _band_hu, selectConfig[i], val, 4, total);
                        this._gameView.updateChipMsg(key, _band_hu, selectConfig[i], val, 6, total);
                    }
                    let _rshift_he = (val.he >> tabArr[i]);
                    let _band_he = (_rshift_he & 0x0000000f);
                    if (_band_he > 0) {
                        this._gameView.updateChipMsg(key, _band_he, selectConfig[i], val, 2, total);
                        this._gameView.updateChipMsg(key, _band_he, selectConfig[i], val, 7, total);
                    }
                }

            }
        }
    }

    resetData() {
        this._isGameEnd = false;
        this._isStartBet = false;
        for (let i = 0; i < this._areaInfoList.length; i++) {
            let areaInfo: betAreaInfo = this._areaInfoList[i];
            areaInfo.myself_betsNum = 0;
            areaInfo.areaTotalNum = 0;
            areaInfo.betFlag.active = false;
            areaInfo.num.string = `<color=#A9D5E7>${0}</c><color=#FAEB44>/${0}</color>`;
            areaInfo.img_star.active = false;
            areaInfo.win_beishu_center.active = false;
            areaInfo.win_beishu_right.active = false;
            areaInfo.win_light.active = false;
            areaInfo.winSameMulti = 0;
            areaInfo.betAreaScoreSumList = [0, 0, 0, 0, 0, 0, 0, 0];
            areaInfo.betAreaDelScoreSumList = [0, 0, 0, 0, 0, 0, 0, 0];
            areaInfo.multi = 1;
        }
        this._playedLuckStarAction = [false, false, false, false, false, false, false, false];
        this._playedLuckStarProcess = [0, 0, 0, 0, 0, 0, 0, 0];
        this._playedLuckStarAreaList = [[], [], [], [], [], [], [], []];
        this._isPlayingBetSound = false;
        this._myself_isbet = false;
        this._runPlayerBetAnimFlag = false;
        this._chipsCount = 0;
        this._userBetMsgTemp1 = [];
        this._userBetMsgTemp2 = [];
        this._flyingOnlineBets1 = [];
        this._flyingOnlineBets2 = [];
        this._settleData = null;
        this.putAllPoolChip();
    }

    //回收筹码
    putAllPoolChip() {
        for (let i = 0; i < this._chipPoolList.length; i++) {
            let item = this._chipPoolList[i];
            CARChipPoolAssisCom.instance.put(item);
        }
        this._chipPoolList = [];
        this._chips = [[], [], [], [], [], [], [], []];
    }

    onGameResultNew(msg) {
        //console.log(msg, "新结算：");
        this._gameView.onResult(msg);
    }

    exit() {
        let self = this;
        this.unbindGameMessage(CAR.CMD.SC_CAR_CONFIG_P, self.onConfigs, self);               //配置
        this.unbindGameMessage(CAR.CMD.SC_CAR_GAMESTATE_P, self.onGameStatus, self);        //游戏状态
        this.unbindGameMessage(CAR.CMD.SC_CAR_OPER_ERROR_P, self.onError, self);              //错误消息
        // this.unbindGameMessage(CAR.CMD.SC_CAR_HISTORY_P,self.onHistory, self);               //历史记录
        this.unbindGameMessage(CAR.CMD.SC_CAR_ALLLIST_P, self.onAllPlayerList, self);        //玩家列表
        this.unbindGameMessage(CAR.CMD.SC_CAR_BET_SUCCESS, self.onBetSuccess, self);
        this.unbindGameMessage(CAR.CMD.SC_CAR_BET_SYNC, self.onBetSYNC, self);
        this.unbindGameMessage(CAR.CMD.SC_CAR_SETTLEMENT_P_NEW, self.onGameResultNew, self);
        this.unbindGameMessage(CAR.CMD.SC_CAR_ONLINENUM_P, self.onUpdateOnlinePlayersNum, self); //更新桌面上玩家在线人数
        EventManager.instance.off(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        CARChipPoolAssisCom.instance.clear();
        TextTips["GameTextTips"] = {};
        super.exit()
    }

    // 进入房间，房间信息
    onRoomInfo(info) {
        super.onRoomInfo(info)
        this._playerView.onSaveUserInfo(info.playerlist);

    }

    // 玩家加入
    onPlayerEnter(info) {
        super.onPlayerEnter(info);
        this._playerView.onUserEnter(info);
    }

    // 玩家离开
    onPlayerQuit(info) {
        this._playerView.onUserLeave(info);
    }

    //退出
    onQuitGame(info) {
        let self = this;
        if (this._notBetCount > 0) {
            let msgContent = this._gameView.stringFormat(GameTextTips.GAME_EXIT_MIN_JU_TIP, [this._notBetCount > 0]);
            AlertHelper.show(msgContent, (() => {
                self.quitGame();
            }), true);
        } else {
            UIHelper.clearAll();
            super.onQuitGame(info);
        }
    }

    // 断线重连
    onToOtherRoom(info) {
        let self = this;
        this._isReconectGame = true;
        this._reconnectMoney = info.chouma;
        this._playerView.updateMyScore(info.chouma, true, true);
        super.onToOtherRoom(info);

        if (info && info.config && info.config.Bet) {
            let bets = [];
            for (var key in info.config.Bet) {
                if (info.config.Bet[key]) {
                    bets.push(Common.toInt(info.config.Bet[key]));
                }
            }
            this._chipNumList = bets;
            this._gameView.autoSelectChip(true);
            this._betTime = info.config.TimeLimit.BuyHorse;
            this._betNeedNum = info.config.BetNeed;
            //  this._gameView.offLineToOnline(info);
        } else {
            cc.log("断线重连可下注配置为空");
        }

        if (info.config && info.config.BetMax) {
            this._BetMax = info.config.BetMax;
        }

        if (info.nextat) {
            this._betTime = info.nextat;
        }

        this._gameState = info.state;
        if (this._gameState == CAR.GameState.Bet) {//只有下注阶段才显示闹钟倒计时，其它显示等待阶段
            this._isStartBet = true;
            this._gameView.betTimeOut();
        }
        else {
            this._gameView.setWaitCountTime(true);
        }

        // if(info.data ){
        //      this._gameView.onUpdateHistoryIcon(info.data);
        //      this._gameView.updateTrendData(info.data);
        // }
    }

    //更新桌面在线人数
    onUpdateOnlinePlayersNum(info) {
        console.log(info, "桌面在线人数：");
        this._gameView.onUpdateOnlinePlayersNum(info);
    }


    // 更新玩家金币
    updatePlayerMoney(info) {
        let self = this;
        console.log(info, "充值成功更新玩家金币：");

        let myPlayerid = self.userInfo.playerid;
        if (info.playerid == myPlayerid) {
            //第一次断线重连时 不更新金币
            if (this._isReconectGame) {
                info.coin = this._reconnectMoney;

            } else {
                super.updatePlayerMoney(info);
                this._playerView.updateMyScore(info.coin, true);
            }
        } else {
            //桌上玩家
            for (let userindex = 0; userindex < CAR.CUSTOM_MYSELF_POS; userindex++) {
                if (info.playerid == this._playerView._playerInfo[userindex].playerid) {
                    this._playerView.updatePlayerMoney(info.playerid, info.coin);
                    break;
                }
            }
            this._playerView.updateUserScore(info);
        }
        //TODO 这里更新下注按钮状态
        this._isReconectGame = false;
    }

    //接收通用道具信息
    public onRoomChat(info: any) {
        console.log("onRoomChat", info);
        let sendPlayerid = Common.toInt(info["sendPlayerid"]);
        let receiverPlayerid = Common.toInt(info["receiverPlayerid"]);
        let type = info["type"];
        let content = info["content"];

        let sendPlayerPos = this._gameView.getHeadPosAtPlayerId(sendPlayerid);//发送者位置
        let receiverPlayerPos = this._gameView.getHeadPosAtPlayerId(receiverPlayerid, true);//接收者位置

        if (Common.isNull(sendPlayerPos)) {//
            return;
        }

        if (Common.isNull(receiverPlayerPos)) {
            this._gameView.doRoomChat(sendPlayerPos, type, content);
        }
        else {
            this._gameView.doRoomIntertChat(sendPlayerPos, receiverPlayerPos, content);
        }
    }



    // 配置信息
    onConfigs(info) {
        console.log(info, "获取游戏配置：");
        if (info.BetMax) {
            this._BetMax = info.BetMax;
        }
        // 下注按钮面值
        if (info.Bet) {
            let bets = [];
            for (var key in info.Bet) {
                if (info.Bet[key]) {
                    bets.push(Common.toInt(info.Bet[key]));
                }
            }
            this._chipNumList = bets;
            this._gameView.autoSelectChip(true);
        }

        // 下注时间
        this._betTime = info["TimeLimit"]["BuyHorse"]

        // 下注需要金币
        this._betNeedNum = info.BetNeed;
    }

    // 游戏状态
    onGameStatus(info) {
        console.log(info, "游戏状态：");
        this._gameState = info.state;
        if (this._gameView._test) {
            return;
        }
        if (info.state == CAR.GameState.Bet) {  //下注状态
            this._gameView.onStartBet();
        } else if (info.state == CAR.GameState.ShowResult) {   //亮牌状态
            this._gameView.onStopBet();
        } else if (info.state == CAR.GameState.GameEnd) {       //结算状态
            // this._gameView.onSleep();
        }
    }

    // 错误提示消息
    onError(info) {
        console.log(info, "onError")

        let self = this;
        let code = info.code;
        if (code == CAR.GAME_ERROR.GAME_ERROR_NOT_MONEY) {
            this._gameView.show(GameTextTips.GOLD_BET_ERR);
        } else if (code == CAR.GAME_ERROR.GAME_ERROR_BUY_LIMIT) {
            this._gameView.show(GameTextTips.GOLD_BET_MAX_ERR);
            // self.model.isBetMax = true;
            // this._gameView.betFaild();
        }
        else if (code == CAR.GAME_ERROR.GAME_ERROR_NOT_ROUND) {
            this._gameView.show(GameTextTips.GAME_MAIN_MIN_ERR);

        } else if (code == CAR.GAME_ERROR.GAME_ERROR_OZ_STATE) {
            this._gameView.show(GameTextTips.GAME_STATE_NOT_XZ_ERR);

        } else if (code == CAR.GAME_ERROR.GAME_ERROR_ZHUANG_NO_MONEY) {
            // let moneyTxt = this._gameView.moneyFormat(self.model.bankerNeed);
            // this._gameView.show(this._gameView.stringFormat(GameTextTips.GAME_SZ_MONEY_TIP, [moneyTxt]));

        } else if (code == CAR.GAME_ERROR.GAME_ERROR_NEXT_ROUND) {
            this._gameView.show(GameTextTips.GAME_XZ_NEXT_TIP);
        } else if (code == CAR.GAME_ERROR.GAME_ERROR_OFFZHUANG_WUNIU) {
            this._gameView.show(GameTextTips.GOLD_BET_MAX_ERR);
            // self.model.isBetMax = true;
            // this._gameView.betFaild();
        } else if (code == CAR.GAME_ERROR.GAME_ERROR_APPLYZHUANG_OK) {
            this._gameView.show(GameTextTips.GAME_APPLY_SZ_TIP);
        } else if (code == CAR.GAME_ERROR.GAME_ERROR_NOT_MONEY_TO_BET) {
            let moneyTxt = this._gameView.moneyFormat(info.value);
            this._gameView.show(this._gameView.stringFormat(GameTextTips.GAME_GOLD_BET_MIN_TIP, [moneyTxt]));

        } else if (code == CAR.GAME_ERROR.GAME_ERROR_FOLLOW_TO_BET) {
            this._gameView.show(GameTextTips.GAME_BET_XT_ERR);
        } else if (code == CAR.GAME_ERROR.GAME_ERROR_FOLLOW_LIMIT) {
            this._gameView.show(GameTextTips.GAME_BET_XT_MIN_ERR);
        } else if (code == CAR.GAME_ERROR.GAME_ERROR_SLIENTCE_TOMANNY) {      //10局没下注踢出房间
            this._notBetCount = info.value;
        } else if (code == CAR.GAME_ERROR.GAME_ERROR_BET_TOOMORE) {
            this._gameView.show(GameTextTips.GAME_BET_MONEY_MAX_TIP);
            // self.model.isBetMax = true;
            // this._gameView.betFaild();
        } else if
            (
            code == CAR.GAME_ERROR.GAME_ERROR_STATE_ERROR
            || code == CAR.GAME_ERROR.GAME_ERROR_BUY_POS_ERROR
            || code == CAR.GAME_ERROR.GAME_ERROR_BANKER_BET
            || code == CAR.GAME_ERROR.GAME_ERROR_ILLEGAL_BET
            || code == CAR.GAME_ERROR.GAME_ERROR_BET_TOOFAST

        ) {
            this._gameView.show(GameTextTips.GAME_BET_ADD_ERR);
        }
    }

    // 走势图返回
    onHistory(info) {
        //console.log(info, "历史记录:");
        // let self = this;
        // if(info["data"]) {
        //      this._gameView.updateTrendData(info.data);
        //      this._gameView.onUpdateHistoryIcon(info.data);
        //      this._gameView.initTrendData(info);
        //     self.model.lastRefreshHistoryTime =new Date().getTime()
        // }
    }

    // 玩家列表(请求下发)
    onAllPlayerList(info) {
        //console.log(info, "玩家列表：");
        this._playerView.onUpdateUserData(info);
        this._playerView.initPlayerListData(info);
        this._gameView.showOnlineUserEnd();
        EventManager.instance.emit(CAR.GameEvent.ONLINE_PLAYER_CHANGE_PAGE, info);
    }

    // 发送协议
    // 下注
    sendBet(area, money) {
        let info = {}
        info["odds"] = money
        info["direction"] = area
        super.sendGameMessage(CAR.CMD.CS_CAR_BUYHORSE_P, info)
    }

    // 请求玩家列表
    requestAllPlayerList(pageNum: number = 0) {
        let info = {};
        info["page"] = pageNum;
        super.sendGameMessage(CAR.CMD.CS_CAR_ALLLIST_P, info)
    }

    //请求历史记录
    requestHistory() {
        super.sendGameMessage(CAR.CMD.CS_CAR_HISTORY_P);
    }
}
