{"ver": "1.2.4", "uuid": "c82879f6-6165-4572-b1d7-e172b3955e8b", "rawTextureUuid": "4897076c-43db-4273-b053-7c4a873fbddd", "size": {"width": 984, "height": 944}, "type": "Texture Packer", "subMetas": {"CB_di.png": {"ver": "1.0.4", "uuid": "151be380-314f-4b7e-8793-98e2c59a0e6c", "rawTextureUuid": "4897076c-43db-4273-b053-7c4a873fbddd", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 2, "trimY": 2, "width": 980, "height": 270, "rawWidth": 980, "rawHeight": 270, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "spriteType": "normal", "subMetas": {}}, "CB_ding.png": {"ver": "1.0.4", "uuid": "a0b6f8a6-8ddc-49cc-b091-a4de8b2bd28e", "rawTextureUuid": "4897076c-43db-4273-b053-7c4a873fbddd", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 2, "trimY": 274, "width": 974, "height": 270, "rawWidth": 974, "rawHeight": 270, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "spriteType": "normal", "subMetas": {}}, "CB_jiantou.png": {"ver": "1.0.4", "uuid": "86a90489-aa5a-4d12-b901-3e53fd2a71f4", "rawTextureUuid": "4897076c-43db-4273-b053-7c4a873fbddd", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 2, "trimY": 810, "width": 154, "height": 132, "rawWidth": 154, "rawHeight": 132, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "spriteType": "normal", "subMetas": {}}, "CB_zhongjian.png": {"ver": "1.0.4", "uuid": "687a1e49-8fc8-4f5e-bce5-5f54d006af5c", "rawTextureUuid": "4897076c-43db-4273-b053-7c4a873fbddd", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 2, "trimY": 546, "width": 972, "height": 262, "rawWidth": 972, "rawHeight": 262, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "spriteType": "normal", "subMetas": {}}, "tx_bcbm4.png": {"ver": "1.0.4", "uuid": "2210d224-e36e-495f-98e3-1a3402beef0f", "rawTextureUuid": "4897076c-43db-4273-b053-7c4a873fbddd", "trimType": "auto", "trimThreshold": 1, "rotated": true, "offsetX": 0, "offsetY": 0, "trimX": 158, "trimY": 810, "width": 46, "height": 288, "rawWidth": 300, "rawHeight": 300, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "spriteType": "normal", "subMetas": {}}}}