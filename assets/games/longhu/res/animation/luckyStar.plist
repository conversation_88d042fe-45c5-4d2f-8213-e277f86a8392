<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>angle</key>
	<real>0.0</real>
	<key>angleVariance</key>
	<real>0.0</real>
	<key>blendFuncDestination</key>
	<integer>771</integer>
	<key>blendFuncSource</key>
	<integer>770</integer>
	<key>duration</key>
	<real>-1</real>
	<key>emitterType</key>
	<real>0.0</real>
	<key>finishColorAlpha</key>
	<real>1</real>
	<key>finishColorBlue</key>
	<real>1</real>
	<key>finishColorGreen</key>
	<real>1</real>
	<key>finishColorRed</key>
	<real>1</real>
	<key>finishColorVarianceAlpha</key>
	<real>0.0</real>
	<key>finishColorVarianceBlue</key>
	<real>0.0</real>
	<key>finishColorVarianceGreen</key>
	<real>0.039999999105930328</real>
	<key>finishColorVarianceRed</key>
	<real>0.0</real>
	<key>finishParticleSize</key>
	<real>0.0</real>
	<key>finishParticleSizeVariance</key>
	<real>0.0</real>
	<key>gravityx</key>
	<real>0.0</real>
	<key>gravityy</key>
	<real>0.0</real>
	<key>maxParticles</key>
	<real>50</real>
	<key>maxRadius</key>
	<real>100</real>
	<key>maxRadiusVariance</key>
	<real>0.0</real>
	<key>minRadius</key>
	<real>0.0</real>
	<key>particleLifespan</key>
	<real>0.75</real>
	<key>particleLifespanVariance</key>
	<real>0.10000000149011612</real>
	<key>radialAccelVariance</key>
	<real>0.0</real>
	<key>radialAcceleration</key>
	<real>0.0</real>
	<key>rotatePerSecond</key>
	<real>0.0</real>
	<key>rotatePerSecondVariance</key>
	<real>0.0</real>
	<key>rotationEnd</key>
	<real>-180</real>
	<key>rotationEndVariance</key>
	<real>90</real>
	<key>rotationStart</key>
	<real>0.0</real>
	<key>rotationStartVariance</key>
	<real>90</real>
	<key>sourcePositionVariancex</key>
	<real>0.0</real>
	<key>sourcePositionVariancey</key>
	<real>0.0</real>
	<key>sourcePositionx</key>
	<real>203.96958923339844</real>
	<key>sourcePositiony</key>
	<real>256.49212646484375</real>
	<key>speed</key>
	<real>0.0</real>
	<key>speedVariance</key>
	<real>0.0</real>
	<key>startColorAlpha</key>
	<real>1</real>
	<key>startColorBlue</key>
	<real>1</real>
	<key>startColorGreen</key>
	<real>1</real>
	<key>startColorRed</key>
	<real>1</real>
	<key>startColorVarianceAlpha</key>
	<real>0.0</real>
	<key>startColorVarianceBlue</key>
	<real>0.0</real>
	<key>startColorVarianceGreen</key>
	<real>0.0</real>
	<key>startColorVarianceRed</key>
	<real>0.0</real>
	<key>startParticleSize</key>
	<real>24</real>
	<key>startParticleSizeVariance</key>
	<real>6</real>
	<key>tangentialAccelVariance</key>
	<real>0.0</real>
	<key>tangentialAcceleration</key>
	<real>0.0</real>
	<key>textureFileName</key>
	<string>luckyStar.png</string>
	<key>textureImageData</key>
	<string>H4sIAAAAAAAAAwGuBVH6iVBORw0KGgoAAAANSUhEUgAAACIAAAAgCAYAAAB3j6rJAAAAAXNSR0IArs4c6QAABWhJREFUWAm1V11oHFUUPrMzye5C083W1PzamBZ9aU1aQkxTmxAkBRFB8MGfin0QX3yTVvsgvhVEECtiIT40+FehhfpQ1Dy0VarVtlEkibtUqxCNpqgYDNnWtJvszPh9d+5sdic7kybRA2fvnXvPz3fPOfdnjZd602LExCPD1R0R0xKpShQ/wzqQkvf15FNoC2GCHF+4KWKXSriGEncdERqqSO4iporzenAf2sd1/yzaId2v2ETZ9GOxRJEo7QURKodwAuNDJXNH0edYRXllCzbDKBQIFZYBcohONzQ4ijUANVYJDG1FUSQQGnRogCspZVdS4srzzPDe5/5RrLKNMYynymShRxu0FUWRQKjI4gqs0MD3cY41by5IKu0oZl/LcY4y/nd5gYagWRaIAlNeK1tRPw/EoLnvwByWi1iA2ecY5wBiqw9kuZT4uG4JiDKKEMNJFUL/IVi6789LjPnQKWOfY5yjDGXBy6ZkRUAo7NjK6ABA3V0Vd2XgkfliNPyocIxzlAEPUOdWyexrTYqhKg0qfsvuYp9Ruws8gKEjWO36h57IS2MLl4vRAK+rceWHMXU89WP2KpjlPgOmZIC0E8wEDzSepdvA28E7wB2a16FVYU7XubKjBxXM2qhAnPv8E1dmpo1mTB/XItfRjmseRTsGzoJx1npk4IjvwBF/AJ90fo8eLzaMzIaNrjTeYUt9syM7++clnqywuKKGSP6GIZfOVcufV2Py+2+m/P2XEVYrGaiNoZZeIxCifox2LMSHzhoQ9sYWG869fjXyvhaazxvyxxRBgadM1SfIwuK9c8JCUQ3DiQLyMHK/vZspDdAKii6gqT6rLVc23Wkr9koGYRipkpPvxX3xYbNvU3IcoZlDKe25krGk7nZH6usRAd6M/xNnRy05+W5CHG59Vw4i/YMEwo8LAFONid4rWUuamlypq6u8K1TtM1OrZNo/8XZCCgy8Ky8DxCHWoQ+Eg58hTXHblt7LiExrmyPpNIX/u8hM/GTKsaMJmce5B3oFAF4kCAVkd1OSJ6ZaIRb5KQDV4H7ZlcVZ0NZmS20t0+TNr6WdnIjJO28lJM8N68ph/B6kPSxe2Td3NQBIyQCGCWYjQteVGTNl8xZXalMUXn1kJn82ZWgwrrY1bA/C2n60ru+XgTDvQ0QCxPnTEGwrFIz271BYnZ2OJLiFNfqVtLMzhhx5nZHAQkSO4fR+FqlY3LjaediltwA0z7iO8dVNHE6ZURPvARhaBWewENqgLdqE3wrnA84we14h1bjKGpbULEdS6xGKVZ4l1FU1qGwZeTfEjqWFyhAUPwxpZ78F2znsbinKhnSoy1yD2lVKQ+R4soZRAootJrKSXkNEqGsi6AVbWuCIl2rxoit1HAWkjoIJqobctKWGovpx2Fjg/StCm1OqF/iJSo0HhLuF50wFYjS/+RYhA3V12qVvmDJp7rhrOVWL4UAi8qaAJHkvBYDcwC64OBKTc19akrvm+fx42JL+3QXp6XYkGXgmKBteCSibZSj1R1RqmFOprcGPTs30tCFnvzDl4tfFXU/zlyg3m5Odp4Yt4xTu8p57HRnos3Ffed5TsKFrUdmkfJCigPClJk34A/XrpCFnAGA8WwTATcjnw6sI+HnKwWUvmhfAD14YiZlg6djmyB4AaoaN8axKobJJ+SBZEalRSt//GJPhM14dQBkvZvkAzLsiW/rGxfd52CIo6u0HPzmeiVWDpa0VML3ghAKJMWQh3MjxiV/UM28O/cPgLeCn4SQLDqMsZbQsdea0DfpRNtEu8Wl2pZfcNb4Drr4V/Cb4UaTgI0Qgx0le24rQ+l09UtrkEIXT8En96+DbwG+A+XBeQv8CnLQNQQ15JFwAAAAASUVORK5CYIKhO/jwrgUAAA==</string>
</dict>
</plist>
