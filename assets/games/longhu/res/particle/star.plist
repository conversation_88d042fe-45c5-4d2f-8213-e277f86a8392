<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>angle</key>
	<real>0.0</real>
	<key>angleVariance</key>
	<real>0.0</real>
	<key>blendFuncDestination</key>
	<integer>1</integer>
	<key>blendFuncSource</key>
	<integer>770</integer>
	<key>duration</key>
	<real>2</real>
	<key>emitterType</key>
	<real>0.0</real>
	<key>finishColorAlpha</key>
	<real>1</real>
	<key>finishColorBlue</key>
	<real>0.0</real>
	<key>finishColorGreen</key>
	<real>0.0</real>
	<key>finishColorRed</key>
	<real>0.0</real>
	<key>finishColorVarianceAlpha</key>
	<real>0.0</real>
	<key>finishColorVarianceBlue</key>
	<real>0.0</real>
	<key>finishColorVarianceGreen</key>
	<real>0.0</real>
	<key>finishColorVarianceRed</key>
	<real>0.0</real>
	<key>finishParticleSize</key>
	<real>0.0</real>
	<key>finishParticleSizeVariance</key>
	<real>0.0</real>
	<key>gravityx</key>
	<real>0.0</real>
	<key>gravityy</key>
	<real>0.0</real>
	<key>maxParticles</key>
	<real>50</real>
	<key>maxRadius</key>
	<real>100</real>
	<key>maxRadiusVariance</key>
	<real>0.0</real>
	<key>minRadius</key>
	<real>0.0</real>
	<key>particleLifespan</key>
	<real>1</real>
	<key>particleLifespanVariance</key>
	<real>0.0</real>
	<key>radialAccelVariance</key>
	<real>142.22756958007812</real>
	<key>radialAcceleration</key>
	<real>0.0</real>
	<key>rotatePerSecond</key>
	<real>0.0</real>
	<key>rotatePerSecondVariance</key>
	<real>0.0</real>
	<key>rotationEnd</key>
	<real>0.0</real>
	<key>rotationEndVariance</key>
	<real>270.43267822265625</real>
	<key>rotationStart</key>
	<real>0.0</real>
	<key>rotationStartVariance</key>
	<real>0.0</real>
	<key>sourcePositionVariancex</key>
	<real>60</real>
	<key>sourcePositionVariancey</key>
	<real>60</real>
	<key>sourcePositionx</key>
	<real>163.75563049316406</real>
	<key>sourcePositiony</key>
	<real>205.61373901367188</real>
	<key>speed</key>
	<real>0.0</real>
	<key>speedVariance</key>
	<real>0.0</real>
	<key>startColorAlpha</key>
	<real>1</real>
	<key>startColorBlue</key>
	<real>0.0</real>
	<key>startColorGreen</key>
	<real>0.81367790699005127</real>
	<key>startColorRed</key>
	<real>1</real>
	<key>startColorVarianceAlpha</key>
	<real>0.0</real>
	<key>startColorVarianceBlue</key>
	<real>0.0</real>
	<key>startColorVarianceGreen</key>
	<real>0.0</real>
	<key>startColorVarianceRed</key>
	<real>0.0</real>
	<key>startParticleSize</key>
	<real>32</real>
	<key>startParticleSizeVariance</key>
	<real>16</real>
	<key>tangentialAccelVariance</key>
	<real>0.0</real>
	<key>tangentialAcceleration</key>
	<real>0.0</real>
	<key>textureFileName</key>
	<string>star.png</string>
</dict>
</plist>
