[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "65d5629f-6d97-46b9-880f-008da87d4f66"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 8}, {"__id__": 45}, {"__id__": 262}, {"__id__": 326}, {"__id__": 413}], "_active": true, "_components": [{"__id__": 415}, {"__id__": 416}, {"__id__": 417}, {"__id__": 418}, {"__id__": 419}, {"__id__": 420}, {"__id__": 421}, {"__id__": 422}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [640, 360, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a5esZu+45LA5mBpvttspPD"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e1WoFrQ79G7r4ZuQE3HlNb"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "81GN3uXINKVLeW4+iKSlim"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 6}, {"__id__": 7}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1559, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d0X180dIVOzatxWNffFlis"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2b8aa329-b381-4367-b673-1ed963a21307"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "f02+thBk5DJI8kHcPMjxPM"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -139.5, "_right": -139.5, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1559, "_originalHeight": 720, "_id": "0bF2ncTThEA6MWgGg77O0y"}, {"__type__": "cc.Node", "_name": "topBar", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 9}, {"__id__": 35}], "_active": true, "_components": [{"__id__": 43}, {"__id__": 44}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 328.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "00nQdoWytHr7cQkq7xUkoO"}, {"__type__": "cc.Node", "_name": "LeftButtons", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [{"__id__": 10}, {"__id__": 14}, {"__id__": 18}], "_active": true, "_components": [{"__id__": 34}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1386, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-640, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e91jBSqSlNeJkfaQKevhm8"}, {"__type__": "cc.Node", "_name": "BackButton", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 12}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 59}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9.06, -15.829, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "44fhxpOfVKEa3dhfBQeJ3o"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a02807a1-1566-45b1-ab76-2f4dad900fb7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "a6k9rbeeBEIYzCo66lCstY"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "clickEvents": [{"__id__": 13}], "pressedScale": 0.95, "clickSound": null, "enableTouch": true, "isPlaySound": true, "stopPropagation": false, "_id": "68d0L0T0VEGacwkoRBEwBV"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "onBackHall", "customEventData": ""}, {"__type__": "cc.Node", "_name": "RuleButton", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128.739, -17.848, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3223v6SBBPI4gaGJ9wW9No"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9cf2c88c-9681-4bcb-84f2-fd923955c1d9"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "baEhlq7AFOAoSZM2euZDLB"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "clickEvents": [{"__id__": 17}], "pressedScale": 0.95, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "97wCfMwANIyYyT3bCcnjru"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "onClickShowHelpLayer", "customEventData": ""}, {"__type__": "cc.Node", "_name": "BatteryLayer", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 19}, {"__id__": 22}, {"__id__": 28}], "_active": false, "_components": [{"__id__": 31}, {"__id__": 32}], "_prefab": {"__id__": 33}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [135, -2, 0, 0, 0, 0, 1, 0.5, 0.5, 0.5]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2dA+lUsj1I4bKjk6/l+IJ0"}, {"__type__": "cc.Node", "_name": "FrameBg", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [], "_active": true, "_components": [{"__id__": 20}], "_prefab": {"__id__": 21}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ffZZDvC2BMQb6sLIF2bRfK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2396dea4-3961-4e6e-8a91-1fdfb50cd89f"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "53AlBFioNF/L2KgIKuMXoa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "4091cd7b-ef7e-47f0-b923-6a256934e3e0"}, "fileId": "f9eQ6xLRJNDJgaLQ6gCotE", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [{"__id__": 23}], "_active": true, "_components": [{"__id__": 26}], "_prefab": {"__id__": 27}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2dbc+hf+9C/rR6qm7RKywL"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 24}], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 26}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [37, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d4ItF4/mRBDoO6CzFyyq5h"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ead484c0-3b1c-4308-830a-23611a3690c6"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 1, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "f9aAC7UlVNCqL2/qsdTEp6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "4091cd7b-ef7e-47f0-b923-6a256934e3e0"}, "fileId": "04uiRKjQhBgb9rb32o62A+", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_N$totalLength": 68, "_N$barSprite": {"__id__": 24}, "_N$mode": 0, "_N$progress": 1, "_N$reverse": true, "_id": "7eCesHDw9AmIvNBEA5mFJy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "4091cd7b-ef7e-47f0-b923-6a256934e3e0"}, "fileId": "05/tCQLEtOVaCoDRZRVZFY", "sync": false}, {"__type__": "cc.Node", "_name": "Lightnig", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [], "_active": true, "_components": [{"__id__": 29}], "_prefab": {"__id__": 30}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8a7dkeQDlCXZOyriRFsW4B"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "51bd10f1-a5ed-435b-b6c2-4bb084664ef6"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "1fOIIgG99IdK8LcBlrbwN2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "4091cd7b-ef7e-47f0-b923-6a256934e3e0"}, "fileId": "b46KTyKDZJc5yPzZL8wG2G", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 100, "height": 42}, "_resize": 0, "_N$layoutType": 0, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": true, "_id": "7as9hMLEhNLLCk0M4etVSM"}, {"__type__": "6afdfFtmkFMZKyV2cYKpblz", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "batteryProgress": {"__id__": 26}, "lightnigSprite": {"__id__": 29}, "_id": "bahcucJxRElJARYOXNzIHs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "4091cd7b-ef7e-47f0-b923-6a256934e3e0"}, "fileId": "", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "3aKDtzRBtIeqSUZ180CPJs"}, {"__type__": "cc.Node", "_name": "ShopButton", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [{"__id__": 36}], "_active": true, "_components": [{"__id__": 39}, {"__id__": 40}, {"__id__": 41}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [519.374, -22.503, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "faZVdOjyxAEZd7NTM9y+mg"}, {"__type__": "cc.Node", "_name": "font_add", "_objFlags": 0, "_parent": {"__id__": 35}, "_children": [], "_active": true, "_components": [{"__id__": 37}, {"__id__": 38}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-35.699, -6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8dyPvG3GZIlrdwbzoyoCN9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5623ddbf-e2e4-401b-a192-df3abe18b47f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "a0rispV7VOEL4FHaDA4W8P"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "english": {"__uuid__": "5623ddbf-e2e4-401b-a192-df3abe18b47f"}, "india": {"__uuid__": "2a23ba54-639f-4fe9-85f7-e7dd0d522438"}, "_id": "5dyTcQqDNKfYsvj2h5/vz5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fa77dffa-ba01-4523-a4fb-a5ac1a53b388"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "7eQ9Oa0RVGOoNrh3GpNLGU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 19.625999999999976, "_top": 13.503, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "2bW3Q3cLBNbrnS3CDutpa0"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "clickEvents": [{"__id__": 42}], "pressedScale": 0.95, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "a4EXzYdXNCXLKSpNusPC+v"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "onClickRecharge", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 41, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1386, "_originalHeight": 0, "_id": "79U22rx0BFc7jbnuErvVJ1"}, {"__type__": "67357pa61BBPoPUQ/yG4mxl", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "enableAlignEdge": false, "_id": "9fu1vtkXRNAr0MBIGnGVCl"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>ayer", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 46}, {"__id__": 56}, {"__id__": 66}, {"__id__": 70}, {"__id__": 74}, {"__id__": 137}, {"__id__": 181}, {"__id__": 216}, {"__id__": 217}, {"__id__": 223}, {"__id__": 230}, {"__id__": 246}, {"__id__": 248}, {"__id__": 255}, {"__id__": 260}, {"__id__": 261}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "68bSALHetBvJVzSj9AHlyK"}, {"__type__": "cc.Node", "_name": "set<PERSON>ain<PERSON><PERSON>on", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 47}, {"__id__": 50}], "_active": true, "_components": [{"__id__": 53}, {"__id__": 54}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 129, "height": 46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [144.877, 210.647, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "baMP9tYsFHxKpbCflVVwp2"}, {"__type__": "cc.Node", "_name": "upBanker", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [{"__id__": 48}, {"__id__": 49}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 29}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.57, 3.039, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "83ZlSRGHJGPomAkHzBCJF9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2983aa22-626f-48f2-aa34-ca9d5b262208"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "69Z1lQpT9CP6lBtvLVgW1d"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "english": {"__uuid__": "2983aa22-626f-48f2-aa34-ca9d5b262208"}, "india": {"__uuid__": "e4edebb2-7c62-446a-851e-ff1aad3f9244"}, "_id": "97kFqhUIlDmpmTa0lndG6V"}, {"__type__": "cc.Node", "_name": "endBanker", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [], "_active": false, "_components": [{"__id__": 51}, {"__id__": 52}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 29}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.57, 3.039, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "86e8bgmfFF3bHvOEYEvjih"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "********-f698-446a-9ace-ddbb3d5b19d1"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "1fwzrplItNqKNNaarNSjAi"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "english": {"__uuid__": "********-f698-446a-9ace-ddbb3d5b19d1"}, "india": {"__uuid__": "478be50b-d2a9-4deb-8517-eec9c38381c4"}, "_id": "6bKm7hDAhHx51+zdwvoHjG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ae1290f1-91b7-45c4-bab6-0f8676b4f420"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "c7CjjaAoRHqZDvYk1d0B0O"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "clickEvents": [{"__id__": 55}], "pressedScale": 0.95, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "e0J2MJfetBMIVEeElI8l/o"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "showMainUser", "customEventData": ""}, {"__type__": "cc.Node", "_name": "history<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 57}, {"__id__": 59}], "_active": true, "_components": [{"__id__": 64}, {"__id__": 65}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 812, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.811, -158.37400000000002, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "48uGL8mcVJPK2pB7m3A6Ak"}, {"__type__": "cc.Node", "_name": "mainBox", "_objFlags": 0, "_parent": {"__id__": 56}, "_children": [], "_active": true, "_components": [{"__id__": 58}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 732, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-28, -5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "45pVmHz6hMPY+3JWCxtmnU"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 732, "height": 45}, "_resize": 0, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 1, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 2, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "b9/VzO3chKp7ttk3cW8ARw"}, {"__type__": "cc.Node", "_name": "rightBox", "_objFlags": 0, "_parent": {"__id__": 56}, "_children": [], "_active": true, "_components": [{"__id__": 60}, {"__id__": 61}, {"__id__": 62}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [373.52, -0.1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cbqDnNlTFBTZbhHPXYRdz0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c240c3f0-41b3-46cd-9e4c-c40aaaf570a2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "00ZnEZqF1HuK8iL/a4ccW3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 32, "_left": 0, "_right": 1.4800000000000182, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "d83N4EML5MgpCvEryldRiO"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "clickEvents": [{"__id__": 63}], "pressedScale": 0.95, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "c8hvgfeqFJFoPh20tDcT5w"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "showHistory", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 495.874, "_bottom": 152.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "35WX1anaJBeaFj/J5VNp/G"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d3e6b534-005b-4c5c-91f5-6154d974220d"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "f6knAqORVHl69GdUNmL4x7"}, {"__type__": "cc.Node", "_name": "leftUser", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 67}, {"__id__": 68}, {"__id__": 69}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 360}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-574, 8.479, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "edBKmafKhHo5UnqXVBTA9i"}, {"__type__": "cc.Node", "_name": "user0", "_objFlags": 0, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 147, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "60LmbTDKZDkKNhhCtokyw8"}, {"__type__": "cc.Node", "_name": "user2", "_objFlags": 0, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -12, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fbGyHOxJlCULEa6c8NQo87"}, {"__type__": "cc.Node", "_name": "user4", "_objFlags": 0, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -168, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "46uv92rNpEjKz8YKHB0JH0"}, {"__type__": "cc.Node", "_name": "rightUser", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 71}, {"__id__": 72}, {"__id__": 73}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 360}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [575.232, 4.239, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8fo+cbeaVG5brmL8/Hanq1"}, {"__type__": "cc.Node", "_name": "user1", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 151, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1dRCN/2apEopl5XZ1G121E"}, {"__type__": "cc.Node", "_name": "user3", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c18cpJn61GVrUMlbUG9cjU"}, {"__type__": "cc.Node", "_name": "user5", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -164, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d2cGPvedhFX4eZSVdgMy1X"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 75}, {"__id__": 92}, {"__id__": 113}, {"__id__": 130}, {"__id__": 134}], "_active": true, "_components": [{"__id__": 136}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d38sdp71VLvKd8sf+gc40e"}, {"__type__": "cc.Node", "_name": "longPanel", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [{"__id__": 76}, {"__id__": 78}, {"__id__": 81}, {"__id__": 85}, {"__id__": 88}], "_active": true, "_components": [{"__id__": 90}, {"__id__": 91}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 323, "height": 311}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-304.326, 16.514999999999986, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bavHHZUIdC9qrwnQevmCzD"}, {"__type__": "cc.Node", "_name": "chipPosPanel", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 77}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 323, "height": 311}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fcPKdkwk1Kapuz/fAT8nSr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "d5gNthAdBPtpPUPxo+ofys"}, {"__type__": "cc.Node", "_name": "self<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 79}, {"__id__": 80}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 22.8, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-18.634, -120.40899999999999, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "53Ulm0FZFGM5UgD6+6heFk"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 36, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "48064fdf-1e63-4387-9371-ac9e76c1392c"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 2, "_id": "a6Vn2ru7JMGryhtdVo0kfG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 116.385, "_right": 5, "_top": 118, "_bottom": 15.090999999999994, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 23.94, "_id": "41FVegUtZPn4RhQsgdkPWO"}, {"__type__": "cc.Node", "_name": "totalLabel", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 82}, {"__id__": 83}, {"__id__": 84}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 169, "g": 147, "b": 61, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 15.57, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [34.803, 117.869, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3fREW5E5hMUKQI4HgvST/a"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 28, "_lineHeight": 32, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "cdM+kGJaFCF4zPIKxYhIDS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 41, "_left": 188.518, "_right": 118.912, "_top": 17.471, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 14, "_originalHeight": 0, "_id": "7fDNJlKihEcKedv8raHh9A"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": false, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 1, "_id": "e0Y+GwrvVHppNSXyzRrWVo"}, {"__type__": "cc.Node", "_name": "longStarImg", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [{"__id__": 86}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-55.11, 119.58, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "210VVMZBhOUbU+oYfh52GE"}, {"__type__": "cc.Node", "_name": "bg0", "_objFlags": 0, "_parent": {"__id__": 85}, "_children": [], "_active": true, "_components": [{"__id__": 87}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 19}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8b2Rf6DE9G+5cdYVW/cV4f"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d41545b4-db9e-4465-b7f4-0e92faacc479"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "c8fmQZ/eNJWqkkX3srsccy"}, {"__type__": "cc.Node", "_name": "longClickBet", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 89}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 323, "height": 311}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bbKqVQ6XBFv7j3rtzbtFSK"}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -152, "y": -145}, {"__type__": "cc.Vec2", "x": 105, "y": -145}, {"__type__": "cc.Vec2", "x": 152, "y": 145}, {"__type__": "cc.Vec2", "x": -80, "y": 146}], "_id": "cdZRP0GZ5MBqpUW5Beyil8"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 300, "_right": 0, "_top": 187.985, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "29GG49xnpGv6MOvnClieJw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "37a85cf9-e73f-423e-a33c-dc6992ad97fb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "b7WqJgxtRJb5KGI0CFWaB5"}, {"__type__": "cc.Node", "_name": "he<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [{"__id__": 93}, {"__id__": 96}, {"__id__": 98}, {"__id__": 101}, {"__id__": 105}, {"__id__": 109}], "_active": true, "_components": [{"__id__": 111}, {"__id__": 112}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 311}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.123, 14.669000000000011, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7fxMSiUdVF97WPDmkw0pI4"}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [{"__id__": 94}, {"__id__": 95}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 137, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.673, 26.916, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "23lnmgX6pMdaA5mp9uwLYe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a2aadcde-3789-4172-87b3-b71b9d19fa94"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "52kUyW769FJrCnN83RsJbi"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "english": {"__uuid__": "a2aadcde-3789-4172-87b3-b71b9d19fa94"}, "india": {"__uuid__": "29153c42-cb2b-44e8-960b-13c374d557b0"}, "_id": "49RHTw8bJKto/QP/vfb9Ch"}, {"__type__": "cc.Node", "_name": "chipPosPanel", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [{"__id__": 97}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 311}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e3mu/ZTjdMzL1FftNR3x+e"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "65vmS6hJxAz5mMYMxrqYoV"}, {"__type__": "cc.Node", "_name": "self<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [{"__id__": 99}, {"__id__": 100}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 22.8, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2.496, -118.75999999999999, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b6/cjuj1ZDNZEH1evXtA9q"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 36, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "48064fdf-1e63-4387-9371-ac9e76c1392c"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "512w8W1TBA9KT/Jz/6AB+1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 16.740000000000006, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "40MVb55kVILork3woeX7Ea"}, {"__type__": "cc.Node", "_name": "totalLabel", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [{"__id__": 102}, {"__id__": 103}, {"__id__": 104}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 169, "g": 147, "b": 61, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 15.57, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.8389999999999986, 119.74700000000001, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3eYMsaNv1J4IA5Q8QNOkNp"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 28, "_lineHeight": 32, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "19dWK9bhBI16+e0x4N3u0F"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 41, "_left": 135.376, "_right": 137.054, "_top": 15.593, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 14, "_originalHeight": 0, "_id": "63KRo193pP74/WhY2UFw1K"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": false, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 1, "_id": "ddev3xpN1P9qN/wM6FeaWt"}, {"__type__": "cc.Node", "_name": "heStarImg", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [{"__id__": 106}], "_active": false, "_components": [{"__id__": 108}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-76.528, 121.488, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f7sfEqDdtON60H5x/9aNDa"}, {"__type__": "cc.Node", "_name": "bg0", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 107}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 19}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "63vYZWzLFJ6Idc1fEsXdJB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d41545b4-db9e-4465-b7f4-0e92faacc479"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "88yXhyrT9F87R3NMHe3P/q"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 50.471999999999994, "_right": 0, "_top": 18.012, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "5eRPlNOFdOGqHdJZRB5iqs"}, {"__type__": "cc.Node", "_name": "heClickBet", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [{"__id__": 110}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 323, "height": 311}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "93VA2B33NKaLeWxg4x5LzO"}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 136, "y": -145}, {"__type__": "cc.Vec2", "x": -135, "y": -145}, {"__type__": "cc.Vec2", "x": -104, "y": 145}, {"__type__": "cc.Vec2", "x": 100, "y": 146}], "_id": "c0j20uyu9AT7MM2QTuxbCA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 300, "_right": 0, "_top": 189.831, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "21tLkz5jBBpb1/RLyIsma+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9748ff25-6bc3-4659-984b-e0579aed17b2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "a75iUBnsdIooHHY71YV0IS"}, {"__type__": "cc.Node", "_name": "huPanel", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [{"__id__": 114}, {"__id__": 116}, {"__id__": 119}, {"__id__": 123}, {"__id__": 126}], "_active": true, "_components": [{"__id__": 128}, {"__id__": 129}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 308}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [303.205, 17.186000000000007, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "64muqEVnlAaLacaHHTAnnW"}, {"__type__": "cc.Node", "_name": "chipPosPanel", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 115}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 308}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "00HLii3EZJZqXDwXBWx+ms"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "eebyjkmndO1734LOnrlncp"}, {"__type__": "cc.Node", "_name": "self<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 117}, {"__id__": 118}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 22.8, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [21.393, -121.697, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5aWbWUxoVKHIwOhQMtPPXx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 36, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "48064fdf-1e63-4387-9371-ac9e76c1392c"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "e5nP7bgxBDu7vkzAygCiN2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 147, "_right": 141, "_top": -152, "_bottom": 12.302999999999997, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "e6MW/OWL5OcrxEc/P8i995"}, {"__type__": "cc.Node", "_name": "totalLabel", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 120}, {"__id__": 121}, {"__id__": 122}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 169, "g": 147, "b": 61, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 15.57, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-35.068999999999996, 117.40700000000001, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b3qa55+ABGTrBtBk8r5gqU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 28, "_lineHeight": 32, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "f9dHUbQa5LvpuIYrlznu2c"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 41, "_left": 117.146, "_right": 187.284, "_top": 16.433000000000003, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 14, "_originalHeight": 0, "_id": "b6YjktD55MvaTwinFBPKVN"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": false, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 1, "_id": "bfAGkDG75KxI3u8KVKt+2W"}, {"__type__": "cc.Node", "_name": "huStarImg", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [{"__id__": 124}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-113.616, 118.962, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f13nKrCEZC25llfSU4/hxe"}, {"__type__": "cc.Node", "_name": "bg0", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 125}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 19}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "298uZtuNpA8IeLtRvO60/h"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 124}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d41545b4-db9e-4465-b7f4-0e92faacc479"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "24JQhUMtNAApx8kmw3cIdY"}, {"__type__": "cc.Node", "_name": "huClickBet", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 127}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 323, "height": 311}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0924jLk4BFg5tGMKSJjjU9"}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 152, "y": -145}, {"__type__": "cc.Vec2", "x": -108, "y": -145}, {"__type__": "cc.Vec2", "x": -152, "y": 145}, {"__type__": "cc.Vec2", "x": 74, "y": 146}], "_id": "6dU5ZTnc1OQIkiVMwGc7KC"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 300, "_top": 188.814, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "7cV72GZSFN1LwJ/9QG0sdn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0666a717-ff36-49e4-9665-0a4910e1a986"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "f9H1JlsdFP85MESFSXhv5I"}, {"__type__": "cc.Node", "_name": "gameTime", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [{"__id__": 131}], "_active": false, "_components": [{"__id__": 133}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-144.438, 213.64, 0, 0, 0, 0, 1, 1.1, 1.1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "19PX/6p3JJc4AfXY7H9kLo"}, {"__type__": "cc.Node", "_name": "timeValue", "_objFlags": 0, "_parent": {"__id__": 130}, "_children": [], "_active": true, "_components": [{"__id__": 132}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f0cPTB51VBhJCJ9uGNLztu"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 131}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "15", "_N$string": "15", "_fontSize": 24, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "fb6bfafd-b473-4dde-87c1-6b2ac7d3206f"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "ef1zO0JJVOaoJdk29+wvuZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b17f8b1d-c096-4c66-8622-f1abba4f99b1"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "a8Ustkj+FNjYfdYWeFUSyo"}, {"__type__": "cc.Node", "_name": "moveStar", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": false, "_components": [{"__id__": 135}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [150, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "99dzfumJtKO4nu4BFxYxBe"}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_custom": false, "_file": {"__uuid__": "e418a1bd-01b6-4225-b993-c92988e498ad"}, "_spriteFrame": null, "_texture": null, "_stopped": true, "playOnLoad": true, "autoRemoveOnFinish": true, "totalParticles": 50, "duration": -1, "emissionRate": 66.66666666666667, "life": 0.75, "lifeVar": 0.10000000149011612, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 10, "b": 0, "a": 0}, "angle": 0, "angleVar": 0, "startSize": 24, "startSizeVar": 6, "endSize": 0, "endSizeVar": 0, "startSpin": 0, "startSpinVar": 90, "endSpin": -180, "endSpinVar": 90, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_positionType": 1, "positionType": 1, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "speed": 0, "speedVar": 0, "tangentialAccel": 0, "tangentialAccelVar": 0, "radialAccel": 0, "radialAccelVar": 0, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "_N$preview": true, "_id": "5fq5HZjThPnL+WFgYgA0p1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 800, "_originalHeight": 0, "_id": "cefKAOwDlIf72+YV3sd6PA"}, {"__type__": "cc.Node", "_name": "mainUser", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 138}, {"__id__": 172}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 210, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 268.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ecxfVwY8pKHJ/kl7I0131z"}, {"__type__": "cc.Node", "_name": "mainPanel", "_objFlags": 0, "_parent": {"__id__": 137}, "_children": [{"__id__": 139}, {"__id__": 142}, {"__id__": 145}, {"__id__": 152}], "_active": false, "_components": [{"__id__": 171}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 210, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 33.769, 0, 0, 0, 0, 1, 1.14, 1.14, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b81qJQjpZKd4xuDBax7PWa"}, {"__type__": "cc.Node", "_name": "gameLabel", "_objFlags": 0, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 140}, {"__id__": 141}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 104, "g": 69, "b": 39, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 59.24, "height": 21.42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15, 25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "edhbV2Vj5Kz4oG9MQ4nJU3"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "97207200", "_N$string": "97207200", "_fontSize": 15, "_lineHeight": 17, "_enableWrapText": true, "_N$file": {"__uuid__": "f6ffdb7b-a3f4-4251-97f8-dacabd3c386f"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "e2q0XsyJ5K454neDsiD8mY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": 90, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a7ICq+wJRKGrXtnkDLtcSB"}, {"__type__": "cc.Node", "_name": "name<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 143}, {"__id__": 144}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 104, "g": 69, "b": 39, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 98.02, "height": 21.42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15, 6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c9w4wrMHdJlpyvh3RAzhND"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Current game 10", "_N$string": "Current game 10", "_fontSize": 15, "_lineHeight": 17, "_enableWrapText": true, "_N$file": {"__uuid__": "f6ffdb7b-a3f4-4251-97f8-dacabd3c386f"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "11ojdfIfZBN4zH62D9kuy5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": 90, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "32lC3Ol9JN7LQ/9YUjVLU5"}, {"__type__": "cc.Node", "_name": "goldPanel", "_objFlags": 0, "_parent": {"__id__": 138}, "_children": [{"__id__": 146}, {"__id__": 148}], "_active": true, "_components": [{"__id__": 151}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12, -18, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6cUrSdI3JJS5GuY7J/xdEa"}, {"__type__": "cc.Node", "_name": "goldSprite", "_objFlags": 0, "_parent": {"__id__": 145}, "_children": [], "_active": true, "_components": [{"__id__": 147}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9ftuwkp5ZEU7bHALxUTcfx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 146}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6f339bfd-ef96-44e9-b5a2-8cbddbc814e8"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "d7aLzHcxRDZ5TU/TGIlAHZ"}, {"__type__": "cc.Node", "_name": "goldValue", "_objFlags": 0, "_parent": {"__id__": 145}, "_children": [], "_active": true, "_components": [{"__id__": 149}, {"__id__": 150}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 67.91, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [17, 2.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "afEYkT+DJI74pd7vFojWoV"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "1234567", "_N$string": "1234567", "_fontSize": 17, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "05aa762b-a921-42cb-96c1-d093dbfc336f"}, "_isSystemFontUsed": false, "_spacingX": -4, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "bfmHJLwXFDcqd5MPE/kH9Q"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": 17, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "522T4HmXFBU6gEpTrFj2Zo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 145}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d1b09be7-8862-494d-857b-17938ad28003"}, "_type": 1, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "30vgX7RldGvab4f4VClGxg"}, {"__type__": "cc.Node", "_name": "bankerHeadBg", "_objFlags": 0, "_parent": {"__id__": 138}, "_children": [{"__id__": 153}, {"__id__": 158}, {"__id__": 160}, {"__id__": 162}, {"__id__": 164}, {"__id__": 167}], "_active": true, "_components": [{"__id__": 170}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-61, 1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "abEjqfUE5AFK45GazzV3rh"}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 152}, "_children": [{"__id__": 154}], "_active": true, "_components": [{"__id__": 157}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a2egS3J+NItZYF4Jko1kif"}, {"__type__": "cc.Node", "_name": "headImg", "_objFlags": 0, "_parent": {"__id__": 153}, "_children": [], "_active": true, "_components": [{"__id__": 155}, {"__id__": 156}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d0efl3kgdD86ZY+yySK9iW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 154}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cda5fa94-d53d-4b9c-bce2-fc5bb892097d"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "3bLaeUnUtFY6qhgdbATd3G"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 154}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 50, "_originalHeight": 50, "_id": "f8rCAPRnZGmYLI4dH9FHkt"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": {"__uuid__": "0af7fd26-9deb-4f6b-9524-9492c82693e5"}, "_type": 2, "_segments": 64, "_N$alphaThreshold": 0.7, "_N$inverted": false, "_id": "1dumM4g11KTKz9/jcXN44c"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [{"__id__": 159}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9198M2LkVFraPZuRr3HxIt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "bdd8230f-80b2-4b77-94dc-b699770012bb"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "23Yj/H//hH6qXijNefdovh"}, {"__type__": "cc.Node", "_name": "bankerFlag", "_objFlags": 0, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [{"__id__": 161}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-39, -33, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e9HCbeGCVKQpQuw5+5t7VW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0f9a011-5816-4669-a20d-211f3352195e"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "f78m1Vq0hMDL3YyoR8XrZC"}, {"__type__": "cc.Node", "_name": "winAnimation", "_objFlags": 0, "_parent": {"__id__": 152}, "_children": [], "_active": false, "_components": [{"__id__": 163}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 95.2, "height": 95.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4epCIitFdDrZwVySY07AJ2"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "animation", "_preCacheMode": 0, "_cacheMode": 0, "loop": false, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "animation", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "f8363cc2-81b6-4f61-bc6e-7c93ad88bffa"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": "e3XxKVzdZEkawBVnAES1zI"}, {"__type__": "cc.Node", "_name": "loseScore", "_objFlags": 0, "_parent": {"__id__": 152}, "_children": [{"__id__": 165}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "37KpMK2uRB/rtQ/kntd17f"}, {"__type__": "cc.Node", "_name": "scoreLabel", "_objFlags": 0, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 166}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 161.5, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5dXOPmIBhItY4IgwWzl0g+"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 165}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "-456789", "_N$string": "-456789", "_fontSize": 34, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "b4ce3ea1-9f87-4508-a1cf-05254dd618c7"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "9dKhaf1xRNAawuGnOIDM8W"}, {"__type__": "cc.Node", "_name": "winScore", "_objFlags": 0, "_parent": {"__id__": 152}, "_children": [{"__id__": 168}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bfgZLH3Q9NraduoEyL17Xw"}, {"__type__": "cc.Node", "_name": "scoreLabel", "_objFlags": 0, "_parent": {"__id__": 167}, "_children": [], "_active": true, "_components": [{"__id__": 169}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180.63, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a6vyN0HI9LE69PY1Nl1tKh"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "+1234566", "_N$string": "+1234566", "_fontSize": 34, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d509a073-fc75-4fb0-bcb6-80ebc849806b"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "2404Fpf2BH6Zi/ZGSDAiF1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 152}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": 4, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "4fLJuHX4NAZ4+JK8uv2cgh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 138}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4fd670dc-e24f-4bf5-9a7d-56ee0dc43ada"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "d5FFmazBxDK756JcghT2O8"}, {"__type__": "cc.Node", "_name": "systemPanel", "_objFlags": 0, "_parent": {"__id__": 137}, "_children": [{"__id__": 173}, {"__id__": 175}, {"__id__": 178}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15.741, 1.306, 0, 0, 0, 0, 1, 1.12, 1.12, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5ahQj8KdtP8569qrjmSac6"}, {"__type__": "cc.Node", "_name": "roleSprite", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [], "_active": true, "_components": [{"__id__": 174}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 121, "height": 143}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "15zaUHab1Bl4bT3EQeybQf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "617292c9-86d5-4804-b9a4-b784e509e113"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "9au6sfKIVIYYnofTxdLxhp"}, {"__type__": "cc.Node", "_name": "winScore", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [{"__id__": 176}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "22V3n5BapOGKREqsWf7MRW"}, {"__type__": "cc.Node", "_name": "scoreLabel", "_objFlags": 0, "_parent": {"__id__": 175}, "_children": [], "_active": true, "_components": [{"__id__": 177}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180.63, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2a9m0hqXtMzaU0WMcczpmZ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 176}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "+1234566", "_N$string": "+1234566", "_fontSize": 34, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d509a073-fc75-4fb0-bcb6-80ebc849806b"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "0fkXQpQZFKoJ0+xnyHQgto"}, {"__type__": "cc.Node", "_name": "loseScore", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [{"__id__": 179}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c395BESI5JZIr9xypNN+Pp"}, {"__type__": "cc.Node", "_name": "scoreLabel", "_objFlags": 0, "_parent": {"__id__": 178}, "_children": [], "_active": true, "_components": [{"__id__": 180}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 161.5, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e0Nb7wmIhKi52IByCzF+kf"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "-456789", "_N$string": "-456789", "_fontSize": 34, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "b4ce3ea1-9f87-4508-a1cf-05254dd618c7"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "acq01xIpZGr7oDh3Oxro48"}, {"__type__": "cc.Node", "_name": "myselfNode", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 182}, {"__id__": 195}], "_active": true, "_components": [{"__id__": 215}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-576.201, -304.783, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "19DLXaJodNXrC0rb5tTkAa"}, {"__type__": "cc.Node", "_name": "infoPanel", "_objFlags": 0, "_parent": {"__id__": 181}, "_children": [{"__id__": 183}, {"__id__": 186}], "_active": true, "_components": [{"__id__": 194}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [80, -5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "41Zfyz7UJHpbrT45+RWid1"}, {"__type__": "cc.Node", "_name": "gameIdLabel", "_objFlags": 0, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 184}, {"__id__": 185}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [19.51, 28.497999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "34ukU2lQhP8ZHJyuPyeOtq"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 183}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 22, "_lineHeight": 22, "_enableWrapText": true, "_N$file": {"__uuid__": "f6ffdb7b-a3f4-4251-97f8-dacabd3c386f"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "86TtEP2TdM04Lbc+BMCuno"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 183}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": -2.357999999999997, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "3ekjiWQXNO04qEYFBg3c2h"}, {"__type__": "cc.Node", "_name": "goldPanel", "_objFlags": 0, "_parent": {"__id__": 182}, "_children": [{"__id__": 187}, {"__id__": 190}], "_active": true, "_components": [{"__id__": 192}, {"__id__": 193}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 144, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [82.06, -8.929000000000002, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0bG7v8MHxG27LIq8RRq34g"}, {"__type__": "cc.Node", "_name": "goldValue", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 188}, {"__id__": 189}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 252, "g": 212, "b": 118, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 28.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-31.826999999999998, 0.776, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "91uNnkJ3lLkbUNh5LNCPWN"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 23, "_lineHeight": 23, "_enableWrapText": true, "_N$file": {"__uuid__": "f6ffdb7b-a3f4-4251-97f8-dacabd3c386f"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "66d7kXKPRA7bqSMw9bVmpK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": 40.173, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "31J1HBT9dI0JAE63DvBwyl"}, {"__type__": "cc.Node", "_name": "goldBg", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 191}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-55.574, -0.18, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b9VPcwv3FMwLym1BYZ9Rv/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6f339bfd-ef96-44e9-b5a2-8cbddbc814e8"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "f5F/ryBmFHBqQpeg3BjS7+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 186}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "256adc98-1fcd-40fc-b46e-e44c6a662443"}, "_type": 1, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "25FQbD4UlNmqwcAKER4Z2o"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 186}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 10.060000000000002, "_right": 0, "_top": 27.429000000000002, "_bottom": -14.079999999999998, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 38, "_id": "75/oEHwwNLObhD3BG9RRAT"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 182}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 70, "_right": -81, "_top": 7.5, "_bottom": -7.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "27YmpIVZFBc5VS6cTeaFAB"}, {"__type__": "cc.Node", "_name": "headBg", "_objFlags": 0, "_parent": {"__id__": 181}, "_children": [{"__id__": 196}, {"__id__": 201}, {"__id__": 203}, {"__id__": 205}, {"__id__": 210}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [43, 3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ebmg4b7KlKX5IZouEp1FSz"}, {"__type__": "cc.Node", "_name": "imgMask", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [{"__id__": 197}], "_active": true, "_components": [{"__id__": 200}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "361qb2J4tE8JDeP7tcuMPE"}, {"__type__": "cc.Node", "_name": "headImg", "_objFlags": 0, "_parent": {"__id__": 196}, "_children": [], "_active": true, "_components": [{"__id__": 198}, {"__id__": 199}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "79iD+N3npJyZOziW67IARu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 197}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cda5fa94-d53d-4b9c-bce2-fc5bb892097d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "3f9S27pLtCobVZx3HFd4HD"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 197}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -1, "_right": -1, "_top": -1, "_bottom": -1, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "e2TfNVEl1CV7fIrhkarNpg"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": {"__uuid__": "5244f425-dcd3-46e5-81bc-c633693c54a9"}, "_type": 2, "_segments": 64, "_N$alphaThreshold": 0.7, "_N$inverted": false, "_id": "d4wsIHVAVBlpZdJRQgyoJQ"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 202}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "53VDkGSnlAz5CQuA1mr+9V"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "3abdc62e-c577-4859-82fc-53d4fdf75444"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "6eWdNm97ZO4oVman25NYJQ"}, {"__type__": "cc.Node", "_name": "winAnimation", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": false, "_components": [{"__id__": 204}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 95.2, "height": 95.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.1, 1.1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "feb+0O7YFL/I8etKRYLykU"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 203}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "animation", "_preCacheMode": 0, "_cacheMode": 0, "loop": false, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "animation", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "f8363cc2-81b6-4f61-bc6e-7c93ad88bffa"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": "4cJWQPS1dHgLT9UFE2kAox"}, {"__type__": "cc.Node", "_name": "winScore", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [{"__id__": 206}, {"__id__": 208}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.9, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c4Qc0TdwZH9KTCT82aUYgg"}, {"__type__": "cc.Node", "_name": "winBg", "_objFlags": 0, "_parent": {"__id__": 205}, "_children": [], "_active": true, "_components": [{"__id__": 207}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 201, "height": 59}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "39nl+/nYRH9qfgmSYB0qa3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "echMIwItdEcolX46W0nnfb"}, {"__type__": "cc.Node", "_name": "scoreLabel", "_objFlags": 0, "_parent": {"__id__": 205}, "_children": [], "_active": true, "_components": [{"__id__": 209}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 157.25, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ecagPVHuBFMLx0EGW+oGgx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "+123456", "_N$string": "+123456", "_fontSize": 34, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d509a073-fc75-4fb0-bcb6-80ebc849806b"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "29QBVZ+eBCP7MFLRUJwb+4"}, {"__type__": "cc.Node", "_name": "loseScore", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [{"__id__": 211}, {"__id__": 213}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "23j2pK44FIv7oQCBa+TaDE"}, {"__type__": "cc.Node", "_name": "loseBg", "_objFlags": 0, "_parent": {"__id__": 210}, "_children": [], "_active": true, "_components": [{"__id__": 212}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 185, "height": 59}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "53DK5BR4BLjYA9KVHiqvnS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 211}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "a3uztURRFEzoveaYAb8+1Z"}, {"__type__": "cc.Node", "_name": "scoreLabel", "_objFlags": 0, "_parent": {"__id__": 210}, "_children": [], "_active": true, "_components": [{"__id__": 214}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 184.88, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "622zYiQ/VJXrB5jJQgL/bn"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "-4567894", "_N$string": "-4567894", "_fontSize": 34, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "b4ce3ea1-9f87-4508-a1cf-05254dd618c7"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "68yF0eSW5OxKCR8zlHo+ZA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": 63.79899999999998, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "87cMtZqJ1JeIhlwE6urkBb"}, {"__type__": "cc.Node", "_name": "chipsNode", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1136, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "51fBMV/ytO0JvmkYobztye"}, {"__type__": "cc.Node", "_name": "leftPanel", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 218}], "_active": false, "_components": [{"__id__": 221}, {"__id__": 222}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 438, "height": 297}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-220.5, 13, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2b5gxQVhxCubjJZ5YyD1WR"}, {"__type__": "cc.Node", "_name": "titleImg", "_objFlags": 0, "_parent": {"__id__": 217}, "_children": [], "_active": true, "_components": [{"__id__": 219}, {"__id__": 220}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 49}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [187.774, 104.847, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "25juYWJG1ALrXF8cJR25zl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 218}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0ad7c9f7-0b7a-4239-99dd-161071117d07"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "13xhNHjpNBCq6LUGnw82D8"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 218}, "_enabled": true, "english": {"__uuid__": "0ad7c9f7-0b7a-4239-99dd-161071117d07"}, "india": {"__uuid__": "17128683-da79-4b38-8bb4-cc6d9de57139"}, "_id": "63OXDpbaFChI2aMaZXKhw/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 217}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0f6f2e29-f36a-4631-8f8f-f7d1021b4052"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "22kLBm5klOX43l5FDGr9aj"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 217}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 37, "_left": 0.5, "_right": 0.501171875, "_top": 198.5, "_bottom": 224.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 387, "_originalHeight": 263, "_id": "79eL9DCYVCUriWLe1Kgb6R"}, {"__type__": "cc.Node", "_name": "rightPanel", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 224}], "_active": false, "_components": [{"__id__": 228}, {"__id__": 229}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 374, "height": 298}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, 13, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8d7D5q1iJM3q/4OMlfrEcX"}, {"__type__": "cc.Node", "_name": "titleImg", "_objFlags": 0, "_parent": {"__id__": 223}, "_children": [], "_active": true, "_components": [{"__id__": 225}, {"__id__": 226}, {"__id__": 227}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 49}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.218, 103.15299999999999, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3fEmqvWXBDHp2pb/THcnT3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "3d58e18b-972d-4a51-9a74-7252bffa2eee"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "d99RGu0YJOVKMMA03m+vIz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 53.78200000000001, "_right": 0, "_top": 21.34700000000001, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "0aXuRDBPNNmqvTBNAvQScK"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "english": {"__uuid__": "3d58e18b-972d-4a51-9a74-7252bffa2eee"}, "india": {"__uuid__": "fb8a4fc0-00aa-46b3-92fa-9718a0f7bf54"}, "_id": "0fjcTjh81OrL5pVIsG4KGS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fa3819a0-6323-4f4c-9276-e7f0a1d20fa1"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "e5wY3pIxxFEZWbt1AvjmOD"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 13, "_left": 0.4984375, "_right": 0, "_top": 198, "_bottom": 224, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 263, "_id": "98he8amo1Gcq2LS2u5r8vu"}, {"__type__": "cc.Node", "_name": "cardNode", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 231}, {"__id__": 237}, {"__id__": 243}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "67ATH42ZxJ8ZEAOgQCk4eZ"}, {"__type__": "cc.Node", "_name": "longCard", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [{"__id__": 232}, {"__id__": 234}], "_active": false, "_components": [{"__id__": 236}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 121, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-302.997, 278.806, 0, 0, 0, 0, 1, 0.75, 0.75, 0.66]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ffSc37aohA/YF/iLXW3Dv6"}, {"__type__": "cc.Node", "_name": "valueCard", "_objFlags": 0, "_parent": {"__id__": 231}, "_children": [], "_active": false, "_components": [{"__id__": 233}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3a+cibIVhORoLylHQzp3XI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6c95fd2b-0f0e-4168-878e-3cb72a947c54"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "f2a1c215-343a-42fc-b811-a61ccfcc4690"}, "_id": "515n9/CmVNQqZrFei/PTtn"}, {"__type__": "cc.Node", "_name": "winIcon", "_objFlags": 0, "_parent": {"__id__": 231}, "_children": [], "_active": false, "_components": [{"__id__": 235}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 195, "height": 195}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -14.999999999999966, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b7BOZEQelI/YZX9XWXMC0B"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 234}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5705e1b6-3ad0-49ba-af4f-39334f0a0cc3"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "f5XDVfKllCEK3WT3A+B/PX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "e678be86-f5cb-4d9f-9311-941894403d50"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "f2a1c215-343a-42fc-b811-a61ccfcc4690"}, "_id": "a1at6h61pJALsXTDfxurnj"}, {"__type__": "cc.Node", "_name": "huCard", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [{"__id__": 238}, {"__id__": 240}], "_active": false, "_components": [{"__id__": 242}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 121, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [303.997, 279.748, 0, 0, 0, 0, 1, 0.75, 0.75, 0.66]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "47O3zr8ptF0rPdSXVdHMZr"}, {"__type__": "cc.Node", "_name": "valueCard", "_objFlags": 0, "_parent": {"__id__": 237}, "_children": [], "_active": false, "_components": [{"__id__": 239}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6fLR3ovdhDmJunYXfj1CC3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 238}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6c95fd2b-0f0e-4168-878e-3cb72a947c54"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "f2a1c215-343a-42fc-b811-a61ccfcc4690"}, "_id": "63fNmODDpBvZQWr/l3+Dmb"}, {"__type__": "cc.Node", "_name": "winIcon", "_objFlags": 0, "_parent": {"__id__": 237}, "_children": [], "_active": false, "_components": [{"__id__": 241}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 195, "height": 195}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2fbzT+B3hKMIuwTFMQtDJH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5705e1b6-3ad0-49ba-af4f-39334f0a0cc3"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "38xhu/uvVPbZ7ijwaxcksx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 237}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "46ae982a-26b5-4867-882c-785b248f9145"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "f2a1c215-343a-42fc-b811-a61ccfcc4690"}, "_id": "ccgK5R9ZhLVKKG8tbPeFU4"}, {"__type__": "cc.Node", "_name": "heCard", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [{"__id__": 244}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 121, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -21, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e6rbg+dEhKsLJKMZBvfGYx"}, {"__type__": "cc.Node", "_name": "winIcon", "_objFlags": 0, "_parent": {"__id__": 243}, "_children": [], "_active": false, "_components": [{"__id__": 245}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 195, "height": 195}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "69nsNXDJlJZJ9H0pOaGUbu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 244}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d185fdd2-2ec5-4386-aa7e-56867e854426"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "e7FEesPL5AIqWw/LapT8GP"}, {"__type__": "cc.Node", "_name": "vsPanel", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [], "_active": false, "_components": [{"__id__": 247}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1136, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 12, 0, 0, 0, 0, 1, 1.08, 1.08, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d6vJ/mFNVFo4k3b3d8bqYG"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "animation", "_preCacheMode": 0, "_cacheMode": 0, "loop": false, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "animation", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "52289a24-241a-4098-9348-db4b0ac99854"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": "01lrWOqrdDXI5OGX/cXUNL"}, {"__type__": "cc.Node", "_name": "statePanel", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 249}, {"__id__": 252}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3aXsVWjVRM0rf52S7Aj0HN"}, {"__type__": "cc.Node", "_name": "img_start", "_objFlags": 0, "_parent": {"__id__": 248}, "_children": [], "_active": false, "_components": [{"__id__": 250}, {"__id__": 251}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 425, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "44f7bITyFAhaZf5Djl6Bp9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee594901-9eb7-48a1-bf5e-bd09acceebc7"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "b16qZK99ZNLqGUlBHBHguo"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "english": {"__uuid__": "ee594901-9eb7-48a1-bf5e-bd09acceebc7"}, "india": {"__uuid__": "fcb3189e-6950-4479-8f9c-17e8f5cc2b35"}, "_id": "c8qje+ZH1Ie5e+1l3r0bQp"}, {"__type__": "cc.Node", "_name": "img_stop", "_objFlags": 0, "_parent": {"__id__": 248}, "_children": [], "_active": false, "_components": [{"__id__": 253}, {"__id__": 254}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 425, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "35PP/AZ+NO+LuWv+8WKb53"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 252}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4c2dc0cc-3fb4-4fbc-a5b4-243930899cdd"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "c4RbHTobRF2b+RqyoT8URB"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 252}, "_enabled": true, "english": {"__uuid__": "4c2dc0cc-3fb4-4fbc-a5b4-243930899cdd"}, "india": {"__uuid__": "f980e55a-20de-436a-b17a-0d9fd73a5606"}, "_id": "batzv00nxEjpWMRQ66Hw0z"}, {"__type__": "cc.Node", "_name": "applyBankerTips", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 256}], "_active": false, "_components": [{"__id__": 258}, {"__id__": 259}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 540, "height": 540}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 33.724, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6aH8/Qc+VFL6482mjivIGJ"}, {"__type__": "cc.Node", "_name": "name<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 255}, "_children": [], "_active": true, "_components": [{"__id__": 257}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 97, "g": 13, "b": 3, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "11RLtdqxBC1638dIZrMYi8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 256}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 36, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "f6ffdb7b-a3f4-4251-97f8-dacabd3c386f"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "94sGR/5nhDZrdVMvUGSS84"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 255}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b41675d3-aad3-4e29-b5cb-8a2d27e51307"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "5bUy2pg21OaYTeuYRLQ1LO"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 255}, "_enabled": true, "english": {"__uuid__": "b41675d3-aad3-4e29-b5cb-8a2d27e51307"}, "india": {"__uuid__": "*************-4374-9dcc-52387c3eb96e"}, "_id": "cfbmlLmPdFQ5SS/Mk1N3Uc"}, {"__type__": "cc.Node", "_name": "winSoundNode", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1136, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d8hQlp4v5Mso/Pq140Cgrf"}, {"__type__": "cc.Node", "_name": "sendCardAniNode", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "38HeF03zhKmZSfwA16esKl"}, {"__type__": "cc.Node", "_name": "bottomLayer", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 263}, {"__id__": 314}], "_active": true, "_components": [{"__id__": 325}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1294, "height": 126}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -296, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f21H1x5yNG6IREG0lGzKXQ"}, {"__type__": "cc.Node", "_name": "betPanel", "_objFlags": 0, "_parent": {"__id__": 262}, "_children": [{"__id__": 264}, {"__id__": 272}, {"__id__": 280}, {"__id__": 288}, {"__id__": 296}, {"__id__": 304}], "_active": true, "_components": [{"__id__": 312}, {"__id__": 313}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-30, 4.726, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e355dqh3JPnq8MsOxqE1Me"}, {"__type__": "cc.Node", "_name": "betChip0", "_objFlags": 0, "_parent": {"__id__": 263}, "_children": [{"__id__": 265}, {"__id__": 267}], "_active": true, "_components": [{"__id__": 270}, {"__id__": 271}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-229.375, 0, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "35c0BTAkZCOrjJE5tThbAf"}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 266}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "98tJ7b61VEE4MOp8iJl2Tk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "684934c4-7d52-4769-8849-eac586e377e2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "faZ5jpXPRMQZURU6EKYFGu"}, {"__type__": "cc.Node", "_name": "unSelect", "_objFlags": 0, "_parent": {"__id__": 264}, "_children": [], "_active": false, "_components": [{"__id__": 268}, {"__id__": 269}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "78C5xL5KpKVqH9BE4bzqJI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 267}, "_enabled": true, "_materials": [{"__uuid__": "3a7bb79f-32fd-422e-ada2-96f518fed422"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "620f7e15-5fae-4d33-a358-9875e3389b8b"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "4eCxaIaspG+4TLkTsSu6AM"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 267}, "_enabled": true, "_id": "835wbwJv9C9KOk12XX/FVU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "620f7e15-5fae-4d33-a358-9875e3389b8b"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "69wUfOiL9IhKw5kgw0xCTb"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 264}, "_id": "f4+WOap21JeKJWPb8BY+gd"}, {"__type__": "cc.Node", "_name": "betChip1", "_objFlags": 0, "_parent": {"__id__": 263}, "_children": [{"__id__": 273}, {"__id__": 275}], "_active": true, "_components": [{"__id__": 278}, {"__id__": 279}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-91.125, 0, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "abkcDyN8ZMkqo4kjRbdsBh"}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "_parent": {"__id__": 272}, "_children": [], "_active": false, "_components": [{"__id__": 274}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "66E6pZW1RBTrAnC5Qbz8nb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 273}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "684934c4-7d52-4769-8849-eac586e377e2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "20cp58/sxP+ImeL4CfRiiS"}, {"__type__": "cc.Node", "_name": "unSelect", "_objFlags": 0, "_parent": {"__id__": 272}, "_children": [], "_active": false, "_components": [{"__id__": 276}, {"__id__": 277}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e1RcwzeSdD94dNq7ue4plT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 275}, "_enabled": true, "_materials": [{"__uuid__": "3a7bb79f-32fd-422e-ada2-96f518fed422"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5cbfc38a-c39a-4841-8dfd-c068f2d8e72e"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "e8oXNPMj1O3ZuWThjQOg94"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 275}, "_enabled": true, "_id": "585jGrAUJNSKeQeqLkoFV6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 272}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5cbfc38a-c39a-4841-8dfd-c068f2d8e72e"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "3ff+jkos9BRZtWYQuqrBW9"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 272}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 272}, "_id": "66+i4nru5Or4vTyXz2Nq77"}, {"__type__": "cc.Node", "_name": "betChip2", "_objFlags": 0, "_parent": {"__id__": 263}, "_children": [{"__id__": 281}, {"__id__": 283}], "_active": true, "_components": [{"__id__": 286}, {"__id__": 287}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [47.125, 0, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b3wd2Gbj1Kl7S25VyR1Cy7"}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "_parent": {"__id__": 280}, "_children": [], "_active": false, "_components": [{"__id__": 282}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cbpEBAUktJJL3ywsnMB/gI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 281}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "684934c4-7d52-4769-8849-eac586e377e2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "2fR8PFFGFIZJME+0CZ5kG+"}, {"__type__": "cc.Node", "_name": "unSelect", "_objFlags": 0, "_parent": {"__id__": 280}, "_children": [], "_active": false, "_components": [{"__id__": 284}, {"__id__": 285}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e7w1xj2iRGmphvoz6XMrVv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 283}, "_enabled": true, "_materials": [{"__uuid__": "3a7bb79f-32fd-422e-ada2-96f518fed422"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9594b30d-05c6-4b5b-891c-db7abf9e01ce"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "73zb4zb+hD+YJTvilT7jVP"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 283}, "_enabled": true, "_id": "32dCvhZGJEAYlAN6nfROWg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 280}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9594b30d-05c6-4b5b-891c-db7abf9e01ce"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "8bqo6SlgxOObZ4pXsGlMBk"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 280}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 280}, "_id": "72SNh6yttLR49MfTyYD1Wr"}, {"__type__": "cc.Node", "_name": "betChip3", "_objFlags": 0, "_parent": {"__id__": 263}, "_children": [{"__id__": 289}, {"__id__": 291}], "_active": true, "_components": [{"__id__": 294}, {"__id__": 295}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185.375, 0, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "710KsJYrZC5K6uEj2ZVs0Z"}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "_parent": {"__id__": 288}, "_children": [], "_active": false, "_components": [{"__id__": 290}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "70IGk233dJDrzUtpi0Nuo4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 289}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "684934c4-7d52-4769-8849-eac586e377e2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "b8Zkd3ICZFwbX4y9xsDtpb"}, {"__type__": "cc.Node", "_name": "unSelect", "_objFlags": 0, "_parent": {"__id__": 288}, "_children": [], "_active": false, "_components": [{"__id__": 292}, {"__id__": 293}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3d3bU7oAJIvZQ49mFn/TZw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 291}, "_enabled": true, "_materials": [{"__uuid__": "3a7bb79f-32fd-422e-ada2-96f518fed422"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4f23d9f4-e145-41fc-82c2-cb84478769be"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "f38Ak+8shDabOPvqFZMBdT"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 291}, "_enabled": true, "_id": "85aAK7yoJGkKPDKjq1BEIg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4f23d9f4-e145-41fc-82c2-cb84478769be"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "d9g8PD1rRPJ5bttyWWAvKE"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 288}, "_id": "b7Ycy9EG5DIbMH8B2PecVn"}, {"__type__": "cc.Node", "_name": "betChip4", "_objFlags": 0, "_parent": {"__id__": 263}, "_children": [{"__id__": 297}, {"__id__": 299}], "_active": true, "_components": [{"__id__": 302}, {"__id__": 303}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [323.625, 0, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "eag30XBEBB9Kep/ITSxGp5"}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "_parent": {"__id__": 296}, "_children": [], "_active": false, "_components": [{"__id__": 298}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "28bnJdCCVB8ImKttVXxuwm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 297}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "684934c4-7d52-4769-8849-eac586e377e2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "31M3MmRztH+L7XaQ3YtyAq"}, {"__type__": "cc.Node", "_name": "unSelect", "_objFlags": 0, "_parent": {"__id__": 296}, "_children": [], "_active": false, "_components": [{"__id__": 300}, {"__id__": 301}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7ckE2dK/pHwbFZ3WkxI7lU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 299}, "_enabled": true, "_materials": [{"__uuid__": "3a7bb79f-32fd-422e-ada2-96f518fed422"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ce15af2b-a430-4c54-a0c2-fbe949d8e2a5"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "7dSph4IpxPFrBx3Fj7yY0l"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 299}, "_enabled": true, "_id": "073veNxFpPZLRgqtxjaslT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 296}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ce15af2b-a430-4c54-a0c2-fbe949d8e2a5"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "9ava9F3zdHGaGytXxOR4E+"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 296}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 296}, "_id": "ca2Dpao21OIbe8kFpWblV2"}, {"__type__": "cc.Node", "_name": "betChip5", "_objFlags": 0, "_parent": {"__id__": 263}, "_children": [{"__id__": 305}, {"__id__": 307}], "_active": true, "_components": [{"__id__": 310}, {"__id__": 311}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [461.875, 0, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "02SqvjSDRD7bVEpmAEwYk4"}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "_parent": {"__id__": 304}, "_children": [], "_active": false, "_components": [{"__id__": 306}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b4TMAoGEBHi5K2Et/b7LiJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 305}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "684934c4-7d52-4769-8849-eac586e377e2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "beDwsi/etAp7hPfF/UGPEn"}, {"__type__": "cc.Node", "_name": "unSelect", "_objFlags": 0, "_parent": {"__id__": 304}, "_children": [], "_active": false, "_components": [{"__id__": 308}, {"__id__": 309}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "41/BJkVsxHWbBo4vQ1N1ON"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 307}, "_enabled": true, "_materials": [{"__uuid__": "3a7bb79f-32fd-422e-ada2-96f518fed422"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b03719e7-cac0-4c44-877e-e8c349d74694"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "72tgbVHOdNzr3WEhe9MJYh"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 307}, "_enabled": true, "_id": "ceoj+zF4ZOpJKF+hZAjwcJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 304}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b03719e7-cac0-4c44-877e-e8c349d74694"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "01xM7TYU5Og7SCZI392Ehq"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 304}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 304}, "_id": "9bH9UMiABFepUbwERvgDn1"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 263}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 580, "height": 0}, "_resize": 0, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 17, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": true, "_id": "dcVgS3CIBG4JjL2PnFr3XT"}, {"__type__": "d8b3d8vV2xKtb+yu42FYeK8", "_name": "", "_objFlags": 0, "node": {"__id__": 263}, "_enabled": true, "btnChips": [{"__id__": 264}, {"__id__": 272}, {"__id__": 280}, {"__id__": 288}, {"__id__": 296}, {"__id__": 304}], "_id": "41mS/zw2xEYrsmwbQk5RJb"}, {"__type__": "cc.Node", "_name": "rightPanel", "_objFlags": 0, "_parent": {"__id__": 262}, "_children": [{"__id__": 315}, {"__id__": 319}, {"__id__": 320}], "_active": true, "_components": [{"__id__": 323}, {"__id__": 324}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [490.452, 26, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5e9u+jA19LjpqIkyQfY4SP"}, {"__type__": "cc.Node", "_name": "onlineButton", "_objFlags": 0, "_parent": {"__id__": 314}, "_children": [{"__id__": 316}], "_active": true, "_components": [{"__id__": 318}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [58.958, -32.327, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "510deXTLBMYJLCb7iLDWl5"}, {"__type__": "cc.Node", "_name": "lbOnlinePlayers", "_objFlags": 0, "_parent": {"__id__": 315}, "_children": [], "_active": true, "_components": [{"__id__": 317}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 232, "b": 185, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 9.87, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e0UgX5IC1IobumS+6RwhL/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 316}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 20, "_lineHeight": 20, "_enableWrapText": true, "_N$file": {"__uuid__": "f6ffdb7b-a3f4-4251-97f8-dacabd3c386f"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "5dy2Mjs8BE1JbhHrB+cB9z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 315}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a3a3ca65-fc2f-49b5-b988-702915dbc1a5"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "a4ATW3fJJN4aCWVbWvlESs"}, {"__type__": "cc.Node", "_name": "onlineBetNode", "_objFlags": 0, "_parent": {"__id__": 314}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [61.968, -32.371, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a9AyyGIdNCIbDlFp+/6bRa"}, {"__type__": "cc.Node", "_name": "onClickOther", "_objFlags": 0, "_parent": {"__id__": 314}, "_children": [], "_active": true, "_components": [{"__id__": 321}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [61.968, -32.371, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c9EHCCTI9M1pvj82shbuq0"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 320}, "_enabled": true, "clickEvents": [{"__id__": 322}], "pressedScale": 0.95, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "f0XLABLWZMY6+QnOBoS5re"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "showOnlineUser", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 314}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 32, "_left": 0, "_right": 156.548, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "46Ox+M325KR5rxk+wsUJCl"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 314}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_resize": 0, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 30, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "3btWrMj3VI5KcXUzcyanOd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 262}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "10128d1a-8834-49c3-bd97-b70092e921cc"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "9cTUihCp5Frbxw+iJqu73p"}, {"__type__": "cc.Node", "_name": "alterLayer", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 327}, {"__id__": 328}, {"__id__": 343}, {"__id__": 350}, {"__id__": 357}, {"__id__": 404}], "_active": true, "_components": [{"__id__": 412}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6b/OroKBdLQ68kBkVTUtOp"}, {"__type__": "cc.Node", "_name": "gameEmoji", "_objFlags": 0, "_parent": {"__id__": 326}, "_children": [], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4bNrtEZlxPkZgVGV9AyTuG"}, {"__type__": "cc.Node", "_name": "tipWaitPanel", "_objFlags": 0, "_parent": {"__id__": 326}, "_children": [{"__id__": 329}], "_active": false, "_components": [{"__id__": 341}, {"__id__": 342}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1569, "height": 153}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cbQB4k3iZNKocuNzyZnClk"}, {"__type__": "cc.Node", "_name": "waitL<PERSON>ount", "_objFlags": 0, "_parent": {"__id__": 328}, "_children": [{"__id__": 330}, {"__id__": 333}], "_active": true, "_components": [{"__id__": 340}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 798, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "90JHoyTClO/4aPacu5Lssl"}, {"__type__": "cc.Node", "_name": "waitBetBg", "_objFlags": 0, "_parent": {"__id__": 329}, "_children": [], "_active": true, "_components": [{"__id__": 331}, {"__id__": 332}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 715, "height": 65}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-41.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "85WGbTNPBFLK4++zP5047W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 330}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4f73ec48-d650-467a-8338-f4e9c52b637f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "41tbsT62VPYb3JQUMohLMA"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 330}, "_enabled": true, "english": {"__uuid__": "4f73ec48-d650-467a-8338-f4e9c52b637f"}, "india": {"__uuid__": "e5423a55-41fa-4ab5-81e1-354904384d2f"}, "_id": "94KRdLWfhE4YqMc2lldpnI"}, {"__type__": "cc.Node", "_name": "dotNode", "_objFlags": 0, "_parent": {"__id__": 329}, "_children": [{"__id__": 334}, {"__id__": 336}, {"__id__": 338}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 113, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [342.5, -10, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4bAHDmxaROHYdtFjNDK0VB"}, {"__type__": "cc.Node", "_name": "waitDot1", "_objFlags": 0, "_parent": {"__id__": 333}, "_children": [], "_active": true, "_components": [{"__id__": 335}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 41, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-36, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0cxaYeEDZE9LocyARPYub/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 334}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ad1ff9cb-ed9c-4836-9d49-6f431e0340fe"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "27PsCqENtNNq8jiBHobTjl"}, {"__type__": "cc.Node", "_name": "waitDot2", "_objFlags": 0, "_parent": {"__id__": 333}, "_children": [], "_active": true, "_components": [{"__id__": 337}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 41, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d7YZRlJ2hDqIHDCW6UoQZq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 336}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ad1ff9cb-ed9c-4836-9d49-6f431e0340fe"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "48GCNq9oNHHLqije0UsJ8x"}, {"__type__": "cc.Node", "_name": "waitDot3", "_objFlags": 0, "_parent": {"__id__": 333}, "_children": [], "_active": true, "_components": [{"__id__": 339}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 41, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [36, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "aazIpxtodOaJ2qvjANKjWS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 338}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ad1ff9cb-ed9c-4836-9d49-6f431e0340fe"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "6324qhdiVCOatB+VARlsb7"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 329}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 798, "height": 150}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": -30, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "7aD+zhhZRBPKaIiWUaEcMu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 328}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "85d21802-93eb-4c1e-8645-3b3c777b13fa"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "34iiuG6mZMP4wJ3UqWVXYS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 328}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -144.5, "_right": -144.5, "_top": 283.5, "_bottom": 283.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "8cs06Eu1xP547FWFDwovUJ"}, {"__type__": "cc.Node", "_name": "onlinePanel", "_objFlags": 0, "_parent": {"__id__": 326}, "_children": [{"__id__": 344}], "_active": false, "_components": [{"__id__": 349}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d68lwOh29JFqwfnRno4j6b"}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [], "_active": true, "_components": [{"__id__": 345}, {"__id__": 346}, {"__id__": 347}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1b030nBx9FtLjHLb3KK7eU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 344}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b6/xDIb9lLfJp2ln0QQEFx"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 344}, "_enabled": true, "_id": "76ItjZkvNEwqVMCUWxARc4"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 344}, "_enabled": true, "clickEvents": [{"__id__": 348}], "pressedScale": 1.03, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "9a/KSH1MFIo6hLOTjXKyOk"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "hideOnlineUser", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 343}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "29YFZTl8ZNnrLP59F8385M"}, {"__type__": "cc.Node", "_name": "onHelpPanel", "_objFlags": 0, "_parent": {"__id__": 326}, "_children": [{"__id__": 351}], "_active": false, "_components": [{"__id__": 356}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "127qjHtL1Lx7YEAN1FwaSp"}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 350}, "_children": [], "_active": true, "_components": [{"__id__": 352}, {"__id__": 353}, {"__id__": 354}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "29ko5HlRNORKlgmOElldBF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 351}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "986oQ9nyNA+pzNCfopnWk+"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 351}, "_enabled": true, "_id": "03TBiRQM5HLaHJcp611/tA"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 351}, "_enabled": true, "clickEvents": [{"__id__": 355}], "pressedScale": 1.03, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "8aN1w7smdG0a5KXgMNCunw"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 350}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "00+avhWbRGioGoiCwh9+6i"}, {"__type__": "cc.Node", "_name": "setMainPanel", "_objFlags": 0, "_parent": {"__id__": 326}, "_children": [{"__id__": 358}, {"__id__": 364}], "_active": false, "_components": [{"__id__": 403}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "49px8SKsNAeohHN248MJam"}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 357}, "_children": [], "_active": true, "_components": [{"__id__": 359}, {"__id__": 361}, {"__id__": 362}, {"__id__": 363}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2500, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cc2mMWPjlHC6ozG978xNxm"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 358}, "_enabled": true, "clickEvents": [{"__id__": 360}], "pressedScale": 0.95, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "18NijvGfFLzoLds25rwfy+"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "hide<PERSON>ain<PERSON>ser", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 358}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b41cBfztxKr7azo1jgAqlw"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 358}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -610, "_right": -610, "_top": -180, "_bottom": -180, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a5VC5fNJROmqxFZt+HCYlC"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 358}, "_enabled": true, "_id": "fah7FgvKdNOarz5WAiiF0P"}, {"__type__": "cc.Node", "_name": "mainBg", "_objFlags": 0, "_parent": {"__id__": 357}, "_children": [{"__id__": 365}, {"__id__": 371}, {"__id__": 376}, {"__id__": 384}, {"__id__": 386}, {"__id__": 388}, {"__id__": 390}], "_active": true, "_components": [{"__id__": 401}, {"__id__": 402}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 362, "height": 493}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "652yC1TKNAeL6Pdy/5GkJF"}, {"__type__": "cc.Node", "_name": "titleImg", "_objFlags": 0, "_parent": {"__id__": 364}, "_children": [{"__id__": 366}], "_active": true, "_components": [{"__id__": 369}, {"__id__": 370}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 268, "height": 65}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 221, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2bZAKYQgdL1Y6buaA5VAu7"}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "_parent": {"__id__": 365}, "_children": [], "_active": true, "_components": [{"__id__": 367}, {"__id__": 368}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "297+XZ7+9D7pz9VqFc0sWT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 366}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f5139092-8fed-44ec-90a8-0a41e9ee4fa6"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "56AFfB9cFE74TaUnw1j5cn"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 366}, "_enabled": true, "english": {"__uuid__": "f5139092-8fed-44ec-90a8-0a41e9ee4fa6"}, "india": {"__uuid__": "39241c0d-eb9e-46bd-a43c-628e3c80810d"}, "_id": "eev14MyJdLB535C9pLaQPv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 365}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "55ad12c0-55a9-4b09-b039-598f25c4e02f"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "1ctnci6J5MzaDJGQuMlMFZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 365}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": -7, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "e3IJ+NTC9CFJYA3XUDDeh2"}, {"__type__": "cc.Node", "_name": "closeButton", "_objFlags": 0, "_parent": {"__id__": 364}, "_children": [], "_active": true, "_components": [{"__id__": 372}, {"__id__": 373}, {"__id__": 374}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 49, "height": 49}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [151.585, 220.34, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "61q04+Z71FJIHSzEd053c3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1ed1967e-fbb7-476d-a5e2-592be7361b97"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "1cvqaa5tNAroThPSt/4iFv"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 4.914999999999992, "_top": 1.6599999999999966, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a0Mj5BzvVFdZNxTB2sSU0X"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "clickEvents": [{"__id__": 375}], "pressedScale": 0.95, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "8eCJodmyBJwY+FT2CCQR8j"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "hide<PERSON>ain<PERSON>ser", "customEventData": ""}, {"__type__": "cc.Node", "_name": "scrollView", "_objFlags": 0, "_parent": {"__id__": 364}, "_children": [{"__id__": 377}], "_active": true, "_components": [{"__id__": 383}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326, "height": 264}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1, 50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8eiPbOvtJOZb2i639O3i5w"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 376}, "_children": [{"__id__": 378}], "_active": true, "_components": [{"__id__": 381}, {"__id__": 382}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326, "height": 264}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "68oXpWmhBLVbN2hQceuimT"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 377}, "_children": [], "_active": true, "_components": [{"__id__": 379}, {"__id__": 380}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "909LlQ14pPBLsjOAl0YppK"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 378}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 326, "height": 250}, "_resize": 0, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": -3, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "f0ZAYq4jZIJ4JmYc5ptQ+3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 378}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 40, "_left": 0, "_right": 0, "_top": 100, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 0, "_id": "05P9mzQNtC/qwofwdRRxg1"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 377}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "41uX1oRRNET433Z6FebbiZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 377}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": "94omJYUkZJMaYoPOvhQ/4o"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 376}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 378}, "content": {"__id__": 378}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": "b183XjINFHs68o50A9wWHI"}, {"__type__": "cc.Node", "_name": "descRich1", "_objFlags": 0, "_parent": {"__id__": 364}, "_children": [], "_active": true, "_components": [{"__id__": 385}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 184, "g": 168, "b": 134, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-158, -105, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5dkVNBkLBJo71Bmt+vzgn4"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 384}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": false, "_N$string": "", "_N$horizontalAlign": 0, "_N$fontSize": 16, "_N$font": {"__uuid__": "f6ffdb7b-a3f4-4251-97f8-dacabd3c386f"}, "_N$cacheMode": 0, "_N$maxWidth": 320, "_N$lineHeight": 20, "_N$imageAtlas": null, "_N$handleTouchEvent": true, "_id": "ecr2ljXZBEX5HjJK7rZUFe"}, {"__type__": "cc.Node", "_name": "descRich2", "_objFlags": 0, "_parent": {"__id__": 364}, "_children": [], "_active": true, "_components": [{"__id__": 387}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 195, "g": 108, "b": 20, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [162, -105, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a0Wtm6tsRHm6WoZ7p1ggrq"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 386}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": false, "_N$string": "", "_N$horizontalAlign": 2, "_N$fontSize": 16, "_N$font": {"__uuid__": "f6ffdb7b-a3f4-4251-97f8-dacabd3c386f"}, "_N$cacheMode": 0, "_N$maxWidth": 320, "_N$lineHeight": 20, "_N$imageAtlas": null, "_N$handleTouchEvent": true, "_id": "1d2WQ8q5JIwLVe7MwrOoPn"}, {"__type__": "cc.Node", "_name": "descRich3", "_objFlags": 0, "_parent": {"__id__": 364}, "_children": [], "_active": true, "_components": [{"__id__": 389}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 184, "g": 168, "b": 134, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-157.843, -134, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f2XUBiYulFHaEDUrqjdE99"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 388}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": false, "_N$string": "", "_N$horizontalAlign": 0, "_N$fontSize": 16, "_N$font": {"__uuid__": "f6ffdb7b-a3f4-4251-97f8-dacabd3c386f"}, "_N$cacheMode": 0, "_N$maxWidth": 320, "_N$lineHeight": 20, "_N$imageAtlas": null, "_N$handleTouchEvent": true, "_id": "5doOwjtINB34QYeIfbLtQe"}, {"__type__": "cc.Node", "_name": "okButton", "_objFlags": 0, "_parent": {"__id__": 364}, "_children": [{"__id__": 391}, {"__id__": 394}], "_active": true, "_components": [{"__id__": 397}, {"__id__": 398}, {"__id__": 399}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 203, "height": 74}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -194, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "68qgpJiUBOdp1SkhDdoHM2"}, {"__type__": "cc.Node", "_name": "upBankerText", "_objFlags": 0, "_parent": {"__id__": 390}, "_children": [], "_active": true, "_components": [{"__id__": 392}, {"__id__": 393}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f5QbY4hh5Mh5NqwoUanUuv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "808abe28-713a-492b-bcc7-d11328758c8e"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "3fuemhv+VOMrRw96N/aIJ0"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "english": {"__uuid__": "808abe28-713a-492b-bcc7-d11328758c8e"}, "india": {"__uuid__": "16ca91c8-d641-47de-a09c-67818f91be68"}, "_id": "c2AXKeFO5MI4cgtZU/PL7G"}, {"__type__": "cc.Node", "_name": "endBankerText", "_objFlags": 0, "_parent": {"__id__": 390}, "_children": [], "_active": false, "_components": [{"__id__": 395}, {"__id__": 396}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 182, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "27/KAGP0xB8ZDg066Z3N3k"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 394}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8bf6304c-d47a-457d-aac6-bf333b0768f0"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "cbq7Ef32ZLrK/16ADpRqST"}, {"__type__": "544ad6Zfs5LIpLMPsTcjdEQ", "_name": "", "_objFlags": 0, "node": {"__id__": 394}, "_enabled": true, "english": {"__uuid__": "8bf6304c-d47a-457d-aac6-bf333b0768f0"}, "india": {"__uuid__": "61d42521-e4aa-497a-b9de-4cd2f4e263bd"}, "_id": "7ej4LHWppHqpwNLAYzQMYT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 390}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dfd51b3-bb66-417a-a09a-2cbb318894bc"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "edviC7OXBJ74Q3pOlX0Klr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 390}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 15.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "1blie7x6xHYrjuRXtcdmQi"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 390}, "_enabled": true, "clickEvents": [{"__id__": 400}], "pressedScale": 0.95, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "f3n8xdOqBCyYIzInIT96VZ"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "onClickToBankerBtn", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 364}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d837100c-06d0-4c65-a755-667308f811d7"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "_id": "40tpvfjhJO94XAMFwzFaTI"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 364}, "_enabled": true, "_id": "32F8MpswBGOrWEeL59oWlK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 357}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": "4ekE6DupNE45tFYboKNmHW"}, {"__type__": "cc.Node", "_name": "historyPanel", "_objFlags": 0, "_parent": {"__id__": 326}, "_children": [{"__id__": 405}], "_active": false, "_components": [{"__id__": 411}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "10my+tJdpFha/75MeFoW+G"}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 404}, "_children": [], "_active": true, "_components": [{"__id__": 406}, {"__id__": 407}, {"__id__": 408}, {"__id__": 409}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "00UZWbVgdHoYpir3i+lBF9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 405}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d6rxlTRxxCH6qb2j75Btc6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 405}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "53lCPv9cxLqp0q70zPwcC4"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 405}, "_enabled": true, "_id": "59sgKdtlpM1pEq6ols/hiW"}, {"__type__": "c620bQBh0hKIYFan2uhoOpW", "_name": "", "_objFlags": 0, "node": {"__id__": 405}, "_enabled": true, "clickEvents": [{"__id__": 410}], "pressedScale": 1.03, "clickSound": null, "enableTouch": true, "isPlaySound": false, "stopPropagation": false, "_id": "84rwP0Wg1M4LpZJB+RsRAQ"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "bfe0fxFLvhNorz8mHEAayG4", "handler": "hideHistory", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 404}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "c7TxmhsEJIlqYyPgIFcaCu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 326}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "10C/iS+TBOKbF3jvOA8h6g"}, {"__type__": "cc.Node", "_name": "bankerAlterUserInfo", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 414}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 275, "height": 143}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 231.**************, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bc11s87NdEBLfPtTq0VWvj"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 413}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 552, "_right": 0, "_top": 57.**************, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "43TyK9BDlJAaQjyg6rLuy3"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_fitWidth": false, "_fitHeight": true, "_id": "59Cd0ovbdF4byw5sbjJDx7"}, {"__type__": "bfe0fxFLvhNorz8mHEAayG4", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "mainLayer": {"__id__": 45}, "bottomLayer": {"__id__": 262}, "alterLayer": {"__id__": 326}, "userPrefab": {"__uuid__": "82fc32fa-aebc-4d1c-93d0-2a6a23b8dac6"}, "recordItem": {"__uuid__": "906b712d-de2b-45e4-8b89-b8e7a9897f24"}, "chipNode": {"__uuid__": "2460e337-cf1d-4229-b601-38daca072efa"}, "historyList": {"__id__": 57}, "cardAtlas": {"__uuid__": "f2a1c215-343a-42fc-b811-a61ccfcc4690"}, "mainUiAtlas": {"__uuid__": "83bee91a-a1f8-462c-97aa-67c9a901ffb1"}, "onLinePlayersLayer": {"__uuid__": "99836bad-e407-4fff-a16c-34c044ef7203"}, "zoushiLayer": {"__uuid__": "98058b62-23fa-4c67-9e5e-012110e12554"}, "helpLayer": {"__uuid__": "041f00be-406d-4890-b443-9da817bf5ccf"}, "userMainNode": {"__uuid__": "026c92e7-32d2-4ad3-8527-0d2bee20b3e0"}, "gameEmojiNode": {"__id__": 327}, "_id": "04qyu2FMhIEK8qkpDERVK4"}, {"__type__": "2393fpEFiFHrZnOF16/cF4+", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": "cang4o8FNFU7CCD75pf008"}, {"__type__": "ec77fzcpslEko8NzPIzlbYt", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": "2deGAZEhZCTraUt+MXUWjC"}, {"__type__": "720a2Hx4qhNWqm6zkCBSPZb", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": "05vATay89P9JLF/WZMfjbR"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1136, "_originalHeight": 640, "_id": "1eyPVI6NdN+JYZjX+sT+vL"}, {"__type__": "cc.SafeArea", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": "18k2TS8j9Gm52yh2NJ29rm"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": "88tYfE2C9I1ICuu2pKEG6k"}]