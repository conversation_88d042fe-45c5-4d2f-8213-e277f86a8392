const { ccclass, property,disallowMultiple,menu } = cc._decorator;

import GameCore from "../../../../script/frame/model/GameCore";
import LHGameView from "../view/LHGameView";
import Common from "../../../../script/frame/common/Common";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import EventManager from "../../../../script/frame/manager/EventManager";
import { GameTextTips, LHD, updateGameTextTips } from "./LHDefine";
import { TextTips } from "../../../../script/frame/common/Language";
import { GameEvent } from "../../../../script/frame/common/Define";
import LHChipPoolAssisCom from "./LHChipPoolAssisCom";

@ccclass
@disallowMultiple()
@menu('longhu/LHGameCore')
export default class LHGameCore extends GameCore {
    /** 游戏视图对象 */
    private _gameView: LHGameView = null;

    model = {
        isBetMax: false,
        bankerNeed: 2000,
        betMoneyValue: 10,
        NotBetCount: 0,
        lastRefreshHistoryTime: 0,
        lastHadBet: false,
        BET_TIME: 15,
        betNeed: 0,
        BetMax: 0,
        bankerMaxTurn: 0,
        bankerMinTurn: 0,
        betLimit: {"1":0,"2":0,"3":0},//当前区域下注限额
        TYPE_BEI_CONFIGS: {
            he: 13,
        },
    };
    /** 是否断线重连 */
    private _isOtherRoom: number = -1;
    private _myChouma: number = 0;;
    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this._gameView = this.node.getComponent("LHGameView");
        this.updateGameLan();
        super.onLoad();
    }

    //////////////////////////////////////////////////////////////////////////////

    start() {
        let self = this;
        this.bindGameMessage(LHD.CMD.SC_LHD_CONFIG_P, self.onConfigs, self);               //配置
        this.bindGameMessage(LHD.CMD.SC_LHD_GAMESTATE_P, self.onGameStatus, self);        //游戏状态
        this.bindGameMessage(LHD.CMD.SC_LHD_FIRST_P, self.onFirstPokers, self);            //发牌动画
        this.bindGameMessage(LHD.CMD.SC_LHD_OPTTIME_P, self.onOperateTime, self);          //下注亮牌操作时间
        this.bindGameMessage(LHD.CMD.SC_LHD_BUYHORSE_P, self.onTablePlayerBet, self);       //玩家下注
        this.bindGameMessage(LHD.CMD.SC_LHD_ZHUANG_LIST_P, self.onBankerList, self);        //上庄列表
        this.bindGameMessage(LHD.CMD.SC_LHD_ZHUANG_INFO_P, self.onBankerInfo, self);        //庄家信息
        this.bindGameMessage(LHD.CMD.SC_LHD_NO_ZHUANG_P, self.onNoBanker, self);           //下庄公告
        this.bindGameMessage(LHD.CMD.SC_LHD_NOTICE_NO_ZHUANG_P, self.onCanOffBanker, self);//通知玩家可以主动下庄
        this.bindGameMessage(LHD.CMD.SC_LHD_SHOWCARD_P, self.onShowPokers, self);           //亮牌
        // this.bindGameMessage(LHD.CMD.SC_LHD_SETTLEMENT_P, self.onResult, self);             //游戏结算
        this.bindGameMessage(LHD.CMD.SC_LHD_OPER_ERROR_P, self.onError, self);              //错误消息
        this.bindGameMessage(LHD.CMD.SC_LHD_HISTORY_P, self.onHistory, self);               //历史记录
        this.bindGameMessage(LHD.CMD.SC_LHD_FOLLOW_BUY_P, self.onFollowBet, self);          //续押
        this.bindGameMessage(LHD.CMD.SC_LHD_ALLLIST_P, self.onAllPlayerList, self);        //玩家列表
        this.bindGameMessage(LHD.CMD.SC_LHD_SYNC_BET, self.onPlayerBet, self);
        this.bindGameMessage(LHD.CMD.SC_LHD_BET_SUCCESS, self.onBetSuccess, self);
        this.bindGameMessage(LHD.CMD.SC_LHD_BET_SYNC, self.onBetSYNC, self);
        this.bindGameMessage(LHD.CMD.SC_LHD_SETTLEMENT_P_NEW, self.onGameResultNew, self);
        this.bindGameMessage(LHD.CMD.SC_LHD_ONLINENUM_P, self.onUpdateOnlinePlayersNum, self); //更新桌面上玩家在线人数
        

        EventManager.instance.on(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        LHChipPoolAssisCom.instance.init(this._gameView.chipNode);
        super.start()
    }

    private updateGameLan() {
        updateGameTextTips();
        TextTips["GameTextTips"] = GameTextTips;
    }

    //
    onMyAllBet(msg) {
        let temp = {};
        temp[1] = msg.long;
        temp[2] = msg.hu;
        temp[3] = msg.he;

        this._gameView.updateBetData(temp);
    }

    onBetSuccess(msg) {
        //console.log(msg, "下注成功：");
        if(msg.limit){
            this.model.betLimit = msg.limit;
        }
        
        if (msg.chouma) {
            super.updatePlayerMoney({ coin: msg.chouma, playerid: msg._playerid });
        }
        this._gameView.myBetSuccess(msg);
    }

    onBetSYNC(msg) {
        //console.log(msg.total, "筹码增量信息：");
        let self = this;
        let tabArr: number[] = [0, 4, 8, 12, 16];
        let total = {};
        total[0] = msg.total.long;
        total[1] = msg.total.hu;
        total[2] = msg.total.he;

        if(msg.leftbet){
            this.model.betLimit = msg.leftbet;
        }

        let selectConfig = this._gameView.SELECT_CONFIG;
        if (this._gameView.isSyscnBet == false) {
            return;
        }
        for (let key in msg) {
            if (key != "_playerid" && key != "total" && key != "roundid") {
                let val = msg[key];

                for (let i = 0; i <= tabArr.length; i++) {

                    let _rshift_long = (val.long >> tabArr[i]);
                    let _band_long = (_rshift_long & 0x0000000f);
                    if (_band_long > 0) {
                        this._gameView.updateChipMsg(key, _band_long, selectConfig[i], val, 0, total);
                    }

                    let _rshift_hu = (val.hu >> tabArr[i]);
                    let _band_hu = (_rshift_hu & 0x0000000f);
                    if (_band_hu > 0) {
                        this._gameView.updateChipMsg(key, _band_hu, selectConfig[i], val, 1, total);
                    }
                    let _rshift_he = (val.he >> tabArr[i]);
                    let _band_he = (_rshift_he & 0x0000000f);
                    if (_band_he > 0) {
                        this._gameView.updateChipMsg(key, _band_he, selectConfig[i], val, 2, total);
                    }
                }

            }
        }



    }

    onGameResultNew(msg) {
        //console.log(msg, "新结算：");
        // console.error('新结算时间..........' + Common.formatDateTimeNormal())
        this._gameView.onResult(msg);
    }

    exit() {
        let self = this;
        this.unbindGameMessage(LHD.CMD.SC_LHD_CONFIG_P, self.onConfigs, self);               //配置
        this.unbindGameMessage(LHD.CMD.SC_LHD_GAMESTATE_P, self.onGameStatus, self);        //游戏状态
        this.unbindGameMessage(LHD.CMD.SC_LHD_FIRST_P, self.onFirstPokers, self);            //发牌动画
        this.unbindGameMessage(LHD.CMD.SC_LHD_OPTTIME_P, self.onOperateTime, self);          //下注亮牌操作时间
        this.unbindGameMessage(LHD.CMD.SC_LHD_BUYHORSE_P, self.onTablePlayerBet, self);       //玩家下注
        this.unbindGameMessage(LHD.CMD.SC_LHD_ZHUANG_LIST_P, self.onBankerList, self);        //上庄列表
        this.unbindGameMessage(LHD.CMD.SC_LHD_ZHUANG_INFO_P, self.onBankerInfo, self);        //庄家信息
        this.unbindGameMessage(LHD.CMD.SC_LHD_NO_ZHUANG_P, self.onNoBanker, self);           //下庄公告
        this.unbindGameMessage(LHD.CMD.SC_LHD_NOTICE_NO_ZHUANG_P, self.onCanOffBanker, self);//通知玩家可以主动下庄
        this.unbindGameMessage(LHD.CMD.SC_LHD_SHOWCARD_P, self.onShowPokers, self);           //亮牌
        this.unbindGameMessage(LHD.CMD.SC_LHD_SETTLEMENT_P, self.onResult, self);             //游戏结算
        this.unbindGameMessage(LHD.CMD.SC_LHD_OPER_ERROR_P, self.onError, self);              //错误消息
        this.unbindGameMessage(LHD.CMD.SC_LHD_HISTORY_P, self.onHistory, self);               //历史记录
        this.unbindGameMessage(LHD.CMD.SC_LHD_FOLLOW_BUY_P, self.onFollowBet, self);          //续押
        this.unbindGameMessage(LHD.CMD.SC_LHD_ALLLIST_P, self.onAllPlayerList, self);        //玩家列表
        this.unbindGameMessage(LHD.CMD.SC_LHD_SYNC_BET, self.onPlayerBet, self);
        this.unbindGameMessage(LHD.CMD.SC_LHD_BET_SUCCESS, self.onBetSuccess, self);
        this.unbindGameMessage(LHD.CMD.SC_LHD_BET_SYNC, self.onBetSYNC, self);
        this.unbindGameMessage(LHD.CMD.SC_LHD_SETTLEMENT_P_NEW, self.onGameResultNew, self);
        this.unbindGameMessage(LHD.CMD.SC_LHD_ONLINENUM_P, self.onUpdateOnlinePlayersNum, self); //更新桌面上玩家在线人数

        EventManager.instance.off(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);

        LHChipPoolAssisCom.instance.clear();

        TextTips["GameTextTips"] = {};

        super.exit()
    }

    // 进入房间，房间信息
    onRoomInfo(info) {
        super.onRoomInfo(info)
        this._gameView.onSaveUserInfo(info.playerlist);

    }

    // 玩家加入
    onPlayerEnter(info) {
        super.onPlayerEnter(info);
        this._gameView.onUserEnter(info);
    }

    // 玩家离开
    onPlayerQuit(info) {
        this._gameView.onUserLeave(info);
    }

    onQuitGame(info) {
        let self = this;
        if (self.model.NotBetCount) {
            this._gameView.messageBox(this._gameView.stringFormat(GameTextTips.GAME_EXIT_MIN_JU_TIP, [self.model.NotBetCount])
                , function () {
                    self.quitGame();
                }, true);
        } else {
            UIHelper.clearAll();
            super.onQuitGame(info);
        }
    }

    // 断线重连
    onToOtherRoom(info) {
        let self = this;
        if(info.canbet){
            this.model.betLimit = info.canbet;
        }
        this._isOtherRoom = 1
        this._myChouma = info.chouma;
        this.userInfo.money = info.chouma;
        this._gameView.updateMyScore(this.userInfo.money);
        // let playerlist = info["players"];
        super.onToOtherRoom(info);
        //庄家
        if (info && info.config) {
            self.model.bankerMaxTurn = info.config.Zhuang && info.config.Zhuang.MaxTurn || -1;
            self.model.bankerMinTurn = info.config.Zhuang && info.config.Zhuang.MinTurn || -1;
            self.model.bankerNeed = info.config.Zhuang && info.config.Zhuang.Need || -1;
        }
       
        //赔率
        if (info.Odds) {
            self.model.TYPE_BEI_CONFIGS = info["Odds"];//utils:copyTable(info["Odds"])
        }
        if (info && info.config && info.config.Bet) {
            this._gameView.SELECT_CONFIG = [];
            for (var key in info.config.Bet) {
                if (info.config.Bet[key]) {
                    self._gameView.SELECT_CONFIG.push(Common.toInt(info.config.Bet[key]));
                }
            }
            this._gameView.autoSelectChip();
            self.model.BET_TIME = info.config.TimeLimit.BuyHorse;
            self.model.betNeed = info.config.BetNeed;
            this._gameView.offLineToOnline(info);
        } else {
            cc.log("断线重连可下注配置为空");
        }
        if (info.nextat) {
            self.model.BET_TIME = info.nextat;
            this._gameView.betTimeOut();
        }
        if (info.config) {
            if (info.config.BetMax) {
                self.model.BetMax = info.config.BetMax;
            }
        }


        let msg: any = {};
        msg.zhuangheadid = info.zhuangdata.zhuangheadid;
        msg.zhuangid = info.zhuangdata.zhuangid;
        msg.zhuangturn = info.zhuangdata.zhuangturn;
        msg.chouma = info.zhuangdata.rchouma;
        msg.zhuangname = info.zhuangdata.zhuangname;
        msg.list = info.zhuangdata.zhuanglist;
        msg.issystem = info.zhuangdata.issystem;
        this._gameView.onUpdateBankerInfo(msg, true);
        if (info.zhuangdata.zhuanglist) {
            this._gameView.onUpdateBankerList(msg);
        }
        if (info.data) {
            this._gameView.onUpdateHistoryIcon(info.data);
            this._gameView.updateTrendData(info.data);
        }
        this._gameView.gameState = info.state;
        if (info.state == LHD.LHD_GameState.LHD_GameState_BuyHorse) {
            this._gameView.sendCard();
            this._gameView.show(GameTextTips.GAME_BET_ING_TIP);
        } else if (info.state == LHD.LHD_GameState.LHD_GameState_Combine) {
            this._gameView.showTipWait();
        } else if (info.state == LHD.LHD_GameState.LHD_GameState_End) {
            this._gameView.hideWaitAct();
            this._gameView.showTipWait();
            this._gameView.onSleep();
        }

    }

    // time:1
    handleOnToOtherRoomStatusNone(info) {

    }

    // time:2
    handleOnToOtherRoomStatusStart(info) {

    }

    // time:12
    handleOnToOtherRoomStatusBet(info) {

    }

    // time:7
    handleOnToOtherRoomStatusCompare(info) {

    }

    // time:8
    handleOnToOtherRoomStatusResult(info) {

    }

    onOperateTime(info) {
    }

    // 房间状态
    onRoomState(info) {
    }

    // 更新玩家金币
    updatePlayerMoney(info) {
        let self = this;
        //console.log(info, "充值成功更新玩家金币：");

        let myPlayerid = self.userInfo.playerid;
        if (info.playerid == myPlayerid) {
            //第一次断线重连时 不更新金币
            if (this._isOtherRoom == 1) {
                info.coin = this._myChouma;

            } else {
                super.updatePlayerMoney(info);
                this._gameView.updateMyScore(info.coin);
            }
        } else {
            this._gameView.updateOtherScore(info.playerid, info.coin)
            this._gameView.updateUserScore(info);
        }
        //TODO 这里更新下注按钮状态                                                                                                                                                                                                                                                                                                                 
        this._isOtherRoom = 0;
    }

    // 配置信息
    onConfigs(info) {
        //console.log(info, "获取游戏配置：");
        let self = this;

        if (info.BetMax) {
            self.model.BetMax = info.BetMax;
        }
        // 下注按钮面值
        if (info.Bet) {
            this._gameView.SELECT_CONFIG = [];
            for (var key in info.Bet) {
                if (info.Bet[key]) {
                    this._gameView.SELECT_CONFIG.push(Common.toInt(info.Bet[key]));
                }
            }
            this._gameView.autoSelectChip();
        }
        //赔率
        if (info.Odds) {
            self.model.TYPE_BEI_CONFIGS = info["Odds"];//utils:copyTable(info["Odds"])
        }
        // 下注时间
        self.model.BET_TIME = info["TimeLimit"]["BuyHorse"]
        // 上庄条件
        if (info["Zhuang"] && self.roomInfo.orderid && info["Zhuang"][self.roomInfo.orderid]) {
            let temp = info["Zhuang"][self.roomInfo.orderid]
            self.model.bankerMaxTurn = temp.MaxTurn
            self.model.bankerMinTurn = temp.MinTurn
            self.model.bankerNeed = temp.Need
        }
        // 下注需要金币
        self.model.betNeed = info.BetNeed;

    }

    // 游戏状态
    onGameStatus(info) {
        //console.log(info, "游戏状态：");
        this._gameView.gameState = info.state;
        if (info.state == LHD.LHD_GameState.LHD_GameState_None) {         //无状态
            //print("\n\n\n没有状态============================\n\n\n");
        } else if (info.state == LHD.LHD_GameState.LHD_GameState_BuyHorse) {  //下注状态
            // console.error('下注时间..........' + Common.formatDateTimeNormal())
            this._gameView.onStartBet();
        } else if (info.state == LHD.LHD_GameState.LHD_GameState_Combine) {   //亮牌状态
            // console.error('停止下注时间..........' + Common.formatDateTimeNormal())
            this._gameView.onStopBet();
        } else if (info.state == LHD.LHD_GameState.LHD_GameState_End) {       //结算状态
            //print("\n\n\n游戏休息============================\n\n\n");
            // console.error('结算时间..........' + Common.formatDateTimeNormal())
            this._gameView.onSleep();
        } else if (info.state == 4) {
            //print("\n\n\n状态4不知道什么状态============================\n\n\n");
        }
    }

    // 开局相关信息
    onBeginInfo(info) {
        // //console.log(info,"onBeginInfo",10)
    }

    //下注信息（断线重连恢复场景用）
    onUserBetInfo(info) {

    }

    // 发牌动画 1:east 2:south 3:west 4:north banker:zhuang
    onFirstPokers(info) {

    }

    //玩家下注
    onPlayerBet(info) {

    }

    // 在桌玩家下注
    onTablePlayerBet(info) {
        //console.log(info,"玩家下注：")

        this._gameView.onDeskUserBetMsg(info);
    }

    // 玩家续押
    onFollowBet(info) {
        //console.log(info, "玩家续押")
    }

    // 在线玩家下注
    onOnlinePlayerBet(info) {
        //console.log(info,"在线玩家下注")
    }

    // 上庄列表(主动下发)
    onBankerList(info) {
        //console.log(info, "上庄列表：");

        this._gameView.onUpdateBankerList(info);
    }

    // 当前庄家信息(主动下发)
    onBankerInfo(info) {
        //console.log(info, "当前庄家信息：")
        ////console.log(info,"onBankerInfo",10)
        let self = this;
        if(info.leftbet){
            this.model.betLimit = info.leftbet;
        }
        cc.tween(this._gameView._centerLayer)
            .delay(0.4)
            .call(function () {
                self._gameView.onUpdateBankerInfo(info);
            }).start();

    }

    // 玩家下庄
    onNoBanker(info) {
        //console.log(info, "玩家下庄：");
        // this._gameView.onUserCancelBanker(info){;
    }

    // 玩家可以下庄
    onCanOffBanker(info) {
        //console.log(info, "玩家可以下庄：");
        if (info["round"]) {
            this._gameView.show(this._gameView.stringFormat(GameTextTips.GAME_XZ_JU_TIP, [info["round"]]));
        }
    }

    // 开牌
    onShowPokers(info) {
        //console.log(info, "亮牌：");
        // console.error('亮牌时间..........' + Common.formatDateTimeNormal())
        this._gameView.onLookCard(info);
        ////console.log(info,"onShowPokers",10)
        // this._gameView.openPokers(info){
    }

    // 错误提示消息
    onError(info) {
        //console.log(info, "onError")
        ////[[
        let self = this;
        let code = info.code;
        if (code == LHD.GAME_ERROR.GAME_ERROR_NOT_MONEY) {
            this._gameView.show(GameTextTips.GOLD_BET_ERR);
        } else if (code == LHD.GAME_ERROR.GAME_ERROR_BUY_LIMIT) {
            this._gameView.show(GameTextTips.GOLD_BET_MAX_ERR);
            self.model.isBetMax = true;
            this._gameView.betFaild();
        }
        else if (code == LHD.GAME_ERROR.GAME_ERROR_NOT_ROUND) {
            this._gameView.show(GameTextTips.GAME_MAIN_MIN_ERR);

        } else if (code == LHD.GAME_ERROR.GAME_ERROR_OZ_STATE) {
            this._gameView.show(GameTextTips.GAME_STATE_NOT_XZ_ERR);

        } else if (code == LHD.GAME_ERROR.GAME_ERROR_ZHUANG_NO_MONEY) {
            let moneyTxt = this._gameView.moneyFormat(self.model.bankerNeed);
            this._gameView.show(this._gameView.stringFormat(GameTextTips.GAME_SZ_MONEY_TIP, [moneyTxt]));

        } else if (code == LHD.GAME_ERROR.GAME_ERROR_NEXT_ROUND) {
            this._gameView.show(GameTextTips.GAME_XZ_NEXT_TIP);
        } else if (code == LHD.GAME_ERROR.GAME_ERROR_OFFZHUANG_WUNIU) {
            this._gameView.show(GameTextTips.GOLD_BET_MAX_ERR);
            self.model.isBetMax = true;
            this._gameView.betFaild();
        } else if (code == LHD.GAME_ERROR.GAME_ERROR_APPLYZHUANG_OK) {
            this._gameView.show(GameTextTips.GAME_APPLY_SZ_TIP);
        } else if (code == LHD.GAME_ERROR.GAME_ERROR_NOT_MONEY_TO_BET) {
            self.model.betMoneyValue = info.value;
            let moneyTxt = this._gameView.moneyFormat(info.value);
            this._gameView.show(this._gameView.stringFormat(GameTextTips.GAME_GOLD_BET_MIN_TIP, [moneyTxt]));

        } else if (code == LHD.GAME_ERROR.GAME_ERROR_FOLLOW_TO_BET) {
            this._gameView.show(GameTextTips.GAME_BET_XT_ERR);
        } else if (code == LHD.GAME_ERROR.GAME_ERROR_FOLLOW_LIMIT) {
            this._gameView.show(GameTextTips.GAME_BET_XT_MIN_ERR);
        } else if (code == LHD.GAME_ERROR.GAME_ERROR_SLIENTCE_TOMANNY) {      //10局没下注踢出房间
            self.model.NotBetCount = info.value;
            // this._gameView.show( this._gameView.stringFormat(GameTextTips.GAME_EXIT_MIN_JU_TIP,[info.value]));
            //LhdShowTips:show("您已"..info.value.."局没有参与游戏，感谢关注", nil, tipsBG)
        } else if (code == LHD.GAME_ERROR.GAME_ERROR_BET_TOOMORE) {
            this._gameView.show(GameTextTips.GAME_BET_MONEY_MAX_TIP);
            self.model.isBetMax = true;
            this._gameView.betFaild();
        } else if
            (
            code == LHD.GAME_ERROR.GAME_ERROR_STATE_ERROR
            || code == LHD.GAME_ERROR.GAME_ERROR_BUY_POS_ERROR
            || code == LHD.GAME_ERROR.GAME_ERROR_BANKER_BET
            || code == LHD.GAME_ERROR.GAME_ERROR_ILLEGAL_BET
            || code == LHD.GAME_ERROR.GAME_ERROR_BET_TOOFAST

        ) {
            this._gameView.show(GameTextTips.GAME_BET_ADD_ERR);
        }
    }

    // 奖池信息
    onJackpotInfo(info) {
        // //console.log(info,"onJackpotInfo",10)

        if (info["data"]) {
            // this._gameView.reloadRewardPlayerList(info["data"])
        }
    }

    // 走势图返回
    onHistory(info) {
        //console.log(info, "历史记录:");
        let self = this;
        if (info["data"]) {
            this._gameView.updateTrendData(info.data);
            this._gameView.onUpdateHistoryIcon(info.data);
            this._gameView.initTrendData(info);
            self.model.lastRefreshHistoryTime = new Date().getTime()
        }
    }

    onClearBetInfo(info) {
        ////console.log(info,"onClearBetInfo",10)
        let self = this;

        if (info["playerid"] == self.userInfo.playerid) {
            self.model.lastHadBet = false
            // this._gameView.setXuyaBtnEnabled(false)
        }
    }

    // (未处理)
    onNoticeBankerList(info) {
        ////console.log(info,"onNoticeBankerList",10)
    }

    // 在桌玩家列表(主动下发)
    onTablePlayerList(info) {
        // //console.log(info,"onTablePlayerList",10)
        //print("在桌玩家列表(主动下发)");
        //self.model.tablePlayerList = info["playerlist"]
        // this._gameView.showTablePlayerList(self.model.tablePlayerList)
    }

    //更新桌面在线人数
    onUpdateOnlinePlayersNum(info){
        console.log(info, "桌面在线人数：");
        this._gameView.onUpdateOnlinePlayersNum(info); 
    }

    // 玩家列表(请求下发)
    onAllPlayerList(info) {
        console.log(info, "玩家列表：");
        
        this._gameView.onUpdateUserData(info);
        this._gameView.initPlayerListData(info);
        this._gameView.showOnlineUserEnd();
        EventManager.instance.emit(LHD.GameEvent.ONLINE_PLAYER_CHANGE_PAGE,info);    
    }

    // // 结算消息
    onResult(info) {
        //console.log(info, "游戏结算：");
        this._gameView.onGameResult(info);

    }

    //接收通用道具信息
    public onRoomChat(info: any) {
        console.log("onRoomChat", info);
        let sendPlayerid = Common.toInt(info["sendPlayerid"]);
        let receiverPlayerid = Common.toInt(info["receiverPlayerid"]);
        // let type = info["type"];
        let content = info["content"];

        let sendPlayerPos = this._gameView.getHeadPosAtPlayerId(sendPlayerid);//发送者位置
        let receiverPlayerPos = this._gameView.getHeadPosAtPlayerId(receiverPlayerid,true);//接收者位置

        if(Common.isNull(sendPlayerPos) || Common.isNull(receiverPlayerPos)){
            return;
        }
    
        this._gameView.doRoomIntertChat(sendPlayerPos, receiverPlayerPos, content);
    }

    // 发送协议
    // 下注
    sendBet(area, money) {
        let info = {}
        info["odds"] = money
        info["direction"] = area
        super.sendGameMessage(LHD.CMD.CS_LHD_BUYHORSE_P, info)
    }

    // 请求申请上庄列表(没看到请求)
    requestBankerList() {
        super.sendGameMessage(LHD.CMD.CS_LHD_REQUEST_ZHUANG_LIST_P)
    }

    // 申请上庄
    applyOnBanker() {
        super.sendGameMessage(LHD.CMD.CS_LHD_REQUEST_ZHUANG_P)
    }

    // 取消申请上庄
    cancelApplyOnBanker() {
        super.sendGameMessage(LHD.CMD.CS_LHD_REQUEST_NOT_ZHUANG_P)
    }

    //自己在庄上申请下庄
    offBanker() {
        super.sendGameMessage(LHD.CMD.CS_LHD_ZHUANG_OFF_P);
    }

    // 申请下注(当前自己是庄家)
    applyOffBanker() {
        //super.sendGameMessage(LHD.CMD.CS_ZHUANG_OFF_P)
    }



    // 续押
    followHistoryBet() {
        //super.sendGameMessage(LHD.CMD.CS_FOLLOW_BUY_P)
    }

    // 请求奖池信息
    requestJackpotInfo() {
        //super.sendGameMessage(LHD.CMD.CS_COLOR_POOL_P)
    }

    // 请求在桌玩家(不用请求，服务器主动下发)
    requestTablePlayerList() {
        //super.sendGameMessage(LHD.CMD.CS_RANKLIST_P)
    }

    // 请求玩家列表
    requestAllPlayerList(pageNum: number = 0) {
        let info = {};
        info["page"] = pageNum;
        console.log("请求玩家列表：", LHD.CMD.CS_LHD_ALLLIST_P + ' 当前页：' + pageNum);
        super.sendGameMessage(LHD.CMD.CS_LHD_ALLLIST_P,info)
    }

    // 请求状态时间配置(断线重新正在下注阶段请求)
    requestStatusTime() {
        //super.sendGameMessage(LHD.CMD.CS_TIME_P)
    }

    //请求历史记录
    requestHistory() {
        super.sendGameMessage(LHD.CMD.CS_LHD_HISTORY_P);
    }
}
