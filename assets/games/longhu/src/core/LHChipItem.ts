
const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('longhu/LHChipItem')
export default class LHChipItem extends cc.Component {


    private _release: boolean;

    public reuse() {
        this._release = false;
    }

    public unuse() {
        this._release = true;
    }

    public set isRelease(release: boolean) {
        this._release = release;
    }
    public get isRelease(): boolean {
        return this._release;
    }
    
    public resetUI() {
       this.node.active = false;
       this.node.x = 0;
       this.node.y = 0;
       this.node.angle = 0;
       this.node.opacity = 255;
    }
}
