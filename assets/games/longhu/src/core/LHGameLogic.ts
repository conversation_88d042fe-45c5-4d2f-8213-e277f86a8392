import Common from "../../../../script/frame/common/Common";
import Config from "../../../../script/frame/config/Config";
import HttpRequest from "../../../../script/frame/network/HttpRequest";

const {ccclass} = cc._decorator;

@ccclass
export default class LHGameLogic extends cc.Component  {

   
   
    private _animSpeed:number = 0.18;        //弹窗动画速度
   
    init () {
     
    }
    //显示弹窗效果
    showLayer(layerNode:cc.Node,callBack?){
        layerNode.scale =0.9;
        layerNode.opacity=0;
        layerNode.active = true;
        cc.tween(layerNode)
        .show()
        .to(this._animSpeed,{opacity:255,scale:1.0})
        .call(function(){
            if(callBack){
                callBack()
            }
        })
        .start();
    }
    //隐藏弹窗效果
    hideLayer(layerNode:cc.Node,callBack?){
        layerNode.scale =1.0;
        layerNode.opacity=255;
        layerNode.active = true;
        cc.tween(layerNode)
        .to(this._animSpeed,{opacity:0,scale:0.9})
        .hide()
        .call(function(){
            layerNode.active=false;
            if(callBack){
                callBack()
            }
        })
        .start();
    }
    //str 需要截断的字符串
    //maxChars 保留的汉字长度
    //suffix 添加的后缀 （注意，如果后缀不为null或者'' ，则要占用一个汉字的位置,具体看下方的示例代码)
    public strClamp(str:string, maxChars:number =14, suffix:string =null) {
        return Common.textClamp(str,maxChars,suffix);
    }

    //弹出效果
    popUpEffect(node: cc.Node, parent: cc.Node = null, isOpen: boolean = false,callback: Function = null,maskLayer: cc.Node = null){

        if(!node){
            return;
        }
        node.stopAllActions();
        if(isOpen){
            !!parent && (parent.active = true);
            node.active = true;
            node.scale = 0;
            node.opacity = 0;
            cc.tween(node)
            cc.tween(node)
            .parallel(
                cc.tween().to(0.15,{scale: 0.75},{ easing: 'backIn' }),
                cc.tween().to(0.15,{opacity: 30})
            )
            .parallel(
                cc.tween().by(0.05,{opacity: 225}),
                cc.tween().by(0.05,{scale: 0.25},{ easing: 'backOut' })
            )
            .call(()=>{
                callback && callback();
                if(!!maskLayer){
                    maskLayer.opacity = 0;
                    cc.tween(maskLayer)
                    .to(0.2,{opacity: 153})
                    .start()
                }
            })
            .start();
        }
        else{
            cc.tween(node)
            .to(0.2,{scale: 0},{ easing: 'backIn' })
            .call(()=>{
                callback && callback();
                node.active = false;
            })
            .start();

            if(!!parent){
                if(!!maskLayer){
                    cc.tween(maskLayer)
                    .to(0.2,{opacity: 0})
                    .call(()=>{
                        parent.active = false;
                    })
                    .start()
                }
                else{
                    parent.active = false;
                }
            }
        }
    }
    
   
}
