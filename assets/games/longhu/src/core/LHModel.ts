namespace LHModel {

    /**
     * 筹码数据
     */  
     export class ChipConfig{
     
       /** 筹码对应的值 1K */
       public valName:string;
       /** 筹码背景key */
       public bgKey:string;
       public betBgKey:string;
       /** 筹码选中背景 */
       public bgSelectKey:string;
    }
    class basePlayer{
        public node: cc.Node;
        public head: cc.Node;
        public headBg: cc.Node;
        public score: cc.Node;
        public fnt_LoseScore: cc.Node;
        public fnt_WinScore: cc.Node;
        
        public fnt_WinAnimation: cc.Node;
        public fnt_LoseScore_startPos:cc.Vec2;
        public fnt_WinScore_startPos:cc.Vec2;
        public playerid:number = 0;
        public bankerCount:cc.Node;
        public nickName:cc.Node;
        public alterUserInfo:cc.Node;
        public img_Null:cc.Node;
        public startPos:cc.Vec2;
         /** 椅子id */
       public chairid:number;
       public alertUserInfo:cc.Node;
       public isMove:boolean;
    }
   /**
    * 我的信息数据
    */
    export class MyInfo extends basePlayer {
       /** 用户金币值 */
       public money:number;
       /** 用户是否下注 */
       public isBet:boolean;
       public chairid:number;
       public isBankerList:any;
       public playerid:number = -1;
   
       public nickName:cc.Node = null;
       public alterUserInfo:cc.Node=null;
   }
   /**
    * 坐位玩家数据
    */
    export  class Player extends basePlayer{
       /** 用户头像图标 （土豪 神算子） */
       public headIcon:cc.Node;
       /** 坐位没有玩家节点 */
       public imgNull:cc.Node;
       /** 用户信息数据 */
       public userInfo:cc.Node;
      
      
       
   }
    //牌
    export class Card{
        public card: cc.Node;
        public valueCard: cc.Node;
        public startPos:cc.Vec2;
        public movePos:cc.Vec2;
    
    }
    //牌
    export class DeskCard extends Card{
        public winnerIcon:cc.Node;
        public node:cc.Node;
        
    
    }
   /**
    * 桌面数据
    */
   export class DesktopBet{
       public node: cc.Node;
        public star: cc.Node;
      
        public total_label: cc.Label;
        public self_label:cc.Label;
        public total_number: number;
        public self_number:number;
   }
}
export default LHModel;

