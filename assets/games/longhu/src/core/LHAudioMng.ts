const {ccclass, property} = cc._decorator;
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
@ccclass
export default class LHAudioMng extends cc.Component {
   
    bEnterBackgroundFlag: Boolean = false;
    init(){
    }
    playMusic () {
        let self = this;
        let path = "res/sound/bg";
        AudioHelper.instance.playMusic(path,true);
        
    }
    setOnEnterBackgroundFlag(bEnterBackgroundFlag: boolean){
        this.bEnterBackgroundFlag = bEnterBackgroundFlag;
    }
    pauseMusic() {
        AudioHelper.instance.pauseMusic();
    }

    resumeMusic() {
        AudioHelper.instance.resumeMusic();
    }
  
    _loadPlaySFX (path) {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        AudioHelper.instance.playEffect(path);
    }

    playCard() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/sendcard");
    }

    playAlert() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/alert");
    }
    playClick() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/click");
    }
    playBet() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/bet");
    }
    playBet2() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/bet2");
    }
    playCountDown() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/countdown");
    }
    playFlipCard() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/flipcard");
    }
    playSendCard() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/sendcard");
    }
    playStartWav() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/start");
    }
    playStopWav() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/stop");
    }
    playWinBet() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/win_bet");
    }
    playWinBet1() {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/win_bet1");
    }
    //播放牌方位龙虎声音及点数
    playCardDian(dian,area) {
        let self = this;
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        self._loadPlaySFX("res/sound/lhb_p_area"+area);
        self.scheduleOnce(()=>{
            self && (self._loadPlaySFX("res/sound/lhb_p_"+dian));
        },0.5)
    }
    //播放赢的声音
    playWindNum(num) {
        if(!!this.bEnterBackgroundFlag){
            return;
        } 
        this._loadPlaySFX("res/sound/win_"+num);
    }
    
}

