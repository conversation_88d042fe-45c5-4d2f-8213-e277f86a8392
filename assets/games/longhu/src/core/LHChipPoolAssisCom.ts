import LHChipItem from "./LHChipItem";


const DEFAULT_CHIP_COUNT = 200;        // 默认生成个数
const MAX_CHIP_COUNT = 1000;           // 默认生成个数

const { ccclass } = cc._decorator;
@ccclass
export default class LHChipPoolAssisCom  {
    private static SINGLETON_MSG: string = "[LHChipPoolAssisCom] @func ctor : singleton already constructed!";
    private static _instance: LHChipPoolAssisCom;
    public static get instance(): LHChipPoolAssisCom {
        if (!LHChipPoolAssisCom._instance) {
            LHChipPoolAssisCom._instance = new LHChipPoolAssisCom();
        }
        return LHChipPoolAssisCom._instance;
    }

    constructor() {
        if (LHChipPoolAssisCom._instance) throw Error(LHChipPoolAssisCom.SINGLETON_MSG);
    }

    private _chipPool = new cc.NodePool('LHChipItem');
    private _curSize = 0;
    private _chipItemNode: cc.Prefab = null;

    public init(chipItemNode: cc.Prefab,size: number = DEFAULT_CHIP_COUNT) {
        if(chipItemNode){
            this._chipItemNode = chipItemNode
            for (let i = 0; i < size; ++i) {
                let node = cc.instantiate(chipItemNode);
                this._chipPool.put(node);
                this._curSize++;
            }
        }
    }

    /**
     * @description 清空对象池
     */
    public clear() {
        this._chipPool.clear();
        this._curSize = 0;
    }

    public get(): cc.Node | null {
        if (this._chipPool.size() > 0) {
            let chip = this._chipPool.get();
            return chip;
        } else if (this._curSize < MAX_CHIP_COUNT) {
            this.init(this._chipItemNode,30);
            return this.get();
        } else {
            return null;
        }
    }

    public put(chip: cc.Node) {
        if (!!chip && cc.isValid(chip, true)) {
            let cls = chip.getComponent(LHChipItem);
            if (!cls.isRelease) {
                chip.stopAllActions();
                cls.isRelease = true;
                chip.getComponent(LHChipItem).resetUI()
                this._chipPool.put(chip);
            } else {
                // console.error('ChipPoolAssisCom put failed!.');
            }
        }
    }

    public getSize(){
        return this._curSize;
    }
}