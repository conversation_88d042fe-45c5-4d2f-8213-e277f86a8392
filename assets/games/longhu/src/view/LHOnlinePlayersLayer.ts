import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import EventManager from "../../../../script/frame/manager/EventManager";
import { LHD } from "../core/LHDefine";
import LHGameCore from "../core/LHGameCore";
import LHGameView from "./LHGameView";


const { ccclass, property,disallowMultiple,menu } = cc._decorator;

@ccclass
@disallowMultiple()
@menu('longhu/LHOnlinePlayersLayer')
export default class LHOnlinePlayersLayer extends BaseLayer {

    @property([cc.Node])
    panelNodeList: cc.Node[] = [];

    @property([cc.Node])
    selNodeList: cc.Node[] = [];

    @property(cc.SpriteAtlas)
    atlas: cc.SpriteAtlas = null;

    @property([cc.Node])
    page2PlayerNodeList: cc.Node[] = [];

    _gameCore: LHGameCore = null;
    //在线玩家数据
    onlineData: any = null;
    //当前点击页
    onClickPageNum: number = 0;
    //是否初始化当前界面数据 点击换页时 不更新数据
    initPageFlagList: boolean[] = [false,false];
    //显示界面数
    maxShowPage: number = 2;
    //回调隐藏界面
    closeCallBack: Function = null;
    //第二页头像显示最大个数
    page2ShowPlayerMaxNum: number = 14;
    //第二页头像当前页
    page2CurPage: number = 1;
    //实际最大页数
    page2MaxPage: number = 0;
    //是否点击了翻页
    clickPageBtnFlag: boolean = false;

    //主界面
    _gameview: LHGameView = null;

    onLoad () {
    }

    onStart(){
        
    }

    //设置当前界面数据
    setData(_view:LHGameView,gameCore:LHGameCore,data: any,show: boolean,closeCallBack: Function){
        EventManager.instance.on(LHD.GameEvent.ONLINE_PLAYER_CHANGE_PAGE, this.onAllPlayerList, this);
        this._gameview = _view;
        this.onlineData = data;
        this.closeCallBack = closeCallBack;
        this._gameCore = gameCore;
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)
        this.onShowLayer(show);
        this.setPageData();
    }

    //点击切换 big winner 及 game players 界面
    onClickChangePage(target: any, customEventData: any) {
        let clickIndex = Common.toNumber(customEventData);
        if(clickIndex == this.onClickPageNum){
            return;
        }
        this.onClickPageNum = clickIndex;
        this.setPageData();
    }

    //点击关闭
    onClickClose(target: any, customEventData: any) {
        this.onShowLayer(false);
    }

    resetData(){
        this.onClickPageNum = 0;
        this.LayerItems.scrollView_other.getComponent(cc.ScrollView).scrollToTop();
        this.LayerItems.content_ohter.removeAllChildren()
        this.selNodeList[this.onClickPageNum].active = true;
        this.selNodeList[(this.onClickPageNum + 1) % this.maxShowPage].active = false;
        this.panelNodeList[this.onClickPageNum].active = true;
        this.panelNodeList[(this.onClickPageNum + 1) % this.maxShowPage].active = false;
        this.page2PlayerNodeList.forEach(item=>{item.active = false});
        this.LayerItems.players_layout.removeAllChildren();

        this.LayerItems.total_online_num.getComponent(cc.Label).string = '0';
        this.LayerItems.page.getComponent(cc.Label).string = `0 / 0`;
        this.page2CurPage = 1;
        this.page2MaxPage = 0;
        this.clickPageBtnFlag = false;
    }

    //显示隐藏当前界面
    onShowLayer(show: boolean){
        this.initPageFlagList = [false,false];
        if(!show){
            EventManager.instance.off(LHD.GameEvent.ONLINE_PLAYER_CHANGE_PAGE, this.onAllPlayerList, this);
            this.resetData();
            this.closeCallBack && this.closeCallBack();
        }
    }


    // 玩家列表 重新更新
    onAllPlayerList(info) {
        if(this.node && !!this.node.active && this.clickPageBtnFlag){
            this.clickPageBtnFlag = false;
            this.onlineData = info;
            this.setPage2Data();
        }
    }

    /*
    1) CS_LHD_ALLLIST_P //请求玩家列表
	参数1："page" //查询页, 0-查询所有排行榜数据与玩家列表。  1-n查询指定页的玩家信息
	
    2) SC_LHD_ALLLIST_P //返回玩家列表
        参数1："totalplayernum" //房间内玩家总数
        参数2："roundid" //回合ID
        参数3："betrank" //下注排行榜(只在page=0时返回)
                参数1："playerid" //玩家ID
                参数2："name" //玩家昵称
                参数3："headid" //玩家系统头像id
                参数4："wxheadurl" //自定义头像url
                参数5："coin" //玩家的金币
                参数6："bet" //玩家下注额
                参数7："winnum" //玩家赢的局数
                参数8："winscore" //玩家赢的金币
        参数4："winscorerank" //赢分排行榜(只在page=0时返回)
                参数1："playerid" //玩家ID
                参数2："name" //玩家昵称
                参数3："headid" //玩家系统头像id
                参数4："wxheadurl" //自定义头像url
                参数5："winpos" //赢的位置: 1-龙 2-虎 3-和
                参数6："bet" //玩家下注额
                参数7："winscore" //玩家赢的金币
                参数8："time" //当局时间
        参数5："playerlist" //房间内的玩家列表(通过page指定查询, 每页14条数据)
                参数1："playerid" //玩家ID
                参数2："name" //玩家昵称
                参数3："headid" //玩家系统头像id
                参数4："wxheadurl" //自定义头像url
                参数5："coin" //玩家的金币
    */

    //显示当前页数据
    setPageData(){
        this.selNodeList[this.onClickPageNum].active = true;
        this.selNodeList[(this.onClickPageNum + 1) % this.maxShowPage].active = false;
        this.panelNodeList[this.onClickPageNum].active = true;
        this.panelNodeList[(this.onClickPageNum + 1) % this.maxShowPage].active = false;

        if(!this.initPageFlagList[this.onClickPageNum]){//当前页未初始化数据
            this.initPageFlagList[this.onClickPageNum] = true;

            this.onClickPageNum == 0 ? this.setPage1Data() : this.setPage2Data();            
        }
    }

    setPage1Data(){
        this.LayerItems.content_ohter.removeAllChildren();
        if(!this.onlineData || !this.onlineData?.winscorerank){
            return;
        }

        for (let key in this.onlineData.winscorerank) {
            if(this.onlineData.winscorerank[key]){
                let player = this.onlineData.winscorerank[key];
                if(Common.isNull(player.playerid)){
                    continue;
                }
                let item = cc.instantiate(this.LayerItems.item); 
                this.LayerItems.content_ohter.addChild(item);
                item.active = true;
                
                item.getChildByName('time').getComponent(cc.Label).string = player.time;
                item.getChildByName('rewards').getComponent(cc.Label).string = this._gameview.moneyFormat(player.winscore ?? 0);
                item.getChildByName('bet').getComponent(cc.Label).string = '₹' + this._gameview.moneyFormat(player.bet ?? 0);;
                item.getChildByName('type').getComponent(cc.Sprite).spriteFrame = this.atlas.getSpriteFrame(`lh_icon_${(player.winpos??1) - 1}`) ;
                item.getChildByName('name').getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");
                super.setPlayerHead(item.getChildByName('imgMask').getChildByName('headImg').getComponent(cc.Sprite), player.headid, player.wxheadurl??"");
            }
            
        }
    }

    setPage2Data(){
        if(!this.onlineData){
            return;
        }
        if(!!this.onlineData.betrank){
            this.page2PlayerNodeList.forEach(item=>{item.active = false});
            for (let key in this.onlineData.betrank) {
                if(Common.toNumber(key) > 2){
                    break;
                }
                let item = this.page2PlayerNodeList[Common.toNumber(key) - 1];
                if(this.onlineData.betrank[key]){
                    item.active = true;
                    let player = this.onlineData.betrank[key];
                    if(Common.isNull(player.playerid)){
                        continue;
                    }
                    super.setPlayerHead(item.getChildByName('imgMask').getChildByName('headImg').getComponent(cc.Sprite), player.headid, player.wxheadurl??"");
                    item.getChildByName('name').getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");
                    item.getChildByName('win1').getComponent(cc.Label).string = player.winnum ?? 0;
                    item.getChildByName('win2').getComponent(cc.Label).string = this._gameview.moneyFormat(player.winscore ?? 0);;
                }
            }
        }

        this.LayerItems.players_layout.removeAllChildren();
        this.LayerItems.players_layout.stopAllActions();
        if(!!this.onlineData.playerlist){
            for (let key in this.onlineData.playerlist) {
                if(this.onlineData.playerlist[key]){
                    let player = this.onlineData.playerlist[key];
                    if(Common.isNull(player.playerid)){
                        continue;
                    }
                    let item = cc.instantiate(this.LayerItems.headItem); 
                    this.LayerItems.players_layout.addChild(item);
                    item.active = false;                    
                    item.getChildByName('num').getComponent(cc.Label).string = this._gameview.moneyFormat(player.coin ?? 0);
                    item.getChildByName('name').getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");
                    super.setPlayerHead(item.getChildByName('imgMask').getChildByName('headImg').getComponent(cc.Sprite), player.headid, player.wxheadurl??"");
                    cc.tween(this.LayerItems.players_layout)
                    .delay(0.08 * Common.toInt(key))
                    .call(()=>{
                        item && (item.active = true);
                    })
                    .start();
                }
            }
        }

        let totalplayernum = this.onlineData.totalplayernum ?? 0
        this.LayerItems.total_online_num.getComponent(cc.Label).string = totalplayernum;
        this.page2MaxPage = Math.ceil(totalplayernum / this.page2ShowPlayerMaxNum);
        let page2CurPage = this.page2MaxPage > 0 ? this.page2CurPage : 0;
        this.LayerItems.page.getComponent(cc.Label).string = `${page2CurPage} / ${this.page2MaxPage}`;

    }

    //点击切换 big winner 及 game players 界面
    onClickChangePage2(target: any, customEventData: any) {
        let clickIndex = Common.toNumber(customEventData);
        let clickDesc = {prev:0,next:1}; //翻上页 或下页
        if(clickIndex == clickDesc.prev){
            if(this.page2CurPage - 1 <=  0){
                return;
            }
            this.page2CurPage -= 1;
        }
        else{
            if(this.page2CurPage + 1 > this.page2MaxPage){
                return;
            }
            this.page2CurPage += 1;
        }
        this.clickPageBtnFlag = true;
        this._gameview && this._gameview.updateOnlineLayerState();
        //--请求玩家列表
        this._gameCore.requestAllPlayerList(this.page2CurPage <= 1 ? 0 : this.page2CurPage); //第一页传0 其它传实际页
        
    }
    
}
