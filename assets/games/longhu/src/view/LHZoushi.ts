import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";

const { ccclass, property,disallowMultiple,menu } = cc._decorator;

@ccclass
@disallowMultiple()
@menu('longhu/LHZoushi')
export default class LHZoushi extends BaseLayer {

    daluGundong: cc.Node;
    xiaoluGundong: cc.Node;
    zhupaluGundong: cc.Node;
   
    updateTr: any;
    Image_Title: any;
    Label_Long: cc.Node;
    Label_Hu: cc.Node;
    ProgressBar_Long: cc.Node;
    ProgressBar_Hu: cc.Node;
    win_hu: cc.Node;
    win_long: cc.Node;
    win_he: cc.Node;
    win_zong: cc.Node;
    zhuzailu: any = {};
    xiaopailu: any[];
    dalu: any;
    dayanlu: any;
    xiaolu: any;
    zhanglanglu: any;
    dayanluGundong: cc.Node;
    zhanglangluGundong: cc.Node;
    historicalData: any[];
    showData: any;
    lhResultPanel: any = {};
  
     lhUIZoushi = []
     daplu_h = 6
     daplu_w = 12
     luMax = 40
     xiaoplu_length = 20
     ui:any = null
     blinkTag = 302
     isyuce = true 
     m_long = 1;
     m_hu = 2;
     m_he = 3;
     _init = false;

     imageResArr = ["","lh_record0","lh_record1","lh_record2"]
     closeCallback : Function = null;

     onLoad() {
        // this.initzoushiUI();  
     }

     start() {
        
    }
    initzoushiUI(closeCallback: Function){
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)
        this.closeCallback = closeCallback;
        this.ui = cc.Canvas.instance.getComponent("LHGameView");
        this.initUI()
   }
    
    initzoushi(Recorddata,count?){
    
        let len = Recorddata.longwinnum + Recorddata.huwinnum + Recorddata.hewinnum;
    
        let d = []
        
        for(let i=1;i<=len;i++){
            if (Recorddata[i]){
                d.push(Recorddata[i].win)
           }
       }
    
        Recorddata.record = d
    
        this.initdata(Recorddata.record)
   }
    initdata(data: any) {
        let self = this;
        self.historicalData = []
        self.showData = []
        //-- 各路显示所参照的数据
        self.historicalData = data

        
        while (self.historicalData.length > 70){
            self.historicalData.splice(0,1);
        }
        //print("初始化走势长度。。。。后。。", #self.historicalData)
        self.zhuzailuInitData()
        self.daluInitData()

        
        self.loadYucePic_new()
        self.lhResultPanel.state = 1
        //-- 已经初始化
        if (self.lhResultPanel.cache) {
            //print("初始化 拿缓存")
            self.addData(self.lhResultPanel.cache)
            self.lhResultPanel.cache = { }
        }
    }
      
    initUI(){
        let self = this;
        //  self.Image_Title = this.LayerItems.centerBg; 
         self.Label_Long = self.LayerItems.longPercentLabel;// self.ui.historyNode.getChildByName("topBox").getChildByName("leftBox").getChildByName("longPercentLabel")
         self.Label_Hu = self.LayerItems.huPercentLabel;// self.ui.historyNode.getChildByName("topBox").getChildByName("rightBox").getChildByName("huPercentLabel")
         self.ProgressBar_Long = self.LayerItems.longbg;//self.ui.historyNode.getChildByName("topBox").getChildByName("leftBox").getChildByName("longbg")
         self.ProgressBar_Hu = self.LayerItems.hubg;//self.ui.historyNode.getChildByName("topBox").getChildByName("rightBox").getChildByName("hubg")
         self.win_hu = self.LayerItems.huNum;//self.ui.historyNode.getChildByName("bottomBox").getChildByName("huLabel")
         self.win_long = self.LayerItems.longNum;//self.ui.historyNode.getChildByName("bottomBox").getChildByName("longLabel")
         self.win_he = self.LayerItems.heNum;//self.ui.historyNode.getChildByName("bottomBox").getChildByName("heLabel")
         self.win_zong = self.LayerItems.totalNum;//self.ui.historyNode.getChildByName("bottomBox").getChildByName("totalLabel")
         let Pailuda = self.LayerItems.zoushiMainBg;//self.ui.historyNode;
        //  let bottomBox = self.ui.historyNode.getChildByName("bottomBox")
        // let yuce_long = Pailuda.getChildByName("yuce_long")
        // let yuce_hu = Pailuda.getChildByName("yuce_hu")
        // this.addTouchEventListener(yuce_long, handler(self, self.nextLong), true);
        // this.addTouchEventListener(yuce_hu, handler(self, self.nextHu), true);
      
        self.zhuzailu = []
        self.xiaopailu = []
        self.dalu = {}
        self.dalu.node = []
        self.dayanlu = []
        self.xiaolu = []
        self.zhanglanglu = []
    
        // self.dayanlu.yuce_red = yuce_long.getChildByName("yuce_rd")
        // self.dayanlu.yuce_black = yuce_hu.getChildByName("yuce_bd")
        // self.xiaolu.yuce_red = yuce_long.getChildByName("yuce_rx")
        // self.xiaolu.yuce_black = yuce_hu.getChildByName("yuce_bx")
        // self.zhanglanglu.yuce_red = yuce_long.getChildByName("yuce_rz")
        // self.zhanglanglu.yuce_black = yuce_hu.getChildByName("yuce_bz")
        self.zhupaluGundong = Pailuda.getChildByName("leftHistoryBox").getChildByName("scrollView");
        let dapailuNode = self.zhupaluGundong.getChildByName("view").getChildByName("content");
       
        for(let i=0;i<self.daplu_w;i++){
            let node = dapailuNode.children[i]
            for(let j = 0;j<self.daplu_h;j++){
                let item = node.children[j];
                item.opacity = 0;
                self.zhuzailu.push(item);
           }
       }
    
       
        for (let i = 0;i<self.LayerItems.historyBox.childrenCount;i++){
            
            //self.ui.historyBox.children[i].opacity = 0;
            self.xiaopailu.push(self.LayerItems.historyBox.children[i]);
       }
        self.daluGundong  =Pailuda.getChildByName("rightHistoryBox").getChildByName("minHistoryBox").getChildByName("scrollView");
        // 大路
        let daluNode =  self.daluGundong.getChildByName("view").getChildByName("content");
        let luMaxCount = 20;
        daluNode.removeAllChildren();
        for (let i = 0;i<luMaxCount*2;i++){
            self.ui.loadAsset("res/prefabs/recordColumn",cc.Prefab,function(assets:cc.Prefab){
                let node = cc.instantiate(assets);
                let daluWdata =new Array();
                for (let j = 0;j<6;j++){
                    let item = node.children[j];
                    item.opacity = 0;
                    //item.getChildByName("valueLabel").anchorY=1;
                    // item.getChildByName("valueLabel").getComponent(cc.Label).string=(j+1)+"";
                    daluWdata.push(item);
                }
                self.dalu.node.push(daluWdata);
                daluNode.addChild(node);
            })
            //let node = daluNode.children[i]
           
          
       }
    
       self.dayanluGundong  =Pailuda.getChildByName("rightHistoryBox").getChildByName("minPanel").getChildByName("minLeftHistoryBox").getChildByName("scrollView");
        // 大眼路
        let dayanluNode = self.dayanluGundong.getChildByName("view").getChildByName("content");
        dayanluNode.removeAllChildren();
        //路眼索引 界面设计一列 四个 0123 分成2列 第一列数据0 2 第二列数据取 1 3  
        let luIndexArr = [0,2,0,2,0,2,1,3,1,3,1,3]
        for (let i = 0;i<luMaxCount;i++){
            self.ui.loadAsset("res/prefabs/recordMinColumn",cc.Prefab,function(assets:cc.Prefab){
                let node = cc.instantiate(assets);
                //let node = dayanluNode.children[i]
                let dayanluWdata = new Array();
                let indexi = 0;
            
                for(let j =0;j<6;j++){
                    let rowItem = node.children[(j%3)];
                    if(j>0 && j%3==0){
                        self.dayanlu.push(dayanluWdata);
                        dayanluWdata = new Array();
                    }
                    for (let k = 0;k<2;k++){
                        indexi= luIndexArr[(j*2)+k];
                        let item = rowItem.children[indexi];
                        //item.opacity = 0;
                        dayanluWdata.push(item);
                    }
                }
                dayanluNode.addChild(node);
                self.dayanlu.push(dayanluWdata);
            });
           
       }
       self.xiaoluGundong  =Pailuda.getChildByName("rightHistoryBox").getChildByName("minPanel").getChildByName("minRightHistoryBox").getChildByName("scrollView");
        // 小路
        let xiaoluNode = self.xiaoluGundong.getChildByName("view").getChildByName("content");
        xiaoluNode.removeAllChildren()
        for (let i = 0;i<luMaxCount;i++){
            self.ui.loadAsset("res/prefabs/recordMinColumn",cc.Prefab,function(assets:cc.Prefab){
                let node = cc.instantiate(assets);
                // let node = xiaoluNode.children[i]
                let dayanluWdata = new Array();
                let indexi = 0;
                for(let j =0;j<6;j++){
                    let rowItem = node.children[(j%3)];
                    if(j>0 && j%3==0){
                        self.xiaolu.push(dayanluWdata);
                        dayanluWdata = new Array();
                    }
                    for (let k = 0;k<2;k++){
                        indexi= luIndexArr[(j*2)+k];
                        let item = rowItem.children[indexi];
                        item.opacity = 0;
                        dayanluWdata.push(item);
                    }
                }
                self.xiaolu.push(dayanluWdata)
                xiaoluNode.addChild(node);
            });
       }
       self.zhanglangluGundong  =Pailuda.getChildByName("rightHistoryBox").getChildByName("minPanel").getChildByName("minNextBox").getChildByName("scrollView");
        // 蟑螂路
        let zllNode = self.zhanglangluGundong.getChildByName("view").getChildByName("content");
        zllNode.removeAllChildren();
        for (let i = 0;i<luMaxCount;i++){
            self.ui.loadAsset("res/prefabs/recordMinColumn",cc.Prefab,function(assets:cc.Prefab){
                let node = cc.instantiate(assets);
                // let node = zllNode.children[i]
                let dayanluWdata = new Array();
                let indexi = 0;
                for(let j =0;j<6;j++){
                    let rowItem = node.children[(j%3)];
                    if(j>0 && j%3==0){
                        self.zhanglanglu.push(dayanluWdata);
                        dayanluWdata = new Array();
                    }
                    for (let k = 0;k<2;k++){
                        indexi= luIndexArr[(j*2)+k];
                        let item = rowItem.children[indexi];
                        item.opacity = 0;
                        dayanluWdata.push(item);
                    }
                }
                self.zhanglanglu.push(dayanluWdata)
                zllNode.addChild(node);
            });
       }
        self.lhResultPanel.state = 0
        // 状态初始值
        self.historicalData = []
        self.showData = []
   }
    
     delayTag = 308
    // 更新函数  外部调用
    updateData(data, time){
        let self = this;
        time = - time || 0
        if (time > 10){
            return
        }else if (time <= 10 && time >= 4){
            time = 0
        }else{
            time = 0
       }
        let callback = function(){
            self.addData(data)
            self.show()
            self.loadYucePic_new()
            self.newzhuzailuBlink()
            self.newallOtherluBlink()
           
       }
       self.LayerItems.zoushiMainBg.stopAllActions();
       
       cc.tween(self.LayerItems.zoushiMainBg)
       .delay(time)
       .call(callback).start();
   }
    
    addData(newdata){
        let self = this;
        if (self.lhResultPanel.state == 0){
            self.lhResultPanel.cache = newdata
            return
       }
        if (self.historicalData.length >= 70){
            self.historicalData.splice(0,1);
       }
       self.historicalData.push(newdata);
        this.zhuzailuAddData(newdata)
        this.daluAddData(newdata)
   }
    
    getData(){
        let self = this;
        if (self.historicalData){
            return self.historicalData
        }else{
            return false
       }
   }
    
    testludan(num:number, showtype){
        let self = this;
        for(let i=0;i<num;i++){
            self.historicalData.push(showtype)
       }
   }
    
    
    // 显示
    show(){
        this.zhuzailuShow()
        this.xiaopailuShow()
        this.daluShow()
        this.allOtherluInitData()
        // 大路完毕后运行
        this.allOtherluShow()
        this.setProgressBar()
      
   }

   //隐藏时 将路子滚回去原处
    hide(){
        this.zhupaluGundong.getComponent(cc.ScrollView).scrollToLeft();
        this.daluGundong.getComponent(cc.ScrollView).scrollToLeft();
        this.dayanluGundong.getComponent(cc.ScrollView).scrollToLeft();
        this.xiaoluGundong.getComponent(cc.ScrollView).scrollToLeft();
        this.zhanglangluGundong.getComponent(cc.ScrollView).scrollToLeft();
    }

    
    // 最新闪烁
    newzhuzailuBlink(){
        let self = this;
        self.zhuzailu[self.zhuzailu.curPos].stopAllActions();
        cc.tween(self.zhuzailu[self.zhuzailu.curPos]).blink(2,2).start();
        
   }
    
    // 最新otherlu闪烁
    newotherluBlink(viewlist){
        if (viewlist.numW && viewlist.uidata.length > 0){
           
            viewlist[viewlist.numW][viewlist.numH].stopAllActions()
            cc.tween(viewlist[viewlist.numW][viewlist.numH]).blink(2,2).start();
       }
   }
    
    // 最新allotherlu闪烁
    newallOtherluBlink(){
        let self = this;
        this.newotherluBlink(self.dalu.node)
        this.newotherluBlink(self.dayanlu)
        this.newotherluBlink(self.xiaolu)
        this.newotherluBlink(self.zhanglanglu)
   }
    //- ====  zhuzai  ====
    // zhuzai init 数据
    zhuzailuInitData(){
        let self = this;
        self.zhuzailu.uidata = []
        for(let v in self.historicalData){
            self.zhuzailu.uidata.push(self.historicalData[v]);
       }
    
        while (self.zhuzailu.uidata.length > 70){
            self.zhuzailu.uidata.splice(0,1);
       }
    
   }
    // zhuzai add 数据
    zhuzailuAddData(data){
        let self = this;
        self.zhuzailu.uidata.push(data);
        if (self.zhuzailu.uidata.length > 70){
            self.zhuzailu.uidata.splice(0,1);
       }
   }
    
    // zhuzai路   移动范围 390起步 50递增4次到590止  0开始 8.35% 递增
    zhuzailuShow(){
        let self = this;
        self.zhuzailu.curPos = 0
        
        for(let i =0;i< self.daplu_h * self.daplu_w;i++){
            if (self.zhuzailu.uidata){
                if (self.zhuzailu.uidata[i] != null){
                    
                    let itemSprite = self.zhuzailu[i].getChildByName("bg").getComponent(cc.Sprite);
                    itemSprite.spriteFrame = self.ui.mainUiAtlas.getSpriteFrame(this.imageResArr[self.zhuzailu.uidata[i]]);

                    self.zhuzailu[i].opacity = 255
                    self.zhuzailu.curPos = i
                }else{
                    self.zhuzailu[i].opacity = 0
               }
           }
       }
        let moveDis = Math.floor(self.zhuzailu.curPos / 6)
        if (moveDis >= 11){
            self.zhupaluGundong.getChildByName("view").getChildByName("content").width=529;//.setContentSize(cc.size(530, self.zhupaluGundong.height))
        }else if (moveDis > 7){
            self.zhupaluGundong.getChildByName("view").getChildByName("content").width=353 + 44 *(moveDis - 6);//.setContentSize(cc.size(354 + 43 *(moveDis - 6), self.zhupaluGundong.height))
        }else{
            self.zhupaluGundong.getChildByName("view").getChildByName("content").width=353;//.setContentSize(cc.size(354, self.zhupaluGundong.height))
       }
        self.zhupaluGundong.getComponent(cc.ScrollView).scrollToPercentHorizontal(100, 1, true)
       
   }
    // 小牌路
    xiaopailuShow(){
        let self = this;
        let len = self.historicalData.length;
        let start = 0
        if(len > 20){
            start = len - 20;
        }
        let count = 0;
        for(let i=0;i<20;i++){
            count += 1;
            let newFlag = self.xiaopailu[i].getChildByName('NewFlag');
            if (self.historicalData[start + i] != null){
                let itemSprite = self.xiaopailu[i].getComponent(cc.Sprite);
                itemSprite.spriteFrame = self.ui.mainUiAtlas.getSpriteFrame(this.imageResArr[self.historicalData[start + i]]);
               self.xiaopailu[i].opacity = 255
            //    newFlag.active = count == maxCount;
            }else{
                self.xiaopailu[i].opacity = 0;
                newFlag.active = false;
           }
           
       }
   }
    
    // 创建下路数据模型
    initShowData(){
        let self = this;
        self.showData = {}
        let numW = 0
        //  31
        let numH = 0
        //  6
        self.showData.numW = numW
        self.showData.numH = numH
        self.showData[numW] = []
        self.showData[numW][numH] = []
        self.showData[numW][numH].numValue = 0
        let data;
        for(let i in self.dalu.uidata){
            data = self.dalu.uidata[i];
            // console.log("大路 数据   值  为。。。........数据模型....。",i,data)
    
            while (true) {
                if (Number(data) == self.m_he){
                    // 为  he
                    self.showData[numW][numH].numValue = self.showData[numW][numH].numValue + 1
                    break
               }
                if (numH == 0 && !self.showData[numW][numH].data){
                    // 初始原点
                    self.showData[numW][numH].data = data
                    // console.log("大路    初始值  为。。",numW,numH,self.showData[numW][numH].data)
    
                    break
               }
                if (self.showData[numW][numH].data == data){
                    numH = numH + 1
                }else{
                    numW = numW + 1
                    numH = 0
                    self.showData[numW] = []
               }
    
                self.showData[numW][numH] = []
                self.showData[numW][numH].numValue = 0
    
                self.showData[numW][numH].data = data
                // console.log("大路    值  为。。",numW,numH,self.showData[numW][numH].data)
                self.showData.numW = numW
                self.showData.numH = numH
                break
           }
    
       }
   }
    
    //- ====  大路 显示 ====
    // 大路 init 数据
    daluInitData(){
        let self = this;
        self.dalu.uidata = []
        for(let v in self.historicalData) {
            self.dalu.uidata.push(self.historicalData[v]);
       }
    
        while (self.dalu.uidata.length > 70){
            self.dalu.uidata.splice(0,1);
       }
        this.initShowData()
   }
    // da add 数据
    daluAddData(data){
        let self = this;
        if(!self.dalu.uidata){
            return;
        }
        self.dalu.uidata.push(data)
        if (self.dalu.uidata.length > 70){
            self.dalu.uidata.splice(0,1)
       }
        this.initShowData()
   }
    
    // 移动对应的大路数据一列
    moveDaluData(notdisplay){
        let self = this;
        let tempdata = self.dalu.uidata
    
        let value = self.dalu.node[0][0].data
        // console.log("牌路 第一列删除。、、、、、",#tempdata,value,tempdata[1])
        let i = 0
        while (true){
            if (!tempdata[i]){
                self.dalu.uidata = []
                self.dalu.numW = 0
                self.dalu.numH = 0
                break
           }
            if (tempdata[i] == value){
                // console.log("sh 杀出 删除值为。。。", tempdata[i] ,#self.dalu.uidata)
                tempdata.splice(0,1)
            }else if (tempdata[i] == 1){
                tempdata.splice(0,1)
            }else{
                break
           }
    
       }
        if (!tempdata){
            return
       }
        // console.log("牌路 第一列删除。、、、、、  后",#tempdata ,value)
        this.daluShow(notdisplay)
   }
    // 刷新对应的大路数据
    daluShow(notdisplay?){
        let self = this;
        for(let i=0;i<self.luMax;i++){
            for(let j=0;j<6;j++){
                self.dalu.node[i][j].data = -1
                self.dalu.node[i][j].numValue = 0
           }
       }
        if (!self.dalu.uidata){
            return
       }
        let numW = 0
        //  31
        let numH = 0
        //  6
        let tempW = 0
        let startPos:number = 0
        // 初始原点
        self.dalu.numW = numW
        self.dalu.numH = numH
      
        let data;
        for(let i in self.dalu.uidata){
            data = self.dalu.uidata[i];
            // console.log("大路 数据   值  为。。。............。",data,i)
            while (true) {
                if (Number(data) == self.m_he){
                    // wei  he
                    //console.log("和赢===============================", self.dalu.node[self.dalu.numW][self.dalu.numH].numValue, self.dalu.node[self.dalu.numW][self.dalu.numH].numValue + 1);
                    self.dalu.node[self.dalu.numW][self.dalu.numH].numValue = self.dalu.node[self.dalu.numW][self.dalu.numH].numValue + 1
                    startPos = startPos + 1
                    //self.dalu.node[numW][numH].data = data;
                    break
               }
                if (Number(i) == startPos){
                    // 初始原点
                    self.dalu.node[numW][numH].data = data
                    break
               }
                if (self.dalu.node[numW][numH].data == data){
                    if (self.dalu.node[numW][numH + 1] == null || self.dalu.node[numW][numH + 1].data != -1){
                        // 到顶
                        // console.log("到顶。。。。。。",numW,numH,data)
                        let movedis = 1
                        movedis = numW + movedis
                        // if (numH == 1){
                        //     this.moveDaluData(notdisplay)
                        //     return
                        //}
                        while (true) {
                            if (self.dalu.node[movedis][numH].data == -1){
                                self.dalu.node[movedis][numH].data = data
                                self.dalu.numW = movedis
                                self.dalu.numH = numH
                                // console.log("到顶 找到。。。 ",numW,movedis,numH,data)
                                break
                            }else{
                                movedis = movedis + 1
                                if (movedis > self.luMax - 1){
                                    this.moveDaluData(notdisplay)
                                    return
                                    // self.daluViewList[index][movedis][numH].data = data.cbWinOrLose
                                    // break
                               }
                           }
                       }
                        break
                    }else{
                        numH = numH + 1
                   }
                }else{
                    numW = 0
                    while (self.dalu.node[numW][0].data != -1){
                        numW = numW + 1
                        if (numW > self.luMax - 1){
                            // 越界
                            this.moveDaluData(notdisplay)
                            return
                            // break
                       }
                   }
                    numH = 0
               }
                self.dalu.node[numW][numH].data = data
                self.dalu.numW = numW
                self.dalu.numH = numH
                break
           }
       }
        if (!notdisplay){
            this.updateDalu()
       }
        if (self.dalu.numW >= 39){
             self.daluGundong.getChildByName("view").getChildByName("content").width = 881;//.getChildByName("content").setContentSize(cc.size(881, self.daluGundong.height))
       } else if(self.dalu.numW > 24){
             self.daluGundong.getChildByName("view").getChildByName("content").width = 529 + 22 *(self.dalu.numW - 22);//.getChildByName("content").setContentSize(cc.size(529 + 22 *(self.dalu.numW - 23), self.daluGundong.height))
        }else{
             self.daluGundong.getChildByName("view").getChildByName("content").width = 529;//.getChildByName("content").setContentSize(cc.size(529, self.daluGundong.height))
       }
         self.daluGundong.getComponent(cc.ScrollView).scrollToPercentHorizontal(100, 1, true)
   }
    
    // 更新大路 
    updateDalu(){
        let self = this;
        let prevImgs ="";//记录上
       for(let i=0;i<self.luMax;i++){
            for(let j=0;j<6;j++){
                // console.log("大路 数据 值为",i ,j ,self.dalu.node[i][j].data)
                
                self.dalu.node[i][j].opacity = 255
                self.dalu.node[i][j].getChildByName("bg").getChildByName("valueLabel").opacity = 0
                //console.log("显示赢的图标***********************************", self.dalu.node[i][j].numValue);
                if (self.dalu.node[i][j].numValue > 0){
                    // he
                    //console.log("显示和赢图标===============================");
                    // self.dalu.node[i][j].getComponent(cc.Sprite).spriteFrame = self.ui.historyAtlas.getSpriteFrame("");
                    //:loadTexture(GAME_LHD_IMAGES_RES.."history/jilu_he1.png", 1)
                    self.dalu.node[i][j].getChildByName("bg").getChildByName("valueLabel").opacity = 255
                    self.dalu.node[i][j].getChildByName("bg").getChildByName("valueLabel").getComponent(cc.Label).string = self.dalu.node[i][j].numValue;
                    // self.dalu.node[i][j].text:setString(self.dalu.node[i][j].numValue)
               }
                if (self.dalu.node[i][j].data == self.m_long){
                    self.dalu.node[i][j].getChildByName("bg").getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_1");
                    // self.dalu.node[i][j]:loadTexture(GAME_LHD_IMAGES_RES.."history/lh_img_long_4.png", 1)
               } else if(self.dalu.node[i][j].data ==self.m_hu){
                
                    self.dalu.node[i][j].getChildByName("bg").getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_1");
                    // self.dalu.node[i][j]:loadTexture(GAME_LHD_IMAGES_RES.."history/lh_img_hu_4.png", 1)
                }else{
                    self.dalu.node[i][j].opacity = 0
               }
           }
       }
   }
    
    //- ====  大眼路 xiao zlang 显示 ====
    
    allOtherluInitData(){
        let self = this;
        this.otherluInitData(self.dayanlu,0)
        this.otherluInitData(self.xiaolu, 1)
        this.otherluInitData(self.zhanglanglu, 2)
   }
    // init 数据
    otherluInitData(viewlist, offset){
        let self = this;
        viewlist.uidata = []
        viewlist.startPos_w = 1 + offset
        // 起始大路列数
        viewlist.startPos_h = 1
        // 起始大路行数
        // console.log(offset,"其他路  起始  参照点 。。。计算前。",viewlist.startPos_w,viewlist.startPos_h)
        // console.log("大路  2  2  值、、",self.dalu.node[viewlist.startPos_w][viewlist.startPos_h].data)
        if (self.showData[viewlist.startPos_w]){
            if (!self.showData[viewlist.startPos_w][viewlist.startPos_h]){
                viewlist.startPos_w = viewlist.startPos_w + 1
                viewlist.startPos_h = 0
           }
        }
        // console.log(offset,"其他路  起始  参照点 。。。。",viewlist.startPos_w,viewlist.startPos_h)
   }
    
    // 显示所有其他路
    allOtherluShow(notdisplay?){
        let self = this;
        this.RefDaluLen()
        this.otherluShow_new(self.dayanlu, 0, notdisplay)
        this.otherluShow_new(self.xiaolu, 1, notdisplay)
        this.otherluShow_new(self.zhanglanglu, 2, notdisplay)
   }
    
    // 移动对应的大眼路数据一列
    moveOtherluData(viewlist, offset, notdisplay?){

        let tempdata = viewlist.uidata
        // console.log("牌路 第一列删除。、、、、、",#tempdata)
        let value = viewlist[0][0].data
        while (true) {
            let i = 0
            if (!tempdata[i]){
                viewlist.uidata = []
                break
           }
            if (tempdata[i] == value){
                // console.log("sh 杀出 删除值为。。。", tempdata[i].cbWinOrLose ,#self.daluViewList[index].uidata)
                tempdata.splice(i,1);
            }else{
                break
           }
    
       }
        if (!tempdata){
            return
       }
        this.loadotherlu(viewlist, offset, notdisplay)
   }
    
    // 获得对应的路数据
    otherluShow_new(viewlist, offset, notdisplay){
        let self = this;
        let offsetVal = offset+1;
        // 黑1  红2
        viewlist.uidata = []
        let i = viewlist.startPos_w
        let j = viewlist.startPos_h
        while (self.showData[i]){
    
            while (self.showData[i][j]) {
                if (j == 0){
                    // 等于1 的时候
                    // console.log("第一行、、、、、、",j,self.dalu.node[i-1].len,self.dalu.node[i-1].len)
                    if (self.showData[i - 1].len == self.showData[i - offsetVal - 1].len){
                        // 齐
    
                        // console.log("齐。。。。   红",tempW)
                        viewlist.uidata.push(1);
                   } else {
                        // 不
                        // console.log("不 ，，齐。。。。  黑",tempW)
                        viewlist.uidata.push(2);
                   }
                }else{
                    // 一行以上的时候
                    // console.log("第后面  记行、、、、、、",j)
                    if (self.showData[i - offsetVal][j]){
                        // 有粒
                        // console.log("有 ，，粒。。。。   红",tempW)
                        viewlist.uidata.push(1);
                    }else{
                        // 无粒
                        if (!self.showData[i - offsetVal][j - 1] && j >= 2){
                            // console.log("无 ，，粒。。。。 红",tempW)
                            viewlist.uidata.push(1);
                        }else{
                            // console.log("无 ，，粒。。。。   黑",tempW)
                            
                            viewlist.uidata.push(2);
                       }
                   }
               }
                j = j + 1
           }
            i = i + 1
            j = 0
       }
        // console.log("显示的  小路 为。。。。。",offset)
    
        if (!notdisplay){
            // this.loadotherlu_new(viewlist,offset,notdisplay)
            this.loadotherlu(viewlist, offset, notdisplay)
       }
    
   }
    
    
    // 刷新对应的大眼路数据
    loadotherlu(viewlist, offset, notdisplay){
        // 黑1  红2
        let self = this;
       for(let i=0;i<self.luMax;i++){
            for(let j=0;j<6;j++){
                viewlist[i][j].data = -1
           }
       }
        let numW = 0
        //  31
        let numH = 0
        //  6
    
        let tempW = 0
        let startPos = 0
        // 初始原点
        let data;
        for (let i in viewlist.uidata) {
            // console.log("qt 路 数据   值  为。。。。",data,i,offset)
            data = viewlist.uidata[i];
            while (true) {
                if (Number(i) == startPos){
                    // 初始原点
                    viewlist[numW][numH].data = data
                    viewlist.numW = numW
                    viewlist.numH = numH
                    break
               }
    
                if (viewlist[numW][numH].data == data){
                    if (viewlist[numW][numH + 1] == null || viewlist[numW][numH + 1].data != -1){
                        // 越界
                        let movedis = 1
                        movedis = numW + movedis
                        // if (numH == 1){
                        //     this.moveOtherluData(viewlist,offset)
                        //     return
                        //}
                        while (true) {
                            if (viewlist[movedis][numH].data == -1){
                                viewlist[movedis][numH].data = data
                                viewlist.numW = movedis
                                viewlist.numH = numH
                                // console.log("越界 找到。。。 ",numW,movedis,numH)
                                break
                            }else{
                                movedis = movedis + 1
                                if (movedis > self.luMax - 1){
                                    movedis = self.luMax - 1
                                    this.moveOtherluData(viewlist, offset)
                                    return
                                    // self.daluViewList[index][movedis][numH].data = data.cbWinOrLose
                                    // break
                               }
                           }
                       }
                        break
                    }else{
                        numH = numH + 1
                   }
                }else{
                    numW = 0
    
                    while (viewlist[numW][0].data != -1) {
                        numW = numW + 1
                        if (numW > self.luMax - 1){
                            // 越界
                            numW = self.luMax - 1
                            this.moveOtherluData(viewlist, offset)
                            return
                            // break
                       }
                   }
                    numH = 0
               }
                viewlist[numW][numH].data = data
                viewlist.numW = numW
                viewlist.numH = numH
                break
           }
       }
        if (!notdisplay){
            this.updateOtherlu(viewlist, offset)
       }
        let movelist:cc.Node;
        if (offset == 0){
            movelist = self.dayanluGundong
       } else if(offset == 1){
            movelist = self.xiaoluGundong
       } else if(offset == 2){
            movelist = self.zhanglangluGundong
       }
        if (viewlist.numW){
            let num = Common.toInt(Math.floor(viewlist.numW / 2))
            if (num >= 19){
                 movelist.getChildByName("view").getChildByName("content").width=441;//.setContentSize(cc.size(441, 67));
           } else if(num > 11){
                 let addWidth =Common.toInt(num) - 10;
                 movelist.getChildByName("view").getChildByName("content").width=265 + (22 *addWidth);//.setContentSize(cc.size(265 + 22 *(num - 11), 67));
                //movelist:setInnerContainerSize(cc.size(265 + 22 *(num - 11), movelist:getContentSize().height))
            }else{
                 movelist.getChildByName("view").getChildByName("content").width=265;//.setContentSize(cc.size(265,67));
                //movelist:setInnerContainerSize(cc.size(265, movelist:getContentSize().height))
           }
        //    movelist.getComponent(cc.ScrollView).scrollToPercentHorizontal(100,1,true);
            //movelist:scrollToPercentHorizontal(100, 1, true)
       }
       self.dayanluGundong.getComponent(cc.ScrollView).scrollToPercentHorizontal(100,1,true);
       self.xiaoluGundong.getComponent(cc.ScrollView).scrollToPercentHorizontal(100,1,true);
       self.zhanglangluGundong.getComponent(cc.ScrollView).scrollToPercentHorizontal(100,1,true);
   }
    
    // 更新大眼路
    updateOtherlu(viewlist, offset){
        let self = this;
        let imageResArr =[[],[],[]];
        imageResArr[0].push("lh_zoushi_img_bz_1");
        imageResArr[0].push("lh_zoushi_img_rz_1");
        imageResArr[1].push("lh_zoushi_img_bz_2");
        imageResArr[1].push("lh_zoushi_img_rz_2");
        imageResArr[2].push("lh_zoushi_img_bz_3");
        imageResArr[2].push("lh_zoushi_img_rz_3");
       for(let i=0;i<self.luMax;i++){
            for(let j=0;j<6;j++){
                //console.log("大路 数据 值为",i ,j ,viewlist[i][j].data)
                if (viewlist[i][j].data == self.m_long){
                   
                    viewlist[i][j].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame(imageResArr[offset][0]);
                    viewlist[i][j].opacity = 255;
                    //viewlist[i][j]:loadTexture(GAME_LHD_IMAGES_RES.."history/lh_img_bz_" + offset + ".png", 1)
               } else if(viewlist[i][j].data ==self.m_hu){
                    viewlist[i][j].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame(imageResArr[offset][1]);
                    viewlist[i][j].opacity = 255;
                    //viewlist[i][j]:loadTexture(GAME_LHD_IMAGES_RES.."history/lh_img_rz_" + offset + ".png", 1)
                }else{
                    viewlist[i][j].opacity = 0
               }
           }
       }
   }
    
    // 刷新取得大路列数的有值长度
    RefDaluLen(){
        let self = this;
        let i = 0
        let j = 0
        if(self.showData.length==0){
            return;
        }
        while (true) {
            self.showData[i].len = 0
            while (true) {
                if (self.showData[i][j]){
                    j = j + 1
                    self.showData[i].len = j
                }else{
                    break
               }
           }
            // console.log("c長度    jisuan計算，，，，",i ,self.showData[i].len)
            i = i + 1
            j = 0
            if (!self.showData[i]){
                return
           }
       }
        // console.log("c長度    jisuan計算....",i,self.dalu.node[i].len)
    
   }
    
    
    // 下局hong
    nextLong(){
        this.nextState(true)
   }
    
    // 下局hei
    nextHu(){
        this.nextState(false)
   }
     moveDis = 0
    // 47.5 每格
    // 下局预测
    nextState(isWin){
        let self = this;
        if (!self.isyuce || !self.dalu.uidata){ return}
        // console.log("预测。。。。。",isWin)
        self.isyuce = false
        let newdata = self.m_long
        if (isWin){
            newdata = self.m_long
        }else{
            newdata =self.m_hu;
       }
        this.zhuzailuAddData(newdata)
        this.daluAddData(newdata)
        //
        this.zhuzailuShow()
    
        // this.loaddaluPic()
    
        this.daluShow()
        this.allOtherluInitData()
        this.allOtherluShow()
        this.newzhuzailuBlink()
        this.newallOtherluBlink()
    
        this.zhuzailuInitData()
        this.daluInitData()
        let callback = function(){
            self.isyuce = true
            self.show()
           
       }
       self.LayerItems.zoushiMainBg.stopAllActions();
       
       cc.tween(self.LayerItems.zoushiMainBg)
       .delay(2.2)
       .call(callback).start();
     
   }
    
    loadYucePic_new(){
        let self = this;
       
        // console.log("预测 准备。。。。前",a,self.dayanlu.uidata[a],self.xiaolu.uidata[b],self.zhanglanglu.uidata[c])
        let newdata = 2
        // hu
        this.daluAddData(newdata)
        this.allOtherluInitData()
        this.allOtherluShow(true)
        let a = self.dayanlu.uidata.length
        let b = self.xiaolu.uidata.length
        let c = self.zhanglanglu.uidata.length
        a = self.dayanlu.uidata.length-1
        b = self.xiaolu.uidata.length-1
        c = self.zhanglanglu.uidata.length-1
        // console.log("预测 准备。。。。后",a,self.dayanlu.uidata.length,self.dayanlu.uidata[a],self.xiaolu.uidata[b],self.zhanglanglu.uidata[c])
        // let nextPanel = self.ui.historyNode.getChildByName("rightHistoryBox").getChildByName("minPanel").getChildByName("minNextRightHistoryBox");
        let nextPanel = self.LayerItems.minNextRightHistoryBox;
        let nextLongPanel =nextPanel.getChildByName("leftNextBg").getChildByName("typeBox");
        let nextHuPanel =nextPanel.getChildByName("rightNextBg").getChildByName("typeBox");
        if (self.dayanlu.uidata[a] ==self.m_hu){
            nextHuPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_1");
            nextLongPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_1");
            nextHuPanel.children[0].opacity=255;
            nextLongPanel.children[0].opacity=255;
            
       } else if(self.dayanlu.uidata[a] == self.m_long){
            nextHuPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_1");
            nextLongPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_1");
            nextHuPanel.children[0].opacity=255;
            nextLongPanel.children[0].opacity=255;
           
        }else{
            nextHuPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
            nextLongPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
            nextHuPanel.children[0].opacity=0;
            nextLongPanel.children[0].opacity=0;
       }
        if (self.xiaolu.uidata[b] ==self.m_hu){
            nextHuPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_2");
            nextLongPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_2");
            nextHuPanel.children[1].opacity=255;
            nextLongPanel.children[1].opacity=255;
       } else if(self.xiaolu.uidata[b] == self.m_long){
        nextHuPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_2");
        nextLongPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_2");
        nextHuPanel.children[1].opacity=255;
        nextLongPanel.children[1].opacity=255;
        }else{
            nextHuPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
            nextLongPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_3");
            nextHuPanel.children[1].opacity=0;
            nextLongPanel.children[1].opacity=0;
       }

        if (self.zhanglanglu.uidata[c] ==self.m_hu){
            nextHuPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_3");
            nextLongPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
            nextHuPanel.children[2].opacity=255;
            nextLongPanel.children[2].opacity=255;
     
       } else if(self.zhanglanglu.uidata[c] == self.m_long){
        nextHuPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
        nextLongPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_3");
        nextHuPanel.children[2].opacity=255;
        nextLongPanel.children[2].opacity=255;
           
        }else{
            nextHuPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
            nextLongPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_3");
            nextHuPanel.children[2].opacity=0;
            nextLongPanel.children[2].opacity=0;
       }
        this.daluInitData()
   }
    // 预测图标更新
    loadYucePic(){
        let self = this;
        let newdata = 3
        
        // hu
        this.daluAddData(newdata)
        // this.loaddaluPic(true)
        this.daluShow(true)
        this.allOtherluInitData()
        this.allOtherluShow(true)
        // let nextPanel = self.ui.historyNode.getChildByName("rightHistoryBox").getChildByName("minPanel").getChildByName("minNextRightHistoryBox");
        let nextPanel = self.LayerItems.minNextRightHistoryBox;
        let nextLongPanel =nextPanel.getChildByName("leftNextBg").getChildByName("typeBox");
        let nextHuPanel =nextPanel.getChildByName("rightNextBg").getChildByName("typeBox");
        if (self.dayanlu.numW){
            let dayanluValue = self.dayanlu[self.dayanlu.numW][self.dayanlu.numH].data
            if (dayanluValue ==self.m_hu){
                nextHuPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_1");
            
           } else if(dayanluValue == self.m_long){
                nextHuPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_1");
           }
        }else{
            nextHuPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
            
       }
       
       nextHuPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_2");
       
        if (self.xiaolu.numW){
            if (self.xiaolu[self.xiaolu.numW][self.xiaolu.numH].data ==self.m_hu){
                nextHuPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_2");
            }else if (self.xiaolu[self.xiaolu.numW][self.xiaolu.numH].data == self.m_long){
                nextHuPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_2");
           }
        }else{
            nextHuPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
       }
        if (self.zhanglanglu.numW){
            if (self.zhanglanglu[self.zhanglanglu.numW][self.zhanglanglu.numH].data ==self.m_hu){
                nextHuPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_3");
            }else if (self.zhanglanglu[self.zhanglanglu.numW][self.zhanglanglu.numH].data == self.m_long){
                nextHuPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
           }
        }else{
            nextHuPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
       }
        this.daluInitData()
        newdata =2;
        // long
        this.daluAddData(newdata)
        // this.loaddaluPic(true)
        this.daluShow(true)
        this.allOtherluInitData()
        this.allOtherluShow(true)
       
        nextLongPanel.children[0].opacity=0;
        if (self.dayanlu.numW){
            if (self.dayanlu[self.dayanlu.numW][self.dayanlu.numH].data ==self.m_hu){
                nextLongPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_1");
               
                
            }else if (self.dayanlu[self.dayanlu.numW][self.dayanlu.numH].data == self.m_long){
                nextLongPanel.children[0].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_1");
           }
           nextLongPanel.children[0].opacity=255;
        }
        nextLongPanel.children[1].opacity=0;
        if (self.xiaolu.numW){
            if (self.xiaolu[self.xiaolu.numW][self.xiaolu.numH].data ==self.m_hu){
                nextLongPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_2");
                //self.xiaolu.yuce_red:loadTexture(GAME_LHD_IMAGES_RES.."history/lh_img_hu_2.png", 1)
            }else if (self.xiaolu[self.xiaolu.numW][self.xiaolu.numH].data == self.m_long){
                nextLongPanel.children[1].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_2");
                //self.xiaolu.yuce_red:loadTexture(GAME_LHD_IMAGES_RES.."history/lh_img_long_2.png", 1)
           }
           nextLongPanel.children[1].opacity=255;
        }
        nextLongPanel.children[2].opacity=0;
        if (self.zhanglanglu.numW){
            if (self.zhanglanglu[self.zhanglanglu.numW][self.zhanglanglu.numH].data ==self.m_hu){
                nextLongPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_hu_3");
                // self.zhanglanglu.yuce_red:loadTexture(GAME_LHD_IMAGES_RES.."history/lh_img_hu_3.png", 1)
            }else if (self.zhanglanglu[self.zhanglanglu.numW][self.zhanglanglu.numH].data == self.m_long){
                nextLongPanel.children[2].getComponent(cc.Sprite).spriteFrame = self.ui.mainUiAtlas.getSpriteFrame("lh_zoushi_img_long_3");
                // self.zhanglanglu.yuce_red:loadTexture(GAME_LHD_IMAGES_RES.."history/lh_img_long_3.png", 1)
           }
           nextLongPanel.children[2].opacity=255;
        }
        this.daluInitData()
    
   }
    
    // 取得20局long胜率
    getlongPercent(){
        let self = this;
        let len = self.historicalData.length;
        let longwin = 0
        let huwin = 0
        let hewin = 0
        for (let i=1;i<=20;i++){
            if (self.historicalData[len - i]){
                if (self.historicalData[len - i] == self.m_long){
                    longwin = longwin + 1
                }else if (self.historicalData[len - i] ==self.m_hu){
                    huwin = huwin + 1
                }else if (self.historicalData[len - i] == self.m_he){
                    hewin = hewin + 1
               }
           }
       }
    
    
        if (len > 20){
            return Math.floor(longwin *(100 /(20 - hewin)))
        }else{
            return Math.floor(longwin *(100 /(len - hewin)))
       }
    
   }
    
    // 显示输赢
    showWinOrloseResult(){
        let self = this;
        let len = self.historicalData.length;
        let longwin = 0
        let huwin = 0
        let hewin = 0
        for(let i=0;i<len;i++){
            if (self.historicalData[i]){
                if (self.historicalData[i] == self.m_long){
                    longwin = longwin + 1
               } else if(self.historicalData[i] ==self.m_hu){
                    huwin = huwin + 1
               } else if(self.historicalData[i] == self.m_he){
                    hewin = hewin + 1
               }
           }
       }
         self.win_long.getComponent(cc.Label).string = longwin + '';
         self.win_hu.getComponent(cc.Label).string = huwin + '';
         self.win_he.getComponent(cc.Label).string = hewin + '';
         self.win_zong.getComponent(cc.Label).string = (longwin + huwin + hewin) + '';
        // self.win_hu:setString(huwin)
        // self.win_he:setString(hewin)
        // self.win_zong:setString(longwin + huwin + hewin)
   }
    
    // 设置显示条 和 局数 
    setProgressBar(){
        let self = this;
        let longPercent = this.getlongPercent()||0;
        let huPercent = 0;
        let len = self.historicalData.length;
        if(len>0){
            let sizeW = [260.03,-260.03,310/100,310/100];
            huPercent = (100 - longPercent);
            let maxParent = (longPercent*2)>140?140:(longPercent*2);
            // let longWidth =  maxParent*sizeW[2];
            // let huWidth = (200-maxParent)*sizeW[2];
        }
        

        self.LayerItems.longPercentLabel.getComponent(cc.Label).string = longPercent + "%"
        self.Label_Hu.getComponent(cc.Label).string = huPercent + "%"
        //self.ProgressBar_Long.width =longWidth;
        //self.ProgressBar_Hu.width = huWidth;
       
        //self.Image_Title.x = 310-huWidth;
         //  self.Label_Long.x = longWidth-30;
        //  self.Label_Hu.x = 0-huWidth+30;
        // self.Label_Long.setPosition(longWidth-30,0)
        // self.Label_Hu.setPosition(0-huWidth+30,0);
        //self.ProgressBar_Hu:setPercent(50)
        this.showWinOrloseResult()
   }
    clear(){
        let self = this;
        self.historicalData = []
        self.xiaopailu = []
        self.zhuzailu = []
        self.dalu = {}
        self.dayanlu = []
        self.xiaolu = []
        self.zhanglanglu = []
        self.showData = []
   }

   onClickClose(){
    this.closeCallback && this.closeCallback();
   }
}
