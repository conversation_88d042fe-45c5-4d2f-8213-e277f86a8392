import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import EventManager from "../../../../script/frame/manager/EventManager";
import LHD = require("../core/LHDefine");
import LHGameCore from "../core/LHGameCore";


const { ccclass, property,disallowMultiple,menu } = cc._decorator;

@ccclass
@disallowMultiple()
@menu('longhu/LHHelpLayer')
export default class LHHelpLayer extends BaseLayer {

    @property(cc.ScrollView)
    scorll: cc.ScrollView = null;

    //回调隐藏界面
    closeCallBack: Function = null;
    //设置当前界面数据
    setData(show: boolean,closeCallBack: Function){
        this.closeCallBack = closeCallBack;
    }

    
    //点击关闭
    onClickClose(target: any, customEventData: any) {
        this.closeCallBack && this.closeCallBack();
    }

    //滚到最上面
    setScrollToTop(){
        this.scorll.scrollToTop();  
    }
}
