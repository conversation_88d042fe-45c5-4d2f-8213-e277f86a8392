import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";

//////////////////////////////////////////////////////////////////////////////////
const {ccclass, property} = cc._decorator;
@ccclass
export default class LHToastLayer extends BaseLayer {
    //////////////////////////////////////////////////////////////////////////////

    @property(cc.Sprite)
    bgSp: cc.Sprite = null;

    @property(cc.Label)
    textContent: cc.Label = null;

    @property([cc.SpriteFrame])
    changeBgList: cc.SpriteFrame[] = [];

    //////////////////////////////////////////////////////////////////////////////
    start () {
        if (this.textContent) {
            // 调整节点宽度
            this.node.width = Math.ceil(this.textContent.node.width) + 65;
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    // 设置显示的内容
    public set content(value: string) {
        if (this.textContent){
            this.textContent.string =Common.textClamp(value || "",110);
        }
    }

    //设置底图样式及对应字体色 bgType: 底框图类型 0：蓝色底 白色字体显示 1：黑色透明底 白色字体显示 2：蓝白色底 蓝色色体
    public setBgTypeAndFntColor(bgType: number = 1){
        if(this.bgSp){
            if(this.changeBgList[bgType]){
                this.bgSp.spriteFrame = this.changeBgList[bgType];
                this.textContent && (this.textContent.node.color = bgType > 1 ? cc.color(0,105,155) : cc.color(255,255,255));
            }
        }
    }

    // 执行显示动画
    public show(waitTime: number, removeCb: Function) {
        let self = this;
        if (waitTime < 2.0) {
            waitTime = 2.0;
        }
        this.node.stopAllActions();
        cc.tween(this.node)
            .delay(waitTime)
            .by(0.3, {y: 30})
            .call(() => {
                self.node.removeFromParent();
                if (removeCb) {
                    removeCb();
                }
            })
            .start();
    }
    
    //////////////////////////////////////////////////////////////////////////////
}
