import BaseLayer from "../../../../script/frame/component/BaseLayer";
import LHGameCore from "../core/LHGameCore";
import LHGameLogic from "../core/LHGameLogic";
import LHGameView from "./LHGameView";


const { ccclass, property,disallowMultiple,menu } = cc._decorator;

@ccclass
@disallowMultiple()
@menu('longhu/LHBetLayer')
export default class LHBetLayer extends BaseLayer {
    
    @property([cc.Node])
    btnChips: cc.Node[] = [];
    _selectIndex = 0;
    /** 游戏视图 */
    private _gameView: LHGameView;
   
     /** 游戏主对象 */
    private _gameCore: any;

    // LIFE-CYCLE CALLBACKS:
    onLoad () {
        this._gameView = cc.Canvas.instance.getComponent("LHGameView");
        this._gameCore = cc.Canvas.instance.getComponent("LHGameCore");
        this._registerBtns();
    }
    start(){
        this.initBet();
    }
    //获取选中筹码
    getSlectIndex(){
        return this._selectIndex;
    }
    //筹码绑定事件
    _registerBtns () {
        var self = this;
       
        var registerBtn = function (index) {
            self.btnChips[i].on(cc.Node.EventType.TOUCH_START, function (event) {
                let gameView = self._gameView;
                //验证当前是否庄家
                 if (gameView.BankerInfo.playerid != gameView.MyInfo.playerid) {
                   
                     self.setSelectChips(index);
                 }
            }, this);
        };
        for (var i = 0; i < self.btnChips.length; i++) {
            registerBtn(i);
        }
    }
    //选择筹码
    setSelectChips (index:number) {
        
        let self = this;
        let gameView = this._gameView;
        let selectScore = 0;
        if(index<gameView.SELECT_CONFIG.length && gameView.SELECT_CONFIG[index]){
            selectScore = gameView.SELECT_CONFIG[index];
        }
        if(gameView.MyInfo.money<selectScore){
            //金币不够无法选择
            return false;
        }
        this._selectIndex = index;

        for (var i = 0; i < self.btnChips.length; ++i) {
           
            let setNode =  this.btnChips[i];
            if(index==i){
                if(gameView.BankerInfo.playerid != gameView.MyInfo.playerid){setNode.y=20};
            }else{
                setNode.y=0;
            }
            setNode.getChildByName("select").active = index==i;
            index==i && (setNode.getChildByName("unSelect").active = false);
        }
        return true;
    }
    //重置所有筹码
    resetChips () {
        //重置筹码可选
        let self = this;
       
        for (var i = 0; i < self.btnChips.length; ++i) {
            let setNode =  this.btnChips[i];
            let setNodeSelect = setNode.getChildByName("select");
          
            if(this._selectIndex==i){
                setNode.y=20;
                setNodeSelect.active=true;
            }else{
                setNode.y=0;
                setNodeSelect.active=false;
            }
            self.setBetImg(i);
        }
        
    }
    //设置所有筹码不可用
    disableChips (hasEnabled:boolean) {
        //设置bet不可选
        let self = this;
        for (var i = 0; i < self.btnChips.length; ++i) {
           this.setDisableChips(i,hasEnabled);
        }
    }
    //设置筹码不可用
    setDisableChips(index:number,hasEnabled:boolean){
        let self = this;
        if(index>=this.btnChips.length){
            return;
        }
        let setNode =  this.btnChips[index];
        let setNodeSelect = setNode.getChildByName("select");
        self.setBetImg(index,hasEnabled);
        setNode.y=0;
        //setNode.opacity =hasEnabled==true?127:255;
        
        if(this._selectIndex==index){
            setNode.y=hasEnabled==true?0:20;
            setNodeSelect.active=true;//hasEnabled==true?false:true;
        }else{
            setNodeSelect.active=false;
        }
        
    }
    initBet(){
        let gameView = this._gameView;
        for (var i = 0; i < this.btnChips.length; i++) {
            if(i<gameView.SELECT_CHIP_DATA_CONFIG.length){
                let setNode =  this.btnChips[i];
                let setSprite:cc.Sprite = setNode.getComponent(cc.Sprite);
                let imgKey:string = gameView.SELECT_CHIP_DATA_CONFIG[i].bgKey;
                setNode.opacity =255;
                setSprite.spriteFrame = this._gameView.mainUiAtlas.getSpriteFrame(imgKey)                
            }
           
        }
    }
    //设置筹码图片
    //index 筹码位置
    //hasDisable 是否不可用 true 不可用
    setBetImg(index:number,hasDisable:boolean=false){
        let gameView = this._gameView;
        // if(this._gameView.gameAtlas){
            let setNode =  this.btnChips[index];
            setNode.getChildByName("unSelect").active = hasDisable;
        //     let setSprite:cc.Sprite = setNode.getComponent(cc.Sprite);
        //     let imgKey:string = gameView.SELECT_CHIP_DATA_CONFIG[index].bgKey;
        //     if(hasDisable){
        //         imgKey+="_d"
        //     }
        //    // "mortgage_"+keyNumber[index]+(hasDisable==true?"_d":"");
        //     setSprite.spriteFrame = this._gameView.gameAtlas.getSpriteFrame(imgKey)
        // }
    }
    autoSelectChip() {
        let self = this;
        let gameView = this._gameView;
        
        let myMoney:number = this._gameCore.userInfo.money;
        
        //设置不可选筹码
        for(let i=0;i<gameView.SELECT_CONFIG.length;i++){
            if (gameView.SELECT_CONFIG[i]==0 || myMoney < gameView.SELECT_CONFIG[i] || gameView.BankerInfo.playerid == gameView.MyInfo.playerid){
                self.setDisableChips(i,true);
            }else{
                self.setDisableChips(i,false);
            } 
        }
        let selectScore:number = gameView.SELECT_CONFIG[self._selectIndex];
        let selectIndex = self._selectIndex;
        //当前选中筹码，大于用户金额
        if(selectScore >myMoney){
            //设置可选最大金额
            for(let i=0;i<gameView.SELECT_CONFIG.length;i++){
                if (myMoney >= gameView.SELECT_CONFIG[i]){
                    selectIndex=i;
                } 
            }
        }
        self.setSelectChips(selectIndex);
    }
    
    // update (dt) {}
}
