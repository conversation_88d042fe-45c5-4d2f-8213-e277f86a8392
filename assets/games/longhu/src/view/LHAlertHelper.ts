

//////////////////////////////////////////////////////////////////////////////////

import { <PERSON><PERSON><PERSON>r } from "../../../../script/frame/common/Define";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import LHAlertLayer from "./LHAlertLayer";

// 提示框帮助类
export default class LHAlertHelper extends BaseLayer {
    //////////////////////////////////////////////////////////////////////////////
    // 资源名称
    public static RES_NAME: string = "res/prefabs/AlertLayer";
    public static EXIT_RES_NAME: string = "common/prefabs/ExitAlertLayer";

    //////////////////////////////////////////////////////////////////////////////
    // 实例对象
    private static _instance: LHAlertHelper = null;
    public static get instance(): LHAlertHelper {
        return this._instance || (this._instance = new LHAlertHelper());
    }

    //////////////////////////////////////////////////////////////////////////////
    // 是否正在加载资源
    private _loading: boolean = false;
    // 提示框预制对象
    private _alertPrefab: cc.Prefab = null;
    // 提示框实例对象
    private _alertNode: cc.Node = null;
    // 提示框组件对象
    private _alertLayer: LHAlertLayer = null;
    // 显示的内容
    private _showContent: string = "";
    // 显示的样式
    private _showButtonStyle: number = 0;
    // 确认回调函数
    private _confirmCb: Function = null;
    // 取消回调函数
    private _cancelCb: Function = null;
    // 强制显示(不会给后继显示替换)
    private _needShow: boolean = false;
    // 是否立即执行回调
    private _immediately: boolean = true;

    //////////////////////////////////////////////////////////////////////////////
    // 创建并显示提示框
    private createAlert() {
        // 上一个显示框是强制显示时直接返回
        if (this._alertLayer && this._alertLayer.needShow) {
            return ;
        }
        
        // 当前已有显示的提示框
        if (this._alertNode) {
            this._alertNode.stopAllActions();
            this._alertNode.removeFromParent();
        }

        // 创建预制对象
        this._alertNode = cc.instantiate(this._alertPrefab);

        this._alertLayer = this._alertNode && this._alertNode.getComponent("LHAlertLayer");

        // 判断组件是否有效
        if (this._alertLayer) {
            // 设置内容和样式
            this._alertLayer.content = this._showContent;
            this._alertLayer.setButtonStyle(this._showButtonStyle);
            this._alertLayer.confirmCb = this.onAlertConfirm.bind(this);
            this._alertLayer.cancelCb = this.onAlertCancel.bind(this);
            this._alertLayer.needShow = this._needShow;
            this._alertLayer.immediately = this._immediately;

            // 添加到场景
            SceneManager.instance.addChildNode(this._alertNode, ZOrder.DIALOG);

            // 显示动画
            this._alertLayer.show();
        }
        else {
            //清理数据
            this._alertNode = null;
            this._alertLayer = null;
            this._confirmCb = null;
            this._cancelCb = null;
        }
    }

    // 确认回调函数
    private onAlertConfirm() {
        // 执行回调接口
        if (this._confirmCb) {
            this._confirmCb();
        }

        //清理数据
        this._alertNode = null;
        this._alertLayer = null;
        this._confirmCb = null;
        this._cancelCb = null;
    }

    // 取消回调函数
    private onAlertCancel() {
        // 执行回调接口
        if (this._cancelCb) {
            this._cancelCb();
        }

        //清理数据
        this._alertNode = null;
        this._alertLayer = null;
        this._confirmCb = null;
        this._cancelCb = null;
    }

    //////////////////////////////////////////////////////////////////////////////
    // 预加载提示框资源
    public preloadAlertPrefab() {
        cc.resources.preload(LHAlertHelper.RES_NAME, cc.Prefab);
    }

    // 显示提示内容
    public showAlert(content: string, buttonStyle: number, onConfirm?: Function, onCancel?: Function, needShow?: boolean, immediately?: boolean) {
        // 设置内容与样式
        this._showContent = content?content:"";
        this._showButtonStyle = buttonStyle;
        this._confirmCb = onConfirm?onConfirm:null;
        this._cancelCb = onCancel?onCancel:null;
        this._needShow = needShow;
        this._immediately = immediately;

        // 资源已加载
        if (this._alertPrefab) {
            this.createAlert();
            return ;
        }

        // 是否正在加载资源
        if (this._loading) {
            return ;
        }
        this._loading = true;

        // 加载提示框资源
        let self = this;
        super.loadAsset(LHAlertHelper.RES_NAME, cc.Prefab, (alertPrefab: any) => {
            self._loading = false;
            self._alertPrefab = alertPrefab;
            self.createAlert();
        });
    }

    // 关闭当前显示的节点
    public clearAlert() {
        if (this._alertNode) {
            this._alertNode.stopAllActions();
            this._alertNode.removeFromParent();
        }
        this._alertNode = null;
        this._alertLayer = null;
        this._confirmCb = null;
        this._cancelCb = null; 
    }

    //////////////////////////////////////////////////////////////////////////////
    // 预加载提示框资源
    public static preloadAlert() {
        LHAlertHelper.instance.preloadAlertPrefab();
    }

    // 显示提示内容
    public static show(content: string, onConfirm?: Function, needShow: boolean = false, immediately: boolean = true) {
        LHAlertHelper.instance.showAlert(content, 0, onConfirm, null, needShow, immediately);
    }

    // 显示提示内容
    public static confirm(content: string, onConfirm: Function, onCancel: Function, needShow: boolean = false, immediately: boolean = true) {
        LHAlertHelper.instance.showAlert(content, 1, onConfirm, onCancel, needShow, immediately);
    }

    // 清除显示的节点
    public static clear() {
        LHAlertHelper.instance.clearAlert();
    }

    //////////////////////////////////////////////////////////////////////////////

    // 显示提示内容
    public showExitAlert(fee: Number, onConfirm?: Function, onCancel?: Function) {
        super.loadAsset(LHAlertHelper.EXIT_RES_NAME, cc.Prefab, (alertPrefab: any) => {
            let exitalertNode = cc.instantiate(alertPrefab);
            let alertLayer = exitalertNode.getComponent("ExitAlertLayer");
            alertLayer.content = fee;
            alertLayer.confirmCb = onConfirm;
            alertLayer.cancelCb = onCancel;
            // 添加到场景
            SceneManager.instance.addChildNode(exitalertNode, ZOrder.DIALOG);
            // 显示动画
            alertLayer.show();
        });
    }

    
}
