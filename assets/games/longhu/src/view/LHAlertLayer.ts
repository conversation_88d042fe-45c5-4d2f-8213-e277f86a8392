import PopupLayer from "../../../../script/frame/component/PopupLayer";

//////////////////////////////////////////////////////////////////////////////////
const {ccclass, property} = cc._decorator;
@ccclass
export default class LHAlertLayer extends PopupLayer {
    //////////////////////////////////////////////////////////////////////////////
    // 按钮样式1, 只显示确认
    public static BUTTON_STYLE_1 = 0;
    // 按钮样式2, 显示确认与取消
    public static BUTTON_STYLE_2 = 1;

    //////////////////////////////////////////////////////////////////////////////
    @property(cc.Label)
    textContent: cc.Label = null;

    @property(cc.Node)
    confirmButton: cc.Node = null;

    @property(cc.Node)
    cancelButton: cc.Node = null;

    //////////////////////////////////////////////////////////////////////////////
    // 按钮的样式
    private _buttonStyle: number = 0;
    // 确认回调函数
    private _confirmCb: Function = null;
    // 取消回调函数
    private _cancelCb: Function = null;
    // 强制显示(不会给后继显示替换)
    private _needShow: boolean = false;
    // 是否立即执行回调
    private _immediately: boolean = true;

    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        if (this._buttonStyle == LHAlertLayer.BUTTON_STYLE_1) {
            if (this.confirmButton) {
                this.confirmButton.x = 0;
            }
            if (this.cancelButton) {
                this.cancelButton.active = false;
            }
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    // 设置显示的内容
    public set content(value: string) {
        if (this.textContent) {
            this.textContent.string = value;
        }
    }

    // 设置确认回调函数
    public set confirmCb(cb: Function) {
        this._confirmCb = cb;
    }

    // 设置取消回调函数
    public set cancelCb(cb: Function) {
        this._cancelCb = cb;
    }

    // 强制显示(不会给后继显示替换)
    public get needShow(): boolean {
        return this._needShow;
    }
    public set needShow(value: boolean) {
        this._needShow = value;
    }

    // 设置是否立即执行回调
    public set immediately(value: boolean) {
        this._immediately = value;
    }

    // 设置按钮的样式
    public setButtonStyle(style: number) {
        this._buttonStyle = style;
    }

    //////////////////////////////////////////////////////////////////////////////
    // 点击确认按钮
    private onClickConfirm(event: cc.Event) {
        if (this._immediately) {
            this.close(null);
            if (this._confirmCb) {
                this._confirmCb();
            }
        }
        else {
            this.close(this._confirmCb);
        }
    }

    // 点击取消按钮
    private onClickCancel(event: cc.Event) {
        if (this._immediately) {
            this.close(null);
            if (this._cancelCb) {
                this._cancelCb();
            }
        }
        else {
            this.close(this._cancelCb);
        }
    }
    
    //////////////////////////////////////////////////////////////////////////////

}
