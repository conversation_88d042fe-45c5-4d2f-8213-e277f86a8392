const { ccclass, property,disallowMultiple,menu } = cc._decorator;
import SceneManager from "../../../../script/frame/manager/SceneManager";
import LHBetLayer from "./LHBetLayer";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import Common from "../../../../script/frame/common/Common";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import { Direction, ZOrder, quickPayStyle } from "../../../../script/frame/common/Define";
import LHModel from "../core/LHModel";
import AlertLayer from "../../../../script/frame/component/AlertLayer";
import <PERSON>H<PERSON>oushi from "./LHZoushi";
import LHGameCore from "../core/LHGameCore";
import LHGameLogic from "../core/LHGameLogic";
import DataManager from "../../../../script/frame/manager/DataManager";
import { GameTextTips, LHD, updateGameTextTips } from "../core/LHDefine";
import LHToastHelper from "./LHToastHelper";
import LHAlertHelper from "./LHAlertHelper";
import Config from "../../../../script/frame/config/Config";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import LHChipPoolAssisCom from "../core/LHChipPoolAssisCom";
import HallManager from "../../../../script/frame/manager/HallManager";

/** 可选筹码数量 */
const CHIPS_NUM = 6;

@ccclass
@disallowMultiple()
@menu('longhu/LHGameView')
export default class LHGameView extends BaseLayer {

    @property(cc.Node)
    mainLayer: cc.Node = null;

    @property(cc.Node)
    bottomLayer: cc.Node = null;

    @property(cc.Node)
    alterLayer: cc.Node = null;

    //用户主体预制体
    @property(cc.Prefab)
    userPrefab: cc.Prefab = null;

    //历史走势图标预制体
    @property(cc.Prefab)
    recordItem: cc.Prefab = null;

    // //头像赢钱动效
    // @property(cc.Prefab)
    // winFrameEffect: cc.Prefab = null;

    // //头像赢钱动效
    // @property(cc.Prefab)
    // winStarEffect: cc.Prefab = null;

    //筹码预制体
    @property(cc.Prefab)
    chipNode: cc.Prefab = null;

    @property(cc.Node)
    historyList: cc.Node = null;

    @property(cc.SpriteAtlas)
    cardAtlas: cc.SpriteAtlas = null;

    @property(cc.SpriteAtlas)
    mainUiAtlas: cc.SpriteAtlas = null;

    //在线其它玩家列表
    @property(cc.Prefab)
    onLinePlayersLayer: cc.Prefab = null;

    //走势界面
    @property(cc.Prefab)
    zoushiLayer: cc.Prefab = null;

    //帮助界面
    @property(cc.Prefab)
    helpLayer: cc.Prefab = null;

    //玩家在线列表
    @property(cc.Prefab)
    userMainNode: cc.Prefab = null;

    @property(cc.Node)
    gameEmojiNode: cc.Node = null;
        

    playerNode: any;
    chipsNode: cc.Node;
    //桌上六个玩家数据 及 在线其它玩家数据列表
    playerListData: any[] = null;
    totalplayernum: any;
    userBetMsgSch: any;
    updateScheduler: any;
    allTime: number = 0;
    movingOnlineUserHead: any;
    btnOnline: cc.Node;
    isPlayingBet1Sound: boolean;
    isPlayingBetSound: boolean;
    betScheduler: any;
    lastRoundArea: number;

    lhBetScript: LHBetLayer;
    currentTimeOut: number;


    bankerListMsg: any;
    trackSprite: any;
    isSyscnBet: boolean = true; //是否同步筹码，5秒后同步筹码
    //神算子 幸运星 残影动效
    ghostCanvasList: cc.Sprite[] = [];
    roleCamera: cc.Camera = null;
    role: cc.Node;
    betMsg: any;

    // historyBox: cc.Node;
    // historyNode: cc.Node;
    // lhResultPanel: any;

    _centerLayer: cc.Node = null;
    _currentTime: number[] = [0, 0];
    _timeLabel: cc.Label = null;
    _onlineCountLabel: cc.Label = null;
    //初始变量
    Chips: any[] = [[], [], []];

    Pokers: LHModel.Card[] = [new LHModel.Card, new LHModel.Card]; //牌数据
    compareTb: LHModel.DeskCard[] = [new LHModel.DeskCard, new LHModel.DeskCard, new LHModel.DeskCard]; //牌数据
    TouchBet: LHModel.DesktopBet[] = [new LHModel.DesktopBet, new LHModel.DesktopBet, new LHModel.DesktopBet];//桌面对象
    MyInfo: LHModel.MyInfo = new LHModel.MyInfo(); //我的对象
    Players: LHModel.Player[] = [new LHModel.Player, new LHModel.Player, new LHModel.Player, new LHModel.Player, new LHModel.Player, new LHModel.Player]; //玩家对象 0 神算子 1 大富豪

    BankerInfo: LHModel.MyInfo = new LHModel.MyInfo();
    BankerSystemInfo: LHModel.MyInfo = new LHModel.MyInfo();
    ChipBtn = {};
    chipPos = {};
    resultTb = {};

    userWinChip = {};
    userInfo = {};
    historyListTb: any[] = [];
    userBetMsgTemp: any[] = [];
    userBetMsgTemp1: any[] = [];
    selectScore = -1;
    bankerState = 0;     // 0 没上庄，1 在上庄列表，2 已上庄
    selectChipIndex = null;
    isStartBet = null;      //是否开始下注
    isSelectChip = null;    //是否手动选择筹码
    starArea = -1;        //神算子下注区域
    gameState = 0;         //当前游戏状态
    isGameEnd = false;     //游戏是否结束
    allUsers = {};
    flyingBets: any[] = [];
    flyingOnlineBets: any[] = [];
    lhdletPlayerList = {};
    flyingBets_he: any[] = [];
    isChangeBanker = false;
    SELECT_CONFIG: number[] = [0, 0, 0, 0, 0, 0]; //筹码对应的值

    CHIP_DATA_CONFIG: LHModel.ChipConfig[] = [];
    SELECT_CHIP_DATA_CONFIG: LHModel.ChipConfig[] = [];
    SELECT_CHOUMA_CONFIG: string[] = ["0", "1", "2", "3", "4", "5"]; //选中筹码索引
    // 配置数据存储的键值
    storeChoumaIndexKey: string = "LHDChoumaIndexKey";
    //游戏状态动效
    statePanel: cc.Node = null;
    //龙筹码
    longPanel: cc.Node = null;
    //和筹码
    hePanel: cc.Node = null;
    //虎筹码
    huPanel: cc.Node = null;

    //上庄弹窗
    _mainUserLayer: cc.Node = null;
    //用户信息
    // _userLayer:cc.Node = null;

    //等待下注
    _tipWaitLayer: cc.Node = null;
    //提示确认框
    _messageBoxLayer: cc.Node = null;
    //提示确认返回事件
    _callBackMessage: any = null;
    //  当前是否打开菜单
    private _isOpenMenu: boolean;

    /** 游戏核心对象 */
    private _gameCore: LHGameCore = null;
    /** 游戏逻辑 */
    private _gameLogic: LHGameLogic;
    /** 游戏走势 */
    private _gameZoushi: LHZoushi;
    /** 声音播放 */
    private _gameAudio: any;

    //在线玩家列表层是否初始化创建过
    onlinePanelLayer: any = null;

    //帮助界面是否初始化创建过
    onHelpPanel: any = null;

    //历史走势界面
    _historyLayer: cc.Node = null;

    //是否点击弹开了在线玩家信息列表
    _clickOtherLinePlayerLayer: boolean = false;

    //当前点击的头像
    _curClickHeadIndex: number = -1;

    chipsCount = 0;
    chipPoolList = [];

    // -----------------生命周期函数--------------------//
    onLoad() {
        //动态导入所有节点
        this.LoadAllLayerObject(this.node)

        let self = this;
        this._gameCore = this.node.getComponent("LHGameCore");
        this._gameLogic = this.node.getComponent("LHGameLogic");

        if(!this._historyLayer){//未创建过
            this.LayerItems.historyPanel.active = false;
            this._historyLayer = cc.instantiate(this.zoushiLayer);
            this.LayerItems.historyPanel.addChild(this._historyLayer);
        }

        this.initHistory();
        
        this._gameAudio = this.node.getComponent("LHAudioMng");
        cc.game.on(cc.game.EVENT_HIDE, self.onEnterBackground, self);
        cc.game.on(cc.game.EVENT_SHOW, self.onEnterForgeground, self);

        //下注多边型点击
        self.LayerItems.longClickBet.on(cc.Node.EventType.TOUCH_END, self._onLongTouchEnded, this, true);
        self.LayerItems.huClickBet.on(cc.Node.EventType.TOUCH_END, self._onHuTouchEnded, this, true);
        self.LayerItems.heClickBet.on(cc.Node.EventType.TOUCH_END, self._onHeTouchEnded, this, true);


        let chipValData = [10, 50, 100, 1000, 5000, 10000];
        for (let i = 0; i < chipValData.length; i++) {
            let item: LHModel.ChipConfig = new LHModel.ChipConfig();
            item.valName = chipValData[i] + '';
            item.bgKey = "lh_chip_" + chipValData[i];
            item.betBgKey = "lh_chip_fly_" + chipValData[i];
            item.bgSelectKey = "lh_chip_select";
            this.CHIP_DATA_CONFIG.push(item);
        }
        this.initChouMaData();

        //定义界面控件
        this._mainUserLayer = this.alterLayer.getChildByName("setMainPanel");
        this._tipWaitLayer = this.alterLayer.getChildByName("tipWaitPanel");
        this._messageBoxLayer = this.alterLayer.getChildByName("alertPanel");
        this._centerLayer = this.mainLayer.getChildByName("centerLayer");
        this.LayerItems.ShopButton.active = DataManager.instance.chargeStatus == 1;

        self.statePanel = this.LayerItems.statePanel;
        self.statePanel.active = false;
        let gameTimePanel: cc.Node = this._centerLayer.getChildByName("gameTime");
        gameTimePanel.active = false;
        //初始牌
        this.initCardUI();
        //初始庄家
        this.initZhuangUI();
        //初始化用户
        this.initPlayerUI();
        
        // 改变节点的层级
        this.alterLayer.zIndex = ZOrder.UI + 6;
        this.LayerItems.topBar.zIndex = ZOrder.UI + 5
        this.lhBetScript = this.bottomLayer.getChildByName("betPanel").getComponent('LHBetLayer');
    }
    start() {

        this.initSelfInfo();
        this.autoSelectChip();
        this._gameAudio.playMusic();

    }

    onDestroy() {
        let self = this;
        cc.game.off(cc.game.EVENT_HIDE, self.onEnterBackground, self);
        cc.game.off(cc.game.EVENT_SHOW, self.onEnterForgeground, self);
        self.LayerItems.longClickBet.off(cc.Node.EventType.TOUCH_END, self._onLongTouchEnded, this, true);
        self.LayerItems.huClickBet.off(cc.Node.EventType.TOUCH_END, self._onHuTouchEnded, this, true);
        self.LayerItems.heClickBet.off(cc.Node.EventType.TOUCH_END, self._onHeTouchEnded, this, true);
    }
    update(dt) {
        let currentTime = new Date();
        let hours = currentTime.getHours();
        let minutes = currentTime.getMinutes();
        //设置当前时间
        if (this._timeLabel && (hours > this._currentTime[0] || minutes > this._currentTime[1])) {
            this._timeLabel.string = (hours < 9 ? "0" + hours : hours) + ":" + (minutes < 9 ? "0" + minutes : minutes);
            this._currentTime[0] = hours;
            this._currentTime[1] = minutes;
        }
    }
    //------------------界面方法-------------------//
    private _onLongTouchEnded(event: cc.Event.EventTouch) {
        let self = this;
        if(self._checkIfTouchInBetArea(self.LayerItems.longClickBet,event.touch)){
            self.onClickBet(undefined,'0');
        }
    }

    private _onHuTouchEnded(event: cc.Event.EventTouch) {
        let self = this;
        if(self._checkIfTouchInBetArea(self.LayerItems.huClickBet,event.touch)){
            self.onClickBet(undefined,'1');
        }
    }

    private _onHeTouchEnded(event: cc.Event.EventTouch) {
        let self = this;
        if(self._checkIfTouchInBetArea(self.LayerItems.heClickBet,event.touch)){
            self.onClickBet(undefined,'2');
        }
    }

    //下注点击区域是否在多边形内
    private _checkIfTouchInBetArea(betAreaNd: cc.Node, touch: cc.Touch) {
        if (!!betAreaNd && betAreaNd.getComponent(cc.PolygonCollider)) {
            let polygonCo = betAreaNd.getComponent(cc.PolygonCollider);
            let p = betAreaNd.convertToNodeSpaceAR(touch.getLocation());
            return cc.Intersection.pointInPolygon(p, polygonCo.points);
        } else {
            return false;
        }
    }

    public initChouMaData() {

        this.SELECT_CHIP_DATA_CONFIG = [];
        for (let i = 0; i < CHIPS_NUM; i++) {
            let item: LHModel.ChipConfig = this.CHIP_DATA_CONFIG[i];

            this.SELECT_CHIP_DATA_CONFIG.push(item);
        }

    }
    
    //点击成为庄家
    onClickToBankerBtn(){
        let self = this;
        let gameModel = self._gameCore.model;
        if (self.bankerState == 0) {
            if (!gameModel.bankerNeed) {
                self.show(GameTextTips.GAME_SERVER_CONFIG_ERR);
                return;
            }
            if (self.MyInfo.money >= gameModel.bankerNeed) {
                self._gameCore.applyOnBanker();
            } else {
                self.show(self.stringFormat(GameTextTips.GAME_SZ_MONEY_TIP, [self.moneyFormat(gameModel.bankerNeed)]))
                return;
            }
        } else {
            if (self.bankerState == 2) {
                LHAlertHelper.instance.showAlert(GameTextTips.GAME_ZHUANG_XIA_TIP, AlertLayer.BUTTON_STYLE_2, function () {
                    self._gameCore.offBanker();
                });
            } else {
                self._gameCore.cancelApplyOnBanker();
            }
        }
    }

    private init() {


    }
    //初始化牌
    private initCardUI() {
        let self = this;
        let cardName = ["longCard", "huCard", "heCard"];
        let compareNode = ["leftPanel", "rightPanel"];
        let startPos = [0, 0]
        let lookStartPos = [-123, 125];
        for (let i = 0; i < 2; i++) {
            let cardNode: cc.Node = self.mainLayer.getChildByName("cardNode").getChildByName(cardName[i]);
            self.compareTb[i].card = cardNode;
            self.compareTb[i].valueCard = cardNode.getChildByName("valueCard");
            self.compareTb[i].valueCard.active = false;
            self.compareTb[i].winnerIcon = cardNode.getChildByName("winIcon");
            self.compareTb[i].startPos = cc.v2(lookStartPos[i], -21);
            self.compareTb[i].node = self.mainLayer.getChildByName(compareNode[i]);
            self.compareTb[i].node.active = false;
            self.Pokers[i].card = cardNode;
            self.Pokers[i].valueCard = cardNode.getChildByName("valueCard");
            let movePos = cardNode.getPosition();
            self.Pokers[i].startPos = cc.v2(movePos.x - startPos[i], movePos.y);
            self.Pokers[i].movePos = movePos;
            self.compareTb[i].winnerIcon.y = -15;
            self.compareTb[i].winnerIcon.active = false;

        }
        self.compareTb[2].card = self.mainLayer.getChildByName("cardNode").getChildByName(cardName[2]);
        self.compareTb[2].winnerIcon = self.compareTb[2].card.getChildByName("winIcon");
        self.compareTb[2].winnerIcon.y = -15;
        self.compareTb[2].winnerIcon.active = false;

        //初始化牌桌数据
        let desktopName = ["longPanel", "huPanel", "hePanel"]
        let desktopStarName = ["longStarImg", "huStarImg", "heStarImg"]
        self.chipsNode = self.mainLayer.getChildByName("chipsNode");
        for (let i = 0; i < self.TouchBet.length; i++) {
            let baseNode = self._centerLayer.getChildByName(desktopName[i]);
            self.TouchBet[i].node = baseNode;


            self.TouchBet[i].star = baseNode.getChildByName(desktopStarName[i]);
            self.TouchBet[i].total_label = baseNode.getChildByName("totalLabel").getComponent(cc.Label);
            self.TouchBet[i].self_label = baseNode.getChildByName("selfLabel").getComponent(cc.Label);

            self.TouchBet[i].star.active = false;
            self.TouchBet[i].total_label.string = "0";
            self.TouchBet[i].self_label.string = "0";
            self.TouchBet[i].total_number = 0;
            self.TouchBet[i].self_number = 0;

        }
        self.btnOnline = self.bottomLayer.getChildByName("rightPanel").getChildByName("onlineButton");
    }
    //点击下注区域
    private onClickBet(target: any, customEventData: any) {
        let self = this;
        if(DataManager.instance.isNeedPay(Config.GAME_LIST.longhu.gameId)){//没有充值，提示充值
            HallManager.instance.openCharge({quickPayStyle:quickPayStyle.gameVipPay});
            return;
        }
        if (typeof (customEventData) == "string" && customEventData != "") {
            let area = Common.toInt(customEventData);
            if (this.BankerInfo.playerid != self.MyInfo.playerid) {
                if (this.isStartBet) {
                    let selIndex = self.lhBetScript.getSlectIndex();
                    let gameModel = self._gameCore.model;
                    this._gameAudio.playClick();
                    if (selIndex > -1) {
                        let selectScore: number = self.SELECT_CONFIG[selIndex];
                        if (selectScore < 1) {
                            self.show(GameTextTips.GAME_SERVER_CONFIG_ERR)
                            return;
                        }
                        let myMoney = self.MyInfo.money;
                        if (myMoney < gameModel.betNeed) {
                            // self.show(self.stringFormat(GameTextTips.GAME_BET_MIN_ERR, [gameModel.betNeed]));
                            HallManager.instance.openCharge({quickPayStyle:quickPayStyle.gameQuickPay});
                            return;
                        }
                        if (myMoney >= selectScore) {
                            
                            let myAllBet = 0;
                            for (let i = 0; i < self.TouchBet.length; i++) {
                                myAllBet = self.TouchBet[i].self_number + myAllBet;
                            }
                            myAllBet += selectScore;
                            let MaxBet = gameModel.BetMax;
                            if (MaxBet && myAllBet >= MaxBet) {
                                this.show(GameTextTips.GAME_BET_MONEY_MAX_TIP);
                                return;
                            }

                            if(self.TouchBet[area].total_number + selectScore > this._gameCore.model.betLimit[(area + 1) + '']){
                                this.show(GameTextTips.GOLD_BET_MAX_ERR);
                                return;
                            }


                            self._gameCore.sendBet(area + 1, selectScore);
                            //自家头像移动
                            // self.userMyselfBetHeadMove(self.MyInfo);

                            let areaBet = self.TouchBet[area].self_number + (selectScore)
                            self.MyInfo.money = self.MyInfo.money - selectScore;
                            self.TouchBet[area].self_label.string = self.moneyFormat(areaBet) + "";        //--我在这个区域下的总注
                            self.TouchBet[area].self_number = areaBet;
                            self.TouchBet[area].self_label.node.active = true;
                            self.MyInfo.score.getComponent(cc.Label).string = self.moneyFormat(self.MyInfo.money,2);// :setString(utils:moneyString(self.MyInfo.money));
                            self.deskChipMove(selectScore, self.MyInfo.head, area, function () { },true);
                            self.autoSelectChip();

                        }
                    } else {
                        if (self.SELECT_CONFIG.length <= 0) {
                            self.show(GameTextTips.GAME_SERVER_CONFIG_ERR);
                            return;
                        }
                        self.show(GameTextTips.GAME_PLEASE_SELECT_GOLD_TIP);
                    }
                }
            } else {
                if (this.isStartBet) {
                    this.show(GameTextTips.GAME_ZHUANG_NOT_XZ_TIP);
                }
            }
        }

    }
    //初始化庄家
    private initZhuangUI() {
        let self = this;
        //庄家信息
        self.BankerInfo.node = this.LayerItems.mainUser
        self.BankerInfo.headBg = this.LayerItems.bankerHeadBg;
        self.BankerInfo.head = self.BankerInfo.headBg.getChildByName("imgMask").getChildByName("headImg");
        let userMainPanel: cc.Node = self.BankerInfo.node.getChildByName("mainPanel")
        self.BankerInfo.bankerCount = userMainPanel.getChildByName("nameLabel");
        self.BankerInfo.nickName = userMainPanel.getChildByName("gameLabel");
        self.BankerInfo.score = userMainPanel.getChildByName("goldPanel").getChildByName("goldValue");
        self.BankerInfo.fnt_WinScore = self.BankerInfo.headBg.getChildByName("winScore");
        self.BankerInfo.fnt_LoseScore = self.BankerInfo.headBg.getChildByName("loseScore");
        self.BankerInfo.fnt_WinAnimation = self.BankerInfo.headBg.getChildByName("winAnimation");
        if (self.BankerInfo.fnt_LoseScore && self.BankerInfo.fnt_WinScore) {
            self.BankerInfo.fnt_WinScore_startPos = self.BankerInfo.fnt_WinScore.getPosition();
            self.BankerInfo.fnt_LoseScore_startPos = self.BankerInfo.fnt_LoseScore.getPosition();
            self.BankerInfo.fnt_WinScore.active = false;
            self.BankerInfo.fnt_LoseScore.active = false;
        }
        self.BankerInfo.alterUserInfo = self.node.getChildByName("bankerAlterUserInfo");
        self.BankerInfo.alterUserInfo.active = false;
        // self.BankerInfo.fnt_WinAnimation.active = false;

        self.BankerInfo.node.getChildByName("mainPanel").active = false;
        // self.BankerInfo.headBg.active = false;
        let mainPanel = self.BankerInfo.node.getChildByName("mainPanel");
        mainPanel.getChildByName("gameLabel").active = false;
        mainPanel.getChildByName("goldPanel").getChildByName("goldValue").getComponent(cc.Label).string = self.moneyFormat(9999900)
        mainPanel.getChildByName("nameLabel").active = false;
        // self.BankerInfo.head.getComponent(cc.Sprite).spriteFrame = self.gameAtlas.getSpriteFrame("head_zhuang")


        self.BankerInfo.node.active = true;
        self.BankerInfo.headBg.on(cc.Node.EventType.TOUCH_START, function () {
            self.onClickHead(undefined,LHD.Const.customBankerPos + "");
        });
        //system坐庄数据
        self.BankerSystemInfo.node = self.BankerInfo.node.getChildByName("systemPanel");
        self.BankerSystemInfo.node.active = true;
        self.BankerSystemInfo.fnt_WinScore = self.BankerSystemInfo.node.getChildByName("winScore");
        self.BankerSystemInfo.fnt_LoseScore = self.BankerSystemInfo.node.getChildByName("loseScore");
        if (self.BankerSystemInfo.fnt_LoseScore && self.BankerSystemInfo.fnt_WinScore) {
            self.BankerSystemInfo.fnt_WinScore_startPos = self.BankerSystemInfo.fnt_WinScore.getPosition();
            self.BankerSystemInfo.fnt_LoseScore_startPos = self.BankerSystemInfo.fnt_LoseScore.getPosition();
            self.BankerSystemInfo.fnt_WinScore.active = false;
            self.BankerSystemInfo.fnt_LoseScore.active = false;
        }
    }
    private initPlayerUI() {
        let self = this;
        //--自己的信息
        // let myinfoPanel: cc.Node = self.bottomLayer.getChildByName("leftPanel");
        self.MyInfo.node = self.LayerItems.myselfNode;
        let myHeadNode: cc.Node = self.MyInfo.node.getChildByName("headBg");
        self.MyInfo.head = myHeadNode.getChildByName("imgMask").getChildByName("headImg");
        self.MyInfo.headBg = myHeadNode;
        self.MyInfo.score = self.MyInfo.node.getChildByName("infoPanel").getChildByName("goldPanel").getChildByName("goldValue");
        self.MyInfo.nickName = self.MyInfo.node.getChildByName("infoPanel").getChildByName("gameIdLabel");

        self.MyInfo.fnt_LoseScore = myHeadNode.getChildByName("loseScore");
        self.MyInfo.fnt_WinScore = myHeadNode.getChildByName("winScore");
        self.MyInfo.fnt_WinAnimation = myHeadNode.getChildByName("winAnimation");
        self.MyInfo.fnt_WinScore.zIndex = 20;
        self.MyInfo.fnt_LoseScore.zIndex = 20;
        self.MyInfo.fnt_WinScore.active = false;
        self.MyInfo.fnt_LoseScore.active = false;
        if (self.MyInfo.fnt_LoseScore && self.MyInfo.fnt_WinScore) {
            self.MyInfo.fnt_LoseScore_startPos = self.MyInfo.fnt_LoseScore.getPosition();
            self.MyInfo.fnt_WinScore_startPos = self.MyInfo.fnt_WinScore.getPosition();
        }


        //--其他玩家
        let userLeftPanel: cc.Node = self.mainLayer.getChildByName("leftUser");
        let userRightPanel: cc.Node = self.mainLayer.getChildByName("rightUser");
        let userIndex = 0;
        let setNode: cc.Node;
        updateGameTextTips();
        //头像预处理文件
        for (let i = 0; i < self.Players.length; i++) {

            userIndex = i;

            if (i%2 == 1) {
                setNode = userRightPanel;
            }
            else {
                setNode = userLeftPanel;
            }
            let userNode = setNode.getChildByName("user" + userIndex);
            if (!userNode) {
                continue;
            }
            let playerNode = userNode.getChildByName("userCommon");
            if(!playerNode){
                playerNode = cc.instantiate(this.userPrefab);
                userNode.addChild(playerNode);
            }

            self.Players[i].node = playerNode;
            self.Players[i].img_Null = playerNode.getChildByName("notUserBg");
            if(!!self.Players[i].img_Null){
                self.Players[i].img_Null.getChildByName('empty').getComponent(cc.Label).string = GameTextTips.lh_seat_empty;
            }

            self.Players[i].userInfo = playerNode.getChildByName("userInfo");
            // self.Players[i].userInfo.active = false;
            let headNode: cc.Node = self.Players[i].userInfo.getChildByName("headBg");
            self.Players[i].head = headNode.getChildByName("imgMask").getChildByName("headImg");

            if(i <= 1){//0~1号头像位置 大富豪及神算子 更换对应头像框
                let headFrame = headNode.getChildByName("headFrame");
                headFrame.getComponent(cc.Sprite).spriteFrame = this.mainUiAtlas.getSpriteFrame(`lh_head_bg${i}`);
                headFrame.position = cc.v3(0,14);
            }

            self.Players[i].headBg = headNode;
            self.Players[i].nickName = headNode.getChildByName("nameBg").getChildByName("nameLabel");
            // self.Players[i].headIcon = self.Players[i].userInfo.getChildByName("headIcon");
            self.Players[i].score = self.Players[i].userInfo.getChildByName("goldPanel").getChildByName("goldValue");
            self.Players[i].fnt_LoseScore = self.Players[i].userInfo.getChildByName("loseScore");

            self.Players[i].fnt_WinAnimation = self.Players[i].userInfo.getChildByName("winAnimation");
            self.Players[i].fnt_WinScore = self.Players[i].userInfo.getChildByName("winScore");
            self.Players[i].fnt_LoseScore_startPos = self.Players[i].fnt_LoseScore.getPosition();
            self.Players[i].fnt_WinScore_startPos = self.Players[i].fnt_WinScore.getPosition();
            self.Players[i].chairid = i;
            self.Players[i].startPos = self.Players[i].node.getPosition();
            self.Players[i].fnt_LoseScore.zIndex = 20;
            self.Players[i].fnt_WinScore.zIndex = 20;

            // self.Players[i].headIcon.active = true;

            self.Players[i].img_Null.active = true;
            // self.Players[i].fnt_WinAnimation.active = false;
            self.Players[i].fnt_WinScore.active = false;
            self.Players[i].fnt_LoseScore.active = false;
            let alertUserNode = new cc.Node("alertUserNode");
            let userAlert: cc.Node;
            if (i%2 == 1) {
                userAlert = playerNode.getChildByName("rightAlterUserInfo");
            } else {

                userAlert = playerNode.getChildByName("leftAlterUserInfo");
            }
            alertUserNode.setPosition(self.node.convertToNodeSpaceAR(userAlert.convertToWorldSpaceAR(userAlert.getAnchorPoint())));
            this.node.addChild(alertUserNode);
            self.Players[i].alertUserInfo = alertUserNode;
            self.Players[i].headBg.on(cc.Node.EventType.TOUCH_START, function () {
                self.onClickHead(undefined,i + "");                
            });
            self.Players[i].fnt_WinScore.active = false;
            self.Players[i].fnt_LoseScore.active = false;
            self.Players[i].alertUserInfo.zIndex = 99
            self.Players[i].alertUserInfo.active = false;
        }
    }
    setUserInfo(userinfo: any, player: cc.Node, hasBanker?: boolean) {
        //dump(userinfo, "传入的个人信息：");
        let self = this;
        if (!userinfo.isOpenUserInfo) {
            let showPos = this.mainLayer.convertToNodeSpaceAR(player.convertToWorldSpaceAR(player.getAnchorPoint()));

            let showNode = UIHelper.createPlayerDetails(userinfo, function () {
                userinfo.isOpenUserInfo = false;
                player.active = false;
            });
            if (hasBanker == true) {
                player.removeAllChildren();
                player.addChild(showNode);
                player.active = true;
            } else {

                showNode.setPosition(showPos);
                this.mainLayer.addChild(showNode);
            }
            userinfo.isOpenUserInfo = true;
            // this.node.addChild(showNode);
            //self.openUserInfo(userinfo,player);
        }


        // let city = userinfo.city;
        // if (city == ""){
        //     city =GameTextTips.GAME_CITY_NOT_TEXT;// "未知";
        // }
        //  player.getChildByName("gameLabel").getComponent(cc.Label).string =self._gameLogic.strClamp(userinfo.nickname);
        //  player.getChildByName("let Box").getChildByName("valueLabel").getComponent(cc.Label).string = city;
        //  super.setPlayerHead(player.getChildByName("headBg").getChildByName("imgMask").getChildByName("headImg").getComponent(cc.Sprite),userinfo.headid,userinfo.wxheadurl);

    }

    private initSelfInfo() {
        let self = this;
        self.MyInfo.money = this._gameCore.userInfo.money;
        let userInfo = this._gameCore.userInfo;
        super.setPlayerHead(self.MyInfo.head.getComponent(cc.Sprite), userInfo.headid, userInfo.wxheadurl);
        self.MyInfo.nickName.getComponent(cc.Label).string = this._gameLogic.strClamp(userInfo.nickname);//utils:nameStandardString(tostring(self.model.myInfo.nickname), 22, 165)
        self.MyInfo.score.getComponent(cc.Label).string = this.moneyFormat(self.MyInfo.money,2);//utils:moneyString(self.model.myInfo.money)
        self.MyInfo.playerid = userInfo.playerid;
    }


    //设置下注筹码值
    private betString(betNum: number) {
        return this.moneyFormat(betNum) + "";
    }
    public onBackHall() {
        if (this._isOpenMenu == true) {
            this._isOpenMenu = false;
            UIHelper.clearAll();
            return;
        }
        let self = this;
        let text = GameTextTips.GAME_BET_EXIT_ERR;
        if (self.MyInfo.playerid == self.BankerInfo.playerid) {
            LHAlertHelper.show(GameTextTips.GAME_BANKER_EXIT_ERR)
            return;
        }
        if (self.MyInfo.isBet) {
            LHAlertHelper.confirm(text, function () {
                self.chipsNode.stopAllActions();
                self._centerLayer.stopAllActions();
                // self.chipsNode.removeAllChildren();
                self.putAllPoolChip();
                self._gameCore.quitGame();
            }, null);
            return;
        }
        if (self.MyInfo.isBankerList) {
            text = GameTextTips.GAME_APPLY_EXIT_ERR;
            LHAlertHelper.confirm(text, function () {
                self.chipsNode.stopAllActions();
                self._centerLayer.stopAllActions();
                // self.chipsNode.removeAllChildren();
                self.putAllPoolChip();
                self._gameCore.quitGame();
            }, null);
            return;
        }
        if (self.updateScheduler) {
            this.unschedule(self.updateScheduler);
            this.updateScheduler = null;
        }
        this.LayerItems.onlineBetNode.stopAllActions();
        this.chipsNode.stopAllActions();
        this.putAllPoolChip();
        // self.chipsNode.removeAllChildren();
        this._centerLayer.stopAllActions();

        self._gameCore.quitGame();
    }

    //回收筹码
    putAllPoolChip(){
        for(let i = 0;i < this.chipPoolList.length;i++){
            let item = this.chipPoolList[i];
             LHChipPoolAssisCom.instance.put(item);
        }
        this.chipPoolList = [];
        this.chipsNode.removeAllChildren();
    }

    public openUserInfo(user: any, player: cc.Node) {
        ////throw new Error("Method not implemented.");
        let self = this;
        if (player.active == false) {
            self._gameLogic.showLayer(player);
            cc.tween(player)
                .delay(3)
                .call(function () {
                    user.isOpenUserInfo = false;
                    self._gameLogic.hideLayer(player);
                }).start();
        }
    }
    public getUserInfo(playerid: any) {
        return this.allUsers[playerid];
    }

    //显示帮助
    onClickShowHelpLayer(){
        // this.LayerItems.onHelpPanel.active = true;
        if(!this.onHelpPanel){//未创建过
            this.onHelpPanel = cc.instantiate(this.helpLayer);
            this.LayerItems.onHelpPanel.addChild(this.onHelpPanel);
        }

        let self = this;
        // this._gameLogic.showLayer(this.onHelpPanel,(()=>{
        //     this.onHelpPanel.getComponent('LHHelpLayer').setData(true,this.hideHelpLayer.bind(self));
        // }));
        this._gameLogic.popUpEffect(this.onHelpPanel,this.LayerItems.onHelpPanel,true,(()=>{
            self.onHelpPanel.getComponent('LHHelpLayer').setData(true,this.hideHelpLayer.bind(self));
        }),this.LayerItems.onHelpPanel.getChildByName('mask'));
    }

    //隐藏帮助
    hideHelpLayer(){
        // this.LayerItems.onHelpPanel.active = false;
        // this._gameLogic.hideLayer(this.onHelpPanel);
        let self = this;
        this._gameLogic.popUpEffect(this.onHelpPanel,this.LayerItems.onHelpPanel,false,(()=>{
            self.onHelpPanel.getComponent('LHHelpLayer').setScrollToTop();
        }),this.LayerItems.onHelpPanel.getChildByName('mask'));
    }


    //显示提示框
    public show(text: string, time: number = 2,bgType: number = 1) {
        LHToastHelper.instance.showToast(text, time,bgType);

    }
    
    //隐藏筹码设置
    public hideSetChips() {
        let setBetPanel = this.alterLayer.getChildByName("setBetPanel");

        this._gameLogic.hideLayer(setBetPanel)
    }
    
    /**
     * 弹出充值面板
     */
    private onClickRecharge() {
        // this._gameCore.openPayCenter();
        HallManager.instance.openCharge();
    }
    //显示游戏状态提示框
    public showTipWait() {
        if (this._tipWaitLayer) {
            this._tipWaitLayer.active = true;
            this.runWaitNextDotAni()
        }
    }
    //隐藏游戏状态提示框
    public hideTipWait() {
        if (this._tipWaitLayer) {
            this._tipWaitLayer.active = false;
            this.LayerItems.dotNode.stopAllActions();
        }
    }
    //显示历史记录
    public showHistory() {
        // this.LayerItems.historyPanel.active = true;
        if(!this._historyLayer){//未创建过
            this._historyLayer = cc.instantiate(this.zoushiLayer);
            this.LayerItems.historyPanel.addChild(this._historyLayer);
        }

        let self = this;
        // this._gameLogic.showLayer(this._historyLayer,(()=>{
        //     self._gameZoushi.show();
        // }));
        this._gameLogic.popUpEffect(this._historyLayer,this.LayerItems.historyPanel,true,(()=>{
            self._gameZoushi.show();
        }),this.LayerItems.historyPanel.getChildByName('mask'));
    }
    //隐藏历史记录
    hideHistory() {
        // this.LayerItems.historyPanel.active = false;
        // this._gameLogic.hideLayer(this._historyLayer);
        let self = this;
        this._gameLogic.popUpEffect(this._historyLayer,this.LayerItems.historyPanel,false,(()=>{
            self._gameZoushi.hide();
        }),this.LayerItems.historyPanel.getChildByName('mask'));
    }
    //显示在线玩家
    public showOnlineUser() {
        let self = this;
        if(self._clickOtherLinePlayerLayer){//防止重复点击
            self.scheduleOnce(()=>{
                !!self && !!self._clickOtherLinePlayerLayer && (self._clickOtherLinePlayerLayer = false);
            },2)
            return;
        }
        self._clickOtherLinePlayerLayer = true;
        self._gameCore.requestAllPlayerList();
    }

    //接收玩家信息后显示界面
    showOnlineUserEnd(){
        let self = this;
        if(!self._clickOtherLinePlayerLayer){
            return;
        }
        self._clickOtherLinePlayerLayer = false;

        let bShow: boolean = this.LayerItems.onlinePanel.active;
        !bShow && (this.LayerItems.onlinePanel.active = true);
        
        if(!this.onlinePanelLayer){//未创建过
            this.onlinePanelLayer = cc.instantiate(this.onLinePlayersLayer);
            this.LayerItems.onlinePanel.addChild(this.onlinePanelLayer);
        }
        if(bShow){//已经显示的界面 点击下一页时，不用弹出效果
            self.onlinePanelLayer.getComponent('LHOnlinePlayersLayer').setData(self,self._gameCore,self.playerListData,true,self.hideOnlineUser.bind(self));
        }
        else{
            this._gameLogic.popUpEffect(this.onlinePanelLayer,this.LayerItems.onlinePanel,true,(()=>{
                self.onlinePanelLayer.getComponent('LHOnlinePlayersLayer').setData(self,self._gameCore,self.playerListData,true,self.hideOnlineUser.bind(self));
            }),this.LayerItems.onlinePanel.getChildByName('mask'));

            // this._gameLogic.showLayer(this.onlinePanelLayer,(()=>{
            //     self.onlinePanelLayer.getComponent('LHOnlinePlayersLayer').setData(self,self._gameCore,self.playerListData,true,self.hideOnlineUser.bind(self));
            // }));
        }
    }

    //更新点击在线其它玩家界面状态
    updateOnlineLayerState(){
        let self = this;
        self._clickOtherLinePlayerLayer = true;
    }

    //隐藏在线玩家
    public hideOnlineUser() {
        this._clickOtherLinePlayerLayer = false;
        this._gameLogic.popUpEffect(this.onlinePanelLayer,this.LayerItems.onlinePanel,false,null,this.LayerItems.onlinePanel.getChildByName('mask'));

        // this.LayerItems.onlinePanel.active = false;
        // this._gameLogic.hideLayer(this.onlinePanelLayer);
    }
    //显示上庄列表
    private showMainUser() {
        let self = this;
        //验证当前是否上庄中
        if (self.bankerState == 2) {
            LHAlertHelper.instance.showAlert(GameTextTips.GAME_ZHUANG_XIA_TIP, AlertLayer.BUTTON_STYLE_2, function () {
                self._gameCore.offBanker();
            });
            // self._gameLogic.showLayer(this.alterLayer.getChildByName("cancelZhuangPanel"));
        } else {
            //设置上庄条件
            self.LayerItems.descRich1.getComponent(cc.RichText).string = self.stringFormat(GameTextTips.lh_bankerTips1,[self.moneyFormat(self._gameCore.model.bankerNeed)]);
            self.LayerItems.descRich3.getComponent(cc.RichText).string = self.stringFormat(GameTextTips.lh_bankerTips3,[self.moneyFormat(self._gameCore.model.bankerNeed)]);
            self.LayerItems.descRich2.getComponent(cc.RichText).string = self.stringFormat(GameTextTips.lh_bankerTips2,[0,10]);//排队人数

            self.loadBankerList();
            // self._gameLogic.showLayer(this._mainUserLayer);
            this._gameLogic.popUpEffect(this.LayerItems.mainBg,this.LayerItems.setMainPanel,true,null,this.LayerItems.setMainPanel.getChildByName('mask'));
        }

    }
    //隐藏上庄列表
    public hideMainUser() {
        // this._gameLogic.hideLayer(this._mainUserLayer);
        this._gameLogic.popUpEffect(this.LayerItems.mainBg,this.LayerItems.setMainPanel,false,null,this.LayerItems.setMainPanel.getChildByName('mask'));
    }


    public getOnlineCount() {
        // let playerCount:number = self._gameCore.getPlayerCount();
        // if(this._onlineCountLabel) this._onlineCountLabel.string = GameTextTips.ONLINE_COUNT+ playerCount;

    }
    public hideMessageBox() {
        this._gameLogic.hideLayer(this._messageBoxLayer);
    }
    public okMessageBox() {
        this._gameLogic.hideLayer(this._messageBoxLayer);
        if (this._callBackMessage) {
            this._callBackMessage();
        }
    }
    public messageBox(msgContent: string, callBack: () => void, isHideCloseBtn: boolean = false) {
        LHAlertHelper.show(msgContent, callBack);
    }
    //隐藏等待界面
    public hideWaitAct() {
        if (this._tipWaitLayer) {
            this._tipWaitLayer.active = false;
            this.LayerItems.dotNode.stopAllActions();
        }
    }
    //---------------------网络事件回调---------------------//
    //进入后台
    onEnterBackground() {
        this._gameAudio.setOnEnterBackgroundFlag(true);
        this.chipsNode.stopAllActions();
        this._centerLayer.stopAllActions();
        this.node.stopAllActions();

        //throw new Error("Method not implemented.");
    }
    //切换前台
    onEnterForgeground() {
        this._gameAudio.setOnEnterBackgroundFlag(false);
    }

    updateUserScore(msg: any) {
        let userInfo = this.allUsers[msg.playerid];
        if (userInfo) {
            userInfo.coin = msg.coin;
            userInfo.money = msg.coin;
        }
    }
    updateOtherScore(playerid: number, score: number) {
        for (let i = 0; i < this.Players.length; i++) {
            if (playerid == this.Players[i].playerid) {
                let scoreLabel: cc.Label = this.Players[i].score.getComponent(cc.Label);
                let decimals = 0;
                if(playerid == this.MyInfo.playerid){
                    decimals = 2;
                }
                scoreLabel.string = '₹ ' + this.moneyFormat(score,decimals) + "";
            }

        }
    }


    updateMyScore(score: number) {
        let self = this;
        self.MyInfo.money = score;
        this._gameCore.userInfo.money = score;
        cc.tween(this.MyInfo.score)
            .delay(1.3)
            .call(function () {
                let scoreLabel: cc.Label = self.MyInfo.score.getComponent(cc.Label);
                scoreLabel.string = self.moneyFormat(score,2) + "";
            })
            .start();
        let currentState = LHD.LHD_GameState.LHD_GameState_BuyHorse;
        if (self.gameState == currentState) {
            self.autoSelectChip();
        }

    }

    //更新下注数据
    updateBetData(msg: {}) {
        for (let i = 0; i < this.TouchBet.length; i++) {
            this.TouchBet[i].self_label.string = this.betString(msg[i]);
            if (msg[i] > 0) {
                this.TouchBet[i].self_label.node.active = true;
            }
        }
    }
    //下注成功
    myBetSuccess(msg: any) {
        let self = this;
        self.TouchBet[0].self_number = msg.long;
        self.TouchBet[1].self_number = msg.hu;
        self.TouchBet[2].self_number = msg.he;
        self.TouchBet[0].self_label.string = this.betString(msg.long);
        self.TouchBet[1].self_label.string = this.betString(msg.hu);
        self.TouchBet[2].self_label.string = this.betString(msg.he);
        if (msg.long > 0) {
            self.TouchBet[0].self_label.node.active = true;
        }
        if (msg.hu > 0) {
            self.TouchBet[1].self_label.node.active = true;
        }
        if (msg.he > 0) {
            self.TouchBet[2].self_label.node.active = true;
        }
        let all_long = self.TouchBet[0].total_number;
        let all_hu = self.TouchBet[1].total_number;
        let all_he = self.TouchBet[2].total_number;

        if (all_long < msg.long_total) {
            self.TouchBet[0].total_label.string = this.betString(msg.long_total);
            self.TouchBet[0].total_number = msg.long_total;
        }
        if (all_hu < msg.hu_total) {
            self.TouchBet[1].total_label.string = this.betString(msg.hu_total);
            self.TouchBet[1].total_number = msg.hu_total;
        }
        if (all_he < msg.he_total) {
            self.TouchBet[2].total_label.string = this.betString(msg.he_total);
            self.TouchBet[2].total_number = msg.he_total;
        }
        self.MyInfo.score.getComponent(cc.Label).string = this.moneyFormat(msg.chouma,2) + "";
        this._gameCore.userInfo.money = msg.chouma;
        self.MyInfo.money = msg.chouma;
        self.MyInfo.isBet = true;
        self.autoSelectChip();
    }



    //结算特效
    resultAct(player) {

        if(!player || !player.fnt_WinAnimation){
            return;
        }
        player.fnt_WinAnimation.active = true;
        let spine: sp.Skeleton = player.fnt_WinAnimation.getComponent(sp.Skeleton);
        // spine.timeScale = 0.8;
        spine.setAnimation(0,'animation',false);
        // cc.tween(player.fnt_WinAnimation)
        // .delay(2)
        // .call(()=>{
        //     !!player && !!player.fnt_WinAnimation && (player.fnt_WinAnimation.active = false);
        // })
        // .start();
        spine.setCompleteListener(()=>{
            player.fnt_WinAnimation.active = false;
        })
    }
    //数字飞动
    flyNumber(node: cc.Node) {
        //--node:setScale(0.3);
        let oldPos = node.getPosition();

        let move = cc.tween().to(0.4, { position: cc.v2(oldPos.x, oldPos.y + 40) })
        let delay = cc.tween().delay(2);
        node.stopAllActions();
        cc.tween(node)
            .to(0.4, { position: cc.v3(oldPos.x, oldPos.y + 40) })
            .delay(2).call(function () {
                node.active = false;
            })
            .start();
    }

    //结算数字飘动
    resultFlyNumber(player: any, playerid, nChange, playercoin,bDeskPlayerFlag: boolean = false) {
        //--print("\n{\n数字飞动：在桌子上赢了的玩家userID为：", player.playerid, playerid, nChange);
        if(!!player && !!player.img_Null){
            if(!!player.img_Null.active){//人走了 不飘分
                return;
            }
        }
        if (player.playerid == playerid) {
            //--print("数字飞动：", utils:moneyString(nChange), "\n}\n");
            let scoreLabel: cc.Label;
            if (nChange > 0) {
                scoreLabel = player.fnt_WinScore.getChildByName("scoreLabel").getComponent(cc.Label);
                scoreLabel.string = "+" + this.moneyFormat(nChange);
                if (scoreLabel.string.length > 6) {
                    player.fnt_WinScore.scale = 0.8;
                } else if (scoreLabel.string.length > 5) {
                    player.fnt_WinScore.scale = 0.9;
                } else {
                    player.fnt_WinScore.scale = 1;
                }
                player.fnt_WinScore.active = true;
                this.flyNumber(player.fnt_WinScore);
            } else if (nChange < 0) {
                scoreLabel = player.fnt_LoseScore.getChildByName("scoreLabel").getComponent(cc.Label);
                scoreLabel.string = this.moneyFormat(nChange);
                if (scoreLabel.string.length > 6) {
                    player.fnt_LoseScore.scale = 0.8;
                } else if (scoreLabel.string.length > 5) {
                    player.fnt_LoseScore.scale = 0.9;
                } else {
                    player.fnt_LoseScore.scale = 1;
                }
                player.fnt_LoseScore.active = true;
                this.flyNumber(player.fnt_LoseScore);
            }
            if(!!bDeskPlayerFlag){
                let decimals = 0;
                if(playerid == this.MyInfo.playerid){
                    decimals = 2;
                }
                player.score.getComponent(cc.Label).string = "₹ " + this.moneyFormat(playercoin,decimals);
            }
            else{
                player.score.getComponent(cc.Label).string = this.moneyFormat(playercoin,2);//utils:moneyString(playercoin);
            }
        }

    }

    onSaveUserInfo(playerlist: any) {
        for (let i in playerlist) {
            this.allUsers[playerlist[i].playerid] = playerlist[i];
        }
        //--请求玩家列表
        this._gameCore.requestAllPlayerList();
    }
    //玩家进入
    onUserEnter(info: any) {
        let user = this.getUserInfo(info.palyerid);
        if (!user) {
            this.allUsers[info.playerid] = info;
        }
    }
    //玩家离开
    onUserLeave(info: any) {
        let user = this.getUserInfo(info.palyerid);
        if (user) {
            this.allUsers[info.playerid] = null;
        }

    }
    //开始下注
    onStartBet() {
        //print("\n开始下注============================\n"); 
        let self = this;
        // self.chipsNode.removeAllChildren();
        self.LayerItems.winSoundNode.removeAllChildren();
        for (let i = 0; i < self.Chips.length; i++) {
            for (let j = 0; j < self.Chips[i].length; j++) {
                if (self.Chips[i][j]) {
                    // self.Chips[i][j].node.removeFromParent()
                    LHChipPoolAssisCom.instance.put(self.Chips[i][j].node)
                }
            }
            self.Chips[i] = [];
        }
        
        self.betTimeOut();
        let stateImg = this.LayerItems.img_start
        stateImg.active = true;
        this.LayerItems.img_stop.active = false;
        self.playStartAct();
        let size = { width: stateImg.width, height: stateImg.height };
        let sceneSize = SceneManager.instance.sceneSize;
        self.statePanel.x = 0 - ((sceneSize.width / 2) - size.width / 2);
        let moveTo1 = cc.tween().to(0.3, { position: cc.v3(0, 0) }, { easing: "expoOut" });
        let moveTo2 = cc.tween().to(0.3, { position: cc.v3(size.width + size.width / 2, 0) }, { easing: "expoIn" });
        let delay = cc.tween().delay(0.5);
        this.scheduleOnce(function () {
            this.isStartBet = true;
            self._gameAudio.playStartWav();
            self.statePanel.active = true;
            cc.tween(self.statePanel)
                .then(moveTo1)
                .then(delay)
                .then(moveTo2)
                .call(function () {
                    self.statePanel.active = false;
                })
                .start();
        }, 0.7);



    }
    //下注倒计时
    betTimeOut() {
        let self = this;
        if (self.betScheduler) {
            self.unschedule(self.betScheduler);
            self.betScheduler = null;
        }

        let gameTimePanel: cc.Node = this._centerLayer.getChildByName("gameTime");
        let gameTimeVale: cc.Label = gameTimePanel.getChildByName("timeValue").getComponent(cc.Label)
        self.currentTimeOut = this._gameCore.model.BET_TIME;
        gameTimeVale.string = self.currentTimeOut + "";
        gameTimePanel.active = true;
        self.betScheduler = function () {
            //--yg:调整读秒显示问题
            self.currentTimeOut = self.currentTimeOut - 1;
            gameTimeVale.string = (self.currentTimeOut < 0 ? 0 : self.currentTimeOut)  + "";
            if (0 < self.currentTimeOut && self.currentTimeOut <= 3) {
                self._gameAudio.playCountDown();
            }
            //--yg:提前一秒不能再下注，因为丢筹码预先表现了，放在最后一秒下注导致最后数据不一致
            if (self.currentTimeOut <= 1) {
                //gameTimePanel.active=false;
                if(self.gameState == LHD.LHD_GameState.LHD_GameState_BuyHorse){//下注倒计时0秒时
                    // self.isStartBet = false;
                    // console.error('倒计时时间..........' + Common.formatDateTimeNormal())
                }
                //self.lhBetScript.disableChips(true);
            }
            if (self.currentTimeOut < 0) {
                gameTimePanel.active = false;
                if (self.betScheduler) {
                    self.unschedule(self.betScheduler);
                    self.betScheduler = null;
                }
            }
        }
        this.schedule(self.betScheduler, 1);

    }
    //播放开始游戏动效
    playStartAct() {
        this.hideTipWait();
        this._gameAudio.playAlert();
        let vsNode: cc.Node = this.mainLayer.getChildByName("vsPanel");
        let vsSpine: sp.Skeleton = vsNode.getComponent(sp.Skeleton);
        vsNode.active = true;
        vsSpine.setAnimation(0, "animation", false);
        cc.tween(vsNode).delay(1.1).call(function () {
            vsNode.active = false;
        }).start();

    }
    //停止下注
    onStopBet() {
        let self = this;

        self.LayerItems.onlineBetNode.stopAllActions();
        self.flyingOnlineBets =[];

        this._gameAudio.playAlert();
        self.chipsCount = 0;
        self.isStartBet = false;
        if (self.updateScheduler) {
            self.unschedule(self.updateScheduler);
            self.updateScheduler = null;
        }
        
        if (self.betScheduler) {
            self.currentTimeOut = 0;
            self.betScheduler();
            self.unschedule(self.betScheduler);
            self.betScheduler = null;
        }
        let stateImg = this.LayerItems.img_stop
        stateImg.active = true;
        this.LayerItems.img_start.active = false;

        this._gameAudio.playStopWav();

        let size = { width: stateImg.width, height: stateImg.height };
        let sceneSize = SceneManager.instance.sceneSize;
        self.statePanel.x = 0 - ((sceneSize.width / 2) - size.width / 2);
        self.statePanel.active = true;
        let moveTo1 = cc.tween().to(0.3, { position: cc.v3(0, 0) }, { easing: "expoOut" });
        let moveTo2 = cc.tween().to(0.3, { position: cc.v3(size.width + size.width / 2, 0) }, { easing: "expoIn" });
        let delay = cc.tween().delay(0.5);
        cc.tween(self.statePanel)
            .then(moveTo1)
            .then(delay)
            .then(moveTo2)
            .call(function () {
                self.statePanel.active = false;
            })
            .start();
    }
    //休息时间
    onSleep() {
        let self = this;
        // let delayTime = 2;
        // console.error('发牌时间..........' + Common.formatDateTimeNormal())
        self.LayerItems.sendCardAniNode.stopAllActions();
        cc.tween(self.LayerItems.sendCardAniNode)//.delay(delayTime)
            // .call(function () {
                // if (self.isChangeBanker) {
                //     // console.error('发牌时间3333..........' + Common.formatDateTimeNormal())
                //     cc.tween(self.LayerItems.sendCardAniNode)
                //         .delay(1.4)
                //         .call(function () {
                //             self.sendCard();
                //             self.hideTipWait();
                //         })
                //         .start();

                //     self.isChangeBanker = false;
                // } else {
                    // console.error('发牌时间22222..........' + Common.formatDateTimeNormal())
                    // cc.tween(self.LayerItems.sendCardAniNode)
                        .delay(3)
                        .call(function () {
                            self.sendCard();
                            self.hideTipWait();
                        })
                        .start();
                    // console.error('发牌时间..........' + Common.formatDateTimeNormal())
                    // self.sendCard();
                    // self.hideTipWait();
                // }
                // self.isChangeBanker && (self.isChangeBanker = false);
            // })
            // .start();

    }
    //--ASCII表中，3，4，5，6分别代表红桃，方块，梅花，黑桃。2代表无花色。0为错误牌
    //--大小对应为A 2 3 4 5 6 7 8 9 10 J Q K  小王 大王 听用牌 = 1 2 3 4 5 6 7 8 9 10 11 12 13  15 16 。 0为错误牌
    //--发牌动画
    sendCard() {
        let self = this;
        let delayTime = 0.2;
        
        // self.LayerItems.applyBankerTips.stopAllActions();
        // self.LayerItems.applyBankerTips.active = false;
        self.LayerItems.winSoundNode.removeAllChildren();
        self._centerLayer.stopAllActions();
        self.chipsNode.stopAllActions();
        // self.chipsNode.removeAllChildren();
        self.LayerItems.onlineBetNode.stopAllActions();
        let orgShowPos = [-50,80];
        let orgShowScale = [0.75,1];
        for (let i = 0; i < self.Pokers.length; i++) {
            let cardObj = self.Pokers[i];
            let cardNode: cc.Node = cardObj.card;
            if (cardNode) {
                cardObj.valueCard.active = false;
                cardNode.scale = orgShowScale[i];
                cardNode.setPosition(orgShowPos[i], 0);
                let delay = 0;
                i == 1 && (delay == 0.15);
                cardNode.active = true;
                let t = cc.tween;
                cardNode.stopAllActions();
                t(cardNode)
                    .delay(delayTime)
                    .call(()=>{
                        self._gameAudio.playCard()
                    })
                    .parallel(
                        t().to(delay, { scale: 0.75 }),
                        t().to(delay, { position: cc.v3(orgShowPos[0],0) })
                    )
                    .parallel(
                        t().to(0.15, { scale: 0.75 }),
                        t().to(0.15, { position: cardObj.startPos })
                    ).start();
                delayTime = delayTime + 0.15;
            }
        }


    }

    //加载桌位玩家数据
    onDeskUserBetMsg(msg: any) {
        let self = this;
        let gameModel = self._gameCore;
        if (!self.updateScheduler) {
            self.chipsCount = 0;
            self.updateScheduler = this.schedule(self.updateChip, 0, 0);
            self.onlineBetAct();
        }
        self.onUpdateUserBetUserInfo(msg);
        let player = null;
        let playerNode = null;
        if (msg.playerid == gameModel.userInfo.playerid) {
            player = self.MyInfo.head;
            self.MyInfo.isBet = true;
            self.MyInfo.money = msg.chouma;
        } else {
            for (let userindex = 0; userindex < self.Players.length; userindex++) {
                if (msg.playerid == self.Players[userindex].playerid) {
                    player = self.Players[userindex];
                    playerNode = self.Players[userindex];
                    let decimals = 0;
                    if(self.Players[userindex].playerid == gameModel.userInfo.playerid){
                        decimals = 2;
                    }
                    self.Players[userindex].score.getComponent(cc.Label).string = '₹ ' + self.moneyFormat(msg.chouma,decimals) + "";//:setString(utils:moneyString(msg.chouma));
                }
            }

            if (playerNode) {
                self.userBetHeadMove(playerNode);
            }

        }
        if (Common.isNull(player)) {
            self.chipsCount += 1;
            // self.onOtherUserBet();
            // self.onlineUserBetHeadMove();
            if(Math.ceil(self.chipsCount/50)%2 == 1){
                self.userBetMsgTemp.push(msg);
                self.onOtherUserBet1();
            }
            else{
                self.userBetMsgTemp1.push(msg);
                self.onOtherUserBet2();
            }
            self.onOtherUserBet1();
            return;
        }
        msg.direction = msg.direction - 1;
        //--yg:已经预先表现
        if (msg.playerid == gameModel.userInfo.playerid) {
            //--dump(msg, "自己下注：")
            self.setUserBetAreaInfo(msg);
        } else {
            
            self.deskChipMove(msg.odds, player.head || player, msg.direction, function () {
                self.setUserBetAreaInfo(msg);
            });
        }

        self.shenSZBet(msg.playerid, msg.direction);
    }
    //更新上庄列表
    onUpdateBankerList(msg: any) {
        let self = this;
        self.bankerListMsg = msg;

        let setMainPanel: cc.Node = self.alterLayer.getChildByName("setMainPanel");
        if (setMainPanel.active == true) {
            self.loadBankerList(false);
        }
    }
    //加载上庄列表数据
    private loadBankerList(isTop: boolean = true) {
        let self = this;
        let msg = self.bankerListMsg;
        let setMainPanel: cc.Node = self.alterLayer.getChildByName("setMainPanel");
        let bankerScrollView = setMainPanel.getChildByName("mainBg").getChildByName("scrollView")
        let bankerListView = bankerScrollView.getChildByName("view").getChildByName("content");
        bankerListView.removeAllChildren();
        // super.loadAsset("res/prefabs/mainUserItem", cc.Prefab, function (userMainNode) {
            let isMyUpBanke = null;
                   
            if (msg && msg.list) {
                let tempNewBankerList = {};
                let len: number = 0;
                if (msg.list.list) {
                    msg.list = msg.list.list;
                }
                
                for (let i in msg.list) {
                    if (i == "isempty") {
                        continue;
                    }
                    let userinfo = msg.list[i];
                    if (userinfo) {
                        userinfo.sort = i;
                        tempNewBankerList[userinfo.playerid] = userinfo;
                        len++;
                    }
                }
                let realNum = len;
                let myPos = -1;
                //len = len > 10 && 10 || len;
                for (let i = 1; i <= len; i++) {
                    if (i <= 10) {
                        let Item = cc.instantiate(self.userMainNode);
                        self.setBankerListUserData(Item, msg, i);
                        bankerListView.addChild(Item);  
                    }
                    if(msg.list[i].playerid == self.MyInfo.playerid){
                        myPos = i;
                    }
                }
                if(myPos == -1){//自己不在申请列表
                    self.LayerItems.descRich2.getComponent(cc.RichText).string = self.stringFormat(GameTextTips.lh_bankerTips2,[realNum,10]);//排队人数
                }
                else{
                    self.LayerItems.descRich2.getComponent(cc.RichText).string = self.stringFormat(GameTextTips.lh_bankerTips4,[realNum,myPos]);//排队人数
                }

                //设置滑动区域内容高度
                let childCount = bankerListView.childrenCount;
                let itemHeight: number = childCount > 0 ? bankerListView.children[0].height : 0;
                itemHeight += 5;
                bankerListView.height = itemHeight * childCount - 5;
                if (isTop) {
                    bankerScrollView.getComponent(cc.ScrollView).scrollToPercentVertical(100);
                }

                let myUserInfo = tempNewBankerList[self.MyInfo.playerid];
                if (myUserInfo) {
                    // self.btnMyApplyBanker.getComponent(cc.Sprite).spriteFrame = self.bankerAtlas.getSpriteFrame("sheng_an_cancel")
                    self.LayerItems.endBanker.active = self.BankerInfo.playerid == self.MyInfo.playerid;
                    self.LayerItems.upBanker.active = !self.LayerItems.endBanker.active;
               
                    self.bankerState = 1;
                    self.MyInfo.isBankerList = true;
                    isMyUpBanke = true;
                } else {
                    self.MyInfo.isBankerList = false;
                    // self.btnMyApplyBanker.getComponent(cc.Sprite).spriteFrame = self.bankerAtlas.getSpriteFrame("sheng_an")
                    self.LayerItems.upBanker.active = true; 
                    self.LayerItems.endBanker.active = false;
                }
                if (!isMyUpBanke) {
                    self.bankerState = 0;
                }


            } else {
                self.MyInfo.isBankerList = false;
                if (self.BankerInfo.playerid == self.MyInfo.playerid) {
                    isMyUpBanke = true;
                }
                if (self.bankerState == 1) {
                    self.bankerState = 0;
                }
                //print("上庄队列：", self.bankerState, isMyUpBanke, self.BankerInfo.playerid, self.MyInfo.playerid);
                if (!isMyUpBanke && self.bankerState == 0) {
                    // self.btnMyApplyBanker.getComponent(cc.Sprite).spriteFrame = self.bankerAtlas.getSpriteFrame("sheng_an")
                    self.LayerItems.upBanker.active = true; 
                    self.LayerItems.endBanker.active = false; //下庄字
                }
            }
            self.LayerItems.upBankerText.active = self.bankerState == 0;
            self.LayerItems.endBankerText.active = !self.LayerItems.upBankerText.active;
        // })
    }
    //--设置庄家列表用户数据
    setBankerListUserData(Item: cc.Node, msg, index) {
        let self = this;

        if (msg.list[index] && msg.list[index].playerid) {
            Item.getChildByName("rankBox").getChildByName("rankLabel").getComponent(cc.Label).string = "No." + (index)
            let userinfo = self.getUserInfo(msg.list[index].playerid);
            let decimals = 0
            if (msg.list[index].playerid == self.MyInfo.playerid) {
                decimals = 2;//自己保留2位小数
                // Item.getChildByName("selfBg").active = true;
            } else {
                // Item.getChildByName("selfBg").active = false;
            }
            super.setPlayerHead(Item.getChildByName("headBox").getChildByName("headImg").getComponent(cc.Sprite), msg.list[index].headid, userinfo && userinfo.wxheadurl || "");
            Item.getChildByName("goldBox").getChildByName("gameLabel").getComponent(cc.Label).string = Common.textClamp(msg.list[index].nickname, 10, "...");
            Item.getChildByName("goldBox").getChildByName("goldLabel").getComponent(cc.Label).string = self.moneyFormat(msg.list[index].coin,decimals);

        }


    }
    //字符格式化
    public stringFormat(strContent: string, params: any[]): string {
        var str = strContent;
        for (var i = 0; i < params.length; i++) {
            var reg = new RegExp("\\{" + i + "\\}", "gm");
            str = str.replace(reg, params[i]);
        }
        return str;
    }
    //亮牌
    onLookCard(msg: any) {
        let self = this;

        if (self.userBetMsgSch) {
            this.unschedule(self.userBetMsgSch);
            self.userBetMsgSch = null;
        }
        self.chipsCount = 0;
        if (self.updateScheduler) {
            this.unschedule(self.updateScheduler);
            self.updateScheduler = null;
        }
        self.LayerItems.onlineBetNode.stopAllActions();
        for (let i = 0; i < self.compareTb.length; i++) {
            self.compareTb[i].winnerIcon.active = false;
        }
        let tempMsg: any[] = [];
        tempMsg[0] = msg.long;
        tempMsg[1] = msg.hu;
        if (!msg.long || !msg.hu) {
            return;
        }
        self.LayerItems.cardNode.stopAllActions()
        cc.tween(self.LayerItems.cardNode)
            .delay(1.5)
            .call(function () {
                for (let i = 0; i < self.Pokers.length; i++) {
                    let pos: cc.Vec2 = self.compareTb[i].startPos;
                    if (self.compareTb[i].node) {
                        self.compareTb[i].node.active = true;
                    }
                    self.Pokers[i].card.active = true;
                    self.Pokers[i].card.stopAllActions();
                    cc.tween(self.Pokers[i].card)
                        .to(0.2, { scale: 1.13, position: cc.v3(pos.x, pos.y)})
                        .call(()=>{
                            self._gameAudio.playFlipCard();
                        })
                        // .delay(0.2)
                        // .call(function () {
                        //     if (self.compareTb[i].node) {
                        //         self.compareTb[i].node.active = true;
                        //     }

                        // })
                        .start();
                }
            }).start();

        cc.tween(self.LayerItems.cardNode)
            .delay(1.8)
            .call(function () {
                let delayTime = 0.5;
                for (let i = 0; i < self.Pokers.length; i++) {
                    let item = self.Pokers[i];
                    // if(tempMsg[0].number==tempMsg[1].number && tempMsg[0].color==tempMsg[1].color){
                    //     console.log("出现两张相同的扑克");
                    //     console.log("出现两张相同的扑克"+JSON.stringify(tempMsg) );
                    //     console.log("出现两张相同的扑克"+JSON.stringify(msg) );
                    // }
                    if (item.card) {
                        cc.tween(item.card)
                            .delay(delayTime)
                            .call(function () {
                                self.pokerAction(item.card, 0.4, tempMsg[i].number, tempMsg[i].color, function () {
                                    self._gameAudio.playCardDian(tempMsg[i].number,i);
                                });
                            })
                            .start();
                        delayTime = delayTime + 1;
                    }
                }
            })
            .delay(3)
            .call(function () {
                let resultPos = null;
                if (msg.long.number > msg.hu.number) {
                    resultPos = 0;
                } else if (msg.long.number < msg.hu.number) {
                    resultPos = 1;
                } else {
                    resultPos = 2;
                }
                self.resultWinCard(resultPos);
            }).start();

    }
    resultWinCard(area: any, endFunc?: any) {
        //print("播放哪个区域赢：", area);
        let self = this;
        this._gameAudio.playWindNum(area + 1);
        for (let i = 0; i < self.compareTb.length; i++) {
            if (i == area) {
                self.compareTb[area].winnerIcon.scale = 5;
                self.compareTb[area].winnerIcon.active = true;
                // self.compareTb[area].card.zIndex = 1;
            } else {
                self.compareTb[i].winnerIcon.active = false;
            }
        }
        cc.tween(self.compareTb[area].winnerIcon)
            .to(0.2, { scale: 1 })
            // .delay(0.65)
            .call(function () {
            //     // self.compareTb[area].card.zIndex = 0;
                // console.error('牌型结束时间..........' + Common.formatDateTimeNormal())
            //     if (endFunc) {
            //         endFunc();
            //     }
            })
            .start();

    }

    //--翻牌动画
    pokerAction(poker: cc.Node, time: number, code: number, color: any, endFunc: () => void) {
        let self = this;
        let scale = 1.13;
        poker.scale = 1.13;
        poker.active = true;
        cc.tween(poker)
            .to(time / 2, { scaleX: 0 })
            .call(function () {
                code = code || 0;
                let valueCard: cc.Node = poker.getChildByName("valueCard")
                let pk = self.setCardVal(valueCard, code, color);
                if (pk) {
                    valueCard.active = true;
                    cc.tween(poker)
                        .to(time / 2, { scaleX: scale })
                        .call(function () {

                            if (endFunc) {
                                endFunc();
                            }
                        }).start();

                }
            }).start();

    }
    //设置扑克值
    private setCardVal(valueCard: cc.Node, code: number, colorVal: number): boolean {
        if (code <= 0 || colorVal <= 0) {
            return;
        }
        let cd = code < 10 ? "0" + code : code;
        let spFrame = this.cardAtlas.getSpriteFrame("poker_" + colorVal + cd)
        if (spFrame) {
            valueCard.getComponent(cc.Sprite).spriteFrame = spFrame;
            return true;
        }
        return false;
    }
    // 获取金币字符串 count: 0不加K; 1-n: 超过指定位数时加上K.  decimals: 显示小数位数
    moneyFormat(money: number, decimals: number = 0): string {
        // return Common.moneyString(money, count, decimals);
        return money/Config.SCORE_RATE == 0 ? '0' : Number((money/Config.SCORE_RATE).toFixed(decimals)).toString();
    }
    //重置自己的下注
    betFaild() {
        let self = this;
        if (self._gameCore.model.isBetMax && self.betMsg) {
            //print("重置自己的下注, ", self.betMsg.long_self, self.betMsg.hu_self, self.betMsg.he_self);
            self.TouchBet[0].self_label.getComponent(cc.Label).string = self.moneyFormat(self.betMsg.long_self)
            self.TouchBet[1].self_label.getComponent(cc.Label).string = self.moneyFormat(self.betMsg.hu_self)
            self.TouchBet[2].self_label.getComponent(cc.Label).string = self.moneyFormat(self.betMsg.he_self)
            self.autoSelectChip();
        }
    }
    //初始化历史记录
    private initHistory() {
        //--历史记录
        // self.historyNode = this.alterLayer.getChildByName("historyPanel").getChildByName("mainBg");
        // self.historyBox = self.historyNode.getChildByName("historyBox");
        // self.lhResultPanel = {};
        this._gameZoushi = this._historyLayer.getComponent("LHZoushi");
        this._gameZoushi.initzoushiUI(this.hideHistory.bind(this));
    }
    //--结算更新走势
    updateResultTrend(data) {
        this._gameZoushi && this._gameZoushi.updateData(data, 3);
    }
    //--更新走势
    updateTrendData(data: any) {
        this._gameZoushi && this._gameZoushi.initzoushi(data);
    }
    initTrendData(msg: any) {
        let self = this;
        self.lastRoundArea = 0;
        for (let i = 0; i < msg.data.length; i++) {
            if (self.historyListTb.length < 18) {
                self.historyListTb.push(msg.data[i]);
            }
            if (self.historyListTb.length >= 18) {
                self.historyListTb.splice(0, 1);
                self.historyListTb.push(msg.data[i]);
            }
        }

    }
    //更新游戏走势小图
    onUpdateHistoryIcon(msg: any) {
        this.historyList.removeAllChildren();
        if (!this.historyList) {
            return
        }
        //计算只显示最后17局数据
        let keys = [];
        for (let key in msg) {
            if (Number(key) > 0) {
                keys.push(key);
            }
        }
        if (keys.length > 17) {
            keys.splice(0, keys.length - 17);
        }

        for (let i = 0; i < 17; i++) {

        }
        for (let i = 0; i < keys.length; i++) {
            this.addHistoryItem(msg[keys[i]].win - 1);
        }

    }

    //--结算更新历史记录
    addHistoryItem(winArea: number) {
        let self = this;
        let imagArr = ["lh_record0","lh_record1","lh_record2"];//["img_dot_item_long", "img_dot_item_hu", "img_dot_item_he"]
        if (this.historyList.childrenCount >= 17) {
            this.historyList.children[0].removeFromParent();
        }
        let item: cc.Node = cc.instantiate(this.recordItem); //self.historyList.children[index];
        let img_res = imagArr[winArea];
        item.getComponent(cc.Sprite).spriteFrame = self.mainUiAtlas.getSpriteFrame(img_res);
        self.historyList.addChild(item);
        item.active = true;
        // self.historyList.children.forEach((item,index,nodes)=>{
        //     item.getChildByName('NewFlag').active = index == nodes.length - 1;
        //     item.stopAllActions();
        //     item.scale = 1;
        //     if(index == nodes.length - 1){
        //         cc.tween(item)
        //         .to(0.25,{scale : 1.2})
        //         .to(0.25,{scale : 0.8})
        //         .to(0.25,{scale : 1})
        //         .start()
        //     }
        // });
    }
    //修改在线用户数据
    onUpdateUserData(msg: any) {
        let self = this;
        if(!self._clickOtherLinePlayerLayer){
            if(!!msg && !!msg.betrank){
                for (let key in msg.betrank) {
                    if(msg.betrank[key]){   
                        let player =  msg.betrank[key];
                        let user = self.allUsers[player.playerid];
                        if (user) {
                            user.money = player.coin && player.coin || player.playercoin;
                            user.headid = player.headid && player.headid || user.headid;
                            user.nickname = player.name && player.name || user.nickname;
                            if (user.isOpenUserInfo) {
                                //self.setUserInfo(user);
                            }
                        }
                    }
                }
            }
        }
    }
    //修改桌位玩家数据
    onUpdateDeskUserInfo(msg: any) {
        let self = this;
        for (let i = 0; i < self.Players.length; i++) {
            let val = self.Players[i];
            let key = i;
            let player = msg[key]
            val && val.img_Null && (val.img_Null.getChildByName('empty').getComponent(cc.Label).string = GameTextTips.lh_seat_empty);
            if (val && player && player.playerid > 0) {
                val.img_Null.active = false;
                // val.userInfo.active = true;
                let userInfo = self.getUserInfo(player.playerid);
                super.setPlayerHead(val.head.getComponent(cc.Sprite), player.headid, (userInfo && userInfo.wxheadurl) || "");
                let decimals = 0;
                if(player.playerid == self.MyInfo.playerid){
                    decimals = 2;
                }
                val.score.getComponent(cc.Label).string = '₹ ' + self.moneyFormat(player.coin,decimals);
                val.playerid = player.playerid;
                val.nickName.getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");

            } else {
                // val.userInfo.active = false;
                val.nickName.getComponent(cc.Label).string = ''
                val.score.getComponent(cc.Label).string = ''
                val.img_Null.active = true;
                val.head.getComponent(cc.Sprite).spriteFrame = null;
                val.playerid = null;
            }
        }

    }
    //初始化在线玩家数据
    initPlayerListData(msg) {
        let tempTb = msg;
        let self = this;
        //--保存前六个玩家显示到桌子上
        let deskPlayerList = [];
        for (let i = 0; i < 6; i++) {
            if(!!msg.betrank){
                deskPlayerList.push(msg.betrank[i + 1])
            }
        }

        if (self.isGameEnd) {
            self.onUpdateDeskUserInfo(deskPlayerList);
            self.isGameEnd = false;
            return;
        }
        
        if(!self._clickOtherLinePlayerLayer){
            self.onUpdateDeskUserInfo(deskPlayerList);
        }
        
        //dump(msg, "玩家列表：");

        //self.totalplayernum = msg.totalplayernum;

        self.playerListData = tempTb;
    }

    //结算
    onResult(msg: any) {
        let self = this;
        let resultMsg: any = {};
        resultMsg._playerid = msg._playerid;
        resultMsg.nextat = msg.nextat;
        resultMsg.other = {};
        resultMsg.resultPos = msg.resultPos - 1;
        resultMsg.roundid = msg.roundid;
        resultMsg.win_value = msg.win_value;
        resultMsg.zhuang = msg.zhuang;
        resultMsg.others = {betrank:{}};
        if (msg.other) {
            let count = 0;
            for (let key in msg.other) {
                let item = msg.other[key];
                let tmp = item.split(",");
                let userData: any = {};
                userData.playercoin = parseInt(tmp[0]);
                userData.nChange = parseInt(tmp[1]);
                userData.playerid = key;
                count += 1;
                resultMsg.others.betrank[count.toString()] = userData;
                // resultMsg.others.push(userData)
            }

        }
        self.onGameResult(resultMsg);

    }
    //游戏结算
    onGameResult(msg: any) {
        let self = this;
        //隐藏亮牌节点
        for (let i = 0; i < 2; i++) {
            self.compareTb[i].node.active = false;
            self.compareTb[i].card.active = false;
            self.compareTb[i].winnerIcon.active = false;
        }
        self.compareTb[2].winnerIcon.active = false;
        let gameModel = self._gameCore.model;
        cc.tween(self._centerLayer)
            // .delay(0.3)
            .call(function () {
                // console.error('飞币时间..........' + Common.formatDateTimeNormal())
                self.flyResultNumber(msg);
                self.addHistoryItem(msg.resultPos);
                self.updateResultTrend(msg.resultPos + 1);
                cc.tween(self._centerLayer)
                    .delay(1.0)
                    .call(function () {
                        let data: any = {};
                        data.win = msg.resultPos;
                        data.Value = msg.win_value;
                        self.historyListTb.unshift();
                        self.historyListTb.push(data);

                        if (self.BankerInfo.playerid != self.MyInfo.playerid) {
                            self.autoSelectChip();
                        }

                        self.onUpdateUserData(msg.others);
                        // console.error('清理时间111..........' + Common.formatDateTimeNormal())
                        
                        cc.tween(self._centerLayer)
                            .delay(0.6)
                            .call(function () {
                                self.LayerItems.winSoundNode.removeAllChildren();
                                self._gameCore.requestAllPlayerList();
                                self.isGameEnd = true;
                                // console.error('清理时间222..........' + Common.formatDateTimeNormal())
                                cc.tween(self._centerLayer).delay(1).call(function () {
                                    // console.error('清理时间333..........' + Common.formatDateTimeNormal())
                                    self.cleanGameData();
                                }).start();
                            }).start();
                        self.MyInfo.isBet = false;
                    }).start();
            }).start();

    }
    //清除游戏数据
    cleanGameData() {
        //console.log("===================清除游戏数据==================");
        let self = this;

        for (let i = 0; i < self.compareTb.length; i++) {
            if (self.compareTb[i].node) {
                self.compareTb[i].node.active = false;
            }
            if (self.compareTb[i].valueCard) {
                self.compareTb[i].valueCard.active = false;
            }
            if (self.compareTb[i].winnerIcon) {
                self.compareTb[i].winnerIcon.active = false;
            }
        }
        self.MyInfo.fnt_LoseScore.active = false;
        self.MyInfo.fnt_LoseScore.setPosition(self.MyInfo.fnt_LoseScore_startPos);
        self.MyInfo.fnt_WinScore.active = false;
        self.MyInfo.fnt_WinAnimation.active = false;
        self.MyInfo.fnt_WinScore.setPosition(self.MyInfo.fnt_WinScore_startPos);
        self.BankerInfo.fnt_LoseScore.active = false;
        self.BankerInfo.fnt_LoseScore.setPosition(self.BankerInfo.fnt_LoseScore_startPos);
        self.BankerInfo.fnt_WinScore.active = false;
        self.BankerInfo.fnt_WinAnimation.active = false;
        self.BankerInfo.fnt_WinScore.setPosition(self.BankerInfo.fnt_WinScore_startPos);

        self.BankerSystemInfo.fnt_WinScore.setPosition(self.BankerSystemInfo.fnt_WinScore_startPos);
        self.BankerSystemInfo.fnt_WinScore.active = false;

        self.BankerSystemInfo.fnt_LoseScore.active = false;
        self.BankerSystemInfo.fnt_LoseScore.setPosition(self.BankerSystemInfo.fnt_LoseScore_startPos);
        for (let i = 0; i < self.Players.length; i++) {
            let val = self.Players[i];
            if (val) {
                val.fnt_LoseScore.active = false;
                val.fnt_WinScore.active = false;
                val.fnt_WinAnimation.active = false;
                val.fnt_LoseScore.setPosition(val.fnt_LoseScore_startPos);
                val.fnt_WinScore.setPosition(val.fnt_WinScore_startPos);
            }
        }
        for (let i = 0; i < self.TouchBet.length; i++) {
            let val = self.TouchBet[i];
            if (val) {
                val.total_label.string = "0";
                val.self_label.string = "0";
                val.total_number = 0;
                val.self_number = 0;
                val.star.active = false;

            }
        }

        this.putAllPoolChip();
        self.starArea = -1;
        self.userBetMsgTemp = [];
        self.userBetMsgTemp1 = [];
        self.flyingBets = [];
        self.flyingOnlineBets =[];
        self.betMsg = null;
        self.chipsCount = 0;
        self._gameCore.model.isBetMax = false;
        for (let i = 0; i < self.Chips.length; i++) {
            self.Chips[i] = [];
        }
       
        // self.chipsNode.removeAllChildren();
       
        self.LayerItems.winSoundNode.removeAllChildren();

    }

    //在线其它玩家筹码飞动
    onlineChipsMove(chip: any,betTime: number) {
        let self = this;
        
        let chipPosNode = self.TouchBet[chip.area].node;
        // let offsetX = chipPosNode.x;
        // let offsetY = chipPosNode.y;
        // let moveToPos = cc.v2(Common.random(offsetX - 70, offsetX + 70), Common.random(offsetY - 55, offsetY + 55));

        // let time = 0.6;
        // let showSize = 0.4
        // let moveSize = 0.5
        // let putTime = 0.05    
        // let time1 = 0.1;
        let addOrFalse = (Common.random(1,10)) % 2 == 1 ? 1 : -1  // 左或者右
        let addOrFalse2 = (Common.random(1,20)) % 2 == 1 ? 1 : -1 
        let eP = cc.v3(chipPosNode.x + addOrFalse * Math.random()*(chipPosNode.width* 0.69/2-38),chipPosNode.y + addOrFalse2 *Math.random()*(chipPosNode.height* 0.68/2-40))
        
        chip.node.active = true;
        cc.tween(chip.node)
        .to(betTime, { position: eP }, { easing: 'expoOut' })
        // .to(0.1, { angle: (Common.random(-50, 50) + 360 * 4) })
            // .parallel(
            //         cc.tween().to(betTime, { position: eP }, { easing: 'expoOut' }),
            //         cc.tween().to(0.1, { angle: (Common.random(-50, 50) )+ 360 * 4 }),//, scale: moveSize
            // //     // cc.tween().to(time, { position: cc.v3(moveToPos.x, moveToPos.y) }) //.to(putTime, { scale: showSize })
            // //     cc.tween().to(time, { position: eP }, { easing: 'expoOut' }),
            // //     cc.tween().call(()=>{
            // //         if (self.isStartBet) {
            // //             self.onlineUserBetHeadMove();
            // //         }
            // //     })
            // )
            
            .call(()=>{
                self.setUserBetAreaInfo({dirctionall:chip.dirctionall,direction:chip.area});
            })
            .start();

        // if (chip.score < 100 * Config.SCORE_RATE) {
        //     if (!self.isPlayingBet1Sound) {
        //         self.isPlayingBet1Sound = true
        //         this._gameAudio.playBet2();
        //         this.scheduleOnce(function () {
        //             self.isPlayingBet1Sound = false;
        //         }, 0.2)

        //     }
        // } else {
            if (!self.isPlayingBetSound ) {
                self.isPlayingBetSound = true
                this._gameAudio.playBet();
                this.scheduleOnce(function () {
                    self.isPlayingBetSound = false;
                }, 0.1)
            }
        // }

    
        self.Chips[chip.area].push({ node: chip.node, score: chip.score });
    }

    //其它在线玩家飞筹码
    private onlineChipsAct(startTime: number,betNumIndex: number) {
        let self = this;
        let newCreateChipList = [[],[],[]];//三个区域所需绘制筹码数
        let list = [Common.random(8,20),Common.random(10,35),Common.random(40,60)]
        let maxDrawNum: number = list[betNumIndex]// Common.random(30,50);//每次最多只用个数
        let len = self.flyingOnlineBets.length > maxDrawNum ? maxDrawNum : self.flyingOnlineBets.length;
        for(let i = len - 1; i >= 0;i--){
            let val = self.flyingOnlineBets[i];
            let chipRestule = self.onlinePlayersChipCreate(val.score, val.player,val.area,val.dirctionall);
            newCreateChipList[val.area].push(chipRestule);
            self.flyingOnlineBets.splice(i, 1);
        }

        for(let i = 0; i < newCreateChipList.length;i++){
            let areaChipList = newCreateChipList[i];
            self.removeAreaMoreChips(i);
            for(let j = 0; j < areaChipList.length;j++){
                if(!self.isStartBet){//快结束一秒时,播放后一个
                    break;
                }
                let chip = areaChipList[j];
                if(!!chip){
                    self.onlineChipsMove(chip,startTime);
                }
            }
        }
    }

    private onlineChipsAct1(startTime: number,betNumIndex: number) {
        let self = this;
        let newCreateChipList = [[],[],[]];//三个区域所需绘制筹码数
        let list = [Common.random(8,15),Common.random(15,30),Common.random(30,50)]
        let maxDrawNum: number = list[betNumIndex]// Common.random(30,50);//每次最多只用个数
        let len = self.flyingBets.length > maxDrawNum ? maxDrawNum : self.flyingBets.length;
        for(let i = len - 1; i >= 0;i--){
            let val = self.flyingBets[i];
            let chipRestule = self.onlinePlayersChipCreate(val.score, val.player,val.area,val.dirctionall);
            newCreateChipList[val.area].push(chipRestule);
            self.flyingBets.splice(i, 1);
        }

        for(let i = 0; i < newCreateChipList.length;i++){
            let areaChipList = newCreateChipList[i];
            self.removeAreaMoreChips(i);
            for(let j = 0; j < areaChipList.length;j++){
                if(!self.isStartBet){//快结束一秒时,播放后一个
                    break;
                }
                let chip = areaChipList[j];
                if(!!chip){
                    self.onlineChipsMove(chip,startTime);
                }
            }
        }
    }

    //删除过多的筹码
    removeAreaMoreChips(area: number){

        let self = this;
        if (!self.Chips[area]) {
            self.Chips[area] = [];
        }
        if (self.Chips[area].length > LHD.Const.areaShowMaxChipsNum) {
            for (let key = 0; key < self.Chips[area].length; key++) {
                if (self.Chips[area].length < LHD.Const.areaShowMaxChipsNum) {
                    break;
                }
                let val = self.Chips[area][key];
                if (val) {
                    // val.node.removeFromParent();
                    LHChipPoolAssisCom.instance.put(val.node);
                    val = null;
                }
                self.Chips[area].splice(key, 1);
                key--;
            }

            // for (let key = 0; key < self.Chips[area].length - LHD.Const.areaShowMaxChipsNum ; key++) {
            //     let val = self.Chips[area][key];
            //     if (val) {
            //         val.node.removeFromParent();
            //         val = null;
            //     }
            //     self.Chips[area].splice(key, 1);
            //     key--;
            // }
        }
    }

    //其它玩家飞币 先创建筹码
    private onlinePlayersChipCreate(score: any, player: any,area: any,dirctionall: any): any {

        let self = this;
        let chouMaIndex = self.SELECT_CONFIG.findIndex(item => item == score);
        let choumaVal: LHModel.ChipConfig;
        if (chouMaIndex > -1 && chouMaIndex < self.CHIP_DATA_CONFIG.length) {
            choumaVal = self.CHIP_DATA_CONFIG[chouMaIndex];
        } else {
            //没有找到筹码
            return null;
        }
        let moveChipImg: cc.SpriteFrame = self.mainUiAtlas.getSpriteFrame(choumaVal.betBgKey)
        if (!moveChipImg) {
            console.error(self.stringFormat(GameTextTips.GAME_BET_SCORE_ERR, [self.moneyFormat(score)]));
            return null;
        }
        let chip: cc.Node = LHChipPoolAssisCom.instance.get();//cc.instantiate(self.chipNode);
        this.chipPoolList.push(chip);
        chip.opacity = 255;
        chip.getComponent(cc.Sprite).spriteFrame = moveChipImg;
        let nodePos = cc.v2(self._centerLayer.convertToNodeSpaceAR(player.convertToWorldSpaceAR(player.getAnchorPoint())));
        
        chip.setPosition(nodePos.x, nodePos.y);
        chip.angle = self.getRandom(-50, 50);
        self.chipsNode.addChild(chip);
        // chip.scale = 0.3;
        chip.active = false;
        
        return { node: chip, score: score ,player: player,area : area,dirctionall: dirctionall};
    }

    //修改筹码回调
    private updateChipCall() {
        // let self = this;
        // let lengthVal = self.flyingBets.length;
        // for (let i = 0; i < lengthVal; i++) {
        //     let val = self.flyingBets[i];
        //     if (val && val.area == 2) {
        //         self.flyingBets_he.push(val);
        //         self.flyingBets.splice(i, 1);
        //         i--;
        //     }
        // }
        // if (lengthVal > 0) {
        //     let val = self.flyingBets[0];
        //     if (val && val.area != 3 && self.isStartBet) {
        //         self.chipMove(val.score, val.player, val.area, val.callback);
        //     }
        //     self.flyingBets.splice(0, 1);
        // }

        // let he_len = self.flyingBets_he.length;
        // if (he_len > 0 && self.isStartBet) {
        //     let val = self.flyingBets_he[0];
        //     self.chipMove(val.score, val.player, val.area, val.callback);
        //     self.flyingBets_he.splice(0, 1);
        // }
    }
    //修改筹码数据
    updateChip() {
        // let self = this;
        // let length = self.flyingBets.length;
        // if (length > 0) {
        //     let v = self.flyingBets[0];
        //     self.chipMove(v.score, v.player, v.area, v.callback)
        //     self.flyingBets.splice(0, 1);
        // }
    }

    //其它玩家金币定时一起飞
    onlineBetAct(){
        let self = this;
        self.LayerItems.onlineBetNode.stopAllActions();
        let startTime = 1.2//LHD.Const.onlineBetTime;
        let randIndex = 1;
        let subTime = 0.01;
        cc.tween(self.LayerItems.onlineBetNode)
        .repeatForever(
            cc.tween()
            .call(()=>{
                //  if(self.isStartBet){
                    // self.onlineUserBetHeadMove();
                    self.onlineChipsAct(startTime,randIndex);
                    // console.error('时间.............' + startTime)
                    if(startTime < 0.3){
                        startTime = 0.3;
                    }
                    else{
                        startTime -= subTime
                        // console.error('当前下注时间=============' + startTime)
                        //Common.random(5,12) * 0.001;
                    }
                    
                    // self.setUserBetAreaInfo(msg);
                // }
            })
             .delay(0.4)
            )
        .start();   

        let startTime1 = 0.8
        let bRessetFlag1 = false;
        cc.tween(self.LayerItems.onlineBetNode)
        .delay(1.2)
        .repeatForever(
            cc.tween()
            .call(()=>{
                //  if(self.isStartBet){
                    // self.onlineUserBetHeadMove();
                    self.onlineChipsAct1(startTime1,2);
                    randIndex = 2;
                    subTime = 0.02
                    // console.error('时间.............' + startTime)
                    if(startTime1 < 0.4){
                        startTime1 = 1;
                        bRessetFlag1 = true;
                    }
                    else{
                        if(bRessetFlag1){
                            startTime1 -= Common.random(15,40) * 0.001
                        }
                        else{
                            startTime1 -= 0.02
                        }
                    }
                    // console.error('当前下注时间111=============' + startTime1)
                    // self.setUserBetAreaInfo(msg);
                // }
            })
             .delay(0.2)
            )
        .start();   
    }

    //同步玩家下注筹码
    updateChipMsg(playerid: any, count: number, chipNum: number, betinfo: any, area: number, total: {}) {
        
        let self = this;
        //console.log("同步玩家筹码数据"+self.flyingBets.length+"|"+self.userBetMsgTemp.length)
        let gameMyInfo = this._gameCore.userInfo;

        if (!self.updateScheduler) {
            self.chipsCount = 0;
            self.updateScheduler = self.updateChipCall;//机关枪似的喷射 暂时没用
            this.schedule(self.updateChipCall, 0);
            self.onlineBetAct(); //由快到慢 一块一块的扔筹码
        }

        let msg: any = {};
        msg.playerid = playerid;
        msg.chouma = betinfo.chouma;
        msg.odds = chipNum;
        msg.direction = area;
        msg.dirctionall = total[area];
        self.onUpdateUserBetUserInfo(msg);
        let player = null;
        let playerNode = null;
        if (playerid == gameMyInfo.playerid) {
            for (let userindex = 0; userindex < self.Players.length; userindex++) {
                if (playerid == self.Players[userindex].playerid) {
                    player = self.Players[userindex];
                    playerNode = self.Players[userindex];
                    self.Players[userindex].score.getComponent(cc.Label).string = self.moneyFormat(msg.chouma,2) + "";//:setString(utils:moneyString(msg.chouma));
                }
            }
            self.shenSZBet(playerid, area);
            return;
        }
        if (msg.playerid == gameMyInfo.playerid) {

        } else {
            for (let userindex = 0; userindex < self.Players.length; userindex++) {
                if (msg.playerid == self.Players[userindex].playerid) {
                    player = self.Players[userindex];
                    playerNode = self.Players[userindex];
                    self.Players[userindex].score.getComponent(cc.Label).string = '₹ ' + self.moneyFormat(msg.chouma) + "";//:setString(utils:moneyString(msg.chouma));
                }
            }

            if (playerNode) {
                self.userBetHeadMove(playerNode);
            }
        }
        if (Common.isNull(player)) {
            self.chipsCount += 1;

            if(Math.ceil(self.chipsCount/50)%2 == 1){
                self.userBetMsgTemp.push(msg);
                self.onOtherUserBet1();
            }
            else{
                self.userBetMsgTemp1.push(msg);
                self.onOtherUserBet2();
            }
            
            // self.onOtherUserBet();
            // if (self.isStartBet) {
            //     self.onlineUserBetHeadMove();
            // }
            // self.setUserBetAreaInfo(msg);

            return;
        }
        if (count > 0) {
            if (msg.playerid != gameMyInfo.playerid) {
                // if (!player.isMove) {
                //     cc.tween(playerNode)
                //     .delay(Common.random(10,100) * 0.01)
                //     .call(()=>{
                //         self.userBetHeadMove(playerNode);
                        self.deskChipMove(msg.odds, player.head || player, msg.direction, function () {
                            let areaAll = msg.dirctionall;
                            let curAreaAll = self.TouchBet[msg.direction].total_number;
                            if (Common.toNumber(areaAll) > Common.toNumber(curAreaAll)) {
                                self.TouchBet[msg.direction].total_label.string = self.moneyFormat(areaAll) + "";     //--这个区域下的总注
                                self.TouchBet[msg.direction].total_number = areaAll;
                            }
                         });
                    // })
                    // .start();
                // }
            }
        }
        let areaAll = msg.dirctionall;
        let curAreaAll = self.TouchBet[msg.direction].total_number;
        if (Common.toNumber(areaAll) > Common.toNumber(curAreaAll)) {

            self.TouchBet[msg.direction].total_label.string = self.moneyFormat(areaAll) + "";     //--这个区域下的总注
            self.TouchBet[msg.direction].total_number = areaAll;
        }

        self.shenSZBet(msg.playerid, msg.direction);
        //console.log("结束同步玩家筹码数据")
    }

    
    //--玩家下注同步玩家数据
    onUpdateUserBetUserInfo(msg: any) {
        let val = this.allUsers[msg.playerid];
        if (val) {
            val.money = msg.chouma;
        }
    }
    //玩家头像移动
    userBetHeadMove(player: LHModel.Player) {
        if(!this.isStartBet){
            return;
        }
        if (!player.isMove) {
            player.isMove = true;
            let movePos = 20;
            if (player.chairid%2 == 1) {
                movePos = -20;
            }
            
            let oldSize = player.node.getPosition();
            cc.tween(player.node)
                .to(0.08, { position: cc.v3(player.node.x + movePos, player.node.y) })//, { easing: 'sineInOut' }
                .to(0.08, { position: cc.v3(oldSize.x, oldSize.y) })//, { easing: 'sineInOut' }
                .call(function () {
                    player.isMove = false;
                }).start();
        }
    }
    //自家头像移动
    userMyselfBetHeadMove(player: LHModel.MyInfo) {
        if (!player.isMove) {
            player.isMove = true;
            let movePosX = 10;
            let movePosY = 10;
            
            let oldSize = player.node.getPosition();
            cc.tween(player.node)
                .to(0.05, { position: cc.v3(player.node.x + movePosX, player.node.y + movePosY) })
                .to(0.05, { position: cc.v3(oldSize.x, oldSize.y) })
                .call(function () {
                    player.isMove = false;
                }).start();
        }
    }
    //玩家下注
    onOtherUserBet() {
        let self = this;
        for (let i = 0; i < self.userBetMsgTemp.length; i++) {
            let key = i;
            let val = self.userBetMsgTemp[i];
            if (val) {
                if (val.direction == 2) {

                }
                if (val.odds > 10000) {
                    let userBet = self.splitUserBetChipScore(val.odds, 3);

                    for (let j = 0; j < userBet.length; j++) {
                        self.flyingBets.push({ score: userBet[j], player: self.btnOnline, area: val.direction })
                    }

                } else {
                    self.flyingBets.push({
                        score: val.odds, player: self.btnOnline, area: val.direction, callback: function () {
                            self.setUserBetAreaInfo(val);
                        }
                    })

                }
            }
            self.userBetMsgTemp.splice(key, 1);
            i--;
        }
    }
    //玩家下注
    onOtherUserBet1() {
        let self = this;
        for (let i = 0; i < self.userBetMsgTemp.length; i++) {
            let key = i;
            let val = self.userBetMsgTemp[i];
            if (val) {
                if (val.odds > 10000) {
                    let userBet = self.splitUserBetChipScore(val.odds, 3);
                    for (let j = 0; j < userBet.length; j++) {
                        self.flyingOnlineBets.push({ score: userBet[j], player: self.btnOnline, area: val.direction ,dirctionall: val.dirctionall})
                    }

                } else {
                    self.flyingOnlineBets.push({score: val.odds, player: self.btnOnline, area: val.direction, dirctionall: val.dirctionall})
                }
            }
            self.userBetMsgTemp.splice(key, 1);
            i--;
        }
        // if(self.flyingOnlineBets.length > 0){
        //     self.flyingOnlineBets.sort((a,b)=>{
        //         return a.area - b.area;
        //     })
        // }
        // console.error('当前时间:....'+startTime)
       
    }
    //玩家下注
    onOtherUserBet2() {
        let self = this;
        for (let i = 0; i < self.userBetMsgTemp1.length; i++) {
            let key = i;
            let val = self.userBetMsgTemp1[i];
            if (val) {
                if (val.odds > 10000) {
                    let userBet = self.splitUserBetChipScore(val.odds, 3);
                    for (let j = 0; j < userBet.length; j++) {
                        self.flyingBets.push({ score: userBet[j], player: self.btnOnline, area: val.direction ,dirctionall: val.dirctionall})
                    }

                } else {
                    self.flyingBets.push({score: val.odds, player: self.btnOnline, area: val.direction, dirctionall: val.dirctionall})
                }
            }
            self.userBetMsgTemp1.splice(key, 1);
            i--;
        }
        // if(self.flyingBets.length > 0){
        //     self.flyingBets.sort((a,b)=>{
        //         return a.area - b.area;
        //     })
        // }
        // console.error('当前时间:....'+startTime)
       
    }
    //结算 玩家赢的分数 计算筹码拆分
    splitChipScore(score: any, selectChipTb?: number[]): number[] {
        let self = this;
        if (score < 1) {
            return [0];
        }
        let publicScore = selectChipTb && selectChipTb || self.SELECT_CONFIG;
        let chipScore = score;
        let tempCount = 0;
        let userScore: number[] = [];
        //每个筹码最大显示数量
        let maxChipCount = [10, 8, 6, 4, 100];
        for (let i = 0; i < publicScore.length; i++) {
            // tempCount = Math.floor(chipScore / publicScore[publicScore.length-i-1]);
            // chipScore = publicScore[publicScore.length-1-i];
            //调整结算筹码 分拆大量
            tempCount = Math.floor(chipScore / publicScore[i]);
            tempCount = maxChipCount[i] < tempCount ? maxChipCount[i] : tempCount;
            let itemChipScore = publicScore[i];

            //--print("拆分筹码循环：", tempCount, i);
            for (let j = 0; j < tempCount; j++) {
                userScore.push(itemChipScore);
                chipScore -= itemChipScore;
            }

            //chipScore = score % publicScore[publicScore.length-1-i];

        }
        return userScore;

    }
    //--根据下注总分数 拆分筹码分数
    splitUserBetChipScore(score: any, count: number): number[] {
        let self = this;
        let publicScore = self.SELECT_CONFIG;
        let chipScore = score;
        let tempCount = 0;
        let userScore: number[] = [];
        for (let i = 0; i < 5; i++) {
            tempCount = Math.floor(chipScore / publicScore[i]);
            chipScore = publicScore[i];
            //--print("拆分筹码分数：", tempCount, i);
            if (tempCount > count) {
                tempCount = Math.floor(count / (i + 1));
            }
            //--print("拆分筹码循环：", tempCount, i);
            for (let j = 0; j < tempCount; j++) {
                userScore.push(chipScore);
            }

            chipScore = score - (userScore.length * 100);
        }
        return userScore;

    }
    //在线用户头像移动
    onlineUserBetHeadMove() {
        let self = this;
        if (!self.movingOnlineUserHead) {
            self.movingOnlineUserHead = true;
            let movePosX = -10;
            let movePosY = 10;
            let startPos = self.btnOnline.getPosition()

            let callFun = function () {
                self.movingOnlineUserHead = false;
            };
            cc.tween(self.btnOnline)
                .to(0.05, { position: cc.v3(startPos.x + movePosX, startPos.y + movePosY) })
                .to(0.05, { position: cc.v3(startPos.x, startPos.y) })
                .call(callFun)
                .start();
        }
    }
    //--设置下注区域筹码数据显示
    setUserBetAreaInfo(msg: any) {
        let gameMyInfo = this._gameCore.userInfo;
        let self = this;
        let areaAll = msg.dirctionall;
        //msg.direction--;
        //console.log("设置下注区域筹码数据"+msg.direction);
        let curAreaAll = self.TouchBet[msg.direction].total_number;
        if (areaAll > curAreaAll) {
            self.TouchBet[msg.direction].total_label.string = self.moneyFormat(msg.dirctionall) + "";     //--这个区域下的总注
            self.TouchBet[msg.direction].total_number = areaAll;
        }

        if (msg.playerid == gameMyInfo.playerid) {
            self.TouchBet[msg.direction].self_label.string = self.moneyFormat(msg.buyall) + "";        //--我在这个区域下的总注
            self.TouchBet[msg.direction].self_label.node.active = true;
            self.MyInfo.score.getComponent(cc.Label).string = self.moneyFormat(msg.chouma) + "";    //utils:moneyString(msg.chouma));
            gameMyInfo.money = msg.chouma;
            self.autoSelectChip();
        }
    }
    //获取指定范围的随机数
    private getRandom(minNum: number, maxNum?: number): number {
        return Common.random(minNum, maxNum);
    }
    //筹码飞动
    chipMove(score: any, player: any, area: any, callback: any) {
        let self = this;
        let moveResult = self.chipMoveAct(score, player, area, callback);
        if (moveResult) {
            self.removeAreaMoreChips(area);
            // if (!self.Chips[area]) {
            //     self.Chips[area] = [];
            // }
            // else if (self.Chips[area].length > LHD.Const.areaShowMaxChipsNum) {
            //     // let len = self.Chips[area].length - 150;

            //     for (let key = 0; key < self.Chips[area].length; key++) {
            //         if (self.Chips[area].length < 150) {
            //             break;
            //         }
            //         let val = self.Chips[area][key];
            //         if (val) {
            //             val.node.removeFromParent();
            //             val = null;
            //         }
            //         self.Chips[area].splice(key, 1);
            //         key--;
            //     }
            // }
            self.Chips[area].push(moveResult);
        }

    }
    //筹码飞动
    deskChipMove(score: any, player: any, area: any, call: () => void,isMe: boolean = false) {
        let self = this;
        if(!self.isStartBet){
            return;
        }
        let moveResult = self.chipMoveAct(score, player, area, call,isMe);
        if (moveResult) {
            if (!self.Chips[area]) {
                self.Chips[area] = [];
            }
            self.Chips[area].push(moveResult);
        }
    }
    //筹码飞动效果
    private chipMoveAct(score: any, player: any, area: any, call: () => void,isMe: boolean = false): any {
        let self = this;

        let chouMaIndex = self.SELECT_CONFIG.findIndex(item => item == score);
        let choumaVal: LHModel.ChipConfig;
        if (chouMaIndex > -1 && chouMaIndex < self.CHIP_DATA_CONFIG.length) {
            choumaVal = self.CHIP_DATA_CONFIG[chouMaIndex];
        } else {
            //没有找到筹码
            return null;
        }
        let moveChipImg: cc.SpriteFrame = self.mainUiAtlas.getSpriteFrame(choumaVal.betBgKey)
        if (!moveChipImg) {
            self.show(self.stringFormat(GameTextTips.GAME_BET_SCORE_ERR, [self.moneyFormat(score)]));//utils:moneyString(score)]);
            return null;
        }
        let chip: cc.Node = LHChipPoolAssisCom.instance.get();//cc.instantiate(self.chipNode);
        this.chipPoolList.push(chip);
        chip.opacity = 255;
        chip.getComponent(cc.Sprite).spriteFrame = moveChipImg;
        let nodePos = cc.v2(self._centerLayer.convertToNodeSpaceAR(player.convertToWorldSpaceAR(player.getAnchorPoint())));
        chip.setPosition(nodePos.x, nodePos.y);
        chip.active = true;
        let rotation = self.getRandom(-50, 50);

        chip.angle = rotation;
        let chipPosNode = self.TouchBet[area].node;
        let offsetX = chipPosNode.x;
        let offsetY = chipPosNode.y;
        // let moveToPos = cc.v2(Common.random(offsetX - 70, offsetX + 70), Common.random(offsetY - 55, offsetY + 55));
        self.chipsNode.addChild(chip);

        let addOrFalse = (Common.random(1,10)) % 2 == 1 ? 1 : -1  // 左或者右
        let addOrFalse2 = (Common.random(1,20)) % 2 == 1 ? 1 : -1 
        let eP = cc.v2(chipPosNode.x + addOrFalse * Math.random()*(chipPosNode.width* 0.69/2-38),chipPosNode.y + addOrFalse2 *Math.random()*(chipPosNode.height* 0.68/2-40))
        
        let distance = nodePos.sub(eP).mag();//cc.pGetLength(cc.pSub(nodePos,moveToPos))
        let distanceTime = 2000;
        if(!isMe){//桌上其它玩家 速率调快
            distanceTime = 2500;
        }
        // else{
        //     console.error('')
        // }
        let time = distance / distanceTime
        // let showSize = 0.4
        // let moveSize = 0.5
        // let putTime = 0.05
        // chip.scale = 0.3;
        // let seq = cc.tween()
        //     .to(time, { postion: moveToPos })
        //     .to(putTime, { scale: showSize })
        //     .call(function () {
        //         if (call) {
        //             call();
        //         }
        //     });

        // let time1 = Math.min(0.10, time);  //--allTime
        // let seq1 = cc.tween().to(time1, { angle: (Common.random(-50, 50) + 360 * 4), scale: moveSize });

        cc.tween(chip)
            //  .parallel(
            //     cc.tween().to(time1, { angle: (Common.random(-50, 50) + 360 * 4) , scale: moveSize}),
                // cc.tween().to(time, { position: cc.v3(moveToPos.x, moveToPos.y)}).to(putTime, { scale: showSize })
            //  )
            // .to(time, { position: cc.v3(moveToPos.x, moveToPos.y)})
            .to(time, { position: cc.v3(eP)}, { easing: 'sineInOut' })//, { easing: 'expoInOut' }
            .start();
            
        if (!self.Chips[area]) {
            self.Chips[area] = [];
        }

        // if (score < 100000) {
        //     if (!self.isPlayingBet1Sound) {
        //         self.isPlayingBet1Sound = true
        //         this._gameAudio.playBet2();
        //         this.scheduleOnce(function () {
        //             self.isPlayingBet1Sound = false;
        //         }, 0.2)

        //     }
        // } else {
            if (!self.isPlayingBetSound) {
                self.isPlayingBetSound = true
                this._gameAudio.playBet();
                this.scheduleOnce(function () {
                    self.isPlayingBetSound = false;
                }, 0.2)
            }
        // }
        return { node: chip, score: score };
        //self.Chips[area].push({node:chip,score:score});
    }
    //--根据分数,创建筹码
    createChips(score: any, area: number, count: number) {
        let self = this;
        //self.Chips[area] = checktable(self.Chips[area]);
        let chipScore = self.splitUserBetChipScore(score, count);

        if (!chipScore || chipScore.length <= 0) {
            return;
        }

        if (self.Chips[area]) {
            if (self.Chips[area]) self.Chips[area] = [];
        }
        for (let i = 0; i < chipScore.length; i++) {

            let chip = self.createChouMa(chipScore[i], area);
            self.Chips[area].push({ node: chip, score: score })
        }
    }
    //创建单个筹码节点
    createChouMa(score: number, area: number): cc.Node {
        let self = this;
        let chouMaIndex = self.SELECT_CONFIG.findIndex(item => item == score);
        let choumaVal: LHModel.ChipConfig;
        if (chouMaIndex > -1 && chouMaIndex < self.SELECT_CHIP_DATA_CONFIG.length) {
            choumaVal = self.SELECT_CHIP_DATA_CONFIG[chouMaIndex];
        } else {
            return;
        }


        let moveChipImg: cc.SpriteFrame = self.mainUiAtlas.getSpriteFrame(choumaVal.betBgKey)
        if (!moveChipImg) {
            self.show(self.stringFormat(GameTextTips.GAME_BET_SCORE_ERR, [self.moneyFormat(score)]));//utils:moneyString(score)]);
            return null;
        }


        let chip: cc.Node = LHChipPoolAssisCom.instance.get();//cc.instantiate(self.chipNode);
        this.chipPoolList.push(chip);
        chip.opacity = 255;
        chip.getComponent(cc.Sprite).spriteFrame = moveChipImg;

        let rotation = self.getRandom(-50, 50);
        chip.angle = rotation;

        let chipPosNode = self.TouchBet[area].node;
        let offsetX = chipPosNode.x;
        let offsetY = chipPosNode.y;
        let moveToPos = cc.v2(Common.random(offsetX - 70, offsetX + 70), Common.random(offsetY - 55, offsetY + 55));
        chip.setPosition(moveToPos);
        chip.active = true;
        self.chipsNode.addChild(chip);
        return chip;
    }
    //--神算子下注显示星
    shenSZBet(playerid: any, area: any) {
        let self = this;
        if (playerid == self.Players[1].playerid) {
            if (self.starArea === -1) {
                self.starArea = area;
                let lucky: cc.Node = self.TouchBet[area].star;
                lucky.active = true;
            }
            return;

        }
    }
    //断线重连
    offLineToOnline(msg: any) {
        let self = this;
        //self.cardCompareNode:hide();
        let config = msg.config;
        if (config && config.Odds) {
            if (config.Odds.he) {
                //self.betArea_heRule:setString("3.和：1:"..config.Odds.he);
                // self.TouchBet[2].node.getComponent(cc.Sprite).spriteFrame = this.gameAtlas.getSpriteFrame("bg_he_" + config.Odds.he);
            }
        }


        this.cleanGameData();
        self.setChipBtnEnabled(false);
        self.autoSelectChip();
        //设置上庄条件
        // if (self.btnBankerLimit && self._gameCore.model.bankerNeed) {
            // self.btnBankerLimit.maxWidth=320;
            // self.btnBankerLimit.lineHeight=20;
            // self.btnBankerLimit.string="<color=#A4895D>Nếu số dư ít hon <color=#A4895D>"+self.moneyFormat(self._gameCore.model.bankerNeed) +"</color> sẽ mất quyền làm cái</color>";
        // }

        if (msg.state == LHD.LHD_GameState.LHD_GameState_BuyHorse) {
            self.isStartBet = true;
            let chipsNum = 150000;
            let count = 15;
            if (Common.toNumber(msg.nextat) > 10) {
                count = 5
            } else if (Common.toNumber(msg.nextat) > 5) {
                count = 10;
            }
            if (msg.TableBetInfo) {
                self.restoreDeskAllInfo(msg.TableBetInfo, count);
            }
        }
        self.MyInfo.money = msg.chouma;
        self.restoreDeskMyInfo(msg.betinfo);

    }
    //--恢复桌子上自己的数据
    restoreDeskMyInfo(msg: any) {
        let temp = {};
        let self = this;
        temp[0] = msg.long;
        temp[1] = msg.hu;
        temp[2] = msg.he;
        let myMoney = self.MyInfo.money;
        for (let i = 0; i < self.TouchBet.length; i++) {
            self.TouchBet[i].self_label.string = self.moneyFormat(temp[i]) + "";//setString(utils:moneyString(temp[i], 0));
            if (temp[i] > 0) {
                self.MyInfo.isBet = true;
                self.TouchBet[i].self_label.node.active = true;
            }
            //myMoney = myMoney - temp[i];
        }
        self.MyInfo.money = myMoney;

        self.MyInfo.score.getComponent(cc.Label).string = this.moneyFormat(myMoney,2);//:setString(utils:moneyString(myMoney));
        self.autoSelectChip();
    }
    //--恢复桌子上的数据
    restoreDeskAllInfo(msg: any, count: number) {
        let self = this;
        let temp = {};
        temp[0] = msg.long;
        temp[1] = msg.hu;
        temp[2] = msg.he;
        for (let i = 0; i < self.TouchBet.length; i++) {
            self.TouchBet[i].total_label.getComponent(cc.Label).string = this.moneyFormat(temp[i]);// :setString(utils:moneyString(temp[i], 0));
            self.TouchBet[i].total_number = temp[i];
            if (temp[i] > 0) {
                if (i == 2) {
                    count = 5;
                }
                self.createChips(temp[i], i, count);
            }
        }
    }

    //切换庄家数据
    onUpdateBankerInfo(msg: any, isOffLine: boolean = false) {
        //throw new Error("Method not implemented.");
        let self = this;
        let decimals = 0
        if (msg.zhuangid == self.MyInfo.playerid) {
            self.bankerState = 2;
            decimals = 2;//自己保留2位小数
        } else {
            self.bankerState = 0;
        }
        if (msg.zhuangturn == 0 && msg.issystem == 0 && self.BankerInfo.playerid != msg.zhuangid && isOffLine == false) {
            self.isChangeBanker = true;
        }

        this.scheduleOnce(function () {
            if (msg.issystem != 1) {
                self.BankerInfo.node.getChildByName("systemPanel").active = false;
                let mainPanel = self.BankerInfo.node.getChildByName("mainPanel");
                mainPanel.getChildByName("gameLabel").getComponent(cc.Label).string = self._gameLogic.strClamp(msg.zhuangname, 10);
                mainPanel.getChildByName("goldPanel").getChildByName("goldValue").getComponent(cc.Label).string = self.moneyFormat(msg.chouma,decimals)
                let bankerCount = msg.zhuangturn + 1;
                mainPanel.getChildByName("nameLabel").getComponent(cc.Label).string = self.stringFormat(GameTextTips.GAME_BANKER_JU_TEXT, [bankerCount]);
                mainPanel.active = true;
                mainPanel.getChildByName("gameLabel").active = true;
                mainPanel.getChildByName("nameLabel").active = true;

            } else {

                self.BankerInfo.node.getChildByName("systemPanel").active = true;
                self.BankerInfo.node.getChildByName("mainPanel").active = false;
            }
            if (msg.zhuangid > 0) {
                let userinfo = self.getUserInfo(msg.zhuangid);
                self.setPlayerHead(self.BankerInfo.head.getComponent(cc.Sprite), msg.zhuangheadid, userinfo && userinfo.wxheadurl || "");
            }
            //print("庄家轮换：", msg.zhuangturn, msg.zhuangname, self.BankerInfo.playerid, msg.zhuangid, isOffLine);
            if (msg.zhuangturn == 0 && msg.issystem == 0 && self.BankerInfo.playerid != msg.zhuangid) {
                if (isOffLine == false) {
                    self.userToBankerAct(msg.zhuangname);
                }
            }

            if (msg.zhuangid == self.MyInfo.playerid) {
                self.LayerItems.upBanker.active = false;
                self.LayerItems.endBanker.active = true;
                // self.btnMyApplyBanker.getComponent(cc.Sprite).spriteFrame = self.bankerAtlas.getSpriteFrame("sheng_an_cancel")
                self.MyInfo.isBankerList = null;
                self.setChipBtnEnabled(true);
            } else {
                self.setChipBtnEnabled(false);
                //--上庄的玩家不是我自己，切换按钮
                // if (self.MyInfo.isBankerList) {
                //     // self.btnMyApplyBanker.getComponent(cc.Sprite).spriteFrame = self.bankerAtlas.getSpriteFrame("sheng_an_cancel")
                //     self.LayerItems.endBanker.active = true;
                //     self.LayerItems.upBanker.active = false;
                // } else {
                    // self.btnMyApplyBanker.getComponent(cc.Sprite).spriteFrame = self.bankerAtlas.getSpriteFrame("sheng_an")
                    this.LayerItems.upBanker.active = true;
                    self.LayerItems.endBanker.active = false;
                // }
                self.autoSelectChip();
            }
            self.BankerInfo.playerid = msg.zhuangid;

        }, 2);

    }

    //--设置选择筹码按钮启用禁用
    setChipBtnEnabled(hasEnabled: boolean) {
        let self = this;
        if (self.lhBetScript) {
            self.lhBetScript.disableChips(hasEnabled);
        }
    }

    //--庄家更换动画
    userToBankerAct(nick: any) {
        let self = this;
        let applyBankerTips = self.mainLayer.getChildByName("applyBankerTips");
        applyBankerTips.getChildByName("nameLabel").getComponent(cc.Label).string = self._gameLogic.strClamp(nick, 10);
        applyBankerTips.active = true;
        applyBankerTips.stopAllActions();
        cc.tween(applyBankerTips)
            .delay(1.5)
            .call(function () {
                applyBankerTips.active = false;
            }).start();
    }
    //结算金币飞动
    flyResultNumber(result: any) {
        let self = this;
        let gameModel = self._gameCore.model;
        if (!result.others || !result.others.betrank) {
            return;
        }
        let winUserPos: cc.Vec2[] = [];
        //在线玩家赢的次数
        let isOnlineCount = 0;
        // console.error('飞币时间111..........' + Common.formatDateTimeNormal())           
        for (let key in result.others.betrank) {           
            if(!!result.others.betrank[key]){ 
                let player = result.others.betrank[key];
                if (player.playerid == self.MyInfo.playerid) {
                    if (player.nChange > 0) {
                        let pos = cc.v2(self.chipsNode.convertToNodeSpaceAR(self.MyInfo.head.convertToWorldSpaceAR(self.MyInfo.head.getAnchorPoint())));
                        winUserPos.push(pos);
                    }
                    self.chipToUser(player.playerid, player.nChange, result.resultPos, self.MyInfo);
                    self.MyInfo.money = player.playercoin;
                    this.scheduleOnce(function () {
                        if (player.nChange > 0) {
                            self.resultAct(self.MyInfo);
                        }
                        self.resultFlyNumber(self.MyInfo, self.MyInfo.playerid, player.nChange, player.playercoin);
                    }, 0.9);

                } else {
                    let hasExists = false;
                    for (let j = 0; j < self.Players.length; j++) {
                        let playerModel = self.Players[j];
                        if (player.playerid == playerModel.playerid) {
                            if (player.nChange > 0) {
                                let pos = cc.v2(self.chipsNode.convertToNodeSpaceAR(playerModel.head.convertToWorldSpaceAR(playerModel.head.getAnchorPoint())));
                                winUserPos.push(pos);
                            }
                            self.chipToUser(player.playerid, player.nChange, result.resultPos, playerModel);
                            this.scheduleOnce(function () {
                                if (player.nChange > 0) {
                                    self.resultAct(playerModel);
                                }
                                self.resultFlyNumber(playerModel, playerModel.playerid, player.nChange, player.playercoin,true);
                            }, 0.9);
                            hasExists = true;
                        }
                    }
                    isOnlineCount += hasExists == false ? 1 : 0
                }
            }
        }

        //--庄家
        if (result.zhuang.nChange > 0) {
            //--dump(self.userWinChip[result.zhuang.playerid], "庄家赢的筹码：");
            //--print("庄家ID 为：", result.zhuang.playerid);
            let bankerNode = null;
            if (result.zhuang.playerid <= 0) {
                bankerNode = self.BankerSystemInfo.node;
            } else {
                bankerNode = self.BankerInfo.node;
            }
            let pos = cc.v2(self.chipsNode.convertToNodeSpaceAR(bankerNode.convertToWorldSpaceAR(bankerNode.getAnchorPoint())));
            winUserPos.push(pos);
            for (let i = 0; i < 3; i++) {
                //有下注的区域，筹码飞动到庄家
                if (self.Chips[i].length > 0) {
                    self.findChip(result.zhuang.playerid, result.zhuang.nChange, i);
                    let pos = cc.v2(self.chipsNode.convertToNodeSpaceAR(bankerNode.convertToWorldSpaceAR(bankerNode.getAnchorPoint())));
                    self.chipMoveToWinUser(self.userWinChip[result.zhuang.playerid], i, pos, 0.3,LHD.Const.customBankerPos);
                }
            }

            this.scheduleOnce(function () {
                if (result.zhuang.playerid > 0) {
                    self.resultAct(self.BankerInfo);
                }
                self.bankerResultFly(result.zhuang.playerid, result.zhuang.nChange, "+");
            }, 0.9);

        } else if (result.zhuang.nChange < 0) {
            this.scheduleOnce(function () {
                self.bankerResultFly(result.zhuang.playerid, result.zhuang.nChange);
            }, 0.9);

        }
        let decimals = 0
        if (result.zhuang.playerid == self.MyInfo.playerid) {//庄家是自己，保留2位小数
            decimals = 2;
        }

        self.BankerInfo.score.getComponent(cc.Label).string = self.moneyFormat(result.zhuang.playercoin,decimals);//utils:moneyString(result.zhuang.playercoin);
        if (isOnlineCount > 0) {
            let pos = cc.v2(self.chipsNode.convertToNodeSpaceAR(self.btnOnline.convertToWorldSpaceAR(self.btnOnline.getAnchorPoint())));
            winUserPos.push(pos);
        }
        // console.error('飞币时间222..........' + Common.formatDateTimeNormal())   
        self.chipMoveToPlayerList();
        // self.flyMoveChips(winUserPos, function (osTime: number) { });
        if (result.zhuang.playerid == self.MyInfo.playerid) {
            self.MyInfo.score.getComponent(cc.Label).string = self.moneyFormat(result.zhuang.playercoin,2);//utils:moneyString(result.zhuang.playercoin);
        }

        // this._gameAudio.playWinBet();
        // this._gameAudio.playWinBet1();
    }
    //玩家赢的筹码拆分
    findChip(playerid: any, score: any, areaIndex: number) {
        let self = this;
        if (areaIndex < 0 || !score) {
            return;
        }
        self.userWinChip[playerid] = {}; //--checktable(self.userWinChip[playerid]);
        self.userWinChip[playerid][areaIndex] = [];//--checktable(self.userWinChip[playerid][areaIndex]);
        let chipScore = self.splitChipScore(score);
        console.log("玩家赢的筹码分数拆分：" + chipScore.length)
        //--dump(chipScore, "玩家赢的筹码分数拆分：");
        for (let i = 0; i < chipScore.length; i++) {
            if (self.Chips[areaIndex]) {
                for (let j = 0; j < self.Chips[areaIndex].length; j++) {
                    let val = self.Chips[areaIndex][j];
                    if (val.score == chipScore[i]) {
                        if (chipScore.length >= self.userWinChip[playerid][areaIndex].length) {
                            if (val) {
                                self.userWinChip[playerid][areaIndex].push(val);
                                self.Chips[areaIndex].splice(j, 1);
                                j--;
                            }
                        }
                    }
                }
            }
        }

        if (self.userWinChip[playerid][areaIndex].length <= 0) {
            for (let i = 0; i < chipScore.length; i++) {

                let chip = self.createChouMa(chipScore[i], areaIndex);
                self.userWinChip[playerid][areaIndex].push({ node: chip, score: chipScore[i] });
            }
        }
    }
    //结束庄家金币飞动
    bankerResultFly(playerid: any, nChange: any, isAdd?: string) {
        let self = this;
        let bankerFntNode: cc.Node = null
        if (playerid > 0) {
            bankerFntNode = isAdd && self.BankerInfo.fnt_WinScore || self.BankerInfo.fnt_LoseScore;
        } else {
            bankerFntNode = isAdd && self.BankerSystemInfo.fnt_WinScore || self.BankerSystemInfo.fnt_LoseScore;
        }
        bankerFntNode.active = true;
        //print("庄家输赢：", bankerFntNode:isVisible(), utils:moneyString(nChange));
        let winMoney = (isAdd && "+" || "") + this.moneyFormat(nChange)//..utils:moneyString(nChange, 2);
        //print("字符串连接：", winMoney);
        let scoreLabel: cc.Label = bankerFntNode.getChildByName("scoreLabel").getComponent(cc.Label);
        scoreLabel.string = winMoney;
        if (scoreLabel.string.length > 6) {
            bankerFntNode.scale = 0.8;
        } else if (scoreLabel.string.length > 5) {
            bankerFntNode.scale = 0.9;
        } else {
            bankerFntNode.scale = 1;
        }
        self.flyNumber(bankerFntNode);
    }
    //--回收筹码
    chipToUser(playerid: any, nChange: any, resultPos: any, player: any) {
        let self = this;
        if (nChange > 0) {
            self.findChip(playerid, nChange, resultPos);
            let pos = cc.v2(self.chipsNode.convertToNodeSpaceAR(player.head.convertToWorldSpaceAR(player.head.getAnchorPoint())));
            self.chipMoveToWinUser(self.userWinChip[playerid], resultPos, pos, 0.3,LHD.Const.customDeskPlayer);
        }
    }

    //筹码飞到胜利一方
    chipMoveToWinUser(chipTab: any, AreaIndex: number, pos: cc.Vec2, delay: number, flyPos: number = -1) {
        let self = this;

        if (!chipTab || !chipTab[AreaIndex]) {
            return;
        }
        if (chipTab[AreaIndex].length > 0) {
            let countOnline = [0,0,0];
            let countBank = [0,0,0];
            let countDeskPlayer = [0,0,0];
            let act = cc.tween(self.chipsNode)
                .delay(0.01)
                .call(function () {
                    let index = 0;
                    let frames = Math.ceil(chipTab[AreaIndex].length)
                    let len = chipTab[AreaIndex].length;

                    for (let i = 0; i < frames; i++) {
                        if (chipTab[AreaIndex].length < index || chipTab[AreaIndex].length <= 0 || index >= chipTab[AreaIndex].length) {
                            //console.log("总数："+chipTab[AreaIndex].length+"未找到筹码对象"+index);
                            self.LayerItems.winSoundNode.removeAllChildren();
                            return;
                        }
                        let item: cc.Node = chipTab[AreaIndex][index].node;
                        if (!item) {
                            self.LayerItems.winSoundNode.removeAllChildren();
                            return;
                        }
                        let startPos: cc.Vec2 = item.getPosition();
                        let delayGap = 0.015
                        let speed = 1700
                        let time = pos.sub(startPos).mag() / speed
                        
                        let mvPos = cc.v2(0, 0)
                        if (startPos.x > pos.x) {
                            mvPos.x = 20
                        } else {
                            mvPos.x = -20
                        }

                        if (startPos.y > pos.y) {
                            mvPos.y = 20
                        } else {
                            mvPos.y = -20
                        }
                        //暂时不用这个拖尾回收
                        let delay_time = delayGap * index
                        if (delay_time > 0.6 && index != len) {
                            delay_time = (Math.random() * (50 - 10) + 10) / 100
                        } else if (delay_time > 0.6 && index == len) {
                            delay_time = 0.6
                        }

                        //固定时间回收筹码
                        if(flyPos == LHD.Const.customOnLine){//在线其它玩家飞币改一下
                            countOnline[AreaIndex] += 1;
                            delay_time = 0.015 * Math.ceil(countOnline[AreaIndex]/10);
                            if(AreaIndex == 0){
                                time = 0.6
                            }
                            if(AreaIndex == 1){
                                time = 0.3
                            }
                            if(AreaIndex == 2){
                                time = 0.45
                            }
                        }
                        if(flyPos == LHD.Const.customBankerPos){
                            countBank[AreaIndex] += 1;
                            delay_time = 0.025 * Math.ceil(countBank[AreaIndex]/5);
                            // if(AreaIndex == 0){
                            //     time = 0.35
                            // }
                            // if(AreaIndex == 1){
                            //     time = 0.3
                            // }
                            // if(AreaIndex == 2){
                            //     time = 0.35
                            // }
                        }

                        if(flyPos == LHD.Const.customDeskPlayer){
                            
                            countDeskPlayer[AreaIndex] += 1;
                            delay_time = 0.03 * Math.ceil(countDeskPlayer[AreaIndex]/2);
                        //     if(AreaIndex == 0){
                        //         time = 0.5
                        //     }
                        //     if(AreaIndex == 1){
                        //         time = 0.6
                        //     }
                        //     if(AreaIndex == 2){
                        //         time = 0.5
                        //     }
                        }
                                               
                        cc.tween(item)
                            .delay(delay_time)
                            .by(0.2, { position: cc.v3(mvPos.x, mvPos.y) })
                            .to(time, { position: cc.v3(pos.x, pos.y) },{easing : 'sineOut'})//,{easing : 'sineInOut'}
                            .to(0.5,{opacity: 0})
                            // .call(function () {
                                // item.active = false;
                            // })
                            .delay(0.7)
                            .call(function () {
                                if (index == len) {
                                    chipTab[AreaIndex].forEach(function (chipItem, k,nodes) {
                                        // chipItem.node.removeFromParent()
                                        LHChipPoolAssisCom.instance.put(chipItem.node);
                                        // console.error('===个数' + nodes.length)
                                        if(k == nodes.length - 1){
                                            self.LayerItems?.winSoundNode?.removeAllChildren();
                                        }
                                    });
                                    chipTab[AreaIndex] = [];
                                }
                            }).start();


                        if (delay < 0.8) {
                            delay = delay + 0.1;
                            self.allTime = delay;
                        }
                        index = index + 1;
                    }
                }).start();


        }
    }
    //桌面上的筹码，平均分给获胜人员
    flyMoveChips(posArray: any[], callBack) {
        let self = this;
        let beforeTime = Common.getCTime();
        let chips = []
        for (let i = 0; i < self.Chips.length; i++) {
            for (let j = 0; j < self.Chips[i].length; j++) {
                chips.push(self.Chips[i][j]);
            }
        }

        if (chips.length < 1) {
            if (callBack) {
                callBack(Common.getCTime() - beforeTime)
            }
            return
        }


        let gapNum = posArray.length;

        let speed = 1700
        let delayGap = 0.015

        if (chips.length > 150) {
            speed = 1700
            delayGap = 0.007
        } else if (chips.length > 50) {
            speed = 1300
            delayGap = 0.01
        }

        let curIndex = chips.length - 1

        let curNum = chips.length
        let m = curIndex;
        let index = 0;
        let len = chips.length;
        // //console.log("筹码总数"+curNum);
        for (let i = 0; i < posArray.length; i++) {
            m = curIndex;
            while (m >= 0) {
                index++;
                let chip = chips[m].node;
                let chipX = chip.x
                let chipY = chip.y

                let endPos = posArray[i]
                let time = endPos.sub(cc.v2(chipX, chipY)).mag() / speed
                let delay_time = delayGap * index
                if (delay_time > 0.6 && index != len) {
                    delay_time = (Math.random() * (50 - 10) + 10) / 100
                } else if (delay_time > 0.6 && index == len) {
                    delay_time = 0.6
                }
                // //console.log("筹码总数 执行index"+m);
                chip.active = true;
                cc.tween(chip)
                    .delay(delay_time)
                    .by(0.2, { position: cc.v3((chipX - endPos.x) / 15, (chipY - endPos.y) / 10) })
                    .to(time, { position: cc.v3(endPos.x, endPos.y) })
                    .call(function () {
                        chip.active = false;
                        // chip.removeFromParent()
                        LHChipPoolAssisCom.instance.put(chip);

                        curNum = curNum - 1
                        if (curNum == 0) {
                            if (callBack) {
                                callBack(Common.getCTime() - beforeTime)
                            }
                        }
                    }).start();

                if (i == 0 && m < gapNum) {
                    m--;
                } else if (m < gapNum) {
                    m = -1;
                } else {
                    m -= gapNum;
                }
            }
            curIndex = curIndex - 1;
        }
        // //console.log("筹码总数 执行数量："+index)
    }
    chipMoveToPlayerList() {
        let self = this;
        // let pos = self.btnOnline.getPosition();
        this.addWinSoundAct();
        let pos = cc.v2(self.node.convertToNodeSpaceAR(self.btnOnline.convertToWorldSpaceAR(self.btnOnline.getAnchorPoint())));
        for (let i = 0; i < 3; i++) {
            self.chipMoveToWinUser(self.Chips, i, pos, 0.3,LHD.Const.customOnLine);
        }
    }

    //增加赢音效
    addWinSoundAct(){
        let self = this;
        self.LayerItems.winSoundNode.removeAllChildren();
        let node1 = new cc.Node();
        self.LayerItems.winSoundNode.addChild(node1);
        cc.tween(node1)
        .repeatForever(
            cc.tween()
            .delay(0.25)
            .call(()=>{
                self._gameAudio.playWinBet()
            })
        )
        .start();

        let node2 = new cc.Node();
        self.LayerItems.winSoundNode.addChild(node2);

        cc.tween(node2)
        .repeatForever(
            cc.tween()
            .delay(0.4)
            .call(()=>{
                self._gameAudio.playWinBet()
            })
        )
        .start();
    }
    //--------------------------------------------------工具方法-----------------------------------
    //--自动选择下注筹码
    autoSelectChip() {
        let self = this;

        //print("自动选择下注筹码：", self.selectChipIndex, self.selectChipIndex);
        let betPanel: cc.Node = self.bottomLayer.getChildByName("betPanel");
        if (betPanel) {
            let lhbet: LHBetLayer = betPanel.getComponent("LHBetLayer");
            lhbet.autoSelectChip();
        }

    }

    //更新桌面在线人数
    onUpdateOnlinePlayersNum(info){
       if(!!info && !!info.totalplayernum){
           this.LayerItems.lbOnlinePlayers.getComponent(cc.Label).string = info.totalplayernum + '';
       } 
    }

    //道具特效运行
    public doRoomIntertChat(sendPlayerPos, receiverPlayerPos, content) {

        let sendPos = this.getHeadNodePositon(sendPlayerPos);
        let receiverPos = this.getHeadNodePositon(receiverPlayerPos);

        if(Common.isNull(sendPos) || Common.isNull(receiverPos)){
            return;
        }

        let startPos = cc.v3(this.gameEmojiNode.convertToNodeSpaceAR(sendPos));
        let endPos = cc.v3(this.gameEmojiNode.convertToNodeSpaceAR(receiverPos));
        let Direction1 = Direction.LEFT;
        if(receiverPlayerPos%2 == 1){
            Direction1 = Direction.RIGHT;
        }

        UIHelper.playInteractExpression(this.gameEmojiNode, startPos, endPos, content, Direction1 == Direction.LEFT);
    }

    // 点击玩家头像
    private onClickHead(target: any, customEventData: any) {

        this._gameAudio.playClick();
        let self = this;
        let headPos = Common.toInt(customEventData);

        let playerid = this.getPlayerIdAtHeadPos(headPos);
        if(!playerid){//座上有效玩家 
            this._curClickHeadIndex = -1;
            return;
        } 

        let pos = this.getHeadNodePositon(headPos);
        if(Common.isNull(pos)){
            this._curClickHeadIndex = -1;
            return;
        }
        let showPos = self.gameEmojiNode.convertToNodeSpaceAR(pos);
        headPos%2 == 0 ? showPos.x += 350 : showPos.x -= 350;
       
        this._curClickHeadIndex = headPos;
        
        let playerInfo = { sendPlayerid: self.MyInfo.playerid, receiverPlayerid: playerid};
        UIHelper.showInteractExpression(self.gameEmojiNode, showPos, playerInfo, (info: any) => {
            self._gameCore.sendChatMessage(info);
        });
    }

    //玩家头像位置
    getHeadNodePositon(pos: number){
        let newPos: cc.Vec2 = null;
        let userHeadNode: cc.Node = null;

        if(Common.isNull(pos)){
            return newPos;
        }

        if(pos == LHD.Const.customMyselfPos){
            userHeadNode = this.MyInfo.headBg;
        }
        else if(pos == LHD.Const.customBankerPos){
            userHeadNode = this.BankerInfo.headBg;
        }
        else if(pos == LHD.Const.customOtherPos){
            userHeadNode = this.LayerItems.onlineBetNode;
        }
        else{
            userHeadNode = this.Players[pos].headBg;
        }
        
        if(Common.isNull(userHeadNode)){
            return newPos;
        }

        newPos = userHeadNode.convertToWorldSpaceAR(userHeadNode.getAnchorPoint())

        return newPos;
    }

    //根据玩家id获取座上玩家位置 包含自己  bReceiverPlayerid:是否道具接收者 如果是 优先从座上玩家列表取位置 （主要处理自己同时坐在桌上六个玩家位置时）
    getHeadPosAtPlayerId(playerId: number,bReceiverPlayerid: boolean = false){
        let pos = null;

        if(Common.isNull(playerId)){
            return pos;
        }
        if(!!bReceiverPlayerid){
            if(this._curClickHeadIndex == LHD.Const.customBankerPos && playerId == this.BankerInfo.playerid){//有点击头像时,优先从点击处获取对应位置  免得玩家又在座上 又在庄上
                pos = LHD.Const.customBankerPos;
            }
            else if(this._curClickHeadIndex == LHD.Const.customMyselfPos && playerId == this.MyInfo.playerid){//自定义自己的一个位置
                pos = LHD.Const.customMyselfPos;
            }
            else{
                for (let i = 0; i < this.Players.length; i++) {
                    if (!!playerId && playerId > 0 && playerId == this.Players[i].playerid) {
                        pos = i;
                        break;
                    }
                }
    
                if(Common.isNull(pos) && playerId == this.BankerInfo.playerid){//自定义庄家的一个位置 
                    pos = LHD.Const.customBankerPos;
                }
    
                if(Common.isNull(pos) && playerId == this.MyInfo.playerid){//自定义自己的一个位置
                    pos = LHD.Const.customMyselfPos;
                }
            }  
        }
        else{//发送者 优先从自己位置
            if(playerId == this.MyInfo.playerid){//自定义自己的一个位置
                pos = LHD.Const.customMyselfPos;
            }
            else{
                if(playerId == this.BankerInfo.playerid){//自定义庄家的一个位置
                    pos = LHD.Const.customBankerPos;
                }
                else{ 
                    for (let i = 0; i < this.Players.length; i++) {
                        if (!!playerId && playerId > 0 && playerId == this.Players[i].playerid) {
                            pos = i;
                            break;
                        }
                    }   
                    if(pos == null){
                        pos = LHD.Const.customOtherPos;
                    } 
                }
            }
        }
        
        return pos;
    }

    //根据玩家位置获取座上玩家id 包含自己
    getPlayerIdAtHeadPos(pos: number){
        let playerid = null;
        
        for (let i = 0; i < this.Players.length; i++) {
            if (pos != null && pos >= 0 && i == pos && this.Players[i].playerid > 0) {
                playerid = this.Players[i].playerid;
                break;
            }
        }
        
        if(!playerid && pos == LHD.Const.customBankerPos){//自定义庄家的一个位置 
            playerid = this.BankerInfo.playerid
        }

        if(!playerid && pos == LHD.Const.customMyselfPos){//自定义自己的一个位置 
            playerid = this.MyInfo.playerid;
        }
                       
        return playerid;
    }

    //等待玩家点动画
    runWaitNextDotAni(){
        let self = this;
        self.LayerItems.dotNode.stopAllActions();
        self.LayerItems.dotNode.children.forEach(item=>item.active = false);
        let curDotIndex = 0
        cc.tween(self.LayerItems.dotNode)
        .repeatForever(
            cc.tween()
            .delay(0.7)
            .call(()=>{
                self.LayerItems.dotNode.children.forEach((item,index)=>item.active = index <= curDotIndex);
                curDotIndex = (curDotIndex + 1)%3;
            })
        )
        .start();   
    }
}
