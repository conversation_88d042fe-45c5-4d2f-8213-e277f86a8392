
//////////////////////////////////////////////////////////////////////////////////

import { <PERSON><PERSON><PERSON><PERSON> } from "../../../../script/frame/common/Define";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import SceneManager from "../../../../script/frame/manager/SceneManager";

// TIPS提示帮助类
export default class LHToastHelper extends BaseLayer {
    //////////////////////////////////////////////////////////////////////////////
    // 资源名称
    public static RES_NAME: string = "res/prefabs/ToastLayer";

    //////////////////////////////////////////////////////////////////////////////
    // 实例对象
    private static _instance: LHToastHelper = null;
    public static get instance(): LHToastHelper {
        return this._instance || (this._instance = new LHToastHelper());
    }

    //////////////////////////////////////////////////////////////////////////////
    // 是否正在加载资源
    private _loading: boolean = false;
    // 提示框预制对象
    private _toastPrefab: cc.Prefab = null;
    // 提示框实例对象数组
    private _toastNodes: Array<cc.Node> = [];
    // 显示的内容
    private _showContent: string = "";
    // 显示等待时间(秒)
    private _showWaitTime: number = 2.0;

    //////////////////////////////////////////////////////////////////////////////
    // 创建并显示提示框
    private createToast(bgType: number = 1) {
        // 创建预制对象
        let toastNode = cc.instantiate(this._toastPrefab);
        let toastLayer = null;
        toastNode && (toastLayer = toastNode.getComponent("LHToastLayer"));

        // 判断组件是否有效
        if (toastLayer) {

            // toastLayer.setBgTypeAndFntColor(bgType);
            // 设置显示的内容
            toastLayer.content = this._showContent;

            // 将所有存在的节点向上移动
            this.toastNodeMoveToUp();

            // 添加到场景
            SceneManager.instance.addChildNode(toastNode, ZOrder.TOAST);
            this._toastNodes.push(toastNode);

            // 显示动画
            let self = this;
            toastLayer.show(this._showWaitTime, () => {
                self.removeNode(toastNode);
            });
        }
    }

    // 将所有存在的节点向上移动
    private toastNodeMoveToUp() {
        let length = this._toastNodes.length;
        for (let i=0; i<length; ++i) {
            let tmpNode = this._toastNodes[i];
            tmpNode.stopAllActions();
            tmpNode.removeFromParent();
            this.removeNode(tmpNode);
        }
    }

    // 移除指定的节点
    private removeNode(node: cc.Node) {
        let length = this._toastNodes.length;
        for (let i=0; i<length; ++i) {
            let tmpNode = this._toastNodes[i];
            if (tmpNode === node) {
                this._toastNodes.splice(i, 1);
            }
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    // 预加载提示框资源
    public preloadToastPrefab() {
        cc.resources.preload(LHToastHelper.RES_NAME, cc.Prefab);
    }

    // 显示提示内容  bgType: 底框图类型 0：蓝色底 白色字体显示 1：黑色透明底 白色字体显示 2：蓝白色底 蓝色色体
    public showToast(content: string, waitTime: number,bgType: number = 1) {
        // 设置内容与等待时间
        this._showContent = content?content:"";
        this._showWaitTime = waitTime;

        // 资源已加载
        if (this._toastPrefab) {
            this.createToast(bgType);
            return ;
        }

        // 是否正在加载资源
        if (this._loading) {
            return ;
        }
        this._loading = true;

        // 加载提示框资源
        let self = this;
        super.loadAsset(LHToastHelper.RES_NAME, cc.Prefab, (toastPrefab: any) => {
            self._loading = false;
            self._toastPrefab = toastPrefab;
            self.createToast(bgType);
        });
    }

    // 清除所有节点
    public clearAllToast() {
        let length = this._toastNodes.length;
        for (let i=0; i<length; ++i) {
            let tmpNode = this._toastNodes[i];
            tmpNode.removeFromParent();
        }
        this._toastNodes = [];
    }

    //////////////////////////////////////////////////////////////////////////////
    // 预加载提示框资源
    public static preloadToast() {
        LHToastHelper.instance.preloadToastPrefab();
    }

    // 显示提示内容
    public static show(content: string, waitTime: number = 2.0,bgType: number = 1) {
        LHToastHelper.instance.showToast(content, waitTime);
    }

    // 清除所有显示节点
    public static clearAll() {
        LHToastHelper.instance.clearAllToast();
    }
    
    //////////////////////////////////////////////////////////////////////////////

}
