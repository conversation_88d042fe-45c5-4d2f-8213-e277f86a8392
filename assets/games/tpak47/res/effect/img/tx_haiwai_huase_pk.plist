<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>LOSE.png</key>
            <dict>
                <key>frame</key>
                <string>{{934,1202},{116,54}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{116,54}}</string>
                <key>sourceSize</key>
                <string>{116,54}</string>
            </dict>
            <key>PK_bg_blue.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{704,200}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{704,200}}</string>
                <key>sourceSize</key>
                <string>{704,200}</string>
            </dict>
            <key>PK_bg_red.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,204},{651,200}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{651,200}}</string>
                <key>sourceSize</key>
                <string>{651,200}</string>
            </dict>
            <key>PK_touxiang.png</key>
            <dict>
                <key>frame</key>
                <string>{{854,940},{138,138}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{138,138}}</string>
                <key>sourceSize</key>
                <string>{138,138}</string>
            </dict>
            <key>TP_dayu.png</key>
            <dict>
                <key>frame</key>
                <string>{{902,1310},{23,30}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{23,30}}</string>
                <key>sourceSize</key>
                <string>{23,30}</string>
            </dict>
            <key>TP_dengyu.png</key>
            <dict>
                <key>frame</key>
                <string>{{480,664},{27,25}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{27,25}}</string>
                <key>sourceSize</key>
                <string>{27,25}</string>
            </dict>
            <key>TP_kuang.png</key>
            <dict>
                <key>frame</key>
                <string>{{243,1506},{75,49}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{75,49}}</string>
                <key>sourceSize</key>
                <string>{75,49}</string>
            </dict>
            <key>TP_paixing_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1506},{239,50}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{239,50}}</string>
                <key>sourceSize</key>
                <string>{239,50}</string>
            </dict>
            <key>TP_win.png</key>
            <dict>
                <key>frame</key>
                <string>{{845,1310},{55,42}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{55,42}}</string>
                <key>sourceSize</key>
                <string>{55,42}</string>
            </dict>
            <key>TP_win_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,664},{476,60}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{476,60}}</string>
                <key>sourceSize</key>
                <string>{476,60}</string>
            </dict>
            <key>WIN.png</key>
            <dict>
                <key>frame</key>
                <string>{{876,540},{130,68}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{130,68}}</string>
                <key>sourceSize</key>
                <string>{130,68}</string>
            </dict>
            <key>bg.front.png</key>
            <dict>
                <key>frame</key>
                <string>{{876,364},{126,174}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{126,174}}</string>
                <key>sourceSize</key>
                <string>{126,174}</string>
            </dict>
            <key>guangxiao_ty.png</key>
            <dict>
                <key>frame</key>
                <string>{{854,1080},{128,120}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{128,120}}</string>
                <key>sourceSize</key>
                <string>{128,120}</string>
            </dict>
            <key>img_AKLZ_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{795,940},{214,57}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{214,57}}</string>
                <key>sourceSize</key>
                <string>{214,57}</string>
            </dict>
            <key>liandui_5_00000.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,406},{512,256}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{512,256}}</string>
                <key>sourceSize</key>
                <string>{512,256}</string>
            </dict>
            <key>tx_guangxiao2.png</key>
            <dict>
                <key>frame</key>
                <string>{{317,726},{226,238}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{226,238}}</string>
                <key>sourceSize</key>
                <string>{226,238}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00002.png</key>
            <dict>
                <key>frame</key>
                <string>{{845,1202},{87,106}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{87,106}}</string>
                <key>sourceSize</key>
                <string>{87,106}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00003.png</key>
            <dict>
                <key>frame</key>
                <string>{{874,614},{139,136}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{139,136}}</string>
                <key>sourceSize</key>
                <string>{139,136}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00004.png</key>
            <dict>
                <key>frame</key>
                <string>{{727,782},{141,156}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{141,156}}</string>
                <key>sourceSize</key>
                <string>{141,156}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00005.png</key>
            <dict>
                <key>frame</key>
                <string>{{845,196},{163,166}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{163,166}}</string>
                <key>sourceSize</key>
                <string>{163,166}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00006.png</key>
            <dict>
                <key>frame</key>
                <string>{{759,1354},{193,172}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{193,172}}</string>
                <key>sourceSize</key>
                <string>{193,172}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00007.png</key>
            <dict>
                <key>frame</key>
                <string>{{704,403},{209,170}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{209,170}}</string>
                <key>sourceSize</key>
                <string>{209,170}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00008.png</key>
            <dict>
                <key>frame</key>
                <string>{{305,1168},{251,184}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{251,184}}</string>
                <key>sourceSize</key>
                <string>{251,184}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00009.png</key>
            <dict>
                <key>frame</key>
                <string>{{516,406},{277,186}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{277,186}}</string>
                <key>sourceSize</key>
                <string>{277,186}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00010.png</key>
            <dict>
                <key>frame</key>
                <string>{{545,685},{281,180}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{281,180}}</string>
                <key>sourceSize</key>
                <string>{281,180}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00011.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1316},{295,188}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{295,188}}</string>
                <key>sourceSize</key>
                <string>{295,188}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00012.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,726},{313,196}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{313,196}}</string>
                <key>sourceSize</key>
                <string>{313,196}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00013.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,924},{307,198}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{307,198}}</string>
                <key>sourceSize</key>
                <string>{307,198}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00014.png</key>
            <dict>
                <key>frame</key>
                <string>{{708,2},{303,192}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{303,192}}</string>
                <key>sourceSize</key>
                <string>{303,192}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00015.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,1124},{301,190}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{301,190}}</string>
                <key>sourceSize</key>
                <string>{301,190}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00016.png</key>
            <dict>
                <key>frame</key>
                <string>{{558,1162},{285,190}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{285,190}}</string>
                <key>sourceSize</key>
                <string>{285,190}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00017.png</key>
            <dict>
                <key>frame</key>
                <string>{{526,968},{267,192}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{267,192}}</string>
                <key>sourceSize</key>
                <string>{267,192}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00018.png</key>
            <dict>
                <key>frame</key>
                <string>{{299,1354},{251,188}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{251,188}}</string>
                <key>sourceSize</key>
                <string>{251,188}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00019.png</key>
            <dict>
                <key>frame</key>
                <string>{{655,204},{197,188}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{197,188}}</string>
                <key>sourceSize</key>
                <string>{197,188}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00020.png</key>
            <dict>
                <key>frame</key>
                <string>{{311,966},{213,200}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{213,200}}</string>
                <key>sourceSize</key>
                <string>{213,200}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00021.png</key>
            <dict>
                <key>frame</key>
                <string>{{552,1354},{205,192}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{205,192}}</string>
                <key>sourceSize</key>
                <string>{205,192}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00022.png</key>
            <dict>
                <key>frame</key>
                <string>{{727,614},{145,166}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{145,166}}</string>
                <key>sourceSize</key>
                <string>{145,166}</string>
            </dict>
            <key>tx_haiwai_vs_shandian_00023.png</key>
            <dict>
                <key>frame</key>
                <string>{{870,782},{143,150}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{143,150}}</string>
                <key>sourceSize</key>
                <string>{143,150}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>tx_haiwai_huase_pk.png</string>
            <key>size</key>
            <string>{1015,1591}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:e16786fc8fb9b4ec7dd03eafab732607:3c96645d06626c3ed1ad0a689f15cb76:676401c912c2cf97cf2af47addd2a560$</string>
            <key>textureFileName</key>
            <string>tx_haiwai_huase_pk.png</string>
        </dict>
    </dict>
</plist>
