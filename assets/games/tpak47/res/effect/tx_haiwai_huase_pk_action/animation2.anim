{"__type__": "cc.AnimationClip", "_name": "animation2", "_objFlags": 0, "_rawFiles": null, "_duration": 0.1, "sample": 60, "speed": 0.5, "wrapMode": 1, "curveData": {"paths": {"VS_bg_blue_2/Text_1_0": {"props": {"position": []}}, "VS_bg_blue_2/touxiangkuang_7": {}, "VS_bg_blue_2/TP_paixing_bg_4": {}, "VS_bg_blue_2/img_AKLZ_bg_3": {}, "VS_bg_blue_2": {"props": {"position": [{"frame": 0, "value": [-319.7975, 46.7303]}, {"frame": 0.1, "value": [-449.5118, 46.7303]}], "scaleX": [{"frame": 0, "value": 1}, {"frame": 0.1, "value": 1}], "scaleY": [{"frame": 0, "value": 1}, {"frame": 0.1, "value": 1}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.1, "value": 0}], "color": [{"frame": 0, "value": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"frame": 0.1, "value": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}], "angle": [{"frame": 0, "value": 0}, {"frame": 0.1, "value": 0}]}, "comps": {"cc.Sprite": {"spriteFrame": [{"frame": 0, "value": {"__uuid__": "8ba53d11-d15a-42e9-bc11-d0ec1959b3de"}}, {"frame": 0.1, "value": {"__uuid__": "8ba53d11-d15a-42e9-bc11-d0ec1959b3de"}}]}}}, "VS_bg_red_3/Text_1": {"props": {"position": []}}, "VS_bg_red_3/touxiangkuang_7_0": {}, "VS_bg_red_3/TP_paixing_bg_4_0": {}, "VS_bg_red_3/img_AKLZ_bg_3_0": {}, "VS_bg_red_3": {"props": {"position": [{"frame": 0, "value": [246.2638, 45.1329]}, {"frame": 0.1, "value": [388.062, 44.9329]}], "scaleX": [{"frame": 0, "value": 1}], "scaleY": [{"frame": 0, "value": 1}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.1, "value": 0}], "color": [{"frame": 0, "value": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}], "angle": [{"frame": 0, "value": 0}]}, "comps": {"cc.Sprite": {"spriteFrame": [{"frame": 0, "value": {"__uuid__": "4127aa8f-39d0-445c-b400-2fd49368cdd3"}}]}}}, "liandui_5_00000_11": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}, "guangxiao_ty_12": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}, "tx_guangxiao2_13": {"props": {"position": [], "scaleX": [], "scaleY": [], "anchorX": [], "anchorY": [], "opacity": [], "angle": []}}, "VS_bg_blue_10": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "anchorX": [], "anchorY": []}}, "VS_bg_red_11": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "anchorX": [], "anchorY": []}}, "Node_1/tx_haiwai_vs_shandian_00001_26": {"props": {"opacity": [], "position": []}}, "Node_1_2/tx_haiwai_vs_shandian_00001_26": {"props": {"opacity": [], "position": []}}, "Node_1_2": {"props": {"position": [], "scaleX": [], "scaleY": []}}, "compare_1/img_leftBg": {"props": {"position": []}}, "compare_1/img_rightBg": {"props": {"position": []}}, "compare_1": {"props": {"position": [], "opacity": []}}, "compare_2/img_leftBg": {"props": {"position": []}}, "compare_2/img_rightBg": {"props": {"position": []}}, "compare_2": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}, "compare_3/img_leftBg": {"props": {"position": []}}, "compare_3/img_rightBg": {"props": {"position": []}}, "compare_3": {"props": {"position": [], "opacity": []}}, "sign_1": {"props": {"opacity": []}}, "sign_2": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}, "sign_3": {"props": {"position": [], "scaleX": [], "scaleY": [], "opacity": [], "angle": []}}}}, "events": []}