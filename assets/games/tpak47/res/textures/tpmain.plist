<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>DL_bg2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{118,119}</string>
                <key>spriteSourceSize</key>
                <string>{118,119}</string>
                <key>textureRect</key>
                <string>{{772,476},{118,119}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>DTshengyin.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{34,34}</string>
                <key>spriteSourceSize</key>
                <string>{34,34}</string>
                <key>textureRect</key>
                <string>{{860,941},{34,34}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Group 443269.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{506,91}</string>
                <key>spriteSourceSize</key>
                <string>{506,91}</string>
                <key>textureRect</key>
                <string>{{1,1},{506,91}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Group 443301.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{69,33}</string>
                <key>spriteSourceSize</key>
                <string>{69,33}</string>
                <key>textureRect</key>
                <string>{{857,781},{69,33}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>Group 443302.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{96,47}</string>
                <key>spriteSourceSize</key>
                <string>{96,47}</string>
                <key>textureRect</key>
                <string>{{602,970},{96,47}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Rectangle 1370.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{494,52}</string>
                <key>spriteSourceSize</key>
                <string>{494,52}</string>
                <key>textureRect</key>
                <string>{{1,94},{494,52}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>active1_base.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{344,118}</string>
                <key>spriteSourceSize</key>
                <string>{344,118}</string>
                <key>textureRect</key>
                <string>{{1,370},{344,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>active2_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{92,37}</string>
                <key>spriteSourceSize</key>
                <string>{92,37}</string>
                <key>textureRect</key>
                <string>{{508,980},{92,37}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>avatar.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{681,236},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>awd56 1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{30,30}</string>
                <key>spriteSourceSize</key>
                <string>{30,30}</string>
                <key>textureRect</key>
                <string>{{815,990},{30,30}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{105,110}</string>
                <key>spriteSourceSize</key>
                <string>{105,110}</string>
                <key>textureRect</key>
                <string>{{396,910},{105,110}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{86,86}</string>
                <key>spriteSourceSize</key>
                <string>{86,86}</string>
                <key>textureRect</key>
                <string>{{508,892},{86,86}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{99,88}</string>
                <key>spriteSourceSize</key>
                <string>{99,88}</string>
                <key>textureRect</key>
                <string>{{811,198},{99,88}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{119,118}</string>
                <key>spriteSourceSize</key>
                <string>{119,118}</string>
                <key>textureRect</key>
                <string>{{347,370},{119,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_base_map.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{2,0}</string>
                <key>spriteSize</key>
                <string>{365,52}</string>
                <key>spriteSourceSize</key>
                <string>{369,52}</string>
                <key>textureRect</key>
                <string>{{509,1},{365,52}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_dialog_box.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{245,49}</string>
                <key>spriteSourceSize</key>
                <string>{245,49}</string>
                <key>textureRect</key>
                <string>{{509,55},{245,49}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_green_base.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{124,37}</string>
                <key>spriteSourceSize</key>
                <string>{124,37}</string>
                <key>textureRect</key>
                <string>{{596,892},{124,37}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_grey_base.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{124,37}</string>
                <key>spriteSourceSize</key>
                <string>{124,37}</string>
                <key>textureRect</key>
                <string>{{596,931},{124,37}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_img.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{239,53}</string>
                <key>spriteSourceSize</key>
                <string>{239,53}</string>
                <key>textureRect</key>
                <string>{{497,106},{239,53}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_mess_dig.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{396,104}</string>
                <key>spriteSourceSize</key>
                <string>{396,104}</string>
                <key>textureRect</key>
                <string>{{1,148},{396,104}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_recharge.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,120}</string>
                <key>spriteSourceSize</key>
                <string>{120,120}</string>
                <key>textureRect</key>
                <string>{{376,731},{120,120}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_shadow.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{546,476},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>blind-1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{68,38}</string>
                <key>spriteSourceSize</key>
                <string>{68,38}</string>
                <key>textureRect</key>
                <string>{{844,711},{68,38}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>blind.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{86,32}</string>
                <key>spriteSourceSize</key>
                <string>{86,32}</string>
                <key>textureRect</key>
                <string>{{811,299},{86,32}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>blind2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{45,32}</string>
                <key>spriteSourceSize</key>
                <string>{45,32}</string>
                <key>textureRect</key>
                <string>{{860,894},{45,32}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_Add.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{191,74}</string>
                <key>spriteSourceSize</key>
                <string>{191,74}</string>
                <key>textureRect</key>
                <string>{{1,936},{191,74}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_betting.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{192,118}</string>
                <key>spriteSourceSize</key>
                <string>{192,118}</string>
                <key>textureRect</key>
                <string>{{182,735},{192,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_check_card.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{192,66}</string>
                <key>spriteSourceSize</key>
                <string>{192,66}</string>
                <key>textureRect</key>
                <string>{{478,476},{192,66}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_close.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{76,71}</string>
                <key>spriteSourceSize</key>
                <string>{76,71}</string>
                <key>textureRect</key>
                <string>{{655,706},{76,71}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_com_tiles-1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{182,118}</string>
                <key>spriteSourceSize</key>
                <string>{182,118}</string>
                <key>textureRect</key>
                <string>{{497,236},{182,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_com_tiles.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{182,118}</string>
                <key>spriteSourceSize</key>
                <string>{182,118}</string>
                <key>textureRect</key>
                <string>{{358,490},{182,118}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_fold.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{182,118}</string>
                <key>spriteSourceSize</key>
                <string>{182,118}</string>
                <key>textureRect</key>
                <string>{{468,356},{182,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_green1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{243,86}</string>
                <key>spriteSourceSize</key>
                <string>{243,86}</string>
                <key>textureRect</key>
                <string>{{182,490},{243,86}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_mess.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,71}</string>
                <key>spriteSourceSize</key>
                <string>{71,71}</string>
                <key>textureRect</key>
                <string>{{771,711},{71,71}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_recharge.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{202,96}</string>
                <key>spriteSourceSize</key>
                <string>{202,96}</string>
                <key>textureRect</key>
                <string>{{399,148},{202,96}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_rule.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,71}</string>
                <key>spriteSourceSize</key>
                <string>{71,71}</string>
                <key>textureRect</key>
                <string>{{787,880},{71,71}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_yellow.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{243,86}</string>
                <key>spriteSourceSize</key>
                <string>{243,86}</string>
                <key>textureRect</key>
                <string>{{270,490},{243,86}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>chaal-1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,30}</string>
                <key>spriteSourceSize</key>
                <string>{64,30}</string>
                <key>textureRect</key>
                <string>{{811,333},{64,30}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>chaal.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{94,33}</string>
                <key>spriteSourceSize</key>
                <string>{94,33}</string>
                <key>textureRect</key>
                <string>{{752,832},{94,33}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>chaal2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{45,32}</string>
                <key>spriteSourceSize</key>
                <string>{45,32}</string>
                <key>textureRect</key>
                <string>{{700,970},{45,32}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>d.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{34,36}</string>
                <key>spriteSourceSize</key>
                <string>{34,36}</string>
                <key>textureRect</key>
                <string>{{737,198},{34,36}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>gold_frame1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,1}</string>
                <key>spriteSize</key>
                <string>{133,38}</string>
                <key>spriteSourceSize</key>
                <string>{133,40}</string>
                <key>textureRect</key>
                <string>{{539,792},{133,38}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>gold_frame2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{238,73}</string>
                <key>spriteSourceSize</key>
                <string>{238,73}</string>
                <key>textureRect</key>
                <string>{{497,161},{238,73}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>gold_frame3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{164,39}</string>
                <key>spriteSourceSize</key>
                <string>{164,39}</string>
                <key>textureRect</key>
                <string>{{498,670},{164,39}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>he_avatr.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{1,1}</string>
                <key>spriteSize</key>
                <string>{141,143}</string>
                <key>spriteSourceSize</key>
                <string>{147,147}</string>
                <key>textureRect</key>
                <string>{{756,55},{141,143}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>he_base.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{171,90}</string>
                <key>spriteSourceSize</key>
                <string>{171,90}</string>
                <key>textureRect</key>
                <string>{{5,844},{171,90}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>he_base_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{108,108}</string>
                <key>spriteSourceSize</key>
                <string>{108,108}</string>
                <key>textureRect</key>
                <string>{{764,366},{108,108}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_exit_lobby.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{37,37}</string>
                <key>spriteSourceSize</key>
                <string>{37,37}</string>
                <key>textureRect</key>
                <string>{{776,962},{37,37}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_i.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{32,32}</string>
                <key>spriteSourceSize</key>
                <string>{32,32}</string>
                <key>textureRect</key>
                <string>{{752,928},{32,32}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_menu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{20,35}</string>
                <key>spriteSourceSize</key>
                <string>{20,35}</string>
                <key>textureRect</key>
                <string>{{876,1},{20,35}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_pk.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{108,110}</string>
                <key>spriteSourceSize</key>
                <string>{108,110}</string>
                <key>textureRect</key>
                <string>{{652,366},{108,110}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_play.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{35,35}</string>
                <key>spriteSourceSize</key>
                <string>{35,35}</string>
                <key>textureRect</key>
                <string>{{815,953},{35,35}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_settinge.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{35,34}</string>
                <key>spriteSourceSize</key>
                <string>{35,34}</string>
                <key>textureRect</key>
                <string>{{852,977},{35,34}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_switch_table.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{36,33}</string>
                <key>spriteSourceSize</key>
                <string>{36,33}</string>
                <key>textureRect</key>
                <string>{{773,198},{36,33}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_agree-1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{125,55}</string>
                <key>spriteSourceSize</key>
                <string>{125,55}</string>
                <key>textureRect</key>
                <string>{{358,674},{125,55}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_agree.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{125,55}</string>
                <key>spriteSourceSize</key>
                <string>{125,55}</string>
                <key>textureRect</key>
                <string>{{379,853},{125,55}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_blue.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{199,74}</string>
                <key>spriteSourceSize</key>
                <string>{199,74}</string>
                <key>textureRect</key>
                <string>{{178,855},{199,74}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_orange.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{200,71}</string>
                <key>spriteSourceSize</key>
                <string>{200,71}</string>
                <key>textureRect</key>
                <string>{{194,931},{200,71}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_reduse.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{125,55}</string>
                <key>spriteSourceSize</key>
                <string>{125,55}</string>
                <key>textureRect</key>
                <string>{{774,597},{125,55}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_refuse.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{125,55}</string>
                <key>spriteSourceSize</key>
                <string>{125,55}</string>
                <key>textureRect</key>
                <string>{{774,654},{125,55}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>img_tag_play.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{99,28}</string>
                <key>spriteSourceSize</key>
                <string>{99,28}</string>
                <key>textureRect</key>
                <string>{{722,858},{99,28}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>line.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{358,2}</string>
                <key>spriteSourceSize</key>
                <string>{358,2}</string>
                <key>textureRect</key>
                <string>{{1,490},{358,2}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>linght_green.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{96,96}</string>
                <key>spriteSourceSize</key>
                <string>{96,96}</string>
                <key>textureRect</key>
                <string>{{676,608},{96,96}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>linght_red.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{175,175}</string>
                <key>spriteSourceSize</key>
                <string>{175,175}</string>
                <key>textureRect</key>
                <string>{{5,490},{175,175}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>linght_yellow.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{175,175}</string>
                <key>spriteSourceSize</key>
                <string>{175,175}</string>
                <key>textureRect</key>
                <string>{{5,667},{175,175}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mine_avatr.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{1,0}</string>
                <key>spriteSize</key>
                <string>{114,114}</string>
                <key>spriteSourceSize</key>
                <string>{118,118}</string>
                <key>textureRect</key>
                <string>{{539,676},{114,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mine_base.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,0}</string>
                <key>spriteSize</key>
                <string>{380,114}</string>
                <key>spriteSourceSize</key>
                <string>{384,116}</string>
                <key>textureRect</key>
                <string>{{1,254},{380,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>paibei.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{94,130}</string>
                <key>spriteSourceSize</key>
                <string>{94,130}</string>
                <key>textureRect</key>
                <string>{{676,476},{94,130}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>pro_green.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{198,24}</string>
                <key>spriteSourceSize</key>
                <string>{198,24}</string>
                <key>textureRect</key>
                <string>{{539,832},{198,24}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>pro_green_base.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{206,32}</string>
                <key>spriteSourceSize</key>
                <string>{206,32}</string>
                <key>textureRect</key>
                <string>{{506,858},{206,32}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>score.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{40,41}</string>
                <key>spriteSourceSize</key>
                <string>{40,41}</string>
                <key>textureRect</key>
                <string>{{734,962},{40,41}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>score_unit.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{39,40}</string>
                <key>spriteSourceSize</key>
                <string>{39,40}</string>
                <key>textureRect</key>
                <string>{{860,852},{39,40}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>show-1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{43,37}</string>
                <key>spriteSourceSize</key>
                <string>{43,37}</string>
                <key>textureRect</key>
                <string>{{725,787},{43,37}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>show.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{94,33}</string>
                <key>spriteSourceSize</key>
                <string>{94,33}</string>
                <key>textureRect</key>
                <string>{{787,784},{94,33}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>side-1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{79,36}</string>
                <key>spriteSourceSize</key>
                <string>{79,36}</string>
                <key>textureRect</key>
                <string>{{733,706},{79,36}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>side.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{75,33}</string>
                <key>spriteSourceSize</key>
                <string>{75,33}</string>
                <key>textureRect</key>
                <string>{{822,784},{75,33}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>tp_img_liwu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{49,51}</string>
                <key>spriteSourceSize</key>
                <string>{51,51}</string>
                <key>textureRect</key>
                <string>{{674,779},{49,51}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>vs.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,68}</string>
                <key>spriteSourceSize</key>
                <string>{128,68}</string>
                <key>textureRect</key>
                <string>{{546,606},{128,68}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>tpmain.png</string>
            <key>size</key>
            <string>{900,1021}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:a14e6ad6ff89f3eebd68f9fd7ccf60a9:25ea26dbf1d2ec7749a402ead544d018:f34c8503d291f4058a705dd3152ef5e6$</string>
            <key>textureFileName</key>
            <string>tpmain.png</string>
        </dict>
    </dict>
</plist>
