import Common from "../../../../script/frame/common/Common";
import { Direction, GameEvent, RoomState, ZOrder, quickPayStyle } from "../../../../script/frame/common/Define";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import WaitHelper from "../../../../script/frame/extentions/WaitHelper";
import EventManager from "../../../../script/frame/manager/EventManager";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import TPAKAudioMng from "../core/TPAKAudioMng";
import { BetType, CardType, Constant, GameState, GameTextTips } from "../core/TPAKDefine";
import TPAKGameCore from "../core/TPAKGameCore";
import TPAKGameLogic from "../core/TPAKGameLogic";
import TPAKCardLayer from "./TPAKCardLayer";
import TPAKChipsLayer from "./TPAKChipsLayer";
import TPAKDealerLayer from "./TPAKDealerLayer";
import TPAKMeChatLayer from "./TPAKMeChatLayer";
import TPAKPlayerLayer from "./TPAKPlayerLayer";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class TPAKGameView extends BaseLayer {
    //////////////////////////////////////////////////////////////////////////////
    @property(cc.Node)
    playersNode: cc.Node = null

    @property(cc.Node)
    topBarNode: cc.Node = null;

    @property(cc.Node)
    addMultisNode: cc.Node = null;

    @property(cc.Node)
    chipLayer: cc.Node = null;

    @property(cc.Node)
    allBetNode: cc.Node = null;

    @property(cc.Node)
    aniNode: cc.Node = null;

    @property(cc.Node)
    aniDownNode: cc.Node = null;

    @property(cc.Prefab)
    cardPrefab: cc.Prefab = null;

    @property(cc.Node)
    compareDialog: cc.Node = null;

    @property(cc.Node)
    rechargeNode: cc.Node = null;

    @property(cc.Node)
    btnRechargeNode: cc.Node = null;

    @property(cc.Node)
    roomInfoNode: cc.Node = null;

    @property(cc.Node)
    gameEmojiNode: cc.Node = null;

    @property(cc.Node)
    imBackNode: cc.Node = null;

    @property(cc.Node)
    meChatNode: cc.Node = null;

    @property(cc.Node)
    waitNode: cc.Node = null;

    @property(cc.Node)
    waitStartNode: cc.Node = null;
    
    @property(cc.Node)
    tipDealerLayer: cc.Node = null;

    @property(cc.Node)
    tipsLayer: cc.Node = null;
    //////////////////////////////////////////////////////////////////////////////
    // 游戏核心对象
    private _gameCore: TPAKGameCore = null;
    private _gameLogic: TPAKGameLogic = null;
    // 声音对象
    private _audioMng: TPAKAudioMng = null;

    /** 筹码视图对象 */
    private _chipLayer: TPAKChipsLayer = null;

    // 游戏玩家操作对象
    private _allPlayers: TPAKPlayerLayer[] = [];

    // 座位号=>操作对象
    private _mapSeatPlayers = new Map<number, TPAKPlayerLayer>();
    /** 当前操作id */
    private _curAwaitSeatId: number = -1;

    /** 是否弹出菜单 */
    private _isOpenMenu: boolean;
    //等待充值金额
    private _needMoney:number = 0

    _compareSchedulerTime = 0;

    //////////////////////////////////////////////////////////////////////////////
    onLoad() {

        // 游戏核心对象
        this._gameCore = this.node.getComponent("TPAKGameCore");
        this._audioMng = this.node.getComponent("TPAKAudioMng");
        this._gameLogic = this.node.getComponent("TPAKGameLogic");

        this._chipLayer = this.chipLayer.getComponent(TPAKChipsLayer);
        this.chipLayer.active = true;
        // 游戏玩家层操作对象
        for (let i = 0; i < Constant.PLAYER_COUNT; ++i) {
            let playerNode = this.playersNode.getChildByName(`user${i}`);
            if (playerNode) {
                let playerLayer = playerNode.getComponent(TPAKPlayerLayer);
                this._allPlayers.push(playerLayer);
                playerLayer.setLocalSeatId(i);
            }
        }
        
        SceneManager.instance.loadDirAsset('res/textures/card', cc.SpriteFrame,(assets)=>{});

        this.imBackNode.active = false;
        this.meChatNode.active = false;
        // 隐藏相关节点
        this.clearUI();
    }

    start() {
        this._audioMng.playMusic();
    }

    onDestroy() {
        this.unscheduleAllCallbacks();
    }

    // 重置所有界面数据
    public resetAllUI() {
        // 重置玩家界面
        for (let i = 0; i < this._allPlayers.length; ++i) {
            let playerLayer = this._allPlayers[i];
            if (playerLayer.node.active) {
                playerLayer.clearData();
                playerLayer.reset();
            }
            playerLayer.showAwait(false);
            playerLayer.node.active = false;
        }

        // 清除位置列表
        this._mapSeatPlayers.clear();
        this.clearUI();
    }

    //清理桌子
    public clearUI() {
        this._chipLayer.resetBet();
        this.updateTableAllbet(0);
        this.addMultisNode.active = false;
        this.compareDialog.active = false;
        this.roomInfoNode.active = false;
        this.aniNode.stopAllActions();
        this.aniNode.removeAllChildren();
        this.aniDownNode.stopAllActions();
        this.aniDownNode.removeAllChildren();
        // this.gameEmojiNode.removeAllChildren();
        this.showReadyBtn(false);
        this.hideNeedRecharge();
        this.hideWaitTips();
        this.hideWaitStartTips();
        this.hidePotLimitTips();

        this._compareSchedulerTime = 0;
        this.unschedule(this.scheduleCompare);

    }

    // 开始重置
    public resetStart() {
        this.clearUI();

        this._mapSeatPlayers.forEach((value: any, key: number) => {
            value.reset();
            value.switchToGaming();
        });

        this._allPlayers.forEach((value: any, key: number) => {
            if(!this.hasSeatPlayer(key)){
                value.reset();
                value.node.active=false;
            }
        });

        this.showLoading(false);
    }
    //////////////////////////////////////////////////////////////////////////////
    // 业务操作相关接口
    //////////////////////////////////////////////////////////////////////////////
    // 指定的位置上是否有玩家 
    public hasSeatPlayer(localSeatId: number): boolean {
        if (this._mapSeatPlayers.has(localSeatId)) {
            return true;
        }
        return false;
    }

    // 设置玩家为游戏中
    public setPlayerGaming(localSeatId: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.setGameing();
            playerLayer.switchToGaming();
            if (playerLayer.isMePlayer) this.showHuanZhuo(false);
        }
    }
    // 设置玩家为等待
    public setPlayerAwaiting(localSeatId: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            //playerLayer.clearData();
            playerLayer.switchToGaming();

            playerLayer.showWaitTip(true);
        }
    }

    /** 切换等待用户 */
    public changeAwait(localSeatId: number, waittime: number, optsumtime: number) {
        let prevPlayerLayer = this._mapSeatPlayers.get(this._curAwaitSeatId);
        if (prevPlayerLayer) {
            prevPlayerLayer.switchToAwait(false, waittime, 0);
        }
        this._curAwaitSeatId = localSeatId;
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            if (playerLayer.isMePlayer) {
                this.updateOptState(playerLayer.isGaming);
            }
            playerLayer.hideOptTips();
            playerLayer.switchToAwait(true, waittime, optsumtime);
        }
    }

    /** 切换自己不可操作 */
    public cancelAwait(localSeatId: number, opt: boolean) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.switchToAwait(false, 0, 0,);

            if (opt) {
                this.updateOptState(true);
            }
        }
    }

    // 设置玩家的扑克数据
    public setPlayerPokerData(localSeatId: number, poker: any) {
        let cards = poker["cards"];
        let lizicards = poker["laizicards"];
        let emtype = Common.toInt(poker["cardtype"]);
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.setPlayerPokerData(cards,lizicards, emtype);
        } else {
            //玩家离开，结算时显示扑克
            playerLayer = this._allPlayers[localSeatId];
            if (playerLayer) {
                playerLayer.setPlayerPokerData(cards,lizicards, emtype);
            }
        }
    }
    /**
     * 更新玩家下注数据
     * @param info 
     */
    public updateBetMoney(localSeatId: number, curBet: any) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.updateBetMoney(curBet);
        }
    }

    // 更新玩家的金币
    public updatePlayerMoney(localSeatId: number, money: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.updateMoney(money);
        }
    }

    //下底注动画
    public doBetDiZhu(callback) {
        let difen = this._gameCore.difen;
        let playingCount = this._gameCore.getPlayingCount();
        let endPos = cc.v3(this.playersNode.convertToNodeSpaceAR(this.allBetNode.convertToWorldSpaceAR(this.allBetNode.getAnchorPoint())));
        let index = 0;
        for (let i = 0; i < this._allPlayers.length; ++i) {
            let playerLayer = this._allPlayers[i];
            if (playerLayer.isGaming) {
                let startPos = cc.v3(this.playersNode.convertToNodeSpaceAR(playerLayer.headWorldPos()));
                index++;
                let setindex=index;
                this._chipLayer.chipMove(difen, startPos, endPos, playerLayer.userDirection == Direction.LEFT, function () {
                    if (setindex == playingCount && callback) {
                        callback();
                    }
                });
                playerLayer.updateBetMoney(difen);

            }
        }
    }

    public doSendCard(callback) {
        let playingCount = this._gameCore.getPlayingCount();
        let cardCount = playingCount * 3;
        let startPos = cc.v3(0, 200);

        let startIndex = 0;
        for (let i = 0; i < this._allPlayers.length; ++i) {
            let playerLayer = this._allPlayers[i];
            if (this._gameCore.playerid == playerLayer.playerid) {
                startIndex = i;
            }
        }

        let index = 0;
        let setIndex = 0;
        let playerIndex = 0;
        let curIndex = startIndex;
        let self = this;
        while (index < this._allPlayers.length) {
            let playerLayer = this._allPlayers[curIndex];
            if (playerLayer.isGaming) {
                for (let cardIndex = 0; cardIndex < 3; cardIndex++) {
                    let card = cc.instantiate(this.cardPrefab);
                    this.aniDownNode.addChild(card);
                    card.position = startPos;
                    card.scale = 0.38;
                    card.angle = -180;

                    let cardNode = playerLayer.cardNodeArr[cardIndex];
                    let endPos = cc.v3(this.aniNode.convertToNodeSpaceAR(cardNode.convertToWorldSpaceAR(cardNode.getAnchorPoint())));

                    if (playerLayer.isMePlayer) {
                        let cardNodeBack = cardNode.getChildByName('img_back')
                        endPos = cc.v3(this.aniNode.convertToNodeSpaceAR(cardNodeBack.convertToWorldSpaceAR(cardNodeBack.getAnchorPoint())));
                    }
                    let cardsort = playingCount * cardIndex + playerIndex;
                    card.zIndex = cardCount - cardsort;

                    let delayTime = cardsort * 0.1;
                    let scale = playerLayer.isMePlayer ? 1 : 0.46;
                    let angle = cardNode.angle;
                    let setIndexfunc = setIndex;

                    cc.tween(card)
                        .delay(delayTime)
                        .call(() => {
                            self._audioMng.playSendCard();
                        })
                        .to(0.2, { position: endPos, scale: scale, angle: angle })
                        .removeSelf()
                        .call(function () {
                            playerLayer.showPokerBack(cardIndex);
                            if ((setIndexfunc == cardCount - 1) && callback) {
                                callback();
                            }
                        })
                        .start();
                    setIndex++;
                }
                playerIndex++;

            }
            curIndex++;
            curIndex = curIndex > this._allPlayers.length - 1 ? 0 : curIndex;
            index++;
        }

    }

    //下注动画
    public doBet(localSeatId: number, bet: number, callback) {
        let endPos = cc.v3(this.playersNode.convertToNodeSpaceAR(this.allBetNode.convertToWorldSpaceAR(this.allBetNode.getAnchorPoint())));
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            let startPos = cc.v3(this.playersNode.convertToNodeSpaceAR(playerLayer.headWorldPos()));
            this._chipLayer.chipMove(bet, startPos, endPos, playerLayer.userDirection == Direction.LEFT, function () {
                if (callback) {
                    callback();
                }
            });
        }
    }

    public doLookCard(localSeatId: number, info: any) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        playerLayer = this._allPlayers[localSeatId];
        if (playerLayer) {
            if (playerLayer.isMePlayer) {
                this.setPlayerPokerData(localSeatId, info);
                this.updateOptState(true);
                playerLayer.playSelfOpenCardAction(true);
            } else {
                playerLayer.showLookTips();
            }
        }

    }

    public doFoldCard(localSeatId: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.hideOptTips();
            playerLayer.showPackedTips();

            if (playerLayer.isMePlayer) {
                playerLayer.showCardType(false);
                this.updateOptState(false);
                this.hideNeedRecharge();
            }
        }

    }

    public doLose(localSeatId: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.showLose();
        }
    }

    public doCompetition(optLocalSeatId: number, compLocalSeatId: number, time: number, sound: boolean = true) {
        let optPlayerLayer = this._mapSeatPlayers.get(optLocalSeatId);
        let compPlayerLayer = this._mapSeatPlayers.get(compLocalSeatId);
        if (optPlayerLayer && compPlayerLayer) {
            if (optPlayerLayer.isMePlayer) {
                this.showCompareDialog(optPlayerLayer, compPlayerLayer, time);
            }

            let playerCount = this._gameCore.getPlayingCount();
            if (playerCount > 2) {
                let startPos = cc.v3(this.aniNode.convertToNodeSpaceAR(compPlayerLayer.headWorldPos()));
                let endPos = cc.v3(this.aniNode.convertToNodeSpaceAR(optPlayerLayer.headWorldPos()));
                let midPos = cc.v3((startPos.x + endPos.x) / 2, (startPos.y + endPos.y) / 2);
                this.playCompareArrow(optLocalSeatId, compLocalSeatId, midPos);

                compPlayerLayer.showBetOptTips('side');
                if (sound) {
                    this._audioMng.playCompareSide();
                }

            } else {
                compPlayerLayer.showBetOptTips('show');
                if (sound) {
                    this._audioMng.playCompareShow();
                }
            }

        }
    }

    public doCompetitionResult(localSeatId: number, confirm: boolean, info: any = null) {
        this.unschedule(this.scheduleCompare);
        this.compareDialog.active = false;

        let arrowNode = this.aniNode.getChildByName("arrowNode");
        if (arrowNode) {
            arrowNode.removeFromParent();
        }

        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        let playingCount = this._gameCore.getPlayingCount();

        if (playerLayer && playingCount > 2) {
            playerLayer.showPKReplay(confirm);
        }

        if (confirm) {
            let optseat = info['optseat'];
            let compseat = info['compseat'];
            let optCards = optseat['cards'];
            let optLaizicards = optseat['laizicards'];
            let comCards = compseat['cards'];
            let comLaizicards = compseat['laizicards'];
            let comLocalSeatId = this._gameCore.getLocalSeatId(compseat['seatid']);

            let loseSeatid = compseat['iswin'] == 1 ? optseat['seatid'] : compseat['seatid'];
            let winSeatid = compseat['iswin'] == 1 ? compseat['seatid'] : optseat['seatid'];
            let loseLocalSeatId = this._gameCore.getLocalSeatId(loseSeatid);
            let winLocalSeatId = this._gameCore.getLocalSeatId(winSeatid);
            let losePlayerLayer = this._mapSeatPlayers.get(loseLocalSeatId);

            let compPlayerLayer = this._mapSeatPlayers.get(this._gameCore.getLocalSeatId(compseat['seatid']));
            if(playingCount == 2&&compPlayerLayer){
                compPlayerLayer.showBetOptTips('show');
                this._audioMng.playCompareShow();
            }

            let self = this;
            let aniFinishCallback = function () {
                if (losePlayerLayer) {
                    losePlayerLayer.showLose();
                    if (losePlayerLayer.isMePlayer) {
                        losePlayerLayer.showCardType(false);
                        self.updateOptState(false);
                    }
                }
            }

            cc.tween(this.aniNode).delay(1.0).call(()=>{
                if (playingCount == 2) {
                    let battleInfo = { optCards: optCards, comCards: comCards, optLaziCards: optLaizicards, comLaziCards: comLaizicards };
                    
                    let samevalue=this._gameLogic.hasSameValue(compseat,optseat);
                    if(samevalue){
                        this.playSameLaziCompareAni(localSeatId, comLocalSeatId, compseat['iswin'] == 1, battleInfo, aniFinishCallback);
                    }else{
                        this.playCompareBattleAni(localSeatId, comLocalSeatId, compseat['iswin'] == 1, battleInfo, aniFinishCallback);
                    }
    
                } else {
                    this.playCompareVSAni(loseLocalSeatId, winLocalSeatId, aniFinishCallback);
                }  
            }).start();
        }
    }

    public doWaitRecharge(localSeatId: number, optsumtime: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            let rechargeBg = this.rechargeNode.getChildByName("recharge_bg");
            let btnRechargeBg = this.rechargeNode.getChildByName("bg_recharge");
            let cicle = cc.find("recharge_cicle/cicle", rechargeBg);
            let time = cc.find("recharge_cicle/time", rechargeBg);

            let remaintime = optsumtime;
            let self = this;
            this.rechargeNode.active = true;
            this.rechargeNode.stopAllActions();
            rechargeBg.active = true;
            btnRechargeBg.active=playerLayer.isMePlayer;

            let cicleAni = cicle.getComponent(cc.Animation);
            let cicleAnimState = cicleAni.play('rechargeCicle');
            cicleAnimState.wrapMode = cc.WrapMode.Loop;

            if (playerLayer.isMePlayer) {
                let timeNode = this.btnRechargeNode.getChildByName("layout").getChildByName("time");
                timeNode.active = false;
            }

            cc.find("layout/name", rechargeBg).getComponent(cc.Label).string = playerLayer.textName.string + ' ';
            time.getComponent(cc.Label).string = remaintime + '';
            cc.tween(this.rechargeNode)
                .repeat(optsumtime,
                    cc.tween().delay(1).call(
                        function () {
                            remaintime--;
                            time.getComponent(cc.Label).string = remaintime + '';
                        }
                    )
                )
                .call(function () {
                    self.rechargeNode.active = false;
                    self.rechargeNode.stopAllActions();
                })
                .start();
        }
    }

    // 玩家摊牌
    public doTanPai(localSeatId: number, isWin: boolean = false) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (!playerLayer) {
            //玩家离开，结算时显示扑克
            playerLayer = this._allPlayers[localSeatId];
        }
        if (playerLayer) {
            if (playerLayer.isMePlayer) {
                playerLayer.hideTips();
                playerLayer.playSelfOpenCardAction();
            } else {
                playerLayer.hideTips();
                playerLayer.playOtherOpenCardAction(isWin);
            }

        }
    }

    // 处理游戏结算
    public doSettlement(winResultInfo: any,loseResultInfo:any,callback) {
        let self = this;

        let prevPlayerLayer = this._mapSeatPlayers.get(this._curAwaitSeatId);
        if (prevPlayerLayer) {
            prevPlayerLayer.switchToAwait(false, 0, 0);
        }

        let winLocalSeatId=winResultInfo.localSeatId;
        let winPlayerLayer = this._mapSeatPlayers.get(winResultInfo.localSeatId);
        if (!winPlayerLayer) return;

        let winScore = winResultInfo.changemoney;
        let startPos = cc.v3(this.playersNode.convertToNodeSpaceAR(this.allBetNode.convertToWorldSpaceAR(this.allBetNode.getAnchorPoint())));
        let endPos = cc.v3(this.playersNode.convertToNodeSpaceAR(winPlayerLayer.headWorldPos()));

        let chipCallback = function () {
            winPlayerLayer.playScoreChange(winScore);
            if (winPlayerLayer.isMePlayer) {
                self.playMeWinAni(() => {
                    if(callback){
                        callback();
                    }
                });
            } else {
                // if(self._mapSeatPlayers.get(winLocalSeatId)){
                //     self.playShangWinAni(endPos, () => {
                //     });
                //     self.playXiaWinAni(endPos, () => {
                //         if(callback){
                //             callback();
                //         }
                //     });
                // }else{
                //     if(callback){
                //         callback();
                //     }
                // }
                self.playShangWinAni(endPos, () => {
                });
                self.playXiaWinAni(endPos, () => {
                    if(callback){
                        callback();
                    }
                });
            }

            self._audioMng.playWin();
            self._audioMng.playWinGame();

        }

        //hide tip
        for (let index = 0; index < this._allPlayers.length; index++) {
            this._allPlayers[index].hideTips();
        }

        this._chipLayer.chipMoveToUser(winScore, startPos, endPos, function () {
            chipCallback();
        });

    }

    public doTipDealer(localSeatId: number){
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if(!playerLayer)return;
        let info:any={};
        info.playerPos= cc.v3(this.tipDealerLayer.convertToNodeSpaceAR(playerLayer.headWorldPos()));
        this.tipDealerLayer.getComponent(TPAKDealerLayer).doTipDealer(info);
    }

    // 处理暂离回来
    public doZanLi() {
        this.imBackNode.active = true;
    }


    // 处理暂离回来
    public doZanLiComback() {
        // this.maskNode.active = false;
    }

    public doRoomChat(sendLocalSeatId, type, content) {
        let playerLayer = this._mapSeatPlayers.get(sendLocalSeatId);
        if (playerLayer) {
            playerLayer.showChat(type, content);
        }

    }

    public doRoomIntertChat(sendLocalSeatId, recLocalSeatId, content) {

        let sendPlayerLayer = this._mapSeatPlayers.get(sendLocalSeatId);
        if (!sendPlayerLayer) return;

        let recPlayerLayer = this._mapSeatPlayers.get(recLocalSeatId);
        if (!recPlayerLayer) return;

        let startPos = cc.v3(this.gameEmojiNode.convertToNodeSpaceAR(sendPlayerLayer.headWorldPos()));
        let endPos = cc.v3(this.gameEmojiNode.convertToNodeSpaceAR(recPlayerLayer.headWorldPos()));


        UIHelper.playInteractExpression(this.gameEmojiNode, startPos, endPos, content, recPlayerLayer.userDirection == Direction.LEFT);
    }


    public doGetTaskAward(localSeatId,award){
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (!playerLayer) return;
        playerLayer.playScoreChange(award);
    }
    //////////////////////////////////////////////////////////////////////////////
    // 界面操作相关接口
    // 显示换桌按钮
    public showHuanZhuo(isShow: boolean) {
        // this.huanZhuoNode.active = isShow;
        // if (isShow) {
        //     this.updateOptState(false, false);
        // }
    }

    // 显示准备按钮
    public showReadyBtn(show: boolean, time: number = 0) {

    }

    //加载
    public showLoading(show: boolean) {
        // this.loadingLayer.active = show;
        // if (show) {
        //     this.loadingLayer.getChildByName("loadAni").getComponent(sp.Skeleton).animation = "loading";
        // }
    }

    // 显示玩家信息
    public showPlayer(localSeatId: number, userInfo: any) {
        if (localSeatId < 0 || localSeatId >= this._allPlayers.length) {
            console.error(`TPGameView.showPlayer: invalid local seat. playerid: ${userInfo.playerid}, localSeat: ${localSeatId}`);
            return;
        }
        let playerLayer = this._allPlayers[localSeatId];
        if (playerLayer) {
            this._mapSeatPlayers.set(localSeatId, playerLayer);

            playerLayer.setUserInfo(userInfo);
            if (this._gameCore.roomState == RoomState.GAME || this._gameCore.gameState == GameState.EM_TEENPATTI_GAMESTATE_END) {
                playerLayer.resetBase();
            } else {
                playerLayer.reset();
            }
            // playerLayer.reset();

            if (!playerLayer.isMePlayer) {
                playerLayer.switchToGaming();
            }
        }
    }

    // 隐藏玩家信息
    public hidePlayer(localSeatId: number) {
        if (!this._mapSeatPlayers) {
            console.error(`TPGameView.hidePlayer: invalid mapSeatPlayers. localSeat: ${localSeatId}`);
            return;
        }
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            if (this._gameCore.roomState == RoomState.GAME || this._gameCore.gameState == GameState.EM_TEENPATTI_GAMESTATE_END) {
                playerLayer.exitInGaming();
                playerLayer.clearDataInGame();
            } else {
                playerLayer.node.active = false;
                playerLayer.clearData();
            }
        }
        this._mapSeatPlayers.delete(localSeatId);
    }

    // 显示玩家的庄家标记
    public showPlayerBankerFlag(localSeatId: number) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.showBankerFlag(true);
        }
    }

    public showPlayerBetTips(localSeatId: number, betType: number, double: boolean = false, seen: boolean = false, animation: boolean = true) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            let name = '';
            let playerCount = this._gameCore.getPlayingCount();
            if (betType == BetType.FILL) {
                if (seen) {
                    name = double ? 'chaalX2' : 'chaal';
                } else {
                    name = double ? 'blindX2' : 'blind';
                }
            } else if (betType == BetType.COMPETITION) {
                name = playerCount == 2 ? 'show' : 'side';
            }
            playerLayer.showBetOptTips(name, animation);
        }
    }

    // 显示玩家扑克
    public showPlayerPoker(localSeatId: number, showType: boolean = false) {
        let playerLayer = this._mapSeatPlayers.get(localSeatId);
        if (playerLayer) {
            playerLayer.showAllPokers();
            if (showType) {
                playerLayer.showCardType(true);
            }
            if(playerLayer.isMePlayer){
                playerLayer.showCardLizi();
            }
        }
    }


    // 显示准备提示
    public showReadyTips(time: number) {
        this.waitStartNode.active = true;
        let self = this;
        let count = Math.floor(time);
        if (count <= 0) return;
        this.waitStartNode.stopAllActions();
        let txt=this.waitStartNode.getChildByName('txt').getComponent(cc.Label);
        txt.string=Common.stringFormat(GameTextTips.UI_TXT_WAIT_START_GAME, [count]);
        cc.tween(this.waitStartNode).then(cc.tween().delay(1.0).call(function () {
            count--;
            txt.string=Common.stringFormat(GameTextTips.UI_TXT_WAIT_START_GAME, [count]);
        })).repeat(count).call(function () {
            self.hideWaitStartTips();
        }).start();
    }

    //隐藏
    public hideWaitStartTips() {
        this.waitStartNode.stopAllActions();
        this.waitStartNode.active = false;
    }

    //等待
    public showWaitTips() {
        this.waitNode.active = true;
    }

    //隐藏
    public hideWaitTips() {
        this.waitNode.active = false;
    }

    //potlimit
    public showPotLimitTips() {
        this.tipsLayer.active = true;
        cc.tween(this.tipsLayer).delay(1.2).call(()=>{
            this.tipsLayer.active = false;
        }).start();
    }

    //隐藏 potlimit
    public hidePotLimitTips() {
        this.tipsLayer.stopAllActions();
        this.tipsLayer.active = false;
    }

    public showBanker(bankerLocalId: number) {
        let bankerPlayerLayer = this._mapSeatPlayers.get(bankerLocalId);
        if (bankerPlayerLayer) {
            bankerPlayerLayer.showBankerFlag(true);
        }
    }

    public updateSeeBtnState() {
        let btnSee = this.addMultisNode.getChildByName("SeenBtn");
        let isMelook = this._gameCore.isMeLook;
        let playerGaming = this._gameCore.isPlayerMeGaming();
        btnSee.active = !isMelook && playerGaming;
    }

    //设置操作状态
    public updateOptState(show: boolean) {
        this.addMultisNode.active = show;
        if (!show) return;

        let btnPack = this.addMultisNode.getChildByName("PackBtn");
        let btnSideShow = this.addMultisNode.getChildByName("ShowBtn");
        let btnChaal = this.addMultisNode.getChildByName("ChaalBtn");
        let btnChaalDouble = this.addMultisNode.getChildByName("ChaalDoubleBtn");
        let btnSee = this.addMultisNode.getChildByName("SeenBtn");

        let showGray = btnSideShow.getChildByName("gray");
        let labShow = btnSideShow.getChildByName("ShowTxt").getComponent(cc.Label);
        let labChaal = btnChaal.getChildByName("ChaalTxt").getComponent(cc.Label);
        let labDouble = btnChaalDouble.getChildByName("DoubleTxt").getComponent(cc.Label);
        let labScoreNum = btnChaal.getChildByName("ScoreNum").getComponent(cc.Label);
        let labDoubleScore = btnChaalDouble.getChildByName("DoubleScoreNum").getComponent(cc.Label);

        let isMelook = this._gameCore.isMeLook;
        let playerGaming = this._gameCore.isPlayerMeGaming();
        let isCurrent = this._gameCore.optLocalSeatId == this._gameCore.meLocalSeatId
            && this._gameCore.optLocalSeatId != this._gameCore.optCompareLocalSeatId;

        btnSee.active = !isMelook && playerGaming;
        btnSideShow.active = isCurrent;
        btnChaal.active = isCurrent;
        btnChaalDouble.active = isCurrent;
        btnPack.active = isCurrent;

        if (!isCurrent) return;

        let playingCount = this._gameCore.getPlayingCount();
        let turnnum = this._gameCore.turnnum;
        let difen = this._gameCore.difen;
        let curtimes = this._gameCore.curtimes;
        let chaallimit = this._gameCore.chaallimit;
        let curbet = 0;
        let doubleCurbet = 0;

        if (isMelook) {
            labChaal.string = GameTextTips.OPT_TXT_CHAAL;
            labDouble.string = GameTextTips.OPT_TXT_CHAAL;
            curbet = difen * curtimes * 2;
        } else {
            labChaal.string = GameTextTips.OPT_TXT_BLIND;
            labDouble.string = GameTextTips.OPT_TXT_BLIND;
            curbet = difen * curtimes;
        }
        if(curbet>chaallimit){
            curbet=chaallimit;
        }
        doubleCurbet=curbet*2;
        if(doubleCurbet>chaallimit){
            doubleCurbet=chaallimit;
        }

        showGray.active = turnnum < this._gameCore.compminturnnum;
        btnSideShow.getComponent(ButtonLayer).enableTouch = turnnum >= this._gameCore.compminturnnum;
        if (playingCount > 2) {
            labShow.string = GameTextTips.OPT_TXT_SIDE_SHOW;
            labShow.fontSize = 28;
        } else {
            labShow.string = GameTextTips.OPT_TXT_SHOW;
            labShow.fontSize = 36;
        }
        labScoreNum.string = Common.moneyString(curbet);
        labDoubleScore.string = Common.moneyString(doubleCurbet);
    }



    //更新底分
    public updateTableCell(cell: number) {
        // let txtCell = this.infoNode.getChildByName("txtScore").getComponent(cc.Label);
        // txtCell.string = Common.moneyString(cell, 6, 0);
    }

    //更新总注
    public updateTableAllbet(total: number) {
        let txtTotal = this.allBetNode.getChildByName("bet").getComponent(cc.Label);
        txtTotal.string = Common.moneyString(total);
    }


    public showNeedRecharge(opttime: number,needMoney:number=0) {
        if(this.rechargeNode.active)return;
        this._needMoney = needMoney
        let rechargeBg = this.rechargeNode.getChildByName("recharge_bg");
        let btnRechargeBg = this.rechargeNode.getChildByName("bg_recharge");
        let timeNode = this.btnRechargeNode.getChildByName("layout").getChildByName("time");
        let remaintime = opttime;
        rechargeBg.active = false;
        btnRechargeBg.active = true;
        timeNode.active = true;
        this.rechargeNode.active = true;
        this.rechargeNode.stopAllActions();
        timeNode.getComponent(cc.Label).string = '(' + remaintime + ')';
        cc.tween(this.rechargeNode)
            .repeat(opttime, cc.tween().delay(1).call(() => {
                remaintime--;
                timeNode.getComponent(cc.Label).string = '(' + remaintime + ')';
            }))
            .call(() => {
                timeNode.active = false;
            })
            .start();
    }

    public hideNeedRecharge() {
        this.rechargeNode.stopAllActions();
        this.rechargeNode.active = false;
    }


    public showCompareDialog(optPlayer: TPAKPlayerLayer, compPlayer: TPAKPlayerLayer, time: number) {
        let compareUser = this.compareDialog.getChildByName('compareUser');
        let targetUser = this.compareDialog.getChildByName('targetUser');
        let labWho = this.compareDialog.getChildByName('whoTxt').getComponent(cc.Label);
        let labRemaining = cc.find("RefuseBtn/bg_dialog_box/remainingTxt", this.compareDialog).getComponent(cc.Label);
        let labTime = this.compareDialog.getChildByName('timeCountDown').getComponent(cc.Label);

        let rejectcompnum = this._gameCore.rejectcompnum;
        let rejectnum = this._gameCore.rejectnum;
        let cdTime = time;
        labRemaining.string = (rejectcompnum - rejectnum) + '';
        labWho.string = compPlayer.textName.string + ' ' + GameTextTips.UI_TXT_REQ_SHOW_CARDS;


        compareUser.getChildByName('bg').getChildByName('userName').getComponent(cc.Label).string = compPlayer.textName.string;
        compareUser.getChildByName('head').getChildByName('sprite').getComponent(cc.Sprite).spriteFrame = compPlayer.userHeadSprite.spriteFrame;
        targetUser.getChildByName('bg').getChildByName('userName').getComponent(cc.Label).string = optPlayer.textName.string;
        targetUser.getChildByName('head').getChildByName('sprite').getComponent(cc.Sprite).spriteFrame = optPlayer.userHeadSprite.spriteFrame;
        labTime.string = cdTime + 's';

        this._compareSchedulerTime = cdTime;
        this.compareDialog.active = true;
        this.unschedule(this.scheduleCompare);
        this.schedule(this.scheduleCompare, 1);
    }

    public scheduleCompare() {
        this._compareSchedulerTime--;
        if (this._compareSchedulerTime <= 0) {
            this.unschedule(this.scheduleCompare);
        }
        let labTime = this.compareDialog.getChildByName('timeCountDown').getComponent(cc.Label);
        labTime.string = Math.floor(this._compareSchedulerTime) + 's';
    }


    private playCompareArrow(optLocalSeatId: number, compLocalSeatId: number, startPos) {
        let angles = [
            [0, 52, 28, 312, 290],
            [234, 0, 345, 287, 270],
            [209, 166, 0, 270, 255],
            [130, 105, 90, 0, 200],
            [110, 90, 75, 20, 0]
        ];

        let pathIndexs = [
            [1, 1, 4, 3, 4],
            [1, 1, 2, 3, 3],
            [4, 2, 1, 3, 3],
            [3, 3, 3, 1, 2],
            [4, 3, 3, 2, 1]
        ];

        let name = "tx_haiwai_jiantou_" + pathIndexs[compLocalSeatId][optLocalSeatId];
        let path = "res/effect/" + name;
        let angle = angles[compLocalSeatId][optLocalSeatId];
        let self = this;

        this.loadAsset(path, cc.Prefab, function (assets: cc.Prefab) {
            let arrowNode = cc.instantiate(assets);
            self.aniNode.addChild(arrowNode);
            arrowNode.position = startPos;
            arrowNode.angle = angle;
            arrowNode.name = "arrowNode";

            let pAni = arrowNode.getComponent(cc.Animation);
            let animState = pAni.play(name);
            animState.wrapMode = cc.WrapMode.Loop;

        })
    }

    private playCompareVSAni(loseLocalSeatId: number, winLocalSeatId: number, callback) {
        let self = this;
        this.loadAsset("res/effect/tx_haiwai_bipai", cc.Prefab, function (assets: cc.Prefab) {
            let bipaiNode = cc.instantiate(assets);
            self.aniNode.addChild(bipaiNode);

            let pAni = bipaiNode.getComponent(cc.Animation);
            pAni.play("tx_haiwai_bipai");
            pAni.on(cc.Animation.EventType.FINISHED, function () {
                self.playCompareShanDian(loseLocalSeatId, winLocalSeatId, callback);
            });

            self._audioMng.playVS();
        })
    }

    private playCompareShanDian(loseLocalSeatId: number, winLocalSeatId: number, callback) {
        let angles: number[][] = [
            [0, 56, 32, 311, 293],
            [234, 0, 343, 285, 270],
            [212, 163, 0, 270, 255],
            [128, 104, 90, 0, 195],
            [110, 91, 75, 16, 0],
        ];

        let scales: number[][] = [
            [1, 1.05, 1.45, 1.85, 1.7],
            [1.05, 1, 0.6, 2.4, 2.4],
            [1.4, 0.6, 1, 2.15, 2.35],
            [1.85, 2.4, 2.15, 1, 0.6],
            [1.7, 2.42, 2.4, 0.6, 1]
        ]

        let compPlayerLayer = this._mapSeatPlayers.get(winLocalSeatId);
        if (!compPlayerLayer) return;

        let angle = angles[winLocalSeatId][loseLocalSeatId];
        let scale = scales[winLocalSeatId][loseLocalSeatId];
        let self = this;
        let startPos = cc.v3(this.aniNode.convertToNodeSpaceAR(compPlayerLayer.headWorldPos()));

        this.loadAsset("res/effect/tx_haiwai_bipai_shandian1", cc.Prefab, function (assets: cc.Prefab) {
            let shandianNode = cc.instantiate(assets);
            self.aniNode.addChild(shandianNode);
            shandianNode.scale = scale;
            shandianNode.angle = 180 + angle;
            shandianNode.position = startPos;

            let pAni = shandianNode.getComponent(cc.Animation);
            let animState = pAni.play("tx_haiwai_bipai_shandian1");
            animState.wrapMode = cc.WrapMode.Loop;

            cc.tween(shandianNode)
                .delay(1)
                .removeSelf()
                .call(function () {
                    self.playCompareShanDian2(loseLocalSeatId, callback);
                })
                .start();

            self._audioMng.playThound();
        })
    }

    private playCompareShanDian2(loseLocalSeatId: number, callback) {
        let self = this;
        let optPlayerLayer = this._mapSeatPlayers.get(loseLocalSeatId);
        if (!optPlayerLayer) return;

        let endPos = cc.v3(this.aniNode.convertToNodeSpaceAR(optPlayerLayer.headWorldPos()));
        let scale = optPlayerLayer.isMePlayer ? 0.55 : 0.65;
        this.loadAsset("res/effect/tx_haiwai_bipai_shandian2", cc.Prefab, function (assets: cc.Prefab) {
            let shandianNode = cc.instantiate(assets);
            self.aniNode.addChild(shandianNode);
            shandianNode.position = endPos
            shandianNode.scale = scale;

            let pAni = shandianNode.getComponent(cc.Animation);
            pAni.play("tx_haiwai_bipai_shandian2");
            pAni.on(cc.Animation.EventType.FINISHED, function () {
                if (callback) {
                    callback();
                }
            });
        })
    }

    private playCompareBattleAni(optLocalSeatId: number, compLocalSeatId: number, compWin: boolean, info: any, callback) {
        let self = this;
        let optPlayerLayer = this._mapSeatPlayers.get(optLocalSeatId);
        let comPlayerLayer = this._mapSeatPlayers.get(compLocalSeatId);
        this.loadAsset("res/effect/tx_haiwai_vs", cc.Prefab, function (assets: cc.Prefab) {
            let vsNode = cc.instantiate(assets);
            vsNode.scale=1.2;

            let pAni = vsNode.getComponent(cc.Animation);
            self.aniNode.addChild(vsNode);
            let aniState = pAni.play('animation0');

            let leftBg = vsNode.getChildByName("VS_bg_blue_2");
            let rightBg = vsNode.getChildByName("VS_bg_red_3");
            let leftU = cc.find("node_head/img_u", leftBg);
            let rightU = cc.find("node_head/img_u", rightBg);
            let leftHead = cc.find("node_head/node_leftHead", leftBg);
            let rightHead = cc.find("node_head/node_rightHead", rightBg);
            let leftName = cc.find("node_head/txt_leftName", leftBg);
            let rightName = cc.find("node_head/txt_rightName", rightBg);
            let leftCardTypeBg = leftBg.getChildByName("img_cardType");
            let rightCardTypeBg = rightBg.getChildByName("img_cardType");
            let leftCardType = leftCardTypeBg.getChildByName("txt_cardType");
            let rightCardType = rightCardTypeBg.getChildByName("txt_cardType");
            let leftCardTypeBgLz = leftBg.getChildByName("img_cardTypeLz");
            let rightCardTypeBgLz = rightBg.getChildByName("img_cardTypeLz");
            leftCardType.active = false;
            rightCardType.active = false;


            if (optPlayerLayer && comPlayerLayer) {
                leftHead.getChildByName('sprite').getComponent(cc.Sprite).spriteFrame = comPlayerLayer.userHeadSprite.spriteFrame;
                rightHead.getChildByName('sprite').getComponent(cc.Sprite).spriteFrame = optPlayerLayer.userHeadSprite.spriteFrame;
                leftName.getComponent(cc.Label).string = comPlayerLayer.textName.string;
                rightName.getComponent(cc.Label).string = optPlayerLayer.textName.string;
                leftU.active = comPlayerLayer.isMePlayer;
                rightU.active = optPlayerLayer.isMePlayer;
                leftName.active = !comPlayerLayer.isMePlayer;
                rightName.active = !optPlayerLayer.isMePlayer;

                let optCards = [];
                let optLaziCards = [];
                let comCards = [];
                let comLaziCards = [];
                for (let key in info.optCards) {
                    optCards.push(info.optCards[key]);
                }
                for (let key in info.comCards) {
                    comCards.push(info.comCards[key]);
                }
                for (let key in info.optLaziCards) {
                    optLaziCards.push(info.optLaziCards[key]);
                }
                for (let key in info.comLaziCards) {
                    comLaziCards.push(info.comLaziCards[key]);
                }

                for (let index = 0; index < 3; index++) {
                    let leftCard = leftBg.getChildByName("left_poker_" + (index + 1)).getChildByName("poker");
                    let lanum = leftCardTypeBgLz.getChildByName("img_num" + (index + 1));
                    let lalogo = leftCardTypeBgLz.getChildByName("img_logo" + (index + 1));
                    let card = comCards[index];
                    let lazicard = comLaziCards[index];
                    let isLazi=self._gameLogic.isLaziCardValue(card);
                    let cardres=self._gameCore.getCardRes(card['color'],card['number']);
                    self.setSpriteFrame(lanum.getComponent(cc.Sprite), "res/textures/card/" + cardres.num);
                    self.setSpriteFrame(lalogo.getComponent(cc.Sprite), "res/textures/card/" + cardres.logo);     

                    let cardLayer= leftCard.getComponent(TPAKCardLayer);
                    if(!cardLayer)leftCard.addComponent(TPAKCardLayer);
                    cardLayer.showPokerData(Common.toInt(lazicard["color"]), Common.toInt(lazicard["number"]));
                    if(isLazi){
                        cardLayer.showPokerLz();
                    }
                }

                for (let index = 0; index < 3; index++) {
                    let rightCard = rightBg.getChildByName("right_poker_" + (index + 1)).getChildByName("poker");
                    let lanum = rightCardTypeBgLz.getChildByName("img_num" + (index + 1));
                    let lalogo = rightCardTypeBgLz.getChildByName("img_logo" + (index + 1));
                    let card = optCards[index];
                    let lazicard =  optLaziCards[index];
                    let isLazi=self._gameLogic.isLaziCardValue(card);
                    let cardres=self._gameCore.getCardRes(card['color'],card['number']);
                    self.setSpriteFrame(lanum.getComponent(cc.Sprite), "res/textures/card/" + cardres.num);
                    self.setSpriteFrame(lalogo.getComponent(cc.Sprite), "res/textures/card/" + cardres.logo);     

                    let cardLayer= rightCard.getComponent(TPAKCardLayer);
                    if(!cardLayer)rightCard.addComponent(TPAKCardLayer);
                    cardLayer.showPokerData(Common.toInt(lazicard["color"]), Common.toInt(lazicard["number"]));
                    if(isLazi){
                        cardLayer.showPokerLz();
                    }
                }

            }

            cc.tween(self.aniNode)
                .delay(0.5)
                .call(() => {
                    //音效
                    self._audioMng.playCompare();
                }
                )
                .start();

            let finishCallback = function () {
                if (aniState.name == "animation0") {
                    let aniName = compWin ? "animation2" : "animation1";
                    aniState = pAni.play(aniName);
                } else {
                    cc.tween(vsNode)
                        .delay(1)
                        .call(function () {
                            if (callback) {
                                callback();
                            }
                        })
                        .removeSelf()
                        .start();
                }
            }

            pAni.on(cc.Animation.EventType.FINISHED, function () {
                finishCallback();
            });
        })
    }


    private playSameLaziCompareAni(optLocalSeatId: number, compLocalSeatId: number, compWin: boolean, info: any, callback) {
        let self = this;
        let optPlayerLayer = this._mapSeatPlayers.get(optLocalSeatId);
        let comPlayerLayer = this._mapSeatPlayers.get(compLocalSeatId);
        this.loadAsset("res/effect/tx_haiwai_huase_pk", cc.Prefab, function (assets: cc.Prefab) {
            let vsNode = cc.instantiate(assets);
            vsNode.scale = 1.2;

            let pAni = vsNode.getComponent(cc.Animation);
            self.aniNode.addChild(vsNode);
            pAni.play('animation0');

            let leftBg = vsNode.getChildByName("VS_bg_blue_2");
            let rightBg = vsNode.getChildByName("VS_bg_red_3");
            let leftU = cc.find("Text_1_0", leftBg);
            let rightU = cc.find("Text_1", rightBg);
            let leftHead = cc.find("touxiangkuang_7/node_leftHead", leftBg);
            let rightHead = cc.find("touxiangkuang_7_0/node_rightHead", rightBg);
            let leftCardTypeBgLz = leftBg.getChildByName("img_AKLZ_bg_3");
            let rightCardTypeBgLz = rightBg.getChildByName("img_AKLZ_bg_3_0");
            let leftWin = leftBg.getChildByName("img_leftWin");
            let rightWin = rightBg.getChildByName("img_rightWin");


            if (optPlayerLayer && comPlayerLayer) {
                leftHead.getChildByName('sprite').getComponent(cc.Sprite).spriteFrame = comPlayerLayer.userHeadSprite.spriteFrame;
                rightHead.getChildByName('sprite').getComponent(cc.Sprite).spriteFrame = optPlayerLayer.userHeadSprite.spriteFrame;
                leftU.active = comPlayerLayer.isMePlayer;
                rightU.active = optPlayerLayer.isMePlayer;

                if (compWin) {
                    self.setSpriteAtlas(leftWin.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "WIN");
                    self.setSpriteAtlas(rightWin.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "LOSE");
                } else {
                    self.setSpriteAtlas(leftWin.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "LOSE");
                    self.setSpriteAtlas(rightWin.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "WIN");
                }

                let optCards = [];
                let optLaziCards = [];
                let comCards = [];
                let comLaziCards = [];
                for (let key in info.optCards) {
                    optCards.push(info.optCards[key]);
                }
                for (let key in info.comCards) {
                    comCards.push(info.comCards[key]);
                }
                for (let key in info.optLaziCards) {
                    optLaziCards.push(info.optLaziCards[key]);
                }
                for (let key in info.comLaziCards) {
                    comLaziCards.push(info.comLaziCards[key]);
                }

                let sortcomcards = [...comCards];
                let sortoptcards = [...optCards];
                self._gameLogic.sortCardDataDown(sortcomcards);
                self._gameLogic.sortCardDataDown(sortoptcards);

                for (let index = 0; index < 3; index++) {
                    let leftCard = leftBg.getChildByName("left_poker_" + (index + 1)).getChildByName("poker");
                    let lanum = leftCardTypeBgLz.getChildByName("img_num_" + (index + 1));
                    let lalogo = leftCardTypeBgLz.getChildByName("img_color_" + (index + 1));
                    let card = comCards[index];
                    let lazicard = comLaziCards[index];
                    let isLazi = self._gameLogic.isLaziCardValue(card);
                    let cardres = self._gameCore.getCardRes(card['color'], card['number']);
                    self.setSpriteFrame(lanum.getComponent(cc.Sprite), "res/textures/card/" + cardres.num);
                    self.setSpriteFrame(lalogo.getComponent(cc.Sprite), "res/textures/card/" + cardres.logo);

                    let cardLayer = leftCard.getComponent(TPAKCardLayer);
                    if (!cardLayer) leftCard.addComponent(TPAKCardLayer);
                    cardLayer.showPokerData(Common.toInt(lazicard["color"]), Common.toInt(lazicard["number"]));
                    if (isLazi) {
                        cardLayer.showPokerLz();
                    }
                }

                for (let index = 0; index < 3; index++) {
                    let rightCard = rightBg.getChildByName("right_poker_" + (index + 1)).getChildByName("poker");
                    let lanum = rightCardTypeBgLz.getChildByName("img_num_" + (index + 1));
                    let lalogo = rightCardTypeBgLz.getChildByName("img_color_" + (index + 1));
                    let card = optCards[index];
                    let lazicard = optLaziCards[index];
                    let isLazi = self._gameLogic.isLaziCardValue(card);
                    let cardres = self._gameCore.getCardRes(card['color'], card['number']);
                    self.setSpriteFrame(lanum.getComponent(cc.Sprite), "res/textures/card/" + cardres.num);
                    self.setSpriteFrame(lalogo.getComponent(cc.Sprite), "res/textures/card/" + cardres.logo);

                    let cardLayer = rightCard.getComponent(TPAKCardLayer);
                    if (!cardLayer) rightCard.addComponent(TPAKCardLayer);
                    cardLayer.showPokerData(Common.toInt(lazicard["color"]), Common.toInt(lazicard["number"]));
                    if (isLazi) {
                        cardLayer.showPokerLz();
                    }
                }

                let signShow=false;
                let compBiger = [-1, -1, -1];
                let cardNumSame= self._gameLogic.judgeCardNumSame(comCards,optCards);

                for (let index = 0; index < 3; index++) {
                    let compareNode = vsNode.getChildByName("compare_" + (index + 1));
                    let leftlanum = compareNode.getChildByName('img_leftBg').getChildByName("img_num_" + (index + 1));
                    let leftlalogo = compareNode.getChildByName('img_leftBg').getChildByName("img_color_" + (index + 1));
                    let rightlanum = compareNode.getChildByName('img_rightBg').getChildByName("img_num_" + (index + 1));
                    let rightlalogo = compareNode.getChildByName('img_rightBg').getChildByName("img_color_" + (index + 1));
                    let signnode = vsNode.getChildByName('sign_' + (index + 1));
                    let signicon = signnode.getChildByName("img_icon");
                    let comcard = sortcomcards[index];
                    let optcard = sortoptcards[index];

                    let comcardres = self._gameCore.getCardRes(comcard['color'], comcard['number']);
                    let optcardres = self._gameCore.getCardRes(optcard['color'], optcard['number']);
                    self.setSpriteFrame(leftlanum.getComponent(cc.Sprite), "res/textures/card/" + comcardres.num);
                    self.setSpriteFrame(leftlalogo.getComponent(cc.Sprite), "res/textures/card/" + comcardres.logo);
                    self.setSpriteFrame(rightlanum.getComponent(cc.Sprite), "res/textures/card/" + optcardres.num);
                    self.setSpriteFrame(rightlalogo.getComponent(cc.Sprite), "res/textures/card/" + optcardres.logo);

                    if (!signShow) {
						signnode.active = true;
					}

                    if(cardNumSame){
                        let comresult = self._gameLogic.compareCardColor(comcard, optcard);
                        compBiger[index] = comresult;
                        if (comresult == 1) {
                            self.setSpriteAtlas(signicon.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "TP_dayu");
                            signicon.scaleX = 1;
                            signShow = true;
                        } else if (comresult == -1) {
                            self.setSpriteAtlas(signicon.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "TP_dayu");
                            signicon.scaleX = -1;
                            signShow = true;
                        } else {
                            self.setSpriteAtlas(signicon.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "TP_dengyu");
                        }
                    }else{
                        let comresult = self._gameLogic.compareCardNum(comcard, optcard);
                        compBiger[index] = comresult;
                        if (comresult == 1) {
                            self.setSpriteAtlas(signicon.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "TP_dayu");
                            signicon.scaleX = 1;
                            signShow = true;
                        } else if (comresult == -1) {
                            self.setSpriteAtlas(signicon.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "TP_dayu");
                            signicon.scaleX = -1;
                            signShow = true;
                        } else {
                            self.setSpriteAtlas(signicon.getComponent(cc.Sprite), "res/effect/img/tx_haiwai_huase_pk", "TP_dengyu");
                        }
                    }

                }


                cc.tween(self.aniNode)
                    .delay(0.5)
                    .call(() => {
                        //音效
                        self._audioMng.playCompare();
                    }
                    )
                    .start();

                let finishCallback = function (value1, state) {
                    if (state.name == "animation0") {
                        cc.tween(vsNode).delay(0.5).call(function () {
                            pAni.play("animation1");
                        }).start();
                    } else if (state.name == "animation1") {
                        let bDelay = false;
                        let comItemShowed = false;
                        for (let index = 0; index < 3; index++) {
                            let compareItemNode;
                            let compareItemAniName;
                            let showWin=false;
                            let selfWin=false;
                            if(compBiger[index]>0){
                                if(comPlayerLayer.isMePlayer){
                                    compareItemNode = cc.find("compare_"+(index+1)+"/compareAniLeft_"+(index+1), vsNode);
                                    compareItemAniName='tx_haiwai_huase_pk2';
                                    showWin=true;
                                    selfWin=true;
                                }else{
                                    compareItemNode = cc.find("compare_"+(index+1)+"/compareLoseRight_"+(index+1), vsNode);
                                    compareItemAniName='tx_haiwai_huase_pk4_lose_right';
                                }
                            }else if(compBiger[index]<0){
                                if(comPlayerLayer.isMePlayer){
                                    compareItemNode = cc.find("compare_"+(index+1)+"/compareLoseLeft_"+(index+1), vsNode);
                                    compareItemAniName='tx_haiwai_huase_pk4_lose_left';
                                }else{
                                    compareItemNode = cc.find("compare_"+(index+1)+"/compareAniRight_"+(index+1), vsNode);
                                    compareItemAniName='tx_haiwai_huase_pk3';
                                    showWin=true;
                                    if(optPlayerLayer.isMePlayer){
                                        selfWin=true;
                                    }
                                }
                            }
                            if(compareItemNode&&compareItemAniName){
                                if(!comItemShowed){
                                    self.playCompareItemAni(compareItemAniName,compareItemNode,showWin,selfWin);
                                    compareItemNode.active=true;
                                    self._audioMng.playCompare1();
                                }
                                comItemShowed=true;
                                bDelay=true;
                            }
                        }
    

                        let ani1delaytime = bDelay ? 0.7 : 0;
                        cc.tween(vsNode).delay(ani1delaytime).call(function () {
                            pAni.play("animation2");
                        }).start();
                    } else {
                        cc.tween(vsNode)
                            .call(function () {
                                if (callback) {
                                    callback();
                                }
                            })
                            .start();
                    }
                }

                pAni.on(cc.Animation.EventType.FINISHED, function (value1, value2) {
                    finishCallback(value1, value2);
                });

                cc.tween(vsNode).delay(0.33).call(() => { self._audioMng.playCompare(); }).start();
            }
        })
    }

    private playCompareItemAni(name, parent, showWin, selfWin) {
        this.loadAsset("res/effect/" + name, cc.Prefab, function (assets: cc.Prefab) {
            let itemNode = cc.instantiate(assets);
            itemNode.parent = parent;

            let pAni = itemNode.getComponent(cc.Animation);
            pAni.play(name);

            if (showWin) {
                let pWin = itemNode.getChildByName('win').getChildByName('TP_win_26');
                let pSelfWin = itemNode.getChildByName('win').getChildByName('TP_self_win');
                pWin.active = !selfWin;
                pSelfWin.active = selfWin;
            }
        });
    }
    


    private playMeWinAni(callback) {
        let self = this;
        this.loadAsset("res/prefabs/winAni", cc.Prefab, function (assets: cc.Prefab) {
            let winNode = cc.instantiate(assets);
            self.aniNode.addChild(winNode);
            winNode.y = 70;

            let winskeleton = winNode.getComponent(sp.Skeleton);
            winskeleton.setAnimation(0, "ziji", false);
            cc.tween(winNode)
                .delay(2)
                .call(function () {
                    if (callback) {
                        callback();
                    }
                })
                .removeSelf()
                .start();
        })
    }


    private playShangWinAni(pos, callback) {
        let self = this;
        this.loadAsset("res/prefabs/winAni", cc.Prefab, function (assets: cc.Prefab) {
            let winNode = cc.instantiate(assets);
            self.aniNode.addChild(winNode);
            winNode.scale = 0.6;
            winNode.position = pos;

            let winskeleton = winNode.getComponent(sp.Skeleton);
            winskeleton.setAnimation(0, "shang", false);
            cc.tween(winNode)
                .delay(2)
                .call(function () {
                    if (callback) {
                        callback();
                    }
                })
                .removeSelf()
                .start();
        })
    }

    private playXiaWinAni(pos, callback) {
        let self = this;
        this.loadAsset("res/prefabs/winAni", cc.Prefab, function (assets: cc.Prefab) {
            let winNode = cc.instantiate(assets);
            self.aniDownNode.addChild(winNode);
            winNode.scale = 0.6;
            winNode.position = pos;

            let winskeleton = winNode.getComponent(sp.Skeleton);
            winskeleton.setAnimation(0, "xia", false);
            cc.tween(winNode)
                .delay(2)
                .call(function () {
                    if (callback) {
                        callback();
                    }
                })
                .removeSelf()
                .start();
        })
    }


    //////////////////////////////////////////////////////////////////////////////

    //匹配
    public onStartMatch() {
        let myscore = this._gameCore.userInfo.money;
        let inmoney = this._gameCore.inmoney;
        if (this._gameCore.isKicked || myscore < inmoney) {
            this.onClickBack();
            return;
        }
        this._gameCore.sendHuanZhuoMessage();
    }

    //////////////////////////////////////////////////////////////////////////////

    public openMeChat(){
        let self = this;
        this.meChatNode.active=true;
        let uichat=this.meChatNode.getChildByName('UIChat').getComponent(TPAKMeChatLayer);
        uichat.show(this._gameCore.playerid, (info: any) => {
            self._gameCore.sendChatMessage(info);
        });
    }
    public closeMeChat(){
        if(this.meChatNode.active){
            this.meChatNode.active=false;
            let uichat=this.meChatNode.getChildByName('UIChat').getComponent(TPAKMeChatLayer);
            uichat.close();
        }
    }

    private onClickBackgroup(){
        this.closeMeChat();
        this.tipDealerLayer.getComponent(TPAKDealerLayer).onClickBg();
    }
    // 返回大厅
    private onClickBack() {
        let self = this;
        if (this._isOpenMenu == true) {
            this._isOpenMenu = false;
            UIHelper.clearAll();
            return;
        }

        if (this._gameCore.isPlayerMeGaming()) {
            AlertHelper.instance.showExitAlert(this._gameCore.getPlayerMeBet(), () => {
                self._gameCore.quitGame();
            });
        } else {
            this._gameCore.quitGame();
        }

    }

    private onClickChat() {
        let self = this;
        let playerInfo = { sendPlayerid: this._gameCore.playerid };
        UIHelper.showGameChatLayer(this.node, playerInfo, (info: any) => {
            self._gameCore.sendChatMessage(info);
        });
    }

    // 显示菜单层
    private onClickMenu() {
        let self = this;
        this._isOpenMenu = true;
        let nowTime= Common.getCTime();
        let switchTime=3000-(nowTime-this._gameCore.enterRoomTime);
        switchTime=Math.floor(switchTime/1000);
        if(switchTime<0)switchTime=0;

        UIHelper.showGameMenu(this.node, [0, 1, 2, 3, 4], (menuIndex: number) => {
            self._isOpenMenu = false;
            if (menuIndex == 1) {
                this.onClickBack();
            }
            else if (menuIndex == 2) {
                this.onClickHuanZhuo();
            }
            else if (menuIndex == 3) {
                UIHelper.showGameRule(this._gameCore.bundleName);
            }
            else if (menuIndex == 4) {
                AudioHelper.instance.openSetupUI();
            }
        }, ZOrder.UI + 8,switchTime);
    }

    // 点击换桌按钮
    private onClickHuanZhuo() {
        let self=this;
        if (this._isOpenMenu == true) {
            this._isOpenMenu = false;
            UIHelper.clearAll();
            return;
        }

        if(this._gameCore.isCompareing)return;

        if (this._gameCore.isPlayerMeGaming()) {
            AlertHelper.instance.showExitAlert(this._gameCore.getPlayerMeBet(), () => {
                self.onStartMatch();
            });
        } else {
            this.onStartMatch();
        }

    }

    // 点击准备按钮
    private onClickReady() {
        this.onStartMatch();
    }

    private onClickPack() {
        this._gameCore.callFold();
        this.updateOptState(true);
    }

    private onClickSideShow() {
        this._gameCore.callCompetition();
        this.updateOptState(true);
    }

    private onClickCallScore(target: any, customEventData: any) {
        let fill = Common.toInt(customEventData);
        this._gameCore.callScore(fill);
        this.updateOptState(true);
    }

    private onClickSee() {
        this._gameCore.callLook()
        this.addMultisNode.getChildByName("SeenBtn").active = false;
    }

    private onClickRefuseCompare() {
        this._gameCore.callCompConfirm(false)
        this.compareDialog.active = false;
        this.unschedule(this.scheduleCompare);
    }

    private onClickAgreeCompare() {
        this._gameCore.callCompConfirm(true)
        this.compareDialog.active = false;
        this.unschedule(this.scheduleCompare);
    }

    private onClickRechage() {
        if(this._gameCore.gameState==GameState.EM_TEENPATTI_GAMESTATE_WAITRECHARGE){
            this._gameCore.openPayCenter({quickPayStyle:quickPayStyle.gameTpQuickPay,payNum:this._needMoney});
        }else{
            this._gameCore.callWaitRecharge();
        }
    }

    private onClickOpenRecharge() {
        this._gameCore.openPayCenter();
    //   this.showNeedRecharge(10);
        // let self = this;
        // for (let index = 0; index < self._allPlayers.length; index++) {
        //     let playerLayer = self._allPlayers[index];
        //     playerLayer.node.active = true;
        //     // if (playerLayer) {
        //     //     playerLayer.cardInfoNode.active = true;
        //     //     playerLayer.showPackedTips();

        //     // }
        //     playerLayer.playLightAni();
        // }
        // self.doSendCard(() => { });
    }

    private onClickRoomInfo() {
        this.roomInfoNode.active = true;
        cc.find("content/layout/amountrow/amount", this.roomInfoNode).getComponent(cc.Label).string = "₹" + Common.moneyString(this._gameCore.difen);
        cc.find("content/layout/blindsrow/blinds", this.roomInfoNode).getComponent(cc.Label).string = "" + this._gameCore.blindroundlimit;
        cc.find("content/layout/chaalrow/chaal", this.roomInfoNode).getComponent(cc.Label).string = "₹" + Common.moneyString(this._gameCore.chaallimit);
        cc.find("content/layout/potrow/pot", this.roomInfoNode).getComponent(cc.Label).string = "₹" + Common.moneyString(this._gameCore.potlimit);
    }

    private onClickCloseRoomInfo() {
        this.roomInfoNode.active = false;
    }

    private onClickIMBack() {
        this.imBackNode.active = false;
        if (this._gameCore.isZanLi) {
            this._gameCore.sendZanLiCombackMessage();
        }
    }
    //////////////////////////////////////////////////////////////////////////////
}