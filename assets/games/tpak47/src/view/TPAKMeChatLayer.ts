import Common from "../../../../script/frame/common/Common";
import { BundleType, ChatExpressionType } from "../../../../script/frame/common/Define";
import { TextTips } from "../../../../script/frame/common/Language";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import TPAKGameCore from "../core/TPAKGameCore";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TPAKMeChatLayer extends BaseLayer {

    private _gameCore: TPAKGameCore = null;
    private _lastTime = 0;
    
    private _sendPlayerid=0;
    // 回调接口
    private _chatCb: Function = null;

    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent('TPAKGameCore');
        this.LoadAllLayerObject(this.node);
    }

    private initFace() {
        let datas = [
            [2, 1, 3, 4],
            [2, 5, 7, 9],
            [2, 6, 3, 9]
        ];
        let randNum = Common.random(0, 2);
        let data = datas[randNum];
        for (let index = 0; index < data.length; index++) {
            let sort = data[index];
            let imgFace = this.LayerItems['imgFace' + (index + 1)];
            let imgIcon = this.LayerItems['imgIcon' + (index + 1)];
            let bgbutton = imgFace.getComponent(ButtonLayer);
            if (!bgbutton) {
                bgbutton = imgFace.addComponent(ButtonLayer);
                let eventHandler = new cc.Component.EventHandler();
                eventHandler.component = cc.js.getClassName(this);
                eventHandler.handler = 'onClickFace';
                eventHandler.target = this.node;
                bgbutton.clickEvents.push(eventHandler);
            }
            bgbutton.clickEvents[0].customEventData = ChatExpressionType[sort-1];
            this.setSpriteFrame(imgIcon.getComponent(cc.Sprite), "common/images/expression/img_exp" + sort,BundleType.HALL);
        }
    }

    private initWord() {
        let datas = [
            [4, 6, 9, 13, 5, 14],           //游戏未开始
            [4, 6, 9, 13, 5, 14],          //游戏未开始
            [1, 2, 3, 4, 5, 9],           //游戏中
            [6, 15, 8, 9, 10, 7],         //弃牌，结算
            [6, 15, 8, 9, 10, 7],          //弃牌，结算
            [6, 15, 8, 9, 10, 7]           //弃牌，结算
        ];
        let state=this._gameCore.gameState;
        if(state<0)state=0;
        if(state>=datas.length)state=datas.length-1;
        let data = datas[state];
        for (let index = 0; index < 3; index++) {
            let sort = data[index];
            let chattext=TextTips['UI_TEXT_CHAT_ITEM' + sort];

            let txtWord = this.LayerItems['txtWord' + (index + 1)];
            let imgTxt = this.LayerItems['imgTxt' + (index + 1)];
            let bgbutton = imgTxt.getComponent(ButtonLayer);
            if (!bgbutton) {
                bgbutton = imgTxt.addComponent(ButtonLayer);
                let eventHandler = new cc.Component.EventHandler();
                eventHandler.component = cc.js.getClassName(this);
                eventHandler.handler = 'onClickWord';
                eventHandler.target = this.node;
                bgbutton.clickEvents.push(eventHandler);
            }
            bgbutton.clickEvents[0].customEventData = chattext;
            txtWord.getComponent(cc.Label).string=chattext;
        }
    }

    private onClickFace(target, custom) {
        let info:any={};
        info.type='meexpression';
        info.content=custom;
        this.onChat(info);
    }

    private onClickWord(target, custom) {
        let info:any={};
        info.type='text';
        info.content=custom;
        this.onChat(info);
    }

    public onChat(info: any) {
        let curTime=Common.getCTime();
        if(curTime-this._lastTime<5000){
            ToastHelper.show(TextTips.UI_TEXT_CHAT_TOO_FAST);
            return;
        }
        this._lastTime=curTime;
        info.sendPlayerid =this._sendPlayerid;
        info.receiverPlayerid =0;

        if (this._chatCb) {
            this._chatCb(info)
        }
        this.close();
    }

    public show(sendPlayerid,callback) {
        this._sendPlayerid=sendPlayerid;
        this._chatCb=callback;
        this.initFace();
        this.initWord();
        this.LayerItems.nodeTxt.scale = 0;
        this.LayerItems.nodeFace.scale = 0;

        cc.tween(this.LayerItems.nodeTxt)
        .to(0.1,{scale:1})
        .start();
        cc.tween(this.LayerItems.nodeFace)
        .to(0.1,{scale:1})
        .start();
        cc.tween(this.node)
        .delay(2)
        .call(()=>{
            this.close();
        })
        .start();
        this.node.active = true;
    }

    public close() {
        this.LayerItems.nodeTxt.stopAllActions();
        this.LayerItems.nodeFace.stopAllActions();
        this.node.stopAllActions();
        this.node.active = false;
    }


}