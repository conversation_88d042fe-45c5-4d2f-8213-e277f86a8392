import Common from "../../../../script/frame/common/Common";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class TPAKGameLogic extends cc.Component {
    //////////////////////////////////////////////////////////////////////////////
    // 排列用户手牌
    public sortCardData(cards: Array<any>) {
        cards.sort((a: any, b: any): number => {
            let value1 = Common.toInt(a["number"]);
            let value2 = Common.toInt(b["number"]);
            if (value1 == value2) {
                let color1 = a["color"];
                let color2 = b["color"];
                if (color1 < color2) {
                    return -1;
                }
            }
            else if (value1 < value2) {
                return -1;
            }
            return 1;
        });
    }

    // 排列用户手牌
    public sortCardDataDown(cards: Array<any>) {
        cards.sort((a: any, b: any): number => {
            let value1 = Common.toInt(a["number"]);
            let value2 = Common.toInt(b["number"]);
            if (value1 == value2) {
                let color1 = a["color"];
                let color2 = b["color"];
                if (color1 < color2) {
                    return 1;
                }
            }
            else if (value1 < value2) {
                return 1;
            }
            return -1;
        });
    }

    public hasSameValue(cardinfo1, cardinfo2): boolean {
        let samevalue = true;
        if (cardinfo1['cardtype'] == cardinfo2['cardtype']) {
            let lazicards1 = []
            let lazicards2 = []
            for (let key in cardinfo1['laizicards']) {
                lazicards1.push(cardinfo1['laizicards'][key]);
            }
            for (let key in cardinfo2['laizicards']) {
                lazicards2.push(cardinfo2['laizicards'][key]);
            }
            this.sortCardData(lazicards1);
            this.sortCardData(lazicards2);
            for (let index = 0; index < lazicards1.length; index++) {
                if (lazicards1[index]['number'] != lazicards2[index]['number']) {
                    samevalue = false;
                    return samevalue;
                }

            }
        } else {
            samevalue = false;
        }
        return samevalue;
    }

    public judgeCardNumSame(cardarr1, cardarr2) {
        let same = true;
        for (let index = 0; index < cardarr1.length; index++) {
            if (cardarr1[index]['number'] != cardarr2[index]['number']) {
                same = false;
                return same;
            }
        }
        return same;
    }


    public compareCard(card1, card2) {
        let result = 0;
        if (card1["number"] > card2["number"]) {
            result = 1;
        } else if (card1["number"] == card2["number"]) {
            if (card1["color"] > card2["color"]) {
                result = 1;
            } else if (card1["color"] == card2["color"]) {
                result = 0;
            } else {
                result = -1;
            }
        } else {
            result = -1;
        }
        return result;
    }


    public compareCardColor(card1, card2) {
        let result = 0;
        if (card1["color"] > card2["color"]) {
            result = 1;
        } else if (card1["color"] == card2["color"]) {
            result = 0;
        } else {
            result = -1;
        }
        return result;
    }

    public compareCardNum(card1, card2) {
        let result = 0;
        if (card1["number"] > card2["number"]) {
            result = 1;
        } else if (card1["number"] == card2["number"]) {
            result = 0;
        } else {
            result = -1;
        }
        return result;
    }

    public hasLaziCard(cards){
        for (let index = 0; index < cards.length; index++) {
            if(this.isLaziCardValue(cards[index])){
                return true;
            } 
        }
        return false;
    }

    public isLaziCardValue(card) {
        let value = Common.toInt(card["number"]);
        return value == 4 || value == 7 || value == 13 || value == 14;
    }


    public isLaziCard(card1, card2) {
        return card1["color"] != card2["color"] || card1["number"] != card2["number"];
    }

}
