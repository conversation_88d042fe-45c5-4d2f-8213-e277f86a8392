import BaseLayer from "../../../../script/frame/component/BaseLayer";
import { Constant } from "../core/FishPCDefine";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FishPCZouShi extends BaseLayer {

    @property(cc.Node)
    panelTrend: cc.Node = null;

    @property([cc.SpriteFrame])
    spriteFrame: cc.SpriteFrame[] = [];

    @property(cc.Prefab)
    trendPrefab: cc.Prefab = null;

    trend: cc.Node[][];
    maxShow = 14;

    onLoad() {
        this.trend = [];

        for (let areaIndex = 0; areaIndex < Constant.AREA_COUNT; areaIndex++) {
            this.trend[areaIndex] = [];
            for (let i = 0; i < this.maxShow; i++) {
                let trend = cc.instantiate(this.trendPrefab);
                let image = trend.getChildByName('Image_trend');
                let text = trend.getChildByName('Text_trend');
                trend.parent = this.panelTrend;
                trend.active = true;
                trend.x = i * 64 + 97;
                trend.y = 363 - areaIndex * 66;
                image.active=false;
                text.active=false;
                this.trend[areaIndex].push(trend);
            }
        }
    }


    updateView(trendHistoryData: any) {
        let length = trendHistoryData.length;
        for (let areaIndex = 0; areaIndex < Constant.AREA_COUNT; areaIndex++) {
            let adindex=0;
            for (let index = this.maxShow-1; index >=0; index--) {
                let area_trend_item = this.trend[areaIndex][index];
                let Image_trend = area_trend_item.getChildByName('Image_trend');
                let Text_trend = area_trend_item.getChildByName('Text_trend');

                adindex++;
                let historyIndex=length-adindex;
                if (trendHistoryData[historyIndex]) {
                    Image_trend.active = true;
                    Text_trend.active = true;
                    let count = trendHistoryData[historyIndex]['' + (areaIndex + 1)];
                    let result = count > 1;
                    if (result) {
                        Text_trend.getComponent(cc.Label).string = `${count}`;
                        Image_trend.getComponent(cc.Sprite).spriteFrame = this.spriteFrame[1];
                    } else {
                        Text_trend.getComponent(cc.Label).string = `X`;
                        Image_trend.getComponent(cc.Sprite).spriteFrame = this.spriteFrame[0];
                    }
                }else{
                    Image_trend.active = false;
                    Text_trend.active = false;
                }
            }
        }
    }

    public isShow(){
        return this.node.active;
    }
    public updateHistory(historyData: any) {
        this.updateView(historyData);
        this.node.active = true;
    }

    // 点击关闭按钮
    private hide() {
        this.node.active = false;
    }
}
