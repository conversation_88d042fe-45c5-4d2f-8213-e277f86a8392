import Common from "../../../../script/frame/common/Common";
import { Z<PERSON>rder } from "../../../../script/frame/common/Define";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import FishPCBetLayer from "./FishPCBetLayer";
import FishPCChipsLayer from "./FishPCChipsLayer";
import FishPCGameCore from "../core/FishPCGameCore";
import FishPCModel from "../core/FishPCModel";
import FishPCAudioMng from "../core/FishPCAudioMng";
import SceneManager from "../../../../script/frame/manager/SceneManager";
import FishPCOnlineLayer from "./FishPCOnlineLayer";
import FishPCZouShi from "./FishPCZouShi";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import { Constant, EM_YXX_GAMESTATE } from "../core/FishPCDefine";
import HallManager from "../../../../script/frame/manager/HallManager";
import FishPCHelpLayer from "./FishPCHelpLayer";
import FishPCDealerLayer from "./FishPCDealerLayer";
import Config from "../../../../script/frame/config/Config";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class FishPCGameView extends BaseLayer {

    @property(cc.Node)
    mainLayer: cc.Node = null;

    @property(cc.Node)
    bottomLayer: cc.Node = null;

    @property(cc.Prefab)
    onlineLayerPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    historyPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    helpPrefab: cc.Prefab = null;

    @property(cc.Node)
    btnOnline: cc.Node = null;

    @property(cc.Node)
    animLayer: cc.Node = null;

    @property(cc.Node)
    upAnimLayer: cc.Node = null;

    @property(cc.Node)
    shaiziLayer: cc.Node = null;

    @property(cc.Node)
    centerLayer: cc.Node = null;

    @property(cc.Node)
    waitLayer: cc.Node = null;

    @property(cc.Node)
    meNode: cc.Node = null;

    @property(cc.Node)
    chipNode: cc.Node = null;

    @property(cc.Node)
    chatLayer: cc.Node = null;

    @property(cc.Node)
    emojiLayer: cc.Node = null;

    @property(cc.Node)
    tipDealerLayer: cc.Node = null;

    /** 在线玩家列表 */
    private _playerListData: any;
    private _allListData: any;
    /** 房间所有用户信息 */
    private _allUsers = {};
    /** 桌面玩家数据 */
    public _playersList: FishPCModel.Player[] = [];
    /** 我的信息界面 */
    public myInfo: FishPCModel.MyInfo = new FishPCModel.MyInfo;
    /** 牌桌对象 包含 下注区域 下注总额  */
    public deskTopBet: FishPCModel.DesktopBet[] = [];

    private _onlineUserLayer: FishPCOnlineLayer = null;
    private _historyLayer: FishPCZouShi = null;
    private _helpLayer: FishPCHelpLayer = null;
    private _gameYaoShai: cc.Node = null;//
    private _chipLayer: FishPCChipsLayer = null;//

    private _resultNode: cc.Node = null;
    private _betLayer: FishPCBetLayer = null;    /** 下注筹码脚本 */


    private _betAniNode: cc.Node = null;

    private _betScheduler: any;
    private _updateScheduler = false;

    private _historyListData = [];

    private _userBetMsgTemp: any[];

    private _flyingBets: any[];

    private _currentTimeOut: number;
    private _movingOnlineUserHead: boolean = false;

    private _audioMng: FishPCAudioMng;  //声音
    private _gameCore: FishPCGameCore;    //core

    private _siceAreaOffset = [
        cc.v2(-80, 33),
        cc.v2(0, 33),
        cc.v2(80, 33),
        cc.v2(-80, -33),
        cc.v2(0, -33),
        cc.v2(80, -33),
    ];
    private _sicePos = [
        cc.v2(-177, 75),
        cc.v2(-60, 125),
        cc.v2(85, 100),
        cc.v2(-125, -55),
        cc.v2(22, -40),
        cc.v2(170, -33),
    ]

    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent(FishPCGameCore);
        this._audioMng = cc.Canvas.instance.getComponent(FishPCAudioMng);
        this._betLayer = this.bottomLayer.getChildByName("Panel_chips").getComponent(FishPCBetLayer);

        this._resultNode = this.mainLayer.getChildByName("ResultLayer");
        this._gameYaoShai = this.animLayer.getChildByName("yaoshai");
        this._chipLayer = this.chipNode.getComponent(FishPCChipsLayer);

        this._betAniNode = this.animLayer.getChildByName("xiazhu");
        this._betAniNode.active = false;

        this.initDesktopUI();
        this.initPlayerUI();
        this.initMyinfoUI();
    }

    start() {
        this.initSelfInfo();
        this.cleanTable();
        this._audioMng.playMusic();
    }

    /**
     * 初始化自己界面
     */
    private initMyinfoUI() {
        this.myInfo.node = this.meNode;
        let selfseat: cc.Node = this.meNode.getChildByName("FileNode_self_seat");
        let panelSeat: cc.Node = selfseat.getChildByName("mySeat").getChildByName("Node").getChildByName("Panel_seat");
        this.myInfo.head = panelSeat.getChildByName("Panel_head").getChildByName("head_mask").getChildByName("head_pos");
        this.myInfo.headBg = panelSeat;
        this.myInfo.score = this.meNode.getChildByName("Text_chips").getComponent(cc.Label);
        this.myInfo.nickName = this.meNode.getChildByName("Text_name").getComponent(cc.Label);

        this.myInfo.chatNode = this.chatLayer.getChildByName("Node_chat0");
        this.myInfo.chatlab =  this.myInfo.chatNode.getChildByName("Text_chat").getComponent(cc.Label);
        this.myInfo.chatNode.active = false;
        this.myInfo.expNode = selfseat.getChildByName("Node_exp");

        this.myInfo.resultPos = panelSeat.convertToWorldSpaceAR(panelSeat.getAnchorPoint());
        this.myInfo.resultPos.y += 50;
        this.myInfo.seatNode = selfseat.getChildByName("mySeat");

        this.myInfo.nodeFire = panelSeat.getChildByName("Node_fire");
        this.myInfo.nodeFire.active = false;
    }

    /**
     * 初始化桌面玩家界面
     */
    private initPlayerUI() {
        for (let i = 0; i < 6; i++) {
            var seatsNode = this.centerLayer.getChildByName("seats");
            var playerNode = seatsNode.getChildByName("seat_" + (i + 1));
            let playerData: FishPCModel.Player = new FishPCModel.Player();

            playerData.node = playerNode;
            playerData.chairId = i;

            let seatNodeNode = playerNode.getChildByName("Seat").getChildByName("Node");
            let panelSeat = seatNodeNode.getChildByName("Panel_seat");
            let panelTouch = seatNodeNode.getChildByName("Panel_touch");

            playerData.headBg = panelSeat;
            playerData.panelHead = panelSeat.getChildByName("Panel_head");
            playerData.head = panelSeat.getChildByName("Panel_head").getChildByName("head_mask").getChildByName("head_pos");

            playerData.nodeFire = panelSeat.getChildByName("Node_fire");
            playerData.nodeFire.active = false;

            playerData.seatNode = playerNode.getChildByName("Seat");
            playerData.scoreBg = seatNodeNode.getChildByName("Image_name_bg");
            playerData.score = seatNodeNode.getChildByName("Image_name_bg").getChildByName("Text_chips").getComponent(cc.Label);
            playerData.nickName = seatNodeNode.getChildByName("Text_name").getComponent(cc.Label);

            playerData.chatNode = this.chatLayer.getChildByName("Node_chat"+(i + 1));
            playerData.chatlab =  playerData.chatNode.getChildByName("Text_chat").getComponent(cc.Label);
            playerData.chatNode.active = false;

            playerData.expNode = playerNode.getChildByName("Node_exp");

            playerData.resultPos = panelSeat.convertToWorldSpaceAR(panelSeat.getAnchorPoint());
            playerData.resultPos.y -= 20;

            if (panelSeat.getChildByName('icon_special')) {
                playerData.headIcon = panelSeat.getChildByName('icon_special');
                playerData.headIcon.active = false;
            }

            if (i == 0 || i == 4 || i == 5) {
                playerData.dirction = 0;
            } else {
                playerData.dirction = 1;
            }

            if (!panelTouch.getComponent('ButtonLayer')) {
                panelTouch.addComponent(ButtonLayer);
                let eventHandler = new cc.Component.EventHandler();
                eventHandler.component = cc.js.getClassName(this);
                eventHandler.handler = 'openUserInfo';
                eventHandler.target = this.node;
                eventHandler.customEventData = i + '';
                panelTouch.getComponent('ButtonLayer').clickEvents.push(eventHandler);
                panelTouch.getComponent(ButtonLayer).pressedScale = 1.0;
            }

            playerNode.active = false;
            this._playersList.push(playerData);
        }
    }

    /**
     * 弹出显示用户信息
     */
    private openUserInfo(target, customData) {
        let index = Common.toInt(customData);
        if (!this._playersList[index]) return;
        let playerData = this._playersList[index];

        let parent = this.mainLayer;
        let showPos = parent.convertToNodeSpaceAR(playerData.headBg.convertToWorldSpaceAR(playerData.headBg.getAnchorPoint()));
        if (playerData.dirction == 0) {
            showPos.x -= 338;
        } else {
            showPos.x += 338;
        }

        let playerInfo = {
            sendPlayerid: this._gameCore.playerid, receiverPlayerid: playerData.userInfo.playerid,
            singlechatfee: this._gameCore.singlechatfee
        };
        UIHelper.showInteractExpression(parent, showPos, playerInfo, (info: any) => {
            this._gameCore.sendChatMessage(info);
        });

    }

    /**
     * 获取用户信息
     * @param playerid 用户id
     * @returns 用户信息
     */
    private getUserInfo(playerid: any): any {
        return this._allUsers[playerid];
    }

    /**
     * 初始化桌面UI
     */
    private initDesktopUI() {
        let areaPanel = this.centerLayer.getChildByName("Node_bet");
        for (let i = 0; i < Constant.AREA_COUNT; i++) {
            let deskTopBet = new FishPCModel.DesktopBet();
            let baseNode = areaPanel.getChildByName("Node_bet_" + (i + 1));
            deskTopBet.node = baseNode;

            deskTopBet.starLucky = baseNode.getChildByName("FileNode_star");
            deskTopBet.totaLabel = baseNode.getChildByName("Text_b").getComponent(cc.Label);
            deskTopBet.selfLabel = baseNode.getChildByName("Text_s_b").getComponent(cc.Label);
            deskTopBet.chipArea = baseNode.getChildByName("Panel_bet_area");
            deskTopBet.selfBetBg = baseNode.getChildByName("Image_s_bet");
            deskTopBet.winBg = baseNode.getChildByName("Image_light");
            deskTopBet.trendBg = baseNode.getChildByName("Image_trend_bg");
            deskTopBet.starLucky.active = false;
            deskTopBet.totaLabel.string = "0";
            deskTopBet.selfLabel.string = "0";
            deskTopBet.total = 0;
            deskTopBet.self = 0;

            let btn = baseNode.getChildByName("Image_bet_bg").getChildByName("Panel_touch");
            if (!btn.getComponent(ButtonLayer)) {
                btn.addComponent(ButtonLayer);
                let eventHandler = new cc.Component.EventHandler();
                eventHandler.component = cc.js.getClassName(this);
                eventHandler.handler = 'onClickBet';
                eventHandler.target = this.node;
                eventHandler.customEventData = i + '';
                btn.getComponent(ButtonLayer).clickEvents.push(eventHandler);
                btn.getComponent(ButtonLayer).pressedScale = 1.0;
            }
            this.deskTopBet.push(deskTopBet);
        }
    }

    //更新自己的信息
    private initSelfInfo() {
        if (!this._gameCore.userInfo) return;
        this.myInfo.money = this._gameCore.userInfo.money;
        let userInfo = this._gameCore.userInfo;
        this.setPlayerHead(this.myInfo.head.getComponent(cc.Sprite), userInfo.headid, userInfo.wxheadurl);
        this.myInfo.nickName.string = Common.textClamp(userInfo.nickname, 12);
        this.myInfo.score.string = "₹" + Common.moneyString(this.myInfo.money);
        this.myInfo.playerid = userInfo.playerid;
        this.myInfo.betscore = [0, 0, 0, 0, 0, 0];
    }

    //初始化在线玩家数据
    public initPlayerListData(msg: any) {
        let playerlist = msg.betrank || [];
        let deskPlayerList = [];
        for (let i = 0; i < 6; i++) {
            deskPlayerList.push(playerlist[i + 1])
        }
        this._playerListData = playerlist;
        this._allListData = msg;

        this.onUpdateDeskUserInfo(deskPlayerList);

        //在线人数
        let count = this.btnOnline.getChildByName("Text_online").getComponent(cc.Label);
        count.string = msg.totalplayernum || 0 + "";

        if (this._onlineUserLayer && this._onlineUserLayer.isShow()) {
            this._onlineUserLayer.updateView(this._allListData);
        }
    }

    //修改桌位玩家数据
    private onUpdateDeskUserInfo(msg: any[]) {
        for (let i = 0; i < this._playersList.length; i++) {
            let val = this._playersList[i];
            let key = i;
            val.node.active = true;
            if (val && msg[key] && msg[key].playerid > 0) {
                val.node.active = true;
                let userInfo = this.getUserInfo(msg[key].playerid);
                val.userInfo = userInfo;
                this.setPlayerHead(val.head.getComponent(cc.Sprite), msg[key].headid, msg[key].wxheadurl);
                if (val.score) {
                    val.score.string = "₹" + Common.moneyString(msg[key].coin);
                }
                val.nickName.string = Common.textClamp(msg[key].name, 13);
                val.playerid = msg[key].playerid;
                val.nickName.node.active = true;
                val.score.node.active = true;
                val.panelHead.active = true;
                if (val.headIcon) {
                    val.headIcon.active = true;
                }
                val.betscore = [0, 0, 0, 0, 0, 0];
                val.nodeFire.active = false;
            } else {
                val.nickName.node.active = false;
                val.score.node.active = false;
                val.panelHead.active = false;
                val.chatNode.active = false;
                val.nodeFire.active = false;
                val.playerid = 0;
                val.betscore = [0, 0, 0, 0, 0, 0];

                if (val.headIcon) {
                    val.headIcon.active = false;
                }
            }
        }
    }

    private onClickBet(target, customData) {
        let index = Common.toInt(customData);
        this._betLayer.onClickBet(index);
    }

    /**
     * 返回大厅
     */
    private onClickBack() {
        let self = this;

        if (self.myInfo.isBet) {

        }
        this.centerLayer.stopAllActions();
        this.closeUpdateScheduler();
        this._gameCore.quitGame();
    }

    private onClickChat() {
        let self = this;
        let playerInfo = { sendPlayerid: this._gameCore.playerid };
        UIHelper.showGameChatLayer(this.node, playerInfo, (info: any) => {
            self._gameCore.sendChatMessage(info);
        }, true);
    }

    private onClickHelp() {
        if (!this._helpLayer) {
            let helpLayer = cc.instantiate(this.helpPrefab);
            this.node.addChild(helpLayer);
            this._helpLayer = helpLayer.getComponent(FishPCHelpLayer);
        }
        this._helpLayer.show();
    }


    private onClickRecharge() {
        HallManager.instance.openCharge();
    }

    private onClickTrend() {
        if (!this._historyLayer) {
            let historyLayer = cc.instantiate(this.historyPrefab);
            this.node.addChild(historyLayer);
            this._historyLayer = historyLayer.getComponent(FishPCZouShi);
        }
        this._historyLayer.updateHistory(this._historyListData);
    }


    //显示在线玩家
    public showOnlineUser() {
        if (!this._onlineUserLayer) {
            let onlineLayer = cc.instantiate(this.onlineLayerPrefab);
            this._onlineUserLayer = onlineLayer.getComponent(FishPCOnlineLayer);
            this._onlineUserLayer.hide();
            this.node.addChild(onlineLayer);
        }
        if (this._playerListData) {
            this._onlineUserLayer.updateView(this._allListData);
        }
    }

    //显示游戏状态提示框
    public showTipWait(time: number) {
        if (!this.waitLayer.active) {
            this.waitLayer.active = true;
            let labTime = this.waitLayer.getChildByName("BitmapFontLabel").getComponent(cc.Label);
            labTime.string = time + "";
            let self = this;
            cc.tween(this.waitLayer)
                .then(
                    cc.tween().delay(1)
                        .call(function () {
                            time--;
                            time = time < 0 ? 0 : time;
                            labTime.string = time + "";
                        })
                )
                .repeat(Math.ceil(time))
                .start();
        }
    }
    //隐藏游戏状态提示框
    public hideTipWait() {
        if (this.waitLayer) {
            this.waitLayer.stopAllActions();
            this.waitLayer.active = false;
        }
    }

    //////////////////////////////////////////////////////////////////////////////

    //////////////////////////////网络回调方法///////////////////////////////////

    /**
     * 保存玩家数据
     * @param playerlist 
     */
    public onSaveUserInfo(playerlist: any) {
        for (let i in playerlist) {
            this._allUsers[playerlist[i].playerid] = playerlist[i];
        }
        //--请求玩家列表
        this._gameCore.requestAllPlayerList();
    }
    /**
     * 玩家进入
     * @param info 
     */
    public onUserEnter(info: any) {
        let user = this.getUserInfo(info.palyerid);
        if (!user) {
            this._allUsers[info.playerid] = info;
        }
    }
    /**
     * 玩家离开
     * @param info 
     */
    public onUserLeave(info: any) {
        let user = this.getUserInfo(info.palyerid);
        if (user) {
            this._allUsers[info.playerid] = null;
        }
    }

    public updateUserScore(msg: any) {
        let userInfo = this._allUsers[msg.playerid];
        if (userInfo) {
            userInfo.coin = msg.coin;
            userInfo.money = msg.coin;
        }
    }
    public updatePlayerScore() {
        for (let i = 0; i < this._playersList.length; i++) {
            let player = this._playersList[i];
            let playerid = player.playerid;
            let userInfo = this._allUsers[playerid];
            if (player.score && userInfo) {
                player.score.string = "₹" + Common.moneyString(userInfo.money);
            }

        }
    }
    public updateMyScore() {
        let score = this._gameCore.userInfo.money;
        this.myInfo.money = score;
        this.myInfo.score.string = "₹" + Common.moneyString(score);
        if (this._gameCore.gameState == EM_YXX_GAMESTATE.EM_YXX_GAMESTATE_BUYHORSE) {
            this._betLayer.autoSelectChip();
        }
    }

    public doRoomChat(sendPlayerid, type, content) {
        let player = this.getPlayer(sendPlayerid);
        if (player) {
            if (type == 'text') {
                player.chatNode.active = true;
                player.chatlab.string = content;
                player.chatNode.stopAllActions();
                cc.tween(player.chatNode).delay(2).call(function () {
                    player.chatNode.active = false;
                }).start();
            } else {
                if (!content) return;
                player.expNode.active = true;
                player.expNode.stopAllActions();
                let spExp = player.expNode.getComponent(sp.Skeleton);
                spExp.setAnimation(0, content, true);
                cc.tween(player.expNode)
                    .delay(3)
                    .call(function () {
                        if (cc.isValid(player.expNode)) {
                            player.expNode.active = false;
                        }
                    })
                    .start();
            }
        }
    }

    public doRoomIntertChat(sendPlayerid, receiverPlayerid, content) {
        let sendPlayer = this.getPlayer(sendPlayerid);
        let sendPos = null;
        if (sendPlayer) {
            sendPos = sendPlayer.headBg.convertToWorldSpaceAR(sendPlayer.headBg.getAnchorPoint());
        } else {
            sendPos = this.btnOnline.convertToWorldSpaceAR(this.btnOnline.getAnchorPoint());
        }
        let recPlayerLayer = this.getPlayer(receiverPlayerid);
        if (!recPlayerLayer) return;

        let startPos = cc.v3(this.emojiLayer.convertToNodeSpaceAR(sendPos));
        let endPos = cc.v3(this.emojiLayer.convertToNodeSpaceAR(recPlayerLayer.headBg.convertToWorldSpaceAR(recPlayerLayer.headBg.getAnchorPoint())));
        UIHelper.playInteractExpression(this.emojiLayer, startPos, endPos, content, recPlayerLayer.dirction == 1);
    }

    public doTipDealer(sendPlayerid: number) {
        let sendPlayer = this.getPlayer(sendPlayerid);
        let sendPos = null;
        if (sendPlayer) {
            sendPos = sendPlayer.headBg.convertToWorldSpaceAR(sendPlayer.headBg.getAnchorPoint());
        } else {
            sendPos = this.btnOnline.convertToWorldSpaceAR(this.btnOnline.getAnchorPoint());
        }
        let info: any = {};
        info.playerPos = cc.v3(this.tipDealerLayer.convertToNodeSpaceAR(sendPos));
        this.tipDealerLayer.getComponent(FishPCDealerLayer).doTipDealer(info);
    }

    //配置信息
    public onConfigs() {
        this._betLayer.initBet();
        //清理桌子
        this.cleanTable();
    }

    /**
     * 开始下注
     */
    public onStartBet(betTime: number) {
        this._gameCore.isStartBet = true;
        this.startBetOut(betTime);
        this._betLayer.autoSelectChip();
        this._audioMng.playStart();
    }

    public startBetOut(betTime: number) {
        let self = this;
        let gameClock: cc.Node = this.centerLayer.getChildByName("alarm_clock");
        let gameTimeVale: cc.Label = gameClock.getChildByName("countdown").getComponent(cc.Label)

        this.stopBetOut();

        this._currentTimeOut = Math.floor(betTime);
        gameTimeVale.string = this._currentTimeOut + "";
        gameClock.active = true;

        this._betScheduler = function () {
            self._currentTimeOut = self._currentTimeOut - 1;
            gameTimeVale.string = self._currentTimeOut + "";

            if (self._currentTimeOut == 3) {
                self._audioMng.playCountDown();
            }
            if (self._currentTimeOut < 0.5) {
                self._gameCore.isStartBet = false;
            }
            if (self._currentTimeOut < 0 && self._betScheduler) {
                self.stopBetOut();
            }
        }
        this.schedule(this._betScheduler, 1);
    }

    //停止倒计时
    public stopBetOut() {
        if (this._betScheduler) {
            this.unschedule(this._betScheduler);
            this._betScheduler = null;
        }
        this._currentTimeOut = 0;
        this.centerLayer.getChildByName("alarm_clock").active = false;
    }

    // 停止下注
    public onStopBet(callback) {
        this._gameCore.isStartBet = false;
        this.stopBetOut();

        let spBet = this._betAniNode.getComponent(sp.Skeleton);
        this._betAniNode.active = true;
        spBet.setAnimation(0, "end", false);
        spBet.setCompleteListener(function (entry) {
            if (entry.animation.name == 'end') {
                if (callback) {
                    callback();
                }
            }
        });

        this._audioMng.playStopBet();

    }

    public hideCardsAni() {
        let spYaoShai = this._gameYaoShai.getComponent(sp.Skeleton);
        spYaoShai.setAnimation(0, "animation", true)
    }

    public showCardsAni(siceresult, callback) {
        let self = this;
        let spYaoShai = this._gameYaoShai.getComponent(sp.Skeleton);
        spYaoShai.setAnimation(0, "animation2", false);
        spYaoShai.setCompleteListener((spvalue) => {
            if (spvalue.animation.name == 'animation2') {
                self.hideCardsAni();
                self.openCards(siceresult, callback);
            }
        });
        this._audioMng.playDice();
    }

    private openCards(siceresult, callback) {
        let areaIndex = [0, 0, 0, 0, 0, 0];
        this.shaiziLayer.active = true;
        let shaiziArr = this.shaiziLayer.children;
        for (let index = 0; index < shaiziArr.length; index++) {
            let shazi = shaiziArr[index];
            shazi.stopAllActions();
            shazi.active = true;
            shazi.scale = 1;
            shazi.setPosition(this._sicePos[index]);
            this.setSpriteFrame(shazi.getComponent(cc.Sprite), this.getSiceRes(siceresult[index]));

            let areaID = siceresult[index] - 1;
            areaIndex[areaID] += 1;

            let worldPos = cc.v2(this._siceAreaOffset[areaIndex[areaID]])
                .add(this.deskTopBet[areaID].node.convertToWorldSpaceAR(this.deskTopBet[areaID].node.getAnchorPoint()))
            let endPos = this.shaiziLayer.convertToNodeSpaceAR(worldPos);
            cc.tween(shazi).delay(0.5).to(0.5, { x: endPos.x, y: endPos.y, scale: 0.8 }).start();
        }
        this.shaiziLayer.stopAllActions();
        cc.tween(this.shaiziLayer).delay(1).call(function () {
            if (callback) {
                callback();
            }
        }).start();
    }


    public showShaizi(siceresult) {
        this.shaiziLayer.stopAllActions();
        this.shaiziLayer.active = true;
        let shaiziArr = this.shaiziLayer.children;
        let areaIndex = [0, 0, 0, 0, 0, 0];
        for (let index = 0; index < shaiziArr.length; index++) {
            let shazi = shaiziArr[index];
            shazi.stopAllActions();
            shazi.active = true;
            shazi.scale = 0.8;

            let areaID = siceresult[index] - 1;
            areaIndex[areaID] += 1;

            let worldPos = cc.v2(this._siceAreaOffset[areaIndex[areaID]])
                .add(this.deskTopBet[areaID].node.convertToWorldSpaceAR(this.deskTopBet[areaID].node.getAnchorPoint()))
            let endPos = this.shaiziLayer.convertToNodeSpaceAR(worldPos);
            shazi.setPosition(endPos);
            this.setSpriteFrame(shazi.getComponent(cc.Sprite), this.getSiceRes(siceresult[index]));
        }
    }

    private hideShaizi() {
        this.shaiziLayer.stopAllActions();
        this.shaiziLayer.active = false;
        let shaiziArr = this.shaiziLayer.children;
        for (let index = 0; index < shaiziArr.length; index++) {
            let shazi = shaiziArr[index];
            shazi.stopAllActions();
            shazi.active = false;
        }
    }

    private getSiceRes(value) {
        let color = Common.random(1, 4);
        return 'res/textures/shaizi/YXX_' + value + '_0' + color;
    }

    //结算
    onResult(result: any) {
        let self = this;
        let posnums = result.posnums;
        let siceresult = result.siceresult;
        let others = result.others || [];
        let posArray = [];
        let scoreArray = [];
        let scorePosArray = [];
        let areaScoreArray = [];
        let isOnlineCount = 0;
        let meWin = false;
        let melose = false;
        for (let i = 0; i < others.length; i++) {
            let other = others[i];
            if (other.playerid == this.myInfo.playerid) {
                if (other.nChange > 0) {
                    let player = this.myInfo.head;
                    let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
                    posArray.push(playerPos);
                    scorePosArray.push(this.myInfo.resultPos);
                    scoreArray.push(other.nChange);
                    meWin = true;
                } else if (other.nChange < 0) {
                    melose = true;
                }
            } else {
                let hasExists = false;
                for (let j = 0; j < this._playersList.length; j++) {
                    let playerModel = this._playersList[j];
                    if (other.playerid == playerModel.playerid) {
                        if (other.nChange > 0) {
                            let player = playerModel.head;
                            let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
                            posArray.push(playerPos);
                            scoreArray.push(other.nChange);
                            scorePosArray.push(playerModel.resultPos);
                        }
                        hasExists = true;
                    }
                }
                isOnlineCount += hasExists == false ? 1 : 0
            }
        }
        if (isOnlineCount > 0 || posArray.length == 0) {
            let player = this.btnOnline;
            let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
            posArray.push(playerPos);
        }
        for (let i = 0; i < posnums.length; i++) {
            if (posnums[i] > 1) {
                areaScoreArray[i]=  this.deskTopBet[i].total*self._gameCore.odds[posnums[i] - 1];
            }
        }
        let showWinArea = function () {
            let posnums = result.posnums || [];
            for (let i = 0; i < posnums.length; i++) {
                if (posnums[i] > 1) {
                    let area = i;
                    let winNode = self.deskTopBet[area];
                    if (!winNode.winBg.active) {
                        winNode.winBg.active = true;
                        winNode.winBg.opacity = 255;
                        winNode.winBg.stopAllActions();
                        cc.tween(winNode.winBg)
                            .then(cc.tween().to(0.5, { opacity: 0 }).to(0.5, { opacity: 255 }))
                            .repeatForever().start();

                        self.playAreaWinAni(self._gameCore.odds[posnums[i] - 1], winNode.winBg.convertToWorldSpaceAR(winNode.winBg.getAnchorPoint()));
                    }

                }
            }
        }

        let settlement = function () {
            self.settleScoreAni(scoreArray, scorePosArray);
            self.updateMyScore();
            self.updatePlayerScore();
            if (others.length > 0) {
                self.onUpdateUserData(others);
            }
            if (meWin) {
                self._audioMng.playWin();
            }

            cc.tween(self.upAnimLayer).delay(2).call(function () {
                self._gameCore.requestAllPlayerList();
            }).start();
        }
        let showCardCallback = function () {
            showWinArea();
            self.updateResultTrend(result.sposnums);
            self._chipLayer.flyResultChips(posnums, posArray, scoreArray,areaScoreArray, settlement);
        }

        this.closeUpdateScheduler();
        this.onStopBet(function () {
            self.showCardsAni(siceresult, showCardCallback);
        });
    }

    public recoveryResult(result) {
        let self = this;
        let others = result.others || [];
        let scoreArray = [];
        let scorePosArray = [];
        for (let i = 0; i < others.length; i++) {
            let other = others[i];
            if (other.playerid == this.myInfo.playerid) {
                if (other.nChange > 0) {
                    scorePosArray.push(this.myInfo.resultPos);
                    scoreArray.push(other.nChange);
                }
            } else {
                for (let j = 0; j < this._playersList.length; j++) {
                    let playerModel = this._playersList[j];
                    if (other.playerid == playerModel.playerid) {
                        if (other.nChange > 0) {
                            scoreArray.push(other.nChange);
                            scorePosArray.push(playerModel.resultPos);
                        }
                    }
                }
            }
        }
        // console.log('recoveryResult',result,scoreArray,this._playersList);
        let siceresult = result.siceresult;
        let showWinArea = function () {
            //设置赢的区域闪烁
            let posnums = result.posnums || [];
            for (let i = 0; i < posnums.length; i++) {
                if (posnums[i] > 1) {
                    let area = i;
                    let winNode = self.deskTopBet[area];
                    if (!winNode.winBg.active) {
                        winNode.winBg.active = true;
                        winNode.winBg.opacity = 255;
                        winNode.winBg.stopAllActions();
                        cc.tween(winNode.winBg)
                            .then(cc.tween().to(0.5, { opacity: 0 }).to(0.5, { opacity: 255 }))
                            .repeatForever().start();

                        self.playAreaWinAni(self._gameCore.odds[posnums[i] - 1], winNode.winBg.convertToWorldSpaceAR(winNode.winBg.getAnchorPoint()));
                    }

                }
            }
        }

        showWinArea();
        self.settleScoreAni(scoreArray, scorePosArray);
        this.showShaizi(siceresult);
    }

    public settleScoreAni(scoreArray: number[], scorePosArray, callback = null) {
        let self = this;
        for (let i = 0; i < scoreArray.length; i++) {
            let score = scoreArray[i];
            let pos = this.upAnimLayer.convertToNodeSpaceAR(scorePosArray[i]);
            if (score > 0) {
                let scorestr = '+' + Common.moneyString(score);
                this.loadAsset("res/effects/tx_floating_num", cc.Prefab, function (assets: cc.Prefab) {
                    let numNode = cc.instantiate(assets);
                    self.upAnimLayer.addChild(numNode);

                    let imgBg = numNode.getChildByName('Node_1').getChildByName('Image_bg');
                    let num = numNode.getChildByName('Node_1').getChildByName('num');
                    let labNum: any = num.getComponent(cc.Label);
                    labNum.string = scorestr;
                    labNum._forceUpdateRenderData(true);
                    imgBg.width = num.width + 10;
                    if (pos.x < 0) {
                        pos.x = pos.x + (imgBg.width / 2 + 50);
                    } else {
                        pos.x = pos.x - (imgBg.width / 2 + 50);
                    }
                    numNode.setPosition(pos);

                    let pAni = numNode.getComponent(cc.Animation);
                    pAni.play("tx_floating_num");
                    pAni.on(cc.Animation.EventType.FINISHED, function () {
                        if (cc.isValid(numNode)) {
                            numNode.removeFromParent();
                        }
                        if (callback) {
                            callback();
                        }
                    });

                })
            }
        }
    }

    private playAreaWinAni(rate, pos) {
        let self = this;
        this.loadAsset("res/effects/YXX_eff_beishu", cc.Prefab, function (assets: cc.Prefab) {
            let numNode = cc.instantiate(assets);
            self.upAnimLayer.addChild(numNode);
            numNode.setPosition(self.upAnimLayer.convertToNodeSpaceAR(pos));

            let num = numNode.getChildByName('BitmapFontLabel_1').getComponent(cc.Label);
            num.string = "wx" + rate;

            let pAni = numNode.getComponent(cc.Animation);
            pAni.play("YXX_eff_beishu");
            pAni.on(cc.Animation.EventType.FINISHED, function () {
                if (cc.isValid(numNode)) {
                    numNode.removeFromParent();
                }
            });

        })
    }

    /**
     * 修改在线用户数据
     * @param msg 
     */
    onUpdateUserData(msg: any) {
        let playerlist = msg.playerlist || [];
        for (let key in playerlist) {
            let user = this._allUsers[playerlist[key].playerid];
            if (user) {
                user.money = playerlist[key].coin && playerlist[key].coin || playerlist[key].playercoin;
                user.headid = playerlist[key].headid && playerlist[key].headid || user.headid;
                user.wxheadurl = playerlist[key].wxheadurl && playerlist[key].wxheadurl || '';
                user.nickname = playerlist[key].name && playerlist[key].name || user.nickname;
            }
        }
    }

    //更新走势
    public updateTrendData(data) {
        let reclist = data.reclist;
        this._historyListData = [];
        if (reclist) {
            for (let key in reclist) {
                this._historyListData.push(reclist[key]);
            }
        }

        if (this._historyLayer && this._historyLayer.isShow()) {
            this._historyLayer.updateHistory(this._historyListData);
        }
        this.onUpdateHistoryIcon();
    }

    //结算更新走势
    public updateResultTrend(posnums) {
        this._historyListData.push(posnums);
        if (this._historyListData.length > 100) {
            this._historyListData.shift();
        }
        if (this._historyLayer && this._historyLayer.isShow()) {
            this._historyLayer.updateHistory(this._historyListData);
        }
        this.onUpdateHistoryIcon();
    }

    //更新游戏走势小图
    public onUpdateHistoryIcon() {
        let length = this._historyListData.length;
        for (let index = 0; index < this.deskTopBet.length; index++) {
            let desk = this.deskTopBet[index];
            for (let i = 0; i < 9; i++) {
                let image = desk.trendBg.getChildByName('Image_trend_' + (i + 1));
                let text = desk.trendBg.getChildByName('Text_trend_' + (i + 1));
                let hasrecord = i < length;
                image.active = hasrecord;
                text.active = hasrecord;
                if (hasrecord) {
                    let num = this._historyListData[length - i - 1]['' + (index + 1)];
                    if (num > 1) {
                        text.getComponent(cc.Label).string = `${num}`;
                        text.color = cc.color(0, 110, 122);
                        this.setSpriteFrame(image.getComponent(cc.Sprite), 'res/textures/fishmain/YXX_zmzs_red');
                    } else {
                        text.getComponent(cc.Label).string = `X`;
                        text.color = cc.color(101, 91, 91);
                        this.setSpriteFrame(image.getComponent(cc.Sprite), 'res/textures/fishmain/YXX_zmzs_gray');
                    }
                }
            }

        }
    }

    //下注成功
    public myBetSuccess(msg: any) {
        let bet = msg.bet;
        let direction = msg.direction;
        let chouma = msg.chouma;
        let totalbet = msg.totalbet;
        let allbet = msg.allbet;
        let mybet = msg.mybet;
        if (bet) {
            let player = this.myInfo.head;
            let playerPos = player.convertToWorldSpaceAR(player.getAnchorPoint());
            this._chipLayer.bet(bet / Config.SCORE_RATE, playerPos, direction - 1, false, function () { });
            this.playSelfSeatBetAnim();

            for (let key in mybet) {
                let area = Common.toInt(key) - 1;
                let score = mybet[key];
                this.setSelfBetArea(area, score, chouma);

                this.myInfo.betscore[area] = score;
            }
        }
        if (allbet) {
            for (let key in allbet) {
                this.setBetAreaTotal(Common.toInt(key) - 1, allbet[key]);
            }
        }

        if (this.getPlayerAllBetScore(this.myInfo) >= Constant.FIRE_SCORE) {
            this.playPlayerFire(this.myInfo);
        }
        this._betLayer.autoSelectChip();
    }

    /**
     * 修改筹码回调
     */
    private updateChipCall() {
        let self = this;
        let lengthVal = self._flyingBets.length;

        if (lengthVal <= 0) return;

        let allScore = [0, 0, 0, 0, 0, 0];
        let startPos = this.btnOnline.convertToWorldSpaceAR(this.btnOnline.getAnchorPoint());
        let lastval = [];
        for (let i = 0; i < lengthVal; i++) {
            let val = self._flyingBets[i];
            if (val.score) {
                allScore[val.area] += val.score;
                lastval[val.area] = val;
            }
        }
        let index = 0;
        for (let i = 0; i < 6; i++) {
            // let count = i < 2 ? 2 : 1;
            let count = 25;
            let chipScore = this._chipLayer.splitUserBetChipScore(allScore[i], count);
            for (let j = 0; j < chipScore.length; j++) {
                let delayTime = Common.random(1, Math.min(500, j * 100)) / 1000
                this._chipLayer.onlineBet(chipScore[j], startPos, i, function () {
                    if (lastval[i] && lastval[i].callback) {
                        lastval[i].callback();
                    }
                }, delayTime);
                index++;
            }
        }
        self._flyingBets = [];
    }

    private openUpdateScheduler() {
        if (!this._updateScheduler) {
            this.schedule(this.updateChipCall, 1);
            this._updateScheduler = true;
            this.updateChipCall();
        }
    }

    private closeUpdateScheduler() {
        if (this._updateScheduler) {
            this.unschedule(this.updateChipCall);
            this._updateScheduler = false;
        }
    }

    /**
     * 同步玩家下注筹码
     * @param playerid 
     * @param count 
     * @param chipNum 
     * @param betinfo 
     * @param area 
     * @param total 
     * @returns 
     */
    public updateChipMsg(playerid: any, count: number, chipNum: number, betinfo: any, area: number, total: {}) {
        let gameMyInfo = this._gameCore.userInfo;

        if (playerid == gameMyInfo.playerid) return;

        // if (this._flyingBets.length > 0) {
        //     this.updateChipCall();
        // }

        let msg: any = {};
        msg.playerid = playerid;
        msg.chouma = betinfo.chouma;
        msg.odds = chipNum;
        msg.direction = area - 1;
        msg.dirctionall = total[area];
        this.onUpdateUserBetUserInfo(msg);

        let player = this.getTablePlayer(playerid);
        //在线玩家
        if (player == null) {
            this._userBetMsgTemp.push(msg);
            this.onOtherUserBet();
            this.setUserBetAreaInfo(msg);
            this.openUpdateScheduler();
            return;
        }

        //座位玩家
        let self = this;
        if (count > 0) {
            if (msg.playerid != gameMyInfo.playerid) {
                let startPos = player.head.convertToWorldSpaceAR(player.head.getAnchorPoint());
                this._chipLayer.playerBet(msg.odds, startPos, msg.direction, function () {
                    self.setBetAreaTotal(msg.direction, msg.dirctionall);
                });

                if (player.score) {
                    player.score.string = "₹" + Common.moneyString(msg.chouma);
                }
                player.betscore[msg.direction] += msg.odds;

                self.playSeatBetAnim(playerid);

                if (this.getPlayerAllBetScore(player) >= Constant.FIRE_SCORE) {
                    this.playPlayerFire(player);
                }

            } else {
                self.setBetAreaTotal(msg.direction, msg.dirctionall);
            }
        } else {
            self.setBetAreaTotal(msg.direction, msg.dirctionall);
        }

        self.shenSZBet(msg.playerid, msg.direction);
    }

    /**
     * 玩家下注同步玩家数据
     * @param msg 
     */
    public onUpdateUserBetUserInfo(msg: any) {
        let val = this._allUsers[msg.playerid];
        if (val) {
            val.money = msg.chouma;
        }
    }


    /**
     * 玩家下注
     */
    public onOtherUserBet() {
        let self = this;
        for (let i = 0; i < self._userBetMsgTemp.length; i++) {
            let key = i;
            let val = self._userBetMsgTemp[i];
            if (val) {
                if (val.direction == 2) {

                }
                if (val.odds > 16000) {
                    let userBet = this._chipLayer.splitUserBetChipScore(val.odds, 3);

                    for (let j = 0; j < userBet.length; j++) {
                        self._flyingBets.push({ score: userBet[j], player: self.btnOnline, area: val.direction })
                    }

                } else {
                    self._flyingBets.push({
                        score: val.odds, player: self.btnOnline, area: val.direction, callback: function () {
                            self.setUserBetAreaInfo(val);
                        }
                    })

                }
            }
            self._userBetMsgTemp.splice(key, 1);
            i--;
        }

    }
    /**
     * 设置下注区域筹码数据显示
     * @param msg 
     */
    public setUserBetAreaInfo(msg: any) {
        let gameMyInfo = this._gameCore.userInfo;
        let direction = msg.direction;
        let areaAll = msg.dirctionall;
        this.setBetAreaTotal(direction, areaAll);

        if (msg.playerid == gameMyInfo.playerid) {
            this.setSelfBetArea(direction, msg.buyall, msg.chouma)
        }
    }

    //更新总注
    public setBetAreaTotal(area: number, total: number) {
        let curAreaAll = this.deskTopBet[area].total;
        if (total >= curAreaAll) {
            this.deskTopBet[area].total = total;     //--这个区域下的总注
            this.deskTopBet[area].totaLabel.string = Common.moneyString(total);//--这个区域下的总注
        }
    }

    //更新自己下注
    public setSelfBetArea(area: number, score: number, chouma: number) {
        if (score <= 0) return;
        this.deskTopBet[area].self = score;
        this.deskTopBet[area].selfBetBg.active = true;;
        this.deskTopBet[area].selfLabel.string = Common.moneyString(score);   //--我在这个区域下的总注
        this.myInfo.score.string = "₹" + Common.moneyString(chouma);
        this.myInfo.isBet = true;

        let gameMyInfo = this._gameCore.userInfo;
        gameMyInfo.money = chouma;
        this._betLayer.autoSelectChip();
    }

    //更新自己下注
    public addSelfBetArea(area: number, score: number) {
        if (!this.deskTopBet[area]) return;
        let gameMyInfo = this._gameCore.userInfo;
        this.deskTopBet[area].self += score;
        this.deskTopBet[area].total += score;
        let curAreaAll = this.deskTopBet[area].total;

        this.setSelfBetArea(area, this.deskTopBet[area].self, gameMyInfo.money);
        this.setBetAreaTotal(area, curAreaAll);
    }

    //自己下注总额
    public getSelfBet() {
        let selfTotal = 0;
        for (let i = 0; i < this.deskTopBet.length; i++) {
            let val = this.deskTopBet[i];
            if (val) {
                selfTotal += val.self;
            }
        }
        return selfTotal;
    }

    //获取区域自己下注
    public getSelfAreaBet(area: number) {
        let selfTotal = 0;
        let val = this.deskTopBet[area];
        if (val) {
            selfTotal += val.self;
        }
        return selfTotal;
    }

    /**
     * 神算子下注显示星
     * @param playerid 
     * @param area 
     */
    shenSZBet(playerid: any, area: any) {
        let self = this;
        if (playerid == self._playersList[0].playerid) {
            let lucky: cc.Node = self.deskTopBet[area].starLucky;
            let loadingBar = lucky.getChildByName('LoadingBar_star').getComponent(cc.ProgressBar);
            let player = self._playersList[0];
            let startPos = player.head.convertToWorldSpaceAR(player.head.getAnchorPoint());
            let endPos = self.deskTopBet[area].node.convertToWorldSpaceAR(lucky.getPosition());
            self._chipLayer.playFlyStar(startPos, endPos, function () {
                lucky.active = true;
                let betscore = player.betscore[area];
                loadingBar.progress = Math.min(betscore / 2000, 100)
            });
        }
    }

    public restoreDeskChips(area: number, score: number) {
        this._chipLayer.createChips(area, score, 20);
    }

    private playSelfSeatBetAnim() {
        let seatAni = this.myInfo.seatNode.getComponent(cc.Animation);
        let animState = seatAni.getAnimationState('right');
        if (!animState.isPlaying) {
            animState.speed = 1.2;
            seatAni.play("right");
        }
    }

    private playSeatBetAnim(playerid: number) {
        let player = this.getTablePlayer(playerid);
        if (!player) return;

        let seatAni = player.seatNode.getComponent(cc.Animation);
        let animState = seatAni.getAnimationState('right');
        if (!animState.isPlaying) {
            animState.speed = 1.2;
            if (player.dirction == 0) {
                seatAni.play("left");
            } else {
                seatAni.play("right");
            }

        }
    }

    private playPlayerFire(player) {
        let fire = player.nodeFire.getChildByName('tx_haiwai_touxiangkuang2').getComponent(cc.Animation);
        let animState = fire.play("tx_haiwai_touxiangkuang2");
        animState.wrapMode = cc.WrapMode.Loop;
        player.nodeFire.active = true;
        player.nodeFire.getChildByName('tx_haiwai_touxiangkuang2').active = true;
    }

    private getPlayer(playerid: number): FishPCModel.Player {
        let player = null;
        if (playerid == this.myInfo.playerid) {
            player = this.myInfo;
        } else {
            player = this.getTablePlayer(playerid);
        }
        return player;
    }

    private getTablePlayer(playerid: number): FishPCModel.Player {
        let player = null;
        for (let i = 0; i < this._playersList.length; i++) {
            if (playerid == this._playersList[i].playerid) {
                player = this._playersList[i];
                break;
            }
        }
        return player;
    }

    private getPlayerAllBetScore(player) {
        let betscore = player.betscore;
        let allnum = 0;
        if (betscore) {
            for (let key in betscore) {
                allnum += betscore[key];
            }
        }
        return allnum;
    }

    /**
     * 清除游戏数据
     */
    cleanTable() {
        for (let i = 0; i < this.deskTopBet.length; i++) {
            let val = this.deskTopBet[i];
            if (val) {
                val.total = 0;
                val.self = 0;
                val.totaLabel.string = "0";
                val.selfLabel.string = "0";
                val.starLucky.active = false;
                val.winBg.active = false;
                val.winBg.stopAllActions();
                val.selfBetBg.active = false;
            }
        }
        for (let index = 0; index < this._playersList.length; index++) {
            let val = this._playersList[index];
            val.nodeFire.active = false;
            val.betscore = [0, 0, 0, 0, 0, 0];
        }
        this._userBetMsgTemp = [];
        this._flyingBets = [];
        this.closeUpdateScheduler();
        this._chipLayer.resetChips();

        this.hideCardsAni();
        this.hideTipWait();
        this.hideShaizi();

        this.myInfo.isBet = false;
        this.myInfo.betscore = [0, 0, 0, 0, 0, 0];
        this.myInfo.nodeFire.active = false;

        this.stopBetOut();
        //清理分数效果
        // this._resultNode.removeAllChildren();
        this.centerLayer.stopAllActions();

        this._betAniNode.active = false;
        this.upAnimLayer.stopAllActions();
        this.upAnimLayer.removeAllChildren();
    }
    //////////////////////////////////////////////////////////////////////////////
}