import Common from "../../../../script/frame/common/Common";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import FishPCGameCore from "../core/FishPCGameCore";
import FishPCGameView from "./FishPCGameView";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import ButtonLayer from "../../../../script/frame/component/ButtonLayer";
import DataManager from "../../../../script/frame/manager/DataManager";
import HallManager from "../../../../script/frame/manager/HallManager";
import { Constant, GameTextTips } from "../core/FishPCDefine";
import Config from "../../../../script/frame/config/Config";
import { quickPayStyle } from "../../../../script/frame/common/Define";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FishPCBetLayer extends BaseLayer {

    @property(cc.Node)
    nodeChips: cc.Node[] = [];

    //选中筹码索引
    private _selectIndex = 0;
    //筹码Y
    private _betChipY = 61;
    //选中筹码Y
    private _selBetChipY = 71;

    private _commonColor =cc.color(255, 255, 255);
    private _grayColor = cc.color(128, 128, 128);
    //游戏core层
    private _gameCore: FishPCGameCore;
    //游戏视图层
    private _gameView: FishPCGameView;

    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent(FishPCGameCore);
        this._gameView = cc.Canvas.instance.getComponent(FishPCGameView);
    }

    public get selectIndex(): number {
        return this._selectIndex;
    }

    public initBet() {
        this._selectIndex = 0;


        for (var i = 0; i < Constant.CHIPS_COUNT; i++) {
            let chipItem = this.nodeChips[i];
            let sprite = chipItem.getChildByName("Image_btn").getComponent(cc.Sprite);
            chipItem.y = this._betChipY;
            chipItem.getChildByName("selected").active = false;
            this.setSpriteFrame(sprite, this.getChipRes(i));

            if (!chipItem.getComponent('ButtonLayer')) {
                chipItem.addComponent(ButtonLayer);
                let eventHandler = new cc.Component.EventHandler();
                eventHandler.component = cc.js.getClassName(this);
                eventHandler.handler = 'onClickChip';
                eventHandler.target = this.node;
                eventHandler.customEventData = i + '';
                chipItem.getComponent('ButtonLayer').clickEvents.push(eventHandler);
            }
        }

        this.disableChips(true);
        this.autoSelectChip();
    }

    private getChipRes(index) {
        let chipres = [10, 50, 100, 1000, 5000, 10000, 20000];
        for (let i = 0; i < chipres.length; i++) {
            if (chipres[i] == this._gameCore.chipsScore[index]) {
                return "res/textures/fishmain/icon_chips_" + (i + 1);
            }
        }
    }

    public onClickChip(event, customData) {
        let index = Common.toInt(customData);
        this.setSelectChips(index);
    }

    /**
    设置筹码筹码选中
    @param index 下注区域筹码索引
    */
    private setSelectChips(index: number) {
        let selectScore = 0;
        if (index < this._gameCore.chipsScore.length && this._gameCore.chipsScore[index]) {
            selectScore = this.getChipScore(index);
        }
        let myMoney: number = this._gameCore.getMeMoney();
        // if (myMoney < selectScore) return;

        this._selectIndex = index;
        for (var i = 0; i < this.nodeChips.length; ++i) {
            let chip = this.nodeChips[i];
            if (myMoney < this.getChipScore(i)) {
                this.setDisableChips(i, true);
            } else {
                chip.getChildByName("Image_btn").color=this._commonColor;
                chip.getComponent(ButtonLayer).enableTouch = true;
            }
            let select = chip.getChildByName("selected");
            if (index == i) {
                chip.y = this._selBetChipY;
                select.active = true;
            } else {
                chip.y = this._betChipY;
                select.active = false;
            }
        }
        return true;
    }

    /**
    * 设置所有筹码是否可选
    * @param index 
    * @param hasEnabled boolean true 不可选 false 可选
    * @returns 
    */
    public disableChips(hasEnabled: boolean) {
        for (var i = 0; i < this.nodeChips.length; ++i) {
            this.setDisableChips(i, hasEnabled);
        }
    }

    /**
     * 设置单个筹码是否可选
     * @param index 
     * @param hasEnabled boolean true 不可选 false 可选
     * @returns 
     */
    private setDisableChips(index: number, hasEnabled: boolean) {
        if (index >= this.nodeChips.length) {
            return;
        }
        let chip = this.nodeChips[index];
        chip.y = this._betChipY;
        chip.getComponent(ButtonLayer).enableTouch = !hasEnabled;
        chip.getChildByName("Image_btn").color = hasEnabled ? this._grayColor : this._commonColor;

        // let select = chip.getChildByName("selected");
        // select.active = !hasEnabled;
    }

    /**
     *根据用户金额，设置筹码是否可选
     */
    public autoSelectChip() {
        let myMoney: number = this._gameCore.getMeMoney();
        if (myMoney < this._gameCore.betNeed) {
            // this.disableChips(true);
            this._selectIndex=0;
            // return;
        }

        let selectScore: number = this.getChipScore(this._selectIndex);
        let selectIndex = this._selectIndex;
        //当前选中筹码，大于用户金额
        if (selectScore > myMoney) {
            //设置可选最大金额
            for (let i = 0; i < this._gameCore.chipsScore.length; i++) {
                if (myMoney >= this.getChipScore(i)) {
                    selectIndex = i;
                }
            }
        }
        this.setSelectChips(selectIndex);
    }

    /**
     * 点击下注区域
     * @param area 下注区域
     * @returns 
     */
    public onClickBet(area: number) {
        let gameCore = this._gameCore;
        let isStartBet: boolean = gameCore.isStartBet;
        if (!isStartBet) return;
        if (this.checkMoney(area)) {
            gameCore.requestBet(area + 1, this._selectIndex);
        }
    }

    //检测余额是否够下注
    private checkMoney(area: number) {
        if (DataManager.instance.isNeedPay(this._gameCore.roomInfo.gameid)) {//没有充值，提示充值
            HallManager.instance.openCharge({quickPayStyle:quickPayStyle.gameVipPay});
            return false;
        }

        let myMoney: number = this._gameCore.userInfo.money;
        let betNeed: number = this._gameCore.betNeed;
        let betMax: number = this._gameCore.betMax;
        let selectMoney = this.getChipScore(this.selectIndex);
        if (myMoney < betNeed) {
            HallManager.instance.openCharge({ gameChargePay: true });
            return false;
        }

        let selfAllBet = this._gameView.getSelfBet();
        if ((selfAllBet + selectMoney) > betMax) {
            ToastHelper.show(Common.stringFormat(GameTextTips.GOLD_BET_MAX_ERR, [betNeed]));
            return false;
        }

        // let leftbet= this._gameCore.leftbet[area];
        // if ((selfAllBet + selectMoney) > betMax) {
        //     ToastHelper.show(Common.stringFormat(GameTextTips.GOLD_BET_MAX_ERR, [betNeed]));
        //     return false;
        // }

        return true;
    }

    private getChipScore(index) {
        return this._gameCore.chipsScore[index] * Config.SCORE_RATE;
    }
}
