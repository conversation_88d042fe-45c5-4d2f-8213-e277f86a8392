import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import FishPCGameCore from "../core/FishPCGameCore";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FishPCDealerLayer extends BaseLayer {

    @property(cc.Node)
    dealerNode: cc.Node = null;

    @property(cc.Node)
    chatNode: cc.Node = null;

    @property(cc.Node)
    tipsBgNode: cc.Node = null;

    @property(cc.Label)
    labTips: cc.Label = null;

    @property(cc.Label)
    labFee: cc.Label = null;

    @property(cc.Node)
    sureNode: cc.Node = null;

    @property(cc.Node)
    loveNode: cc.Node = null;

    @property(cc.Prefab)
    tipPrefab: cc.Prefab = null;

    _gameCore: FishPCGameCore = null;
    _dealer: sp.Skeleton = null;
    _love: sp.Skeleton = null;
    _firstEnter = true;
    _playing = false;
    _dealerPos = cc.v2(0, 202);

    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent('FishPCGameCore');
        this.chatNode.active = false;

        this._dealer = this.dealerNode.getComponent(sp.Skeleton);
        this._dealer.setAnimation(0, "animation", true);

        this._love = this.loveNode.getComponent(sp.Skeleton);
        this.loveNode.active = false;
    }

    private onClickTip() {
        if (this._firstEnter) {
            this.labTips.string = 'Tip the dealer';
            this.tipsBgNode.width = 350;
            this.sureNode.active = true;
            this.labFee.node.active = true;
            this.chatNode.active = true;
            this._firstEnter = false;
        } else {
            this.sendChat();
        }
    }

    public onClickSure() {
       this.onClickBg();
       this.sendChat();
    }

    public onClickBg() {
        if (this.chatNode.active) {
            this.sureNode.active = false;
            this.labFee.node.active = false;
            this.chatNode.stopAllActions();
            this.chatNode.active = false;
            this._firstEnter = false;
        }
    }

    public sendChat() {
        this._gameCore.sendTipDealer({});
        this.close();
    }

    private showTips() {
        let tipdealer = this._gameCore.tipdealerfee;
        let talkStr = ["Thank you and good luck",
            "Thank you,It is your day!"];
        let talkIndex = Common.random(0, 1);
        this.labTips.string = talkStr[talkIndex];
        this.labFee.string = "₹" + Common.moneyString(tipdealer);
        this.tipsBgNode.width = 350;
        this.sureNode.active = false;
        this.labFee.node.active = false;

        this.chatNode.active = true;
        this.chatNode.stopAllActions();
        cc.tween(this.chatNode).delay(3).call(() => {
            this.chatNode.active = false;
        }).start();
    }

    public doTipDealer(info) {
        let playerPos = info.playerPos;
        let tipdealer = this._gameCore.tipdealerfee;

        let tipNode = cc.instantiate(this.tipPrefab);
        tipNode.parent = this.node;
        tipNode.setPosition(playerPos);
        tipNode.opacity = 0;
        tipNode.getChildByName('label').getComponent(cc.Label).string = "₹" + Common.moneyString(tipdealer);
        cc.tween(tipNode).to(0.1, { opacity: 255 }).to(1, { x: this._dealerPos.x, y: this._dealerPos.y }).to(0.1, { opacity: 0 }).call(() => {
            if (this._playing) return;
            this._playing = true;
            let rate = Common.random(1, 10);
            let type = 2;
            if (rate <= 3 && rate >= 1) {
                type = 3;
            }
            this.loveNode.setPosition(this._dealerPos);
            if (type == 2) {
                cc.tween(this.loveNode).delay(1.16).call(() => {
                    this.loveNode.active = true;
                    this._love.setAnimation(0, 'xin', false);
                }).to(1, { position: playerPos }).call(() => {
                    this.showTips();
                    this._love.setAnimation(0, 'maoaixin', false);
                    this._playing = false;
                }).start();
            } else if (type == 3) {
                cc.tween(this.loveNode).delay(2.43).call(() => {
                    this.showTips();
                    this._playing = false;
                }).start();
            }

            let aniName = type == 2 ? "feiwen" : "dazhaohu";
            let aniTime = type == 2 ? 2 : 2.43;
            this._dealer.setAnimation(0, aniName, false);
            this.dealerNode.stopAllActions();
            cc.tween(this.dealerNode).delay(aniTime).call(() => {
                this._dealer.setAnimation(0, "animation", true);
            }).start();
        }).start();
    }

    public close() {
        this.chatNode.active = false;
    }


}