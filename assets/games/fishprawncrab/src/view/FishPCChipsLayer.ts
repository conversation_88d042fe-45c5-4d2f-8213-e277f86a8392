/**
 * 筹码结算相关方法
 * 筹码移动
 * 筹码分拆
 */
import Common from "../../../../script/frame/common/Common";
import FishPCGameView from "./FishPCGameView";
import FishPCModel from "../core/FishPCModel";
import FishPCGameCore from "../core/FishPCGameCore";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import FishPCAudioMng from "../core/FishPCAudioMng";
import { Constant } from "../core/FishPCDefine";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FishPCChipsLayer extends BaseLayer {

    @property(cc.Node)
    dealer: cc.Node = null;

    @property(cc.Prefab)
    chipPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    flyStar: cc.Prefab = null;

    //游戏core层
    private _gameCore: FishPCGameCore;
    /** 游戏视图 */
    private _gameView: FishPCGameView;
    //声音
    private _audioMng: FishPCAudioMng;

    //筹码集合
    private _chipsArr = [];

    private chipsPool: cc.NodePool = null;
    private maxCount: number = 220;
    private chipIndex: number = 0;


    onLoad() {
        this._gameCore = cc.Canvas.instance.getComponent(FishPCGameCore);
        this._gameView = cc.Canvas.instance.getComponent(FishPCGameView);
        this._audioMng = cc.Canvas.instance.getComponent(FishPCAudioMng);

        this.chipsPool = new cc.NodePool();
        this._chipsArr = [];
        for (let i = 0; i < Constant.AREA_COUNT; ++i) {
            this._chipsArr.push([]);

            for (let i = 0; i < this.maxCount; ++i) {
                let chip = cc.instantiate(this.chipPrefab);
                this.chipsPool.put(chip);
            }
        }
    }

    onDestroy() {
        this.chipsPool.clear();
    }

    public resetChips() {
        for (let i = 0; i < this._chipsArr.length; ++i) {
            for (let index = 0; index < this._chipsArr[i].length; index++) {
                this.chipsPool.put(this._chipsArr[i][index]);
            }
        }
        this._chipsArr = [];
        for (let i = 0; i < Constant.AREA_COUNT; ++i) {
            this._chipsArr.push([]);
        }
        this.node.stopAllActions();
        this.chipIndex = 0;
    }

    public resetAreaChip(area) {
        for (let index = 0; index < this._chipsArr[area].length; index++) {
            this.chipsPool.put(this._chipsArr[area][index]);
        }
        this._chipsArr[area] = [];
    }
    /**
     * 根据下注总分数 拆分筹码分数
     * @param score 
     * @param count 
     * @returns 
     */
    public splitUserBetChipScore(score: any, count: number): number[] {
        let publicScore = this._gameCore.chipsScore;
        let chipScore = score;
        let tempCount = 0;
        let userScore: number[] = [];
        for (let i = 0; i < publicScore.length; i++) {
            tempCount = Math.floor(chipScore / publicScore[i]);
            chipScore = publicScore[i];
            if (tempCount > count) {
                tempCount = Math.floor(count / (i + 1));
            }
            for (let j = 0; j < tempCount; j++) {
                userScore.push(chipScore);
            }

            chipScore = score - (userScore.length * 100);
        }
        return userScore;
    }

    //恢复桌上筹码
    public createChips(area, score, count) {
        let chipScore = this.splitUserBetChipScore(score, count);
        if (!chipScore || chipScore.length <= 0) {
            return;
        }
        let length = chipScore.length;
        for (let i = length - 1; i > 0; i--) {
            let chip = this.createChip(chipScore[i], area);
            let endPos = this.getChipFinalPos(chip, area);
            chip.setPosition(endPos);
        }
    }

    public onlineBet(score: number, startPos: cc.Vec2, area: number, callback: Function, delayTime: number = 0) {
        // let playSound = Common.random(1, 100) > 80;
        let playSound = true;
        this.bet(score, startPos, area, playSound, callback, delayTime);
    }

    public playerBet(score: number, startPos: cc.Vec2, area: number, callback: Function, delayTime: number = 0) {
        // let playSound = Common.random(1, 100) > 30;
        let playSound = true;
        this.bet(score, startPos, area, playSound, callback, delayTime);
    }

    public bet(value: number, startPos: cc.Vec2, area: number, playSound: boolean, callback: Function, delayTime: number = 0) {
        let chip = this.createChip(value, area);
        if (chip) {
            startPos = cc.v2(this.node.convertToNodeSpaceAR(startPos));
            chip.x = startPos.x;
            chip.y = startPos.y;
            let endPos = this.getChipFinalPos(chip, area);
            let time = cc.Vec2.len(cc.v2(startPos.x - endPos.x, startPos.y - endPos.y)) / 2800;
            this.chipAction(chip, endPos, time, delayTime, playSound, callback);
        }
    }

    private chipAction(chip: cc.Node, endPos: cc.Vec2, time: number, delayTime = 0, playSound: boolean = true, callback: Function = null) {
        let self = this;
        chip.zIndex = chip.zIndex + 1000;
        if(delayTime>0){
            chip.runAction(cc.sequence(cc.hide(),cc.delayTime(delayTime),cc.show(),cc.moveTo(time,endPos).easing(cc.easeCubicActionOut()) ,cc.callFunc(function(){
                chip.zIndex = chip.zIndex - 1000;
                if (callback) {
                    callback();
                }
            })))
        }else{
            chip.runAction(cc.sequence(cc.moveTo(time,endPos).easing(cc.easeCubicActionOut()) ,cc.callFunc(function(){
                chip.zIndex = chip.zIndex - 1000;
                if (callback) {
                    callback();
                }
            })))
        }

        if (playSound) {
            self._audioMng.playBet();
        }
    }

    private createChip(value: number, area: number, clearOver: boolean = true) {
        let index = this._gameCore.chipsScore.indexOf(value);
        if (index < 0) return;

        let chip = this.chipsPool.get();
        if (!chip) {
            chip = cc.instantiate(this.chipPrefab);
        }

        chip.stopAllActions();
        chip.parent = this.node;
        chip.active = true;
        chip.name = value + '';

        let sprite = chip.getChildByName("Image_icon").getComponent(cc.Sprite);

        this.setSpriteFrame(sprite, this.getChipRes(value));

        this._chipsArr[area].push(chip);

        this.chipIndex++;
        chip.zIndex = this.chipIndex;

        if (clearOver) {
            this.clearOverChip(area);
        }

        return chip;
    }

    private clearOverChip(area: number) {
        let chipsLength = this._chipsArr[area].length;
        if (chipsLength > this.maxCount) {
            let chip = this._chipsArr[area].shift();
            if (chip) {
                this.chipsPool.put(chip);
            }
        }
    }


    private getChipRes(value) {
        let respath = "res/textures/fishmain/icon_chips_1";
        let index = this._gameCore.chipsScore.indexOf(value);
        if (index >= 0 && index < 7) {
            respath = "res/textures/fishmain/icon_chips_" + (index + 1);
        }
        return respath;
    }

    private getChipFinalPos(chip: cc.Node, area: number) {
        let chipPosNode = this._gameView.deskTopBet[area].chipArea;
        let rangPos = cc.v2(this.node.convertToNodeSpaceAR(chipPosNode.convertToWorldSpaceAR(chipPosNode.getAnchorPoint())));
        let subNum = chipPosNode.width * 0.5 - chip.width * 0.5;
        let minX = rangPos.x - subNum;
        let maxX = rangPos.x + subNum;

        subNum = chipPosNode.height * 0.5 - chip.height * 0.5;
        let minY = rangPos.y - subNum;
        let maxY = rangPos.y + subNum;
        let endPos: cc.Vec2 = cc.v2(Common.random(minX, maxX), Common.random(minY, maxY));
        return endPos;
    }

    public flyResultChips(posnums, posArray, scoreArray,areaScoreArray, callback = null) {
        let isWin = false;
        let areaWin = [false, false, false, false, false, false];
        let areaWinIndex = [];
        let lastAreaWinIndex = -1;
        for (let index = 0; index < posnums.length; index++) {
            if (posnums[index] > 1) {
                isWin = true;
                areaWin[index] = true;
                areaWinIndex.push(index);
                lastAreaWinIndex = index;
            }
        }
        let dealerPos = this.node.convertToNodeSpaceAR(this.dealer.convertToWorldSpaceAR(this.dealer.getAnchorPoint()));
        let flyToDealer = cc.tween().call(() => {
            for (let index = 0; index < areaWin.length; index++) {
                if (!areaWin[index]) {
                    let chips = this._chipsArr[index];
                    for (let j = 0; j < chips.length; j++) {
                        let chip = chips[j];
                        let startPos = chip.getPosition();
                        let time = cc.Vec2.len(cc.v2(startPos.x - dealerPos.x, startPos.y - dealerPos.y)) / 1800;
                        this.chipAction(chip, dealerPos, time, 0, true, () => {
                            chip.active = false;
                        });
                    }
                }
            }
        }).delay(1);

        if (isWin) {
            cc.tween(this.node).then(flyToDealer).call(() => {
                for (let index = 0; index < areaWin.length; index++) {
                    if (areaWin[index]) {
                        // let area = areaWinIndex[Common.random(0, areaWinIndex.length - 1)];
                        // let chips = this._chipsArr[index];
                        let chips = this.createDealerChips(areaScoreArray[index],index,dealerPos);
                        for (let j = 0; j < chips.length; j++) {
                            let chip = chips[j];
                            let startPos = chip.getPosition();
                            let endPos = this.getChipFinalPos(chip, index);
                            let time = cc.Vec2.len(cc.v2(startPos.x - endPos.x, startPos.y - endPos.y)) / 1800;
                            let delayTime = Common.random(1, Math.min(500, j * 100)) / 1000;
                            chip.active = true;
                            this.chipAction(chip, endPos, time, delayTime);
                        }
                    }else{
                        this.resetAreaChip(index);
                    }
                }
            }).delay(1).call(() => {
                let posLength = posArray.length;
                for (let i = 0; i < posLength; i++) {
                    posArray[i] = cc.v2(this.node.convertToNodeSpaceAR(posArray[i]));
                }
                for (let i = 0; i < posLength; i++) {
                    let chips = this.getUserWinChips(scoreArray[i], lastAreaWinIndex, i == posLength - 1);
                    for (let j = 0; j < chips.length; j++) {
                        let chip = chips[j];
                        let startPos = chip.getPosition();
                        let endPos = posArray[i];
                        let time = cc.Vec2.len(cc.v2(startPos.x - endPos.x, startPos.y - endPos.y)) / 1000;
                        // let delayTime = Common.random(1, Math.min(500, j * 100)) / 1000;
                        chip.active = true;
                        this.chipAction(chip, endPos, time, 0, true, () => {
                            chip.active = false;
                        });
                    }
                }
            }).call(function () {
                if (callback) {
                    callback();
                }
            }).start();
        } else {
            cc.tween(this.node).then(flyToDealer).call(function () {
                if (callback) {
                    callback();
                }
            }).start();
        }


    }

    private createDealerChips(score,area,pos) {
        let chipScore = this.splitUserBetChipScore(score, 5);
        if (!chipScore || chipScore.length <= 0) {
            return;
        }
        let length = chipScore.length;
        let chips=[];
        for (let i = length - 1; i > 0; i--) {
            let chip = this.createChip(chipScore[i], area);
            chip.setPosition(pos);
            chips.push(chip);
        }
        return chips;
    }

    private getUserWinChips(winscore, area, last = false) {
        let chips = [];
        if (last) {
            for (let index = 0; index < this._chipsArr.length; index++) {
                for (let j = 0; j < this._chipsArr[index].length; j++) {
                    let chip = this._chipsArr[index][j];
                    if (chip.name != 'end') {
                        chips.push(chip);
                    }
                }
            }
        } else {
            let chipScore = this.splitUserBetChipScore(winscore, 3);
            let length = chipScore.length;
            for (let i = 0; i < length; i++) {
                let chip = this.getChipWithScore(chipScore[i], area);
                if (chip) {
                    chip.name = 'end';
                    chips.push(chip);
                }
            }
        }

        return chips;
    }

    private getChipWithScore(score, area) {
        for (let index = 0; index < this._chipsArr.length; index++) {
            for (let j = 0; j < this._chipsArr[index].length; j++) {
                let chip = this._chipsArr[index][j];
                if (Common.toInt(chip.name) == score) {
                    return chip;
                }
            }
        }
        return this.createChip(score, area);
    }

    public playFlyStar(startPos, endPos, callback) {
        let starNode = cc.instantiate(this.flyStar);
        starNode.parent = this.node;
        starNode.zIndex = 10000;
        startPos = this.node.convertToNodeSpaceAR(startPos);
        endPos = this.node.convertToNodeSpaceAR(endPos);
        starNode.setPosition(startPos);

        starNode.runAction(cc.sequence(cc.delayTime(0.5), cc.jumpTo(0.5, endPos.x, endPos.y, 50, 1), cc.callFunc(() => {
            if (callback) {
                callback();
            }
        }), cc.removeSelf()))
    }
}