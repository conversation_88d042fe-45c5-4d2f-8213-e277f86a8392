import Common from "../../../../script/frame/common/Common";
import BaseLayer from "../../../../script/frame/component/BaseLayer";
import ListView from "../../../../script/frame/component/ListView";
import FishPCGameCore from "../core/FishPCGameCore";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class FishPCOnlineLayer extends BaseLayer {
    //////////////////////////////////////////////////////////////////////////////
    @property(cc.Node)
    panelWinner: cc.Node = null;

    @property(cc.Node)
    panelPlayers: cc.Node = null;

    @property(cc.Node)
    contentWinner: cc.Node = null;

    @property(cc.Node)
    contentPlayers: cc.Node = null;

    @property(cc.Node)
    listWinners: cc.Node = null;

    @property(cc.Prefab)
    winnerItem: cc.Prefab = null;

    @property(cc.Node)
    topPlayer1: cc.Node = null;

    @property(cc.Node)
    topPlayer2: cc.Node = null;

    @property(cc.Node)
    listPlayers: cc.Node = null;

    @property(cc.Label)
    labOnlines: cc.Label = null;

    @property(cc.Label)
    playerPage: cc.Label = null;


    selectTab: number = 0;
    onlineData: any = {};
    winscorerank: any = {};

    maxPageIndex: number = 0;
    curPageIndex: number = 1;
    playerMaxNum: number = 14;
    //是否点击了翻页
    clickPageBtnFlag: boolean = false;

    //游戏core层
    private _gameCore: FishPCGameCore;

    protected onLoad(): void {
        this._gameCore = cc.Canvas.instance.getComponent(FishPCGameCore);
        this.onTabSelect();
        this.topPlayer1.active = false;
        this.topPlayer2.active = false;
        let headItem = this.contentPlayers.getChildByName('Panel_item');
        headItem.active = false;
    }


    onClickTab(target: any, customEventData: any) {
        let clickIndex = Common.toNumber(customEventData);
        if (clickIndex == this.selectTab) {
            return;
        }
        this.selectTab = clickIndex;
        this.onTabSelect();
    }

    //显示当前页数据
    onTabSelect() {
        this.contentWinner.active = this.selectTab == 0;
        this.contentPlayers.active = this.selectTab == 1;
        this.panelWinner.getChildByName('Panel_btn_normal').active = this.selectTab == 1;
        this.panelWinner.getChildByName('Panel_btn_select').active = this.selectTab == 0;
        this.panelPlayers.getChildByName('Panel_btn_normal').active = this.selectTab == 0;
        this.panelPlayers.getChildByName('Panel_btn_select').active = this.selectTab == 1;
    }

    public updateWinners() {
        let winscorerank = this.winscorerank;
        this.listWinners.removeAllChildren();
        for (let key in winscorerank) {
            if (winscorerank[key]) {
                let player = winscorerank[key];
                if (Common.isNull(player.playerid)) {
                    continue;
                }
                let item = cc.instantiate(this.winnerItem);
                this.listWinners.addChild(item);
                item.active = true;
                item.x = 0;

                item.getChildByName('Text_time').getComponent(cc.Label).string = player.time;
                item.getChildByName('Text_rewards').getComponent(cc.Label).string = '₹' + Common.moneyString(player.winscore ?? 0);
                item.getChildByName('Text_bet').getComponent(cc.Label).string = '₹' + Common.moneyString(player.bet ?? 0);
                this.setSpriteFrame(item.getChildByName('Image_type_luck').getChildByName('Image_icon').getComponent(cc.Sprite),
                    'res/textures/fishmain/YXX_icon_' + player.winpos);
                item.getChildByName('Text_luck_num').getComponent(cc.Label).string = "x"+player.winodd;
                item.getChildByName('Text_name').getComponent(cc.Label).string = Common.textClamp(player.name, 10, "...");
                this.setPlayerHead(item.getChildByName('imgMask').getChildByName('headImg').getComponent(cc.Sprite), player.headid, player.wxheadurl ?? "");
            }

        }
    }

    public updatePlayers() {
        if (!this.onlineData) {
            return;
        }
        if (!!this.onlineData.betrank) {
            this.topPlayer1.active = false;
            this.topPlayer2.active = false;
            let topPlayers = [this.topPlayer1, this.topPlayer2];
            for (let key in this.onlineData.betrank) {
                if (Common.toNumber(key) > 2) {
                    break;
                }
                let item = topPlayers[Common.toNumber(key) - 1];
                if (this.onlineData.betrank[key]) {
                    item.active = true;
                    let player = this.onlineData.betrank[key];
                    if (Common.isNull(player.playerid)) {
                        continue;
                    }
                    super.setPlayerHead(item.getChildByName('imgMask').getChildByName('Image_head').getComponent(cc.Sprite), player.headid, player.wxheadurl ?? "");
                    item.getChildByName('Image_name').getChildByName('Text_name').getComponent(cc.Label).string = Common.textClamp(player.name, 8, "...");
                    item.getChildByName('Image_win').getChildByName('Text_win').getComponent(cc.Label).string = player.winnum ?? 0;
                    item.getChildByName('Image_round').getChildByName('Text_round').getComponent(cc.Label).string = Common.moneyString(player.winscore ?? 0);;
                }
            }
        }

        this.listPlayers.removeAllChildren();
        this.listPlayers.stopAllActions();
        let headItem = this.contentPlayers.getChildByName('Panel_item');
        headItem.active = false;
        if (!!this.onlineData.playerlist) {
            for (let key in this.onlineData.playerlist) {
                if (this.onlineData.playerlist[key]) {
                    let player = this.onlineData.playerlist[key];
                    if (Common.isNull(player.playerid)) {
                        continue;
                    }
                    let item = cc.instantiate(headItem);
                    this.listPlayers.addChild(item);
                    item.active = false;
                    item.getChildByName('Image_info').getChildByName('Text_money').getComponent(cc.Label).string = Common.moneyString(player.coin ?? 0);
                    item.getChildByName('Image_info').getChildByName('Text_name').getComponent(cc.Label).string = Common.textClamp(player.name, 9, "...");
                    super.setPlayerHead(item.getChildByName('Image_item_bg').getChildByName('imgMask').getChildByName('Image_head').getComponent(cc.Sprite), player.headid, player.wxheadurl ?? "");
                    cc.tween(item)
                        .delay(0.08 * Common.toInt(key))
                        .call(() => {
                            item && (item.active = true);
                        })
                        .start();
                }
            }
        }

        let totalplayernum = this.onlineData.totalplayernum ?? 0
        this.labOnlines.string = totalplayernum;
        this.maxPageIndex = Math.ceil(totalplayernum / this.playerMaxNum);
        let page2CurPage = this.maxPageIndex > 0 ? this.curPageIndex : 0;
        this.playerPage.string = `${page2CurPage} / ${this.maxPageIndex}`;
    }

    onClickChangePage(target: any, customEventData: any) {
        if (this.clickPageBtnFlag) return;
        let clickIndex = Common.toNumber(customEventData);
        let clickDesc = { prev: 0, next: 1 }; //翻上页 或下页
        if (clickIndex == clickDesc.prev) {
            if (this.curPageIndex - 1 <= 0) {
                return;
            }
            this.curPageIndex -= 1;
        }
        else {
            if (this.curPageIndex + 1 > this.maxPageIndex) {
                return;
            }
            this.curPageIndex += 1;
        }
        this.clickPageBtnFlag = true;
        //--请求玩家列表
        this._gameCore.requestAllPlayerList(this.curPageIndex <= 1 ? 0 : this.curPageIndex); //第一页传0 其它传实际页

    }

    public isShow() {
        return this.node.active;
    }

    public updateView(info) {
        this.node.active = true;
        this.onlineData = info;

        if (info.winscorerank) {
            this.winscorerank = info.winscorerank;
            this.updateWinners();
        }
        if (!this.clickPageBtnFlag) {
            this.curPageIndex = 1;
        }

        this.updatePlayers();
        this.clickPageBtnFlag = false;
    }

    public hide() {
        this.node.active = false;
    }

}
