import { LanguageType } from "../../../../script/frame/common/Define"


// 基础数据
export let Constant = {
	AREA_COUNT: 6,
	POKER_COUNT: 6,
	CHIPS_COUNT: 6,
	FIRE_SCORE: 2000,
}

//协议
export enum YXXCMD {
	SC_YXX_CONFIG_P = 1000,								//发送配置
	SC_YXX_OPTTIME_P,									//通知操作时间
	CS_YXX_BUYHORSE_P,									//请求下注
	SC_YXX_OPER_ERROR_P,								//服务端返回操作错误码
	SC_YXX_BET_SUCCESS_P,								//下注成功
	SC_YXX_BET_SYNC_P,									//筹码增量信息
	SC_YXX_SETTLEMENT_P,								//结算
	CS_YXX_HISTORY_P,									//请求历史信息
	SC_YXX_HISTORY_P,									//返回历史信息 
	CS_YXX_ALLLIST_P,									//请求玩家列表
	SC_YXX_ALLLIST_P,									//返回玩家列表		    
}

//场景状态
export enum EM_YXX_GAMESTATE {
	EM_YXX_GAMESTATE_NONE,								//无状态
	EM_YXX_GAMESTATE_BUYHORSE,							//下注阶段
	EM_YXX_GAMESTATE_COMBINE,							//亮牌阶段	
}
//下注方位
export enum YXXPos {
	emYXXPos_None,										//无效
	emYXXPos_Clubs,										//梅花
	emYXXPos_Crown,										//皇冠
	emYXXPos_Spades,									//黑桃
	emYXXPos_Diamonds,									//方块
	emYXXPos_GuoQi,										//国旗
	emYXXPos_Hearts,									//红桃
	emYXXPos_Max,
}

//游戏错误, 由命令SC_XOCDIA_OPER_ERROR_P返回
export enum YXX_GAME_ERROR {
	EM_GAME_ERROR_NOT_MONEY,							//没有足够的金币
	EM_GAME_ERROR_BUY_LIMIT,							//下注上限
	EM_GAME_ERROR_NOT_ROUND,							//当前不能换庄, 小于最小做庄次数(下庄失败)
	EM_GAME_ERROR_ZHUANG_NO_MONEY,						//上庄金钱不足
	EM_GAME_ERROR_NEXT_ROUND,							//下轮下庄
	EM_GAME_ERROR_OFFZHUANG_WUNIU,						//无牛下庄
	EM_GAME_ERROR_APPLYZHUANG_OK,						//申请上庄成功
	EM_GAME_ERROR_NOT_MONEY_TO_BET,						//金钱不足不能下注
	EM_GAME_ERROR_FOLLOW_TO_BET,						//没有续投的记录
	EM_GAME_ERROR_FOLLOW_LIMIT,							//续投超出房间限制
	EM_GAME_ERROR_FOLLOW_NOT_MONEY,						//续投个人金钱不足
	EM_GAME_ERROR_SLIENTCE_TOMANNY,						//沉默次数太多
	EM_GAME_ERROR_BET_TOOMORE,							//超过最大下注
	EM_GAME_ERROR_STATE_ERROR,							//不在下注阶段
	EM_GAME_ERROR_BUY_POS_ERROR,						//下注区域错误
	EM_GAME_ERROR_BANKER_BET,							//庄家下注
	EM_GAME_ERROR_ILLEGAL_BET,							//非法筹码
	EM_GAME_ERROR_BET_TOOFAST,							//下注太快
}

let GameTextCH = {
	UI_TXT_TITLE_TREND: "TREND",
	UI_TXT_TITLE_RULE: "GAME RULES",
	GOLD_BET_MAX_ERR: "当前下注已达上限",
	GOLD_BET_ERR: "金币不足，下注失败",
	GAME_MAIN_MIN_ERR: "坐庄轮次不足不能下庄",
	GAME_GOLD_BET_MIN_TIP: "金币大于{0}元才可以下注，请充值",
	GAME_BET_XT_ERR: "续投失败,没有投注记录",
	GAME_BET_XT_MIN_ERR: "金币不足，续投失败",
	GAME_EXIT_MIN_JU_TIP: "您已{0}局没有参与游戏，感谢关注",
	GAME_BET_MONEY_MAX_TIP: "下注额已到达上限",
	GAME_BET_ADD_ERR: "下注失败",
	GAME_EXIT_ROOM_TIP: "您已被踢出房间",
	GAME_BET_ING_TIP: "当前正在下注",
	GAME_BET_SCORE_ERR: "下注数据错误：{0}",
	GAME_SERVER_CONFIG_ERR: "获取服务器配置失败",
	GAME_PLEASE_SELECT_GOLD_TIP: "请选择下注金币",
	GAME_BET_MIN_ERR: "剩余金币不足{0}元，无法下注",
	GAME_CITY_NOT_TEXT: "未知",
	GAME_API_RESULT_ERR: "数据加载失败。",
}

let GameTextEnglish = {
	UI_TXT_TITLE_TREND: "TREND", //路图
	UI_TXT_TITLE_RULE: "GAME RULES", //获奖名单
	GOLD_BET_MAX_ERR: "Current bet has reached the upper limit", //当前下注已达上限
	GOLD_BET_ERR: "Insufficient coins, bet failed", //金币不足，下注失败
	GAME_MAIN_MIN_ERR: "Insufficient rounds as banker, unable to step down", //坐庄轮次不足不能下庄
	GAME_GOLD_BET_MIN_TIP: "You need more than {0} coins to place a bet, please recharge", //金币大于{0}才可以下注，请充值
	GAME_BET_XT_ERR: "Failed to continue betting, no betting records found", //续投失败,没有投注记录
	GAME_BET_XT_MIN_ERR: "Insufficient coins, failed to continue betting", //金币不足，续投失败
	GAME_EXIT_MIN_JU_TIP: "You haven't participated in {0} rounds of games, thank you for your attention", //您已{0}局没有参与游戏，感谢关注
	GAME_BET_MONEY_MAX_TIP: "The betting amount has reached the upper limit", //下注额已到达上限
	GAME_BET_ADD_ERR: "Bet failed", //下注失败
	GAME_EXIT_ROOM_TIP: "You have been kicked out of the room", //您已被踢出房间
	GAME_BET_ING_TIP: "Currently placing bets", //当前正在下注
	GAME_BET_SCORE_ERR: "Betting data error: {0}", //下注数据错误：{0}
	GAME_SERVER_CONFIG_ERR: "Failed to retrieve server configuration", //获取服务器配置失败
	GAME_PLEASE_SELECT_GOLD_TIP: "Please select the betting coins", //请选择下注金币
	GAME_BET_MIN_ERR: "Insufficient remaining coins, unable to place a bet of {0}", //剩余金币不足{0}，无法下注
	GAME_CITY_NOT_TEXT: "Unknown", //未知
	GAME_API_RESULT_ERR: "Failed to load data", //数据加载失败
}
let GameTextIndia = {
	UI_TXT_TITLE_TREND: "ट्रेंड",
	UI_TXT_TITLE_RULE: "खेल के नियमों",
	GOLD_BET_MAX_ERR: "वर्तमान बेट ऊपरी सीमा तक पहुंच गई है",
	GOLD_BET_ERR: "सिक्के अपर्याप्त हैं, बेट असफल हुई",
	GAME_MAIN_MIN_ERR: "बैंकर के रूप में पर्याप्त गोलियों की कमी, नीचे कदम नहीं उठाया जा सकता",
	GAME_GOLD_BET_MIN_TIP: "आपको बेट लगाने के लिए {0} सिक्के से अधिक की आवश्यकता है, कृपया पुन: भरें",
	GAME_BET_XT_ERR: "बेटिंग जारी रखने में विफल, कोई बेटिंग रिकॉर्ड नहीं मिला",
	GAME_BET_XT_MIN_ERR: "सिक्के अपर्याप्त हैं, बेटिंग जारी रखने में विफल",
	GAME_EXIT_MIN_JU_TIP: "आपने {0} राउंड खेल में भाग नहीं लिया है, आपकी ध्यान की आभारी है",
	GAME_BET_MONEY_MAX_TIP: "बेटिंग राशि ऊपरी सीमा तक पहुंच गई है",
	GAME_BET_ADD_ERR: "बेटिंग असफल हुई",
	GAME_EXIT_ROOM_TIP: "आपको कमरे से निकाल दिया गया है",
	GAME_BET_ING_TIP: "वर्तमान में बेटिंग हो रही है",
	GAME_BET_SCORE_ERR: "बेटिंग डेटा त्रुटि: {0}",
	GAME_SERVER_CONFIG_ERR: "सर्वर कॉन्फ़िगरेशन प्राप्त करने में विफल",
	GAME_PLEASE_SELECT_GOLD_TIP: "कृपया बेटिंग सिक्के का चयन करें",
	GAME_BET_MIN_ERR: "शेष सिक्के {0} के लिए अपर्याप्त हैं, बेट लगाने के लिए असमर्थ हैं",
	GAME_CITY_NOT_TEXT: "अज्ञात",
	GAME_API_RESULT_ERR: "डेटा लोड करने में विफल",
}
// 文本内容
export let GameTextTips = GameTextEnglish

export let updateGameTextTips = () => {
	let curLanguage = cc.sys.localStorage.getItem('curLanguage') || 1;
	if (curLanguage == LanguageType.CHINESE) {
		GameTextTips = GameTextCH;
	} else if (curLanguage == LanguageType.ENGLISH) {
		GameTextTips = GameTextEnglish;
	} else if (curLanguage == LanguageType.INDIA) {
		GameTextTips = GameTextIndia;
	}
}