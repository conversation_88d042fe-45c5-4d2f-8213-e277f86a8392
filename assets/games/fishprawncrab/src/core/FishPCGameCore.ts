import Common from "../../../../script/frame/common/Common";
import { GameEvent } from "../../../../script/frame/common/Define";
import { TextTips } from "../../../../script/frame/common/Language";
import Config from "../../../../script/frame/config/Config";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import ToastHelper from "../../../../script/frame/extentions/ToastHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import EventManager from "../../../../script/frame/manager/EventManager";
import GameCore from "../../../../script/frame/model/GameCore";
import FishPCGameView from "../view/FishPCGameView";
import { YXXCMD, GameTextTips, updateGameTextTips, EM_YXX_GAMESTATE, YXX_GAME_ERROR } from "./FishPCDefine";

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property } = cc._decorator;
@ccclass
export default class FishPCGameCore extends GameCore {

    private _gameView: FishPCGameView = null; //游戏视图对象 

    //////////////////////////////////////////////////////////////////////////////
    public difen: number = 0; //房间底分
    public roundid: number = 0; // 回合ID
    public betNeed: number = 0; // 下注最小金额
    public betMax: number = 0; //下注最大金额
    public odds = [];//下注赔率
    public gameState: number = 0;//游戏状态
    public isBetMax = false;//是否达到下注上线
    public betMoneyValue = 0;//最小下注数
    public notBetCount = 0;//多少局没下注
    public isStartBet: boolean = false;    /** 是否开始下注 */

    public otherRoom = -1;

    public chipsScore = [10, 50, 100, 1000, 5000, 10000];
    public leftbet = [];

    //////////////////////////////////////////////////////////////////////////////

    onLoad() {
        this._gameView = cc.Canvas.instance.getComponent("FishPCGameView");
        this.updateGameLan();
        super.onLoad();
    }

    start() {
        this.bindGameMessage(YXXCMD.SC_YXX_CONFIG_P, this.onConfigs, this);               //配置
        this.bindGameMessage(YXXCMD.SC_YXX_OPTTIME_P, this.onOperateTime, this);          //下注亮牌操作时间
        this.bindGameMessage(YXXCMD.SC_YXX_SETTLEMENT_P, this.onResult, this);             //游戏结算
        this.bindGameMessage(YXXCMD.SC_YXX_OPER_ERROR_P, this.onError, this);              //错误消息
        this.bindGameMessage(YXXCMD.SC_YXX_HISTORY_P, this.onHistory, this);               //历史记录
        this.bindGameMessage(YXXCMD.SC_YXX_ALLLIST_P, this.onAllPlayerList, this);        //玩家列表
        this.bindGameMessage(YXXCMD.SC_YXX_BET_SUCCESS_P, this.onBetSuccess, this);           //下注成功
        this.bindGameMessage(YXXCMD.SC_YXX_BET_SYNC_P, this.onBetSYNC, this);//筹码增量信息
        EventManager.instance.on(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        super.start()
    }

    exit() {
        this.unbindGameMessage(YXXCMD.SC_YXX_CONFIG_P, this.onConfigs, this);               //配置
        this.unbindGameMessage(YXXCMD.SC_YXX_OPTTIME_P, this.onOperateTime, this);          //下注亮牌操作时间
        this.unbindGameMessage(YXXCMD.SC_YXX_SETTLEMENT_P, this.onResult, this);             //游戏结算
        this.unbindGameMessage(YXXCMD.SC_YXX_OPER_ERROR_P, this.onError, this);              //错误消息
        this.unbindGameMessage(YXXCMD.SC_YXX_HISTORY_P, this.onHistory, this);               //历史记录
        this.unbindGameMessage(YXXCMD.SC_YXX_ALLLIST_P, this.onAllPlayerList, this);        //玩家列表
        this.unbindGameMessage(YXXCMD.SC_YXX_BET_SUCCESS_P, this.onBetSuccess, this); //下注成功
        this.unbindGameMessage(YXXCMD.SC_YXX_BET_SYNC_P, this.onBetSYNC, this);//筹码增量信息
        EventManager.instance.off(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        TextTips["GameTextTips"] = {};
        super.exit();
    }

    /**
     * 配置信息
        send["difen"] = 房间底分
        send["roundid"] = 回合ID
     */
    private onConfigs(info) {
        this.difen = Common.toInt(info["difen"]);
        this.roundid = Common.toInt(info["roundid"]);
        this._gameView.onConfigs();
    }

    /**
     * 下注亮牌操作时间
     * 	send["waittime"] 当前操作剩余时间
        send["roomstate"] 游戏状态
        send["roundid"] = 回合ID
     * @param info 
     */
    private onOperateTime(info) {
        this.gameState = Common.toInt(info["roomstate"]);
        this.roundid = Common.toInt(info["roundid"]);
        let waittime = Common.toInt(info["waittime"]);
        if (this.gameState == EM_YXX_GAMESTATE.EM_YXX_GAMESTATE_BUYHORSE) {
            this._gameView.onStartBet(waittime);
        } else if (this.gameState == EM_YXX_GAMESTATE.EM_YXX_GAMESTATE_COMBINE) {   //亮牌状态
            // this._gameView.onStopBet(waittime);
        }
    }

    /**
     * 下注成功
     * @param info 
     */
    private onBetSuccess(info) {
        this.leftbet = [];
        for (let key in info.leftbet) {
            this.leftbet.push(info.leftbet[key]);
        }
        if (info.chouma) {
            super.updatePlayerMoney({ coin: info.chouma, playerid: info._playerid });
        }
        this._gameView.myBetSuccess(info);
    }

    /**
     * 筹码增量信息
     * 	send["playerlist"] = { 玩家列表,(数组类型)
            player["playerid"] 玩家ID
            player["seatid"]  座位号
            player["betnum"] 各位置下注增量,(数组类型)
            player["chouma"] 玩家金币
        }
        send["roundid"] = 回合ID
        send["allbet"] = 所有人各位置下注总数,(数组类型)
     * @param info 
     * @returns 
     */
    private onBetSYNC(info) {
        // let tabArr: number[] = [0, 4, 8, 12, 16, 20, 24, 28];
        let tabArr: number[] = [0, 4, 8, 12, 16, 20];
        let selectConfig = this.chipsScore;
        let allbet = info.allbet;
        let playerlist = info.playerlist;

        for (let key in playerlist) {
            let val = playerlist[key];
            let playerid = val.playerid;
            let betnum = val.betnum;
            for (let i = 0; i < tabArr.length; i++) {
                for (let area in betnum) {
                    let _rshift = (betnum[area] >> tabArr[i]);
                    let _band = (_rshift & 0x0000000f);
                    if (_band > 0) {
                        this._gameView.updateChipMsg(playerid, _band, selectConfig[i], val, Common.toInt(area), allbet);
                    }
                }

            }
        }

    }

    /**
     * 结算消息
     * 	send["winpos"] = 赢的区域,(数组类型)
        send["whitepoint"] = 白色点数
        send["redpoint"] = 红色点数
        send["roundid"] = 回合ID
        send["zhuang"] = { 庄家数据
            zhuang["playerid"] = 庄家playerID
            zhuang["playercoin"] = 庄家金币
            zhuang["changemoney"] = 输赢金币
        }
        send["other"] = { 玩家列表,(数组类型)
            [玩家ID] = "身上金币,输赢金币"  -- 此数据可参考龙虎
        }
     * @param info 
     */
    private onResult(info, recovery = false) {
        let resultMsg: any = {};
        resultMsg.roundid = info.roundid;
        resultMsg.sposnums = info.posnums;

        resultMsg.others = [];
        if (info.other) {
            for (let key in info.other) {
                let item = info.other[key];
                let tmp = item.split(",");
                let userData: any = {};
                userData.playercoin = parseInt(tmp[0]);
                userData.nChange = parseInt(tmp[1]);
                userData.playerid = key;
                resultMsg.others.push(userData)
            }

        }
        resultMsg.posnums = [];
        resultMsg.siceresult = [];
        if (info.posnums) {
            for (let key in info.posnums) {
                let num = info.posnums[key]
                resultMsg.posnums.push(num)
                if (num > 0) {
                    for (let i = 0; i < num; i++) {
                        resultMsg.siceresult.push(Common.toInt(key));
                    }
                }
            }

        }
        if (!recovery) {
            this._gameView.onResult(resultMsg);
        } else {
            this._gameView.recoveryResult(resultMsg);
        }

    }

    /**
     * 错误提示消息
     * 	send["code"] = 错误代码, 参考EM_XOCDIA_GAME_ERROR
        send["value"] = 错误参数,默认为0, 当code=EM_GAME_ERROR_NOT_MONEY_TO_BET时， 返回的是最小下注数
        send["roundid"] = 回合ID
    * @param info 
    */
    private onError(info) {
        let code = info.code;
        if (code == YXX_GAME_ERROR.EM_GAME_ERROR_NOT_MONEY) {
            ToastHelper.show(GameTextTips.GOLD_BET_ERR);
        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_BUY_LIMIT) {
            ToastHelper.show(GameTextTips.GOLD_BET_MAX_ERR);
            this.isBetMax = true;
        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_NOT_ROUND) {
            ToastHelper.show(GameTextTips.GAME_MAIN_MIN_ERR);
        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_ZHUANG_NO_MONEY) {

        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_NEXT_ROUND) {

        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_OFFZHUANG_WUNIU) {
            ToastHelper.show(GameTextTips.GOLD_BET_MAX_ERR);
            this.isBetMax = true;

        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_APPLYZHUANG_OK) {

        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_NOT_MONEY_TO_BET) {
            this.betMoneyValue = info.value;
            let moneyTxt = Common.moneyString(info.value, 6, 0);
            ToastHelper.show(Common.stringFormat(GameTextTips.GAME_GOLD_BET_MIN_TIP, [moneyTxt]));
        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_FOLLOW_TO_BET) {
            ToastHelper.show(GameTextTips.GAME_BET_XT_ERR);
        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_FOLLOW_LIMIT) {
            ToastHelper.show(GameTextTips.GAME_BET_XT_MIN_ERR);
        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_SLIENTCE_TOMANNY) {      //10局没下注踢出房间
            this.notBetCount = info.value;
        } else if (code == YXX_GAME_ERROR.EM_GAME_ERROR_BET_TOOMORE) {
            ToastHelper.show(GameTextTips.GAME_BET_MONEY_MAX_TIP);
            this.isBetMax = true;
        } else if
            (
            code == YXX_GAME_ERROR.EM_GAME_ERROR_STATE_ERROR
            || code == YXX_GAME_ERROR.EM_GAME_ERROR_BUY_POS_ERROR
            || code == YXX_GAME_ERROR.EM_GAME_ERROR_BANKER_BET
            || code == YXX_GAME_ERROR.EM_GAME_ERROR_ILLEGAL_BET
            || code == YXX_GAME_ERROR.EM_GAME_ERROR_BET_TOOFAST

        ) {
            ToastHelper.show(GameTextTips.GAME_BET_ADD_ERR);
        }
    }

    // 进入房间，房间信息
    onRoomInfo(info) {
        super.onRoomInfo(info)
        this._gameView.onSaveUserInfo(info.playerlist);
    }

    // 玩家加入
    onPlayerEnter(info) {
        super.onPlayerEnter(info);
        this._gameView.onUserEnter(info);
    }

    // 玩家离开
    onPlayerQuit(info) {
        this._gameView.onUserLeave(info);
    }

    //离开游戏
    onQuitGame(info) {
        let self = this;
        if (this.notBetCount) {
            AlertHelper.show(Common.stringFormat(GameTextTips.GAME_EXIT_MIN_JU_TIP, [this.notBetCount])
                , function () {
                    self.quitGame();
                }, true);
        } else {
            UIHelper.clearAll();
            super.onQuitGame(info);
        }
    }

    // 断线重连
    onToOtherRoom(info) {
        super.onToOtherRoom(info);
        this.otherRoom = 1;

        this.difen = Common.toInt(info["difen"]);
        this.roundid = Common.toInt(info["roundid"]);
        this.gameState = Common.toInt(info["state"]);
        let waittime = Common.toInt(info["waittime"]);
        let allbet = info["allbet"];
        let playerlist = info["playerlist"];
        let mybet = info["mybet"];
        let leftbet = info["leftbet"];
        //config
        let config = info["config"];
        this.betMax = Common.toInt(config["betmax"]);
        this.betNeed = Common.toInt(config["betneed"]);
        if (config.bet) {
            this.chipsScore = [];
            for (var key in config.bet) {
                if (config.bet[key]) {
                    this.chipsScore.push(Common.toInt(config.bet[key] / Config.SCORE_RATE));
                }
            }
        }
        if (config.odds) {
            this.odds = [];
            for (var key in config.odds) {
                this.odds.push(Common.toInt(config.odds[key]));
            }
        }

        this._gameView.onConfigs();

        //下注信息
        if (allbet) {
            let alltabbet = [];
            for (let key in allbet) {
                alltabbet.push(allbet[key]);
            }
            for (let i = alltabbet.length - 1; i >= 0; i--) {
                this._gameView.setBetAreaTotal(i, alltabbet[i]);

                if (info.state == EM_YXX_GAMESTATE.EM_YXX_GAMESTATE_BUYHORSE) {
                    this._gameView.restoreDeskChips(i, alltabbet[i]);
                }
            }
        }
        if (mybet) {
            for (let betkey in mybet) {
                let area = Common.toInt(betkey) - 1;
                this._gameView.setSelfBetArea(area, mybet[betkey], this.getMeMoney());
            }
        }
        if (leftbet) {
            this.leftbet = [];
            for (let key in leftbet) {
                this.leftbet.push(leftbet[key]);
            }
        }

        if (playerlist) {
            for (let key in playerlist) {
                let player = playerlist[key];
                this._gameView.updateUserScore(player);
                if (player["playerid"] == this.playerid) {
                    this.userInfo.money = player.coin;
                }
            }
            // this.updatePlayerMoney({ playerid: this.playerid, coin: this.userInfo.money });
            this._gameView.updateMyScore();
            this._gameView.updatePlayerScore();
        }

        if (info.state == EM_YXX_GAMESTATE.EM_YXX_GAMESTATE_BUYHORSE) {
            this._gameView.onStartBet(waittime);
        } else if (info.state == EM_YXX_GAMESTATE.EM_YXX_GAMESTATE_COMBINE) {
            this.onResult(info, true);
            this._gameView.showTipWait(waittime);
        }

        //历史
        if (info.data) {
            this._gameView.updateTrendData(info.data);
        }

    }

    // 房间状态
    onRoomState(info) {
    }

    // 更新玩家金币
    updatePlayerMoney(info) {
        let myPlayerid = this.playerid;
        if (info.playerid == myPlayerid) {
            if (this.otherRoom != 1) {
                this.userInfo.money = info.coin;
                super.updatePlayerMoney(info);
            }
        }
        this._gameView.updateUserScore(info);
        this.otherRoom = 0;
    }

    // 走势图返回
    onHistory(info) {
        if (info["data"]) {
            this._gameView.updateTrendData(info.data);
        }
    }

    // 玩家列表(请求下发)
    onAllPlayerList(info) {
        this._gameView.onUpdateUserData(info);
        this._gameView.initPlayerListData(info);
    }

    // 聊天信息
    public onRoomChat(info: any) {
        let sendPlayerid = Common.toInt(info["sendPlayerid"]);
        let receiverPlayerid = Common.toInt(info["receiverPlayerid"]);
        let type = info["type"];
        let content = info["content"];

        if (!receiverPlayerid) {
            this._gameView.doRoomChat(sendPlayerid, type, content);
        } else {
            this._gameView.doRoomIntertChat(sendPlayerid, receiverPlayerid, content);
        }

    }

    public onRoomTipDealer(info: any) {
        this._gameView.doTipDealer(Common.toInt(info['playerid']));
    }
    /////////////////////////////////发送协议//////////////////////////////////// 
    /**
     * 下注
     * @param area 
     * @param money 
     */
    requestBet(area: number, chipIndex: number) {
        let info = {};
        info["bet"] = this.chipsScore[chipIndex] * Config.SCORE_RATE;
        info["direction"] = area;
        super.sendGameMessage(YXXCMD.CS_YXX_BUYHORSE_P, info);
    }


    // 请求玩家列表
    requestAllPlayerList(page = 0) {
        let info = { page: page }
        super.sendGameMessage(YXXCMD.CS_YXX_ALLLIST_P, info)
    }

    //请求历史记录
    requestHistory() {
        super.sendGameMessage(YXXCMD.CS_YXX_HISTORY_P);
    }


    //获取自己的钱
    public getMeMoney() {
        return this.userInfo.money;
    }

    private updateGameLan() {
        updateGameTextTips();
        TextTips["GameTextTips"] = GameTextTips;
    }
}
