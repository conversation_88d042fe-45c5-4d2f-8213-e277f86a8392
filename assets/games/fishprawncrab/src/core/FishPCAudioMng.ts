const { ccclass, property } = cc._decorator;
import Common from "../../../../script/frame/common/Common";
import AudioHelper from "../../../../script/frame/extentions/AudioHelper";
@ccclass
export default class FishPCAudioMng extends cc.Component {

    private _bettime=0;

    playMusic() {
        let path = "res/sound/bg";
        AudioHelper.instance.playMusic(path, true);

    }
    pauseMusic() {
        AudioHelper.instance.pauseMusic();
    }

    resumeMusic() {
        AudioHelper.instance.resumeMusic();
    }

    _loadPlaySFX(path) {
        AudioHelper.instance.playEffect("res/sound/" + path);
    }

    //开始下注
    playStart() {
        this._loadPlaySFX("betbegin");
    }

    //倒计时
    playCountDown() {
        this._loadPlaySFX("timeDown");
    }

    //停止下注
    playStopBet() {
        this._loadPlaySFX("betend");
    }

    playDice() {
        this._loadPlaySFX("dice");
    }

    //下注
    playBet() {
        let curtime=Common.getCTime();
        let offset=curtime-this._bettime;
        if(offset>200){
            this._bettime=curtime;
            this._loadPlaySFX("placeabet");
        }
    }

    playDealcards() {
        this._loadPlaySFX("dealcards");
    }

    //赢
    playWin() {
        this._loadPlaySFX("winner");
    }

}

