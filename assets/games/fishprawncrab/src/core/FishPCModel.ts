namespace FishPCModel {
   /**
    * 筹码数据
    */
   export class ChipConfig {
      /* 筹码对应的分数 */
      public valueGold: number;
      /** 筹码对应的值 1K */
      public valName: string;
      /** 筹码背景key */
      public bgKey: string;
      /** 筹码选中背景 */
      public bgSelectKey: string;
   }
   class basePlayer {
      /** 信息节点 */
      public node: cc.Node;
      /** 用户头像 */
      public head: cc.Node;
      /** 用户头像框 */
      public headFrame: cc.Node;
      
      public seatNode: cc.Node;
      public headBg: cc.Node;
      public panelHead: cc.Node;
      public chatNode: cc.Node;
      public chatlab: cc.Label;
      public expNode: cc.Node;
      public scoreBg: cc.Node;
      public score: cc.Label;
      public nodeFire: cc.Node;
      /** 用户id */
      public playerid: number;
      /** 用户昵称 */
      public nickName: cc.Label;
      /** 结果 */
      public result: cc.Label;
      /** 结果位置 */
      public resultPos: cc.Vec2;
      /** 头像是否移动中 */
      public isMove: boolean;
      /** 是否打开userinfo */
      public isOpenUserInfo: boolean;
      /** userinfo */
      public userInfo: any;

      public betscore: any=[0,0,0,0,0,0];
   }
   /**
    * 我的信息数据
    */
   export class MyInfo extends basePlayer {
      /** 用户金币值 */
      public money: number;
      /** 用户是否下注 */
      public isBet: boolean;
   }
   /**
    * 坐位玩家数据
    */
   export class Player extends basePlayer {
      /** 用户头像图标 （土豪 神算子） */
      public headIcon: cc.Node;
      /** 坐位没有玩家节点 */
      public imgNull: cc.Node;
      /** 椅子id */
      public chairId: number;
      /** 椅子左边还是右边 0:left 1:right*/
      public dirction: number;
   }

   /**
    * 桌面数据
    */
   export class DesktopBet {
      /** 桌面节点 */
      public node: cc.Node;
      /** 神算子下注标识 */
      public starLucky: cc.Node;
      /** 总下注金额 */
      public totaLabel: cc.Label;
      /** 当前下注金额 */
      public selfLabel: cc.Label;
      /** 筹码节点 */
      public chipArea: cc.Node;
      /** 区域赢的背景 */
      public winBg: cc.Node;
      public selfBetBg: cc.Node;
      public trendBg: cc.Node;
      /** 总下注金额 */
      public total: number;
      /** 自己下注金额 */
      public self: number;
   }
}
export default FishPCModel;
