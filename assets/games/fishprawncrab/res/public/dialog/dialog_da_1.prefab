[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_rawFiles": null, "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 23}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_bg_", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}, {"__id__": 17}], "_tag": -1, "_active": true, "_components": [{"__id__": 21}], "_prefab": {"__id__": 22}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 524}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_title_bg_", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_tag": -1, "_active": true, "_components": [{"__id__": 8}, {"__id__": 9}], "_prefab": {"__id__": 10}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 96}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 258.0176, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Text_title", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 5}, {"__id__": 6}], "_prefab": {"__id__": 7}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 135, "height": 54}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 40, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0.4325, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "_useOriginalSize": true, "_actualFontSize": 40, "_fontSize": 54, "_lineHeight": 0, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_N$string": "TREND", "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "60xQH3C2tFAaCo7+WOFDwH", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 44, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0.9924, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1000, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_spriteFrame": {"__uuid__": "a9f3fd93-64c4-47e5-bb78-f1ffd6e4da56"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74v8EV/DVL1r8BUEK1+k7e", "sync": false}, {"__type__": "cc.Node", "_name": "Panel_close", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 12}], "_tag": -1, "_active": true, "_components": [], "_prefab": {"__id__": 16}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [406.5303, 303.13, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Image_close", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 13}, {"__id__": 14}], "_prefab": {"__id__": 15}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [44, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 12, "_left": 0.31820000000000004, "_right": 0, "_top": 0, "_bottom": 0.31820000000000004, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_spriteFrame": {"__uuid__": "d9d52698-fb9c-4da9-8196-eb7ca2f73b21"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99BMB5FF1C3bh8Su38gLQz", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "00zFMKHaNGUajg4fnDsLTg", "sync": false}, {"__type__": "cc.Node", "_name": "Image_content_bg_", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_tag": -1, "_active": true, "_components": [{"__id__": 18}, {"__id__": 19}], "_prefab": {"__id__": 20}, "_id": "", "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 970, "height": 492}, "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_opacityModifyRGB": false, "groupIndex": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1, 3.999900000000025, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "isAlignOnce": true, "_target": null, "_alignFlags": 8, "_left": 0.016000000000000014, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_spriteFrame": {"__uuid__": "b30451d1-aacd-4321-8346-481cfa78825c"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91HWg+GIdO+pF5pX6IPBP6", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_spriteFrame": {"__uuid__": "187cba0f-d786-4338-8ecd-42ba58b9a882"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_atlas": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b5FZGYcoVH5IZ7N+ijGcdz", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6aFycdqWhMJ7dlz6CaOUin", "sync": false}]