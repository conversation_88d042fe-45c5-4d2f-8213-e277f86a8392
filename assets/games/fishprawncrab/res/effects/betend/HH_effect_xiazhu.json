{"skeleton": {"hash": "zfIJwVS2s8/CQxEfnsSkpEoKrVs", "spine": "3.7.93", "width": 1560, "height": 720, "images": "./00/", "audio": ""}, "bones": [{"name": "root", "x": -0.26}, {"name": "HH_bg_bet", "parent": "root", "x": 0.05, "y": -61.67}, {"name": "BetStops", "parent": "root", "x": -0.18, "y": -57.55}, {"name": "xian", "parent": "root", "rotation": 90, "x": -124.05, "y": -18.09, "scaleX": 1.407, "scaleY": 1.407}, {"name": "xian2", "parent": "root", "rotation": 90, "x": 126.58, "y": -109.68, "scaleX": 1.407, "scaleY": 1.407}, {"name": "YL_bg_up", "parent": "root", "length": 69.16, "rotation": -120.79, "x": 28.08, "y": 88.37}, {"name": "YL_gan", "parent": "YL_bg_up", "length": 57.04, "rotation": 0.29, "x": 55.16, "y": 0.79}, {"name": "BetStops2", "parent": "BetStops", "x": 4.7, "y": -0.37}, {"name": "quan", "parent": "root", "x": -12.71, "y": 6.77}, {"name": "quan2", "parent": "root", "x": -12.71, "y": 6.77}], "slots": [{"name": "红黑", "bone": "root"}, {"name": "bet_end", "bone": "root"}, {"name": "YL_bg_d", "bone": "YL_bg_up", "attachment": "YL_bg_d"}, {"name": "YL_gan", "bone": "YL_gan", "attachment": "YL_gan"}, {"name": "YL_ling", "bone": "YL_gan", "attachment": "YL_ling"}, {"name": "YL_bg_up", "bone": "YL_bg_up", "attachment": "YL_bg_up"}, {"name": "quan", "bone": "quan", "attachment": "quan", "blend": "additive"}, {"name": "quan2", "bone": "quan2", "attachment": "quan", "blend": "additive"}, {"name": "HH_bg_bet", "bone": "HH_bg_bet", "attachment": "HH_bg_bet"}, {"name": "BetStops2", "bone": "BetStops", "attachment": "BetBegins"}, {"name": "BetStops", "bone": "BetStops2", "attachment": "BetBegins", "blend": "additive"}, {"name": "xian", "bone": "xian", "attachment": "xian", "blend": "additive"}, {"name": "xian2", "bone": "xian2", "attachment": "xian", "blend": "additive"}], "skins": {"default": {"BetStops": {"BetBegins": {"x": -4.75, "y": -7.61, "width": 348, "height": 76}, "BetStops": {"x": -3.94, "y": -7.48, "width": 313, "height": 75}}, "BetStops2": {"BetBegins": {"x": -0.05, "y": -7.98, "width": 348, "height": 76}, "BetStops": {"x": 0.76, "y": -7.85, "width": 313, "height": 75}}, "HH_bg_bet": {"HH_bg_bet": {"x": 0.26, "y": -1.82, "width": 299, "height": 96}}, "YL_bg_d": {"YL_bg_d": {"x": 101.52, "y": 2.09, "rotation": 90, "width": 130, "height": 54}}, "YL_bg_up": {"YL_bg_up": {"x": 46.41, "y": 1.97, "rotation": 90, "width": 130, "height": 136}}, "YL_gan": {"YL_gan": {"x": 30.91, "y": -0.53, "rotation": 89.71, "width": 10, "height": 30}}, "YL_ling": {"YL_ling": {"x": 59.2, "y": 1.87, "rotation": 89.71, "width": 30, "height": 30}}, "quan": {"quan": {"width": 77, "height": 77}}, "quan2": {"quan": {"width": 77, "height": 77}}, "xian": {"xian": {"x": 2.59, "y": -3.13, "width": 13, "height": 89}}, "xian2": {"xian": {"x": 2.59, "y": -3.13, "width": 13, "height": 89}}}}, "animations": {"end": {"slots": {"BetStops": {"color": [{"time": 0.1333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "BetStops"}]}, "BetStops2": {"color": [{"time": 0.1333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "BetStops"}]}, "HH_bg_bet": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00"}]}, "YL_bg_d": {"attachment": [{"time": 0, "name": null}]}, "YL_bg_up": {"attachment": [{"time": 0, "name": null}]}, "YL_gan": {"attachment": [{"time": 0, "name": null}]}, "YL_ling": {"attachment": [{"time": 0, "name": null}]}, "quan": {"attachment": [{"time": 0, "name": null}]}, "quan2": {"attachment": [{"time": 0, "name": null}]}, "xian": {"color": [{"time": 0.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2667, "name": "xian"}]}, "xian2": {"color": [{"time": 0.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2667, "name": "xian"}]}}, "bones": {"BetStops": {"scale": [{"time": 0.1333, "x": 0.814, "y": 0.814, "curve": [0, 0.18, 1, 0.72]}, {"time": 0.2667, "x": 1.161, "y": 1.161}, {"time": 0.3667, "x": 0.929, "y": 0.929}, {"time": 0.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1}, {"time": 1.1333, "x": 1.112, "y": 1.112}, {"time": 1.2, "x": 0.478, "y": 0.478}]}, "HH_bg_bet": {"scale": [{"time": 0, "x": 0.359, "y": 0.359}, {"time": 0.1, "x": 1.617, "y": 0.591}, {"time": 0.2, "x": 0.955, "y": 1.271}, {"time": 0.2667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0667, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.617, "y": 1}, {"time": 1.2333, "x": 0.331, "y": 0.441}]}, "xian": {"translate": [{"time": 0.2667, "x": 0, "y": 0, "curve": [0.249, 0, 0.627, 0.51]}, {"time": 0.5333, "x": 125.5, "y": 0, "curve": [0.376, 0.51, 0.749, 1]}, {"time": 1, "x": 240.75, "y": 0}], "scale": [{"time": 0.2667, "x": 0.472, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 0.477, "y": 1.835, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.472, "y": 1}]}, "xian2": {"translate": [{"time": 0.2667, "x": 0, "y": 0, "curve": [0.249, 0, 0.627, 0.51]}, {"time": 0.5333, "x": -131.87, "y": 0, "curve": [0.376, 0.51, 0.749, 1]}, {"time": 1, "x": -252.97, "y": 0}], "scale": [{"time": 0.2667, "x": 0.472, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 0.477, "y": 1.835, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.472, "y": 1}]}}}, "start": {"slots": {"BetStops": {"color": [{"time": 0.1333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "BetBegins"}]}, "BetStops2": {"color": [{"time": 0.1333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "BetBegins"}]}, "HH_bg_bet": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00"}]}, "YL_bg_d": {"color": [{"time": 0.0333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.0333, "name": "YL_bg_d"}]}, "YL_bg_up": {"color": [{"time": 0.0333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.0333, "name": "YL_bg_up"}]}, "YL_gan": {"color": [{"time": 0.0333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.0333, "name": "YL_gan"}]}, "YL_ling": {"color": [{"time": 0.0333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.0333, "name": "YL_ling"}]}, "quan": {"color": [{"time": 0.4333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.4333, "name": "quan"}]}, "quan2": {"color": [{"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": "quan"}]}, "xian": {"color": [{"time": 0.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2667, "name": "xian"}]}, "xian2": {"color": [{"time": 0.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2667, "name": "xian"}]}}, "bones": {"BetStops": {"scale": [{"time": 0.1333, "x": 2.214, "y": 2.214, "curve": [0, 0.18, 1, 0.72]}, {"time": 0.2667, "x": 0.806, "y": 0.806}, {"time": 0.3667, "x": 1.062, "y": 1.062}, {"time": 0.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1}, {"time": 1.1333, "x": 1.112, "y": 1.112}, {"time": 1.2, "x": 0.478, "y": 0.478}]}, "HH_bg_bet": {"scale": [{"time": 0, "x": 0.359, "y": 0.359}, {"time": 0.1, "x": 1.617, "y": 0.591}, {"time": 0.2, "x": 0.955, "y": 1.271}, {"time": 0.2667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0667, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.617, "y": 1}, {"time": 1.2333, "x": 0.331, "y": 0.441}]}, "xian": {"translate": [{"time": 0.2667, "x": 0, "y": 0, "curve": [0.249, 0, 0.627, 0.51]}, {"time": 0.5333, "x": 125.5, "y": 0, "curve": [0.376, 0.51, 0.749, 1]}, {"time": 1, "x": 240.75, "y": 0}], "scale": [{"time": 0.2667, "x": 0.472, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 0.477, "y": 1.835, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.472, "y": 1}]}, "xian2": {"translate": [{"time": 0.2667, "x": 0, "y": 0, "curve": [0.249, 0, 0.627, 0.51]}, {"time": 0.5333, "x": -131.87, "y": 0, "curve": [0.376, 0.51, 0.749, 1]}, {"time": 1, "x": -252.97, "y": 0}], "scale": [{"time": 0.2667, "x": 0.472, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 0.477, "y": 1.835, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.472, "y": 1}]}, "YL_bg_up": {"rotate": [{"time": 0.0333, "angle": 25.5}, {"time": 0.1667, "angle": -6.93}, {"time": 0.2667, "angle": 6.24}, {"time": 0.4, "angle": -4.89}, {"time": 0.5, "angle": 12.68}, {"time": 0.7, "angle": -5.1}, {"time": 0.8, "angle": 12.68}, {"time": 0.9667, "angle": -5.1}, {"time": 1.0333, "angle": -11.88}, {"time": 1.1667, "angle": 7.03}], "translate": [{"time": 0.0333, "x": 0, "y": -92.87}, {"time": 0.1667, "x": 0, "y": 3.95}, {"time": 0.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}, {"time": 0.5, "x": 1.79, "y": 0}, {"time": 0.7, "x": -2.74, "y": 0}, {"time": 0.8, "x": 1.79, "y": 0}, {"time": 0.9667, "x": -2.74, "y": 0}, {"time": 1.0333, "x": 0, "y": 7.95}, {"time": 1.1667, "x": 0, "y": -59.07}], "scale": [{"time": 0.0333, "x": 0.417, "y": 0.417}, {"time": 0.1667, "x": 1.099, "y": 1.099}, {"time": 0.2667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1.0333, "x": 1.114, "y": 1.114}, {"time": 1.1667, "x": 0.673, "y": 0.673}]}, "YL_gan": {"rotate": [{"time": 0.0667, "angle": 7.27}, {"time": 0.2, "angle": -12.33}, {"time": 0.3, "angle": 10.67}, {"time": 0.4333, "angle": -19.21}, {"time": 0.5333, "angle": 33.51}, {"time": 0.7333, "angle": -32.95}, {"time": 0.8333, "angle": 33.51}, {"time": 1.0333, "angle": -32.95}, {"time": 1.1667, "angle": 5.61}]}, "BetStops2": {"scale": [{"time": 0.2667, "x": 1, "y": 1}, {"time": 0.8333, "x": 1.1, "y": 1.1}]}, "quan": {"scale": [{"time": 0.4333, "x": 1, "y": 1}, {"time": 0.5667, "x": 3.051, "y": 3.051}, {"time": 0.8, "x": 4.077, "y": 4.077}]}, "quan2": {"scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 0.8, "x": 3.051, "y": 3.051}, {"time": 1.0333, "x": 4.077, "y": 4.077}]}}}}}