<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>angle</key>
    <integer>0</integer>
    <key>angleVariance</key>
    <integer>0</integer>
    <key>blendFuncDestination</key>
    <integer>4</integer>
    <key>blendFuncSource</key>
    <integer>2</integer>
    <key>duration</key>
    <integer>2</integer>
    <key>emitterType</key>
    <integer>0</integer>
    <key>finishColorAlpha</key>
    <integer>0</integer>
    <key>finishColorBlue</key>
    <integer>1</integer>
    <key>finishColorGreen</key>
    <integer>1</integer>
    <key>finishColorRed</key>
    <integer>1</integer>
    <key>finishColorVarianceAlpha</key>
    <integer>0</integer>
    <key>finishColorVarianceBlue</key>
    <integer>0</integer>
    <key>finishColorVarianceGreen</key>
    <integer>0</integer>
    <key>finishColorVarianceRed</key>
    <integer>0</integer>
    <key>finishParticleSize</key>
    <integer>30</integer>
    <key>finishParticleSizeVariance</key>
    <real>50.1</real>
    <key>gravityx</key>
    <integer>0</integer>
    <key>gravityy</key>
    <integer>0</integer>
    <key>maxParticles</key>
    <integer>30</integer>
    <key>maxRadius</key>
    <integer>0</integer>
    <key>maxRadiusVariance</key>
    <integer>0</integer>
    <key>minRadius</key>
    <integer>0</integer>
    <key>particleLifespan</key>
    <integer>1</integer>
    <key>particleLifespanVariance</key>
    <integer>1</integer>
    <key>radialAccelVariance</key>
    <integer>0</integer>
    <key>radialAcceleration</key>
    <integer>0</integer>
    <key>rotatePerSecond</key>
    <integer>0</integer>
    <key>rotatePerSecondVariance</key>
    <integer>0</integer>
    <key>rotationEnd</key>
    <integer>0</integer>
    <key>rotationEndVariance</key>
    <integer>90</integer>
    <key>rotationStart</key>
    <integer>0</integer>
    <key>rotationStartVariance</key>
    <integer>90</integer>
    <key>sourcePositionVariancex</key>
    <integer>0</integer>
    <key>sourcePositionVariancey</key>
    <integer>0</integer>
    <key>sourcePositionx</key>
    <real>373.7277526855469</real>
    <key>sourcePositiony</key>
    <real>478.40472412109375</real>
    <key>speed</key>
    <integer>10</integer>
    <key>speedVariance</key>
    <integer>20</integer>
    <key>startColorAlpha</key>
    <integer>1</integer>
    <key>startColorBlue</key>
    <integer>1</integer>
    <key>startColorGreen</key>
    <integer>1</integer>
    <key>startColorRed</key>
    <integer>1</integer>
    <key>startColorVarianceAlpha</key>
    <integer>0</integer>
    <key>startColorVarianceBlue</key>
    <integer>0</integer>
    <key>startColorVarianceGreen</key>
    <integer>0</integer>
    <key>startColorVarianceRed</key>
    <integer>0</integer>
    <key>startParticleSize</key>
    <integer>10</integer>
    <key>startParticleSizeVariance</key>
    <integer>30</integer>
    <key>tangentialAccelVariance</key>
    <integer>2</integer>
    <key>tangentialAcceleration</key>
    <integer>0</integer>
    <key>positionType</key>
    <integer>0</integer>
    <key>rotationIsDir</key>
    <false/>
    <key>minRadiusVariance</key>
    <integer>0</integer>
    <key>emissionRate</key>
    <integer>20</integer>
    <key>textureFileName</key>
    <string>car_star_fly_5.png</string>
  </dict>
</plist>