import Common from "../common/Common";
import PhoenixWebSocketClient from "../network/PhoenixWebSocket";
import { GameEvent, LanguageType, NetStatus } from "../common/Define";
import { MainProto, RegLogin } from "../common/Protocol";
import { TextServerKey, TextServerTips, checkServerLanguage } from "../common/LanguageServer";
import ToastHelper from "../extentions/ToastHelper";
import DataManager from "../manager/DataManager";
import HeartbeatConfig from "../config/HeartbeatConfig";

//////////////////////////////////////////////////////////////////////////////////
// 网络服务类
export default class NetService {
    //////////////////////////////////////////////////////////////////////////////
    // 心跳开关配置已移至HeartbeatConfig统一管理

    // 网络协议关联事件对象
    private _eventTarget: cc.EventTarget = new cc.EventTarget();
    // 可连接的服务器列表
    private _connServerList: Array<string> = [];
    // WebSocket客户端对象
    private _wsClient: PhoenixWebSocketClient = null;
    // 发送心跳包的间隔时间(毫秒)
    private _heartbeatInterval: number = 5000;
    // 心跳包发送定时器
    private _sendHeartbeatTimer: number = -1;
    // 当前服务器列表索引
    private _currServerIndex: number = 0;
    // 上次连接成功时的索引
    private _lastServerIndex: number = 0;
    // 是否连接成功后发送数据包
    private _enableConnedSend: boolean = false;
    // 当前待发送的数据包
    private _waitSendData: any = null;
    // 当前连接的URL
    private _connUrl: string = "";

    // 重连间隔时间(毫秒)
    private _reconnetTimeOut: number = 3000;
    private _reconnetTimer: number = -1;
    //////////////////////////////////////////////////////////////////////////////
    // 添加服务器
    public addServer(serverUrl: any) {
        this._connServerList.push(serverUrl);
    }

    // 添加服务器列表
    public addServerList(serverList: any[]) {
        for (let key in serverList) {
            this._connServerList.push(serverList[key]);
        }
    }

    // 清除服务器列表
    public clearServerList() {
        this._connServerList = [];
        this._currServerIndex = 0;
        this._lastServerIndex = 0;
    }

    // 当前连接的服务器IP
    public get serverIp(): string {
        let reg = "^(wss?)://([^:/]+):([0-9]+)(.*)";
        let results = this._connUrl.match(reg);
        if (results && results.length >= 2) {
            return results[2];
        }
        return "";
    }

    // 当前连接的服务器索引
    public get currServerIndex(): number {
        return this._currServerIndex;
    }


    // 是否连接成功后发送数据包
    public set enableConnedSend(value: boolean) {
        this._enableConnedSend = value;
    }

    // 开始连接服务器
    public connect() {
        console.log("尝试建立长连接", this._connUrl)

        if (this._connServerList.length <= 0) {
            console.log("连接失败: 服务器列表为空");
            this._eventTarget.emit(GameEvent.NETWORK_STATUS_NOTICE, NetStatus.FAILED);
            return;
        }

        // 已经连接时通知连接成功事件
        if (this._wsClient && this._wsClient.isConnected()) {
            console.log("已连接到服务器，无需重新连接");
            this._eventTarget.emit(GameEvent.NETWORK_STATUS_NOTICE, NetStatus.CONNECTED);
            return;
        }        // 通知正在连接事件
        this._eventTarget.emit(GameEvent.NETWORK_STATUS_NOTICE, NetStatus.CONNECTING);

        // 创建WS网络对象
        this._wsClient = new PhoenixWebSocketClient(this);

        // 开始连接服务器

        // this._connUrl =  "ws://************:11004/"
        // this._connUrl = "ws://*************:10001/"

        let pemfile = ''
        this._connUrl = this._connServerList[this._currServerIndex];
        if (this._connUrl.startsWith('wss') && cc.sys.isNative && (cc.sys.os == cc.sys.OS_ANDROID || cc.sys.os == cc.sys.OS_WINDOWS)) {
            let cacert = cc.url.raw('resources/common/pem/cacert.pem');
            if (cc.loader.md5Pipe) {
                pemfile = cc.loader.md5Pipe.transformURL(cacert)
            }
        }
        console.log("开始连接服务器:", this._connUrl, "当前索引:", this._currServerIndex);
        this._wsClient.connect(this._connUrl, pemfile);
    }

    // 是否已连接?
    public isConnected(): boolean {
        if (!this._wsClient) {
            return false;
        }
        return this._wsClient.isConnected();
    }
    // 是否已连接?
    public isConnecting(): boolean {
        if (!this._wsClient) {
            return false;
        }
        return this._wsClient.isConnecting();
    }
    // 检测是否为心跳包
    public isHeartbeat(dataobj: any) {
        if (dataobj.mainId == MainProto.RegLogin && dataobj.subId == RegLogin.CS_HEART_CHECK_P) {
            return true;
        }
        return false;
    }

    // 发送心跳数据包
    public sendHeartbeat() {
        this.send(MainProto.RegLogin, RegLogin.CS_HEART_CHECK_P);
    }

    // 连接到游戏房间Channel
    public connectToGameRoom(gameId: number, serverId: number): Promise<boolean> {
        cc.log(`🎮 [NET_SERVICE] 尝试连接游戏房间: GameID=${gameId}, ServerID=${serverId}`);
        cc.log(`🎮 [NET_SERVICE] WebSocket客户端状态: client=${!!this._wsClient}, hasMethod=${!!(this._wsClient && this._wsClient.connectToGameRoom)}`);

        if (this._wsClient && this._wsClient.connectToGameRoom) {
            return this._wsClient.connectToGameRoom(gameId, serverId);
        }
        cc.error("🎮 [NET_SERVICE] WebSocket客户端不可用或不支持游戏房间连接");
        return Promise.resolve(false);
    }

    // 离开游戏房间Channel
    public leaveGameRoom(): void {
        if (this._wsClient && this._wsClient.leaveGameRoom) {
            this._wsClient.leaveGameRoom();
        }
    }

    // 发送数据
    public send(mainId: number, subId: number, data?: any) {
        if (this._wsClient) {
            this._wsClient.send({mainId: mainId, subId: subId, data: data})
            // this._waitSendData = null;
        }
        else if (this._enableConnedSend) {
            this._waitSendData = { mainId: mainId, subId: subId, data: data }
            this.connect();
        }
        // 输出日志
        if (CC_DEBUG) {
            cc.log(`===SEND PROTO:[${mainId}][${subId}], INFORMATION:`, data)
            //let tag = `${this._url} [MESSAGE]`;
            //cc.log(Common.dump(dataobj, tag));
        }

    }

    // 关闭当前连接
    public close() {
        this._waitSendData = null;
        if (this._wsClient) {
            if (this._wsClient.isConnected()) {
                this._wsClient.close();
            }
            this._wsClient = null;
        }
        if (this._sendHeartbeatTimer != -1) {
            clearInterval(this._sendHeartbeatTimer);
            this._sendHeartbeatTimer = -1;
        }
        this.stopReconnect();
    }

    // 绑定协议回调接口
    public bindProto(mainId: number, subId: number, callback: Function, target?: any) {
        let eventKey = `PROTO:[${mainId}][${subId}]`;
        this._eventTarget.on(eventKey, callback, target);
    }

    // 取消绑定协议
    public unbindProto(mainId: number, subId: number, callback?: Function, target?: any) {
        let eventKey = `PROTO:[${mainId}][${subId}]`;
        this._eventTarget.off(eventKey, callback, target);
    }

    // 绑定特定事件
    public bindEvent(eventName: string, callback: Function, target?: any) {
        this._eventTarget.on(eventName, callback, target);
    }

    // 取消绑定事件
    public unbindEvent(eventName: string, callback?: Function, target?: any) {
        this._eventTarget.off(eventName, callback, target);
    }

    // 清除所有事件
    public clearAllEvents() {
        this._eventTarget.clear();
    }

    //////////////////////////////////////////////////////////////////////////////
    // 重载函数 - 连接服务器成功
    protected onOpen(e: Event) {
        let self = this;
        // 启动心跳包发送定时器 - 只有当心跳开关启用且间隔大于0时才启动
        if (HeartbeatConfig.ENABLE_APP_HEARTBEAT && HeartbeatConfig.APP_HEARTBEAT_INTERVAL > 0) {
            this._sendHeartbeatTimer = setInterval(() => {
                self.sendHeartbeat();
            }, HeartbeatConfig.APP_HEARTBEAT_INTERVAL);
            cc.log("应用层心跳机制已启用，间隔:", HeartbeatConfig.APP_HEARTBEAT_INTERVAL, "ms");
        } else {
            cc.log("应用层心跳机制已屏蔽");
        }

        // 输出当前心跳状态
        cc.log(HeartbeatConfig.getHeartbeatStatus());

        this.stopReconnect();
        // 通知连接成功事件
        this._eventTarget.emit(GameEvent.NETWORK_STATUS_NOTICE, NetStatus.CONNECTED);

        // 设置连接成功索引
        this._lastServerIndex = this._currServerIndex;

        // 是否有数据包需要发送
        if (this._enableConnedSend && this._waitSendData) {
            this._wsClient.send(this._waitSendData);
            this._waitSendData = null;
        }

        // 输出日志
        cc.log(this._connUrl, "[connected]");
    }

    // 重载函数 - 接收到服务器消息
    protected onMessage(dataobj: any) {
        // 检查是否为Phoenix的超时或错误消息
        if (dataobj && typeof dataobj === 'object' && dataobj.status) {
            // 这是Phoenix库的状态消息，不是游戏协议消息
            if (dataobj.status === 'timeout') {
                cc.warn("Phoenix消息超时:", dataobj);
                return;
            } else if (dataobj.status === 'error') {
                cc.error("Phoenix消息错误:", dataobj);
                return;
            }
        }

        // 检查是否为有效的协议消息
        if (!dataobj || typeof dataobj.mainId === 'undefined' || typeof dataobj.subId === 'undefined') {
            cc.warn("收到无效的协议消息:", dataobj);
            return;
        }

        // 解析数据并判断为心跳包
        if (this.isHeartbeat(dataobj)) {
            return;
        }

        // 输出日志
        if (CC_DEBUG) {
            cc.log(`RECV PROTO:[${dataobj.mainId}][${dataobj.subId}], URL: ${this._connUrl}`, dataobj.data)
            //let tag = `${this._url} [MESSAGE]`;
            //cc.log(Common.dump(dataobj, tag));
        }

        // 派发消息
        let eventKey = `PROTO:[${dataobj.mainId}][${dataobj.subId}]`;
        checkServerLanguage(dataobj.data)
        this._eventTarget.emit(eventKey, dataobj.data);
    }


    // 重载函数 - 连接已关闭
    protected onClose(e: CloseEvent) {
        if (this._sendHeartbeatTimer != -1) {
            clearInterval(this._sendHeartbeatTimer);
            this._sendHeartbeatTimer = -1;
        }

        // 输出日志
        cc.log(this._connUrl, "[closed]");

        // 是否为连接断开
        if (this.isConnected()) {
            this._eventTarget.emit(GameEvent.NETWORK_STATUS_NOTICE, NetStatus.CLOSED);
            return;
        }

        // 下一个连接对象
        this._currServerIndex += 1;
        if (this._currServerIndex >= this._connServerList.length) {
            this._currServerIndex = 0;
        }

        // 无可用的连接信息(通知连接失败)
        if (this._currServerIndex == this._lastServerIndex) {
            this._eventTarget.emit(GameEvent.NETWORK_STATUS_NOTICE, NetStatus.FAILED);
            return;
        }

        // 开始连接新的服务器
        this.connect();
    }

    // 重载函数 - 发送消息失败
    protected onSendFail() {
        this._eventTarget.emit(GameEvent.NETWORK_STATUS_NOTICE, NetStatus.CLOSED);
    }

    public startReconnect() {
        let self = this;
        this.stopReconnect();
        this._reconnetTimer = setTimeout(() => {
            if (!self.isConnected()) {
                self.connect();
            } else {
                self.stopReconnect();
            }
        }, this._reconnetTimeOut);
    }

    public stopReconnect() {
        if (this._reconnetTimer != -1) {
            clearTimeout(this._reconnetTimer);
            this._reconnetTimer = -1;
        }
    }
    //////////////////////////////////////////////////////////////////////////////

}
