import { GameEvent } from "../common/Define";
import Config from "../../frame/config/Config";
import Common from "../../frame/common/Common";
import HttpRequest from "../../frame/network/HttpRequest";
import DataManager from "../../frame/manager/DataManager";
import EventManager from "../../frame/manager/EventManager";

let XXTea = require("XXTea");
let Crypt = require("CryptUtil");
//////////////////////////////////////////////////////////////////////////////////
// 更新服务器列表信息
export default class ServerList {
    //////////////////////////////////////////////////////////////////////////////
    // 数据加密KEY
    private static DataCryptKey = "12sldakj~@!#!@ew";

    //////////////////////////////////////////////////////////////////////////////
    // 加载外部的服务地址
    public static loadConfigAddres() {
        let serverList = window["REQUEST_SERVER_LIST"];
        if (serverList && typeof (serverList) == "object" && serverList.length > 0) {
            Config.SERVER_LIST = [];
            for (let n = 0; n < serverList.length; n++) {
                let sourceData = Crypt.convertFromHex(serverList[n]);
                let serverUrl = XXTea.decrypt(sourceData, ServerList.DataCryptKey);
                Config.SERVER_LIST.push(serverUrl);
            }
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    // 编码请求地址
    public makeRequestUrl(index: number): string {
        let serverList = Config.SERVER_LIST;
        if (index < 0 || index > serverList.length) {
            return '';
        }

        let serverUrl = serverList[index];
        let channelId = DataManager.instance.channelId;
        let ctime = Common.getCTime();
        let version = 0;

        // 设置请求参数(当前时间、渠道号、版本)
        let params = `r=${ctime}&&intsite=${channelId}&intversion=${version}`;

        // 设置玩家标识
        let playerid = DataManager.instance.playerid;
        if (playerid && playerid != 0) {
            params += `&intUserid=${playerid}`;
        }

        // 设置登陆帐号
        let account = DataManager.instance.account;
        if (account && account != '') {
            params += `&at=${account}`;
        }

        // 设置系统类型
        if (cc.sys.os == cc.sys.OS_IOS) {
            params += "&strmobiletype=0";
        }
        else if (cc.sys.os == cc.sys.OS_ANDROID) {
            params += "&strmobiletype=1";
        }
        else {
            params += "&strmobiletype=2";
        }

        // 加密并编码数据
        // let encryptData = XXTea.encrypt(params, ServerList.DataCryptKey);
        // let hexData = Crypt.convertToHex(encryptData);
        // 先用base64编码
        let hexData = btoa(params);
        return `${serverUrl}data=${hexData}`;
    }

    // 开始请求数据
    private async startRequest(index: number): Promise<string> {
        let url = this.makeRequestUrl(index);
        return new Promise((resolve) => {
            HttpRequest.request(url, "GET", {}, (data: any) => {
                if (data && data.length > 0) {
                    // 解密接收到的数据
                    // let sourceData = Crypt.convertFromHex(data);
                    // let jsonData = XXTea.decrypt(sourceData, ServerList.DataCryptKey);
                    let jsonData = atob(data);
                    resolve(jsonData);
                    // console.log(jsonData);
                }
                else {
                    resolve("");
                }
            }, (error: string) => {
                console.error(`ServerList.startRequest: ${error}`);
                resolve("");
            });
        }).then((data: string) => {
            return data;
        });
    }

    // 获取配置信息
    public static async get() {
        let inst = new ServerList();
        let count = Config.SERVER_LIST.length;
        for (let i = 0; i < count; ++i) {
            let content = await inst.startRequest(i);
            if (content && content.length > 0) {
                try {
                    // 解析JSON数据
                    let result = JSON.parse(content);
                    console.log(`大厅服务器地址：${content}`);

                    // 大厅连接地址
                    DataManager.instance.setHallServers(result.connectserver);

                    // 热更新地址
                    let updateurl = result.updateurl;
                    if (updateurl && typeof (updateurl) == "string" && updateurl != "") {
                        let lastIndex = updateurl.length - 1;
                        if (lastIndex >= 0) {
                            if (updateurl[lastIndex] != "/") {
                                updateurl += "/";
                            }
                        }
                        DataManager.instance.hotUpdateUrl = updateurl;
                    }

                    // 远程APP的版本号
                    let installver = result.installver;
                    DataManager.instance.removeAppVersion = Common.toInt(installver);

                    // 远程APP安装包地址
                    let installurl = result.installurl;
                    if (installurl && typeof (installurl) == "string" && installurl != "") {
                        DataManager.instance.appInstallUrl = installurl;
                    }

                    // 远程APP安装包地址
                    let weburl = result.weburl;
                    if (weburl && typeof (weburl) == "string" && weburl != "") {
                        DataManager.instance.appWebUrl = weburl;
                    }

                    // 远程大厅的版本
                    let basever = result.basever;
                    DataManager.instance.remoteHallVersion = Common.toInt(basever);

                    // 头像上传地址
                    let headUploadUrl = result.headUploadUrl;
                    if (headUploadUrl && typeof (headUploadUrl) == "string" && headUploadUrl != "") {
                        DataManager.instance.headUploadUrl = headUploadUrl;
                    }

                    //游戏api地址
                    let gameApiUrl = result.gamewebapiurl;
                    if (gameApiUrl && typeof (gameApiUrl) == "string" && gameApiUrl != "") {
                        DataManager.instance.gameApiUrl = gameApiUrl;
                    }

                    //支付地址
                    let pmurl = result.pmurl;
                    if (pmurl && typeof (pmurl) == "string" && pmurl != "") {
                        DataManager.instance.payUrl = pmurl;
                    }

                    //兑换记录地址
                    if (result.publicurl) {
                        DataManager.instance.exchangeLogUrl = result.publicurl;
                    }

                    //渠道安装包地址
                    if (result.downloadurl) {
                        DataManager.instance.chanelDownUrl = result.downloadurl;
                    }

                    //渠道安装包二维码
                    if (result.downloadurl) {
                        DataManager.instance.chanelQrcodeUrl = result.downloadqrcodeurl;
                    }

                    //兑换状态
                    if (result.exchangestatus) {
                        DataManager.instance.exChangeStatus = Common.toInt(result.exchangestatus);
                    }

                    //支付状态
                    if (result.chargestatus) {
                        DataManager.instance.chargeStatus = Common.toInt(result.chargestatus);
                    }

                    //注册状态
                    if (result.registerstatus) {
                        DataManager.instance.registerStatus = Common.toInt(result.registerstatus);
                    }

                    // 通知获取成功
                    EventManager.instance.emit(GameEvent.REQUEST_SERVERLIST_COMPLATED, true);

                } catch (err) {
                    EventManager.instance.emit(GameEvent.REQUEST_SERVERLIST_COMPLATED, false);
                }
                return
            }
        }
        EventManager.instance.emit(GameEvent.REQUEST_SERVERLIST_COMPLATED, false);
    }

    // 获取加密数据
    public static getWebApiUrl(serverUrl: string, params: string) {
        // 加密并编码数据
        let encryptData = XXTea.encrypt(params, ServerList.DataCryptKey);
        let hexData = Crypt.convertToHex(encryptData);
        return `${DataManager.instance.gameApiUrl}${serverUrl}?data=${hexData}`;
    }

    // 解密数据
    public static getWebApiData(data: string) {
        if (data && data.length > 0) {
            // 解密接收到的数据
            let sourceData = Crypt.convertFromHex(data);
            let jsonData = XXTea.decrypt(sourceData, ServerList.DataCryptKey);
            return jsonData;
        }
        else {
            return "";
        }
    }
    //////////////////////////////////////////////////////////////////////////////

}
