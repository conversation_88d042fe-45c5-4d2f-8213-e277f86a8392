import { Constant, SceneName, GameEvent, NetStatus, ZOrder, HallFeatureType, rewardsType, quickPayStyle } from "../common/Define";
import { MainProto, DbServer, BaseInfo, RegLogin, Game, FindPsw, Money, Rank, MailManager, NoticeManager, XC, Task, HallActivity } from "../common/Protocol";
import { TextTips, updateTextTips } from "../../frame/common/Language";
import Common from "../common/Common";
import Config from "../config/Config";
import DataManager from "./DataManager";
import EventManager from "./EventManager";
import SceneManager from "./SceneManager";
import GameManager from "./GameManager";
import NetService from "../model/NetService";
import WaitHelper from "../extentions/WaitHelper";
import AlertHelper from "../extentions/AlertHelper";
import ToastHelper from "../extentions/ToastHelper";
import PlatformHelper from "../extentions/PlatformHelper";
import PopupLayer from "../component/PopupLayer";
import HallPopupManager from "./HallPopupManager";
import UIHelper from "../extentions/UIHelper";
import EventPointHelper from "../extentions/EventPointHelper";
import AlertLayer from "../component/AlertLayer";

let md5 = require("md5");
//////////////////////////////////////////////////////////////////////////////////
// 大厅管理类
export default class HallManager {
   
    //////////////////////////////////////////////////////////////////////////////
    // 实例对象
    private static _instance: HallManager = null;
    public static get instance(): HallManager {
        return this._instance || (this._instance = new HallManager());
    }

    //////////////////////////////////////////////////////////////////////////////
    private static _isInited: boolean = false;
    public static get isInited(): boolean {
        return this._isInited;
    }

    //////////////////////////////////////////////////////////////////////////////
    // 构造函数
    public constructor() {
        this._netService = new NetService();
    }
    //////////////////////////////////////////////////////////////////////////////
    /*** 打开界面类型 0 充值 1 兑换 */
    private _isOpenType: number = -1;
    // 网络服务对象
    private _netService: NetService = null;
    // 大厅服务器地址
    private _hallServerAddr: string = "";
    // 大厅重连次数
    private _resetCount: number = 0;
    // 已处理充值消息
    private _readyChargeList: number[] = [];
    // 是否需要进入游戏房间
    private _isEnterGameRoom: boolean = false;
    public get isEnterGameRoom() : boolean {
        return this._isEnterGameRoom;
    }
    public set isEnterGameRoom(isEnterGameRoom : boolean) {
        this. _isEnterGameRoom= isEnterGameRoom;
    }
    
    // 需要进入的房间信息
    private _enterGameRoomInfo: any = null;
    public get enterGameRoomInfo() : any {
        return this._enterGameRoomInfo;
    }
    public set enterGameRoomInfo(enterGameRoomInfo : any) {
        this. _enterGameRoomInfo= enterGameRoomInfo;
    }
    
    // 绑定账号
    private _bindingAccount: any = null;
    private _bindingPassword: any = null;

    // 兑换金额
    private _exchangeMoney: number = 0;

    //////////////////////////////////////////////////////////////////////////////
    // 注册协议回调接口
    private registerAllProtos() {
        this._netService.bindProto(MainProto.RegLogin, RegLogin.SC_LOGIN_P, this.onLoginResult, this);
        this._netService.bindProto(MainProto.RegLogin, RegLogin.SC_OHTER_LOGIN_P, this.onOtherLogin, this);
        this._netService.bindProto(MainProto.RegLogin, RegLogin.SC_LOGIN_OTHER_P, this.onLoginOther, this);
        this._netService.bindProto(MainProto.RegLogin, RegLogin.SC_NORMAL_REG_P, this.onNormalReg, this);
        this._netService.bindProto(MainProto.RegLogin, RegLogin.SC_PHONECODE_REG_P, this.onBindPhone, this);
        this._netService.bindProto(MainProto.RegLogin, RegLogin.SC_REQUEST_REG_PHONECODE_P, this.onGetSmsCode, this);
        this._netService.bindProto(MainProto.RegLogin, RegLogin.SC_REQUEST_GAMEVERSIONS_P, this.onGetGameVersions, this);
        this._netService.bindProto(MainProto.RegLogin, RegLogin.SC_HALL_SERVER_VERSION_P, this.onGetHallVersion, this);
        this._netService.bindProto(MainProto.RegLogin, RegLogin.DC_REQUEST_SYSTEM_STATUS_P, this.onGetConfig, this);

        this._netService.bindProto(MainProto.DBServer, DbServer.SC_SET_HEADID_P, this.onSetHeadID, this);
        this._netService.bindProto(MainProto.DBServer, DbServer.SC_CUSTSRV_REPLY_P, this.onCustomServiceMsgList, this);
        this._netService.bindProto(MainProto.DBServer, DbServer.SC_NEW_CHARGE_LIST_P, this.onNewChargeList, this);
        
        this._netService.bindProto(MainProto.BaseInfo, BaseInfo.SC_SET_NICKNAME_P, this.onChangeNickName, this);

        this._netService.bindProto(MainProto.Rank, Rank.SC_RANK_DATA, this.onRankData, this);
        this._netService.bindProto(MainProto.Rank, Rank.SC_SELF_RANK_DATA_P, this.onSelfRankData, this);

        this._netService.bindProto(MainProto.Money, Money.SC_SAVE_MONEY_RESULT_P, this.onSaveMoneyResult, this);
        this._netService.bindProto(MainProto.Money, Money.SC_GET_MONEY_RESULT_P, this.onGetMoneyResult, this);
        this._netService.bindProto(MainProto.Money, Money.SC_SET_MONEY_P, this.onSetUserMoney, this);
        this._netService.bindProto(MainProto.Money, Money.SC_SET_WALLETMONEY_P, this.onSetWalletMoney, this);
        this._netService.bindProto(MainProto.Money, Money.SC_SET_GAME_MONEY_P, this.onSetGameMoney, this);
        this._netService.bindProto(MainProto.Money, Money.DS_BIND_BANK_P, this.onBindBank, this);
        this._netService.bindProto(MainProto.Money, Money.DS_MONEY_CHANG_RMB_P, this.onExchange, this);
        this._netService.bindProto(MainProto.Money, Money.SC_PAY, this.onSendPay, this);
        this._netService.bindProto(MainProto.Money, Money.DC_SEND_MSG_GUEST_SERVER_P, this.onCustomServiceMsg, this);

        this._netService.bindProto(MainProto.MailManager, MailManager.SC_REQUEST_MAILLIST_P, this.onMailList, this);
        this._netService.bindProto(MainProto.MailManager, MailManager.SC_REQUEST_MAIL_INFO_P, this.onMailDetail, this);
        this._netService.bindProto(MainProto.MailManager, MailManager.SC_ADD_MAIL_P, this.onAddMaill, this);
        this._netService.bindProto(MainProto.MailManager, MailManager.SC_REQUEST_NEW_MAIL_COUNT_P, this.onNewMailCount, this);

        this._netService.bindProto(MainProto.FindPsw, FindPsw.SC_FINDPSW_SET_NEW_PSW_RESULT_P, this.onResetPassword, this);

        this._netService.bindProto(MainProto.Game, Game.SC_ADD_GAMELIST_P, this.onAddGameList, this);
        this._netService.bindProto(MainProto.Game, Game.SC_DEL_GAMELIST_P, this.onDelGameList, this);
        this._netService.bindProto(MainProto.Game, Game.SC_GAME_PLAYER_NUM_P, this.onGetPlayerCount, this);

        this._netService.bindProto(MainProto.Game, Game.XS_SYNC_ROOMINFO_P, this.onRoomInfo, this);

        // 注册通知回调
        this._netService.bindProto(MainProto.NoticeManager, NoticeManager.SC_NOTICE_P, this.onNotice, this);

        this._netService.bindProto(MainProto.DBServer, DbServer.SC_WEB_CHANGE_ATTRIB_P, this.onAttributeChange, this);
        
        //比赛协议注册
        this._netService.bindProto(MainProto.Task, Task.SC_UPDATE_MATCH_ATTR, this.onMatchAttr, this);
        this._netService.bindProto(MainProto.Task, Task.SC_GET_MATCH_LIST, this.onMatchList, this);
        this._netService.bindProto(MainProto.Task, Task.SC_GET_MATCH_RANK_REWARD, this.onMatchRankReward, this);
        this._netService.bindProto(MainProto.Task, Task.SC_GET_TODAY_MATCH_LIST, this.onMatchTodayList, this);

        /**<---------------------------------------------印度版本大厅活动协议注册----------begin-----------------------------------------------> */
        //大厅活动协议注册
        //loginCash
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_LOGINCASH_INFO_P, this.onHallActivityLoginCash, this);
        //登录活动 loginCash
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_LOGINCASH_AWARD_P, this.onHallActivityLoginCashAward, this);
        //获取用户金币信息 (需要主动请求)
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GET_USER_MONEY_P, this.onHallGetUserMoney, this);
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_USER_BONUS_P, this.onHallGetUserRewardMoney, this);
        
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GAME_TASK_P, this.onGameTask, this);
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_GAME_TASK_AWARD_P, this.onFetchGameTask, this);
        //七日登录
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GET_SEVEN_DAYS_P, this.onHallSevenDayAns, this);
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_SEVEN_DAYS_AWARD_P, this.onHallSevenDayRewardAns, this);
        //30次刮卡
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GET_THIRTY_CARD_P, this.onHallThirtyCardAns, this);
        //挂卡领取
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_THIRTY_CARD_P, this.onHallThirtyFetchCardAns, this);
        //返回礼包活动信息
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GET_GIFT_CHARGE_P, this.onHallGetGiftChargeAns, this);
         //返回周卡月卡
         this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GET_CARD_TASK_P, this.onHallGetCardTaskAns, this);
        //领取周卡月卡活动奖励
         this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_CARD_TASK_P, this.onHallFetchCardTaskAns, this);
        //返回获取VIP
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GET_VIP_GIFT_P, this.onHallGetVipGiftAns, this);
        //返回VIP活动奖励
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_VIP_GIFT_P, this.onHallFetchVipGiftAns, this);
        //返回 free binus
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GET_FREE_BONUS_P, this.onHallGetFreeBonumsAns, this);
        //返回免费积分活动奖励
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_FREE_BONUS_P, this.onHallFetchFreeBonumsAns, this);

        //返回免费提现
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GET_FREE_CASH_P, this.onHallGetFreeCashAns, this);
        //返回领取免费提现
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_FREE_CASH_P, this.onHallFetchFreeCashAns, this);
        //返回免费提现推广数据
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_GET_FREE_CASH_INVITATION_P, this.onHallGetFreeCashInvitationAns, this);
        //返回破产可领取金额
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_BROKE_AWARD_P, this.onHallFetchBrokeAwardAns, this);
        //返回邮件奖励 返回结构 
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_MAIL_AWARD_P, this.onHallFetchMailAwardAns, this);
        //返回绑定用户手机号码 
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_BIND_PHONE_USER_P, this.onHallBindPhoneAns, this);
        //返回绑定邮箱 状态
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_BIND_MAIL_USER_P, this.onHallBindEmailAns, this);
        this._netService.bindProto(MainProto.HallActivity, HallActivity.SC_FETCH_CDKEY_AWARD_P, this.onHalCdKeyAns, this);
         /**<---------------------------------------------印度版本大厅活动协议注册----------end-----------------------------------------------> */
        
    }
    
    //////////////////////////////////////////////////////////////////////////////
    // 获取NetService实例（供GameManager复用连接）
    public getNetService(): NetService {
        return this._netService;
    }

    // 初始化大厅服务
    public init() {
        // 初始化相关数据
        HallManager._isInited = true;
        this._netService.enableConnedSend = true;

        // 添加大厅服务器列表
        let hallServers = DataManager.instance.hallServers;
        for (let i = 0; i < hallServers.length; ++i) {
            let server = hallServers[i];
            let ServerAddress = server.ip;
            let serverPort = server.port;
            let path = server.path;
            let protocol = server.protocol;

            let serverUrl = "";
            // 检查是否为IP地址
            let ipCheck = /^(\d+)\.(\d+)\.(\d+)\.(\d+)$/;
            if (ipCheck.test(ServerAddress) || protocol == "ws") {
                serverUrl = `ws://${ServerAddress}:${serverPort}/${path}`
            } else {
                serverUrl = `wss://${serverPort}.${ServerAddress}:${serverPort}/${path}`;
            }
            cc.log("wss hall address", serverUrl)

            this._netService.addServer(serverUrl);
        }

        // 绑定网络状态回调接口
        this._netService.bindEvent(GameEvent.NETWORK_STATUS_NOTICE, this.onNetworkStatus, this);

        // 绑定全局事件
        EventManager.instance.on(GameEvent.HALL_POPUP_FUNCTION, this.onPopupFunction, this);

        // 注册协议回调接口
        this.registerAllProtos();
    }

    // 关闭大厅服务
    public closeServer() {
        this._netService.close();
    }

    // 进入大厅场景
    public enterScene() {
        // 保存本地配置信息
        DataManager.instance.saveUserInfo();
        DataManager.instance.saveLocalAccounts();

        //验证是否打开服务条款
        /*if(DataManager.instance.chargeStatus==0 && DataManager.instance.agreeServices == 0){
            this.showPopupByName("AccountServicesPopup")
            return;
        }*/
        // 判断是否需要进入游戏房间
        if (this._isEnterGameRoom && this._enterGameRoomInfo) {
            // 进入游戏房间
            let bundleName = this._enterGameRoomInfo.bundleName;
            let isMiniGame = DataManager.instance.isMiniGame(bundleName);
            if (!isMiniGame) {
                GameManager.instance.enterGameRoom(bundleName, this._enterGameRoomInfo);

                // 清理相关数据
                this._enterGameRoomInfo = null;
                this._isEnterGameRoom = false;
                return;
            }
        }

        // 进入大厅
        SceneManager.instance.enterHallScene();
    }

    // 检测当前连接状态, 提示断开重连
    public checkNetworkState() {
        if (this._netService.isConnected()||this._netService.isConnecting()) {
            return;
        }
        WaitHelper.close();
        if(this._resetCount<1){
            this._resetCount++;
            this.closeServer();
            this.sendLogin();
            if (SceneManager.instance.isHallScene) {
                WaitHelper.show(TextTips.WAIT_HALL_RECONNECT_TIPS);
            }
            return;
        }
        // 显示提示框
        let self = this;
        AlertHelper.confirm(TextTips.ALERT_SERVER_DISCONNECT, () => {
            self.closeServer();
            self.sendLogin();
            if (SceneManager.instance.isHallScene) {
                WaitHelper.show(TextTips.WAIT_HALL_RECONNECT_TIPS);
            }
        }, null);
    }

    //////////////////////////////////////////////////////////////////////////////
    // 创建弹出功能界面
    private createPopupUI(assets: any, params?: any) {
        let basePopupNode =  cc.instantiate(assets);
        if (basePopupNode) {
            let basePopupComp = basePopupNode.getComponent("PopupLayer");
            if (basePopupComp) {
                if (params) {
                    basePopupComp.params = params;
                }
                let DIALOG = ZOrder.DIALOG-1
                if(assets.name &&assets.name =="GetRewards"){
                    DIALOG = ZOrder.DIALOG
                }
                SceneManager.instance.addChildNode(basePopupNode,DIALOG);
                basePopupComp.layerName = assets.name
                basePopupComp.show();
                HallPopupManager.instance.set(assets.name,basePopupComp)
            }
        }
    }

    //创建遮挡层
    //消除弹窗预制件加载时间的空白
    creatShardLayer(){
        let node = new cc.Node()
        let sprite_ = node.addComponent(cc.Sprite)
        sprite_.type = cc.Sprite.Type.SLICED
        sprite_.sizeMode = cc.Sprite.SizeMode.CUSTOM
        cc.resources.load("login/images/loading_bg", cc.SpriteFrame, (error: Error, assets: any) => {
            if (error) {
                return;
            }
            if (assets) {
                sprite_.spriteFrame = assets;
            }
        });
        node.width = cc.view.getVisibleSize().width
        node.height = cc.view.getVisibleSize().height
        node.opacity = 215
        node.addComponent(cc.BlockInputEvents)
        SceneManager.instance.addChildNode(node, ZOrder.DIALOG-1);
        return node
    }

    // 显示弹出功能界面
    public showPopupByName(name: string, params?: any) {
        if(HallPopupManager.instance.checkTime(name, params))return 
        if (HallPopupManager.instance.has(name)) {
            let popup_ = HallPopupManager.instance.get(name)
            if (popup_.node && popup_.node.isValid) {
                HallPopupManager.instance.get(name).show()
            }
            else {
                HallPopupManager.instance.delete(name)
                this.showPopupByName(name, params)
            }
        } else {
            let resName = `hall/prefabs/popup/${name}`;
            let shardNode_ = this.creatShardLayer()
            cc.resources.load(resName, cc.Prefab, (error: Error, assets: any) => {
                if (shardNode_ && shardNode_.isValid) shardNode_.removeFromParent()
                if (this._isOpenType > -1) {
                    WaitHelper.close();
                }
                this._isOpenType = -1;
                if (error) {
                    console.error(`BasePopup: load prefab failed, resName: ${resName}, error: ${error}`);
                    return;
                }
                if (assets) {
                    this.createPopupUI(assets, params);
                }
            });
        }

    }

    // 显示界面
    private showLayerByName(name: string, params?: any) {
        let resName = `hall/prefabs/layer/${name}`;
        cc.resources.load(resName, cc.Prefab, (error: Error, assets: any) => {
            if (error) {
                console.error(`layer: load prefab failed, resName: ${resName}, error: ${error}`);
                return;
            }
            if (assets) {
                let layer = cc.instantiate(assets);
                if (layer) {
                    let baseLayer = layer.getComponent("BaseLayer");
                    if (baseLayer) {
                        if (params) {
                            baseLayer.params = params;
                        }
                        SceneManager.instance.addChildNode(layer, ZOrder.DIALOG);
                    }
                }
            }
        });
    }

    //////////////////////////////////////////////////////////////////////////////
    // 打开切换帐号
    public openSwitchAccount() {
        this.showPopupByName("AccountPopup");
    }

    // 打开登陆其他帐号
    public openLoginOtherAccount() {
        this.showPopupByName("AccountLogPopup");
    }

    // 打开重置密码
    public openResetPassword() {
        this.showPopupByName("AccountFindPopup");
    }

    // 打开声音设置
    public openAudioSetup() {
        this.openHallSet();
    }

    // 打开保险柜
    public openWalletMoney() {
        this.showPopupByName("BankPopup");
    }

    // 打开个人信息
    public openPersonal(popupInfo=null) {
        this.showPopupByName("PersonalPopup",popupInfo);
    }

    // 打开切换头像
    public openSwitchHead(popupInfo=null) {
        //this.showPopupByName("SwitchHeadPopup");
        this.showPopupByName("ChangedHeadPopup",popupInfo);
    }

    // 打开修改密码
    public openChangeNickName() {
        this.showPopupByName("ChangeNickNamePopup");
    }

    // 打开设置界面
    public openHallSet(popupInfo=null) {
        this.showPopupByName("SetPopup",popupInfo);
    }

    // 打开升级账号
    public openUpgradeAccount() {
        if(DataManager.instance.registerStatus==0){
            return;
        }
        this.showPopupByName("UpgradeAccountPopup");
    }

    // 打开排行榜
    public openLoginCash() {
        this.showPopupByName("LoginCashPopup");
    }

    // 打开排行榜
    public openRank() {
        this.showPopupByName("RankPopup");
    }

    // 打开注册活动
    public openRegisterAty() {
        if(DataManager.instance.registerStatus==0){
            return;
        }
        this.showPopupByName("RegisterAtyPopup");
    }

    // 打开比赛活动
    public openMatch() {
        this.showPopupByName("MatchPopup");
    }

    // 打开比赛规则
    public openMatchRule(item: any) {
        this.showPopupByName("MatchRulePopup",item);
    }

    // 打开比赛奖励
    public openMatchReward(item: any) {
        this.showPopupByName("MatchRewardPopup",item);
    }

    // 打开比赛排名
    public openMatchRanking(item: any) {
        this.showPopupByName("MatchRankingPopup",item);
    }

    // 打开兑换
    public openExchange() {
        if(DataManager.instance.exChangeStatus==0){
            return;
        }
        if (WaitHelper.instance.isClosed) {
            this._isOpenType = 1;
            WaitHelper.show(TextTips.TOAST_REQUEST_EXCHANGE_DATA);
            this.sendConfig();
            
        }
    }

    // 打开绑定银行卡
    public openBindBank() {
        this.showPopupByName("BindBankPopup");
    }

    // 打开选择银行卡类型
    public openBindSelBank(data: any) {
        this.showPopupByName("BindSelBankPopup", data);
    }

    // 打开兑换详情
    public openExchangeDetail(data: any) {
        this.showPopupByName("ExchangeDetailPopup", data);
    }

    // 打开兑换规则说明
    public openExchangeRule() {
        this.showPopupByName("ExchangeRulePopup");
    }

    // 打开兑换记录
    public openExchangeRecord() {
        this.showPopupByName("ExchangeRecordPopup");
    }

    // 打开活动
    public openActivity() {
        this.showPopupByName("ActivityPopup");
    }

    // 打开邮件详情
    public openMailDetail(params: any) {
        this.showPopupByName("MailDetailPopup", params);
    }

    // 打开邮件详情
    public openMailGetMoney(params: any) {
        this.showPopupByName("MailGetMoneyPopup", params);
    }

    // 打开充值 （游戏内调用）
    public openCharge(popupInfo=null) {
        if(popupInfo &&popupInfo.quickPayStyle){
            switch (popupInfo.quickPayStyle) {
                case quickPayStyle.gameVipPay:
                    this.openHallVipAddCashPopup(popupInfo)
                    break;
                case quickPayStyle.gameQuickPay:
                    this.openHallQuicklyAddCashPopup(popupInfo)
                    break;
                case quickPayStyle.gameTpQuickPay:
                    this.openHallPayCashQuicklyPopup(popupInfo)
                    break;
                default:
                    break;
            }

        }else{
            this.openHallAddCashPopup(popupInfo)
        }
        
        
    }

    // 打开充值记录
    public openChargeHistory() {
        this.showPopupByName("ChargeHistoryPopup");
    }

    // 打开客服
    public openCustomService(data?: any) {
        if(!DataManager.instance.chanelDownUrl)return;
        let data_ = {
            uid:DataManager.instance.userInfo.playerid,
            channel:DataManager.instance.channelId,
            name:DataManager.instance.userInfo.nickname,
        }
        let serverUrl = DataManager.instance.chanelDownUrl +"?"+ Common.formateObjToParamStr(data_)

        if(cc.sys.isNative){
            //webview
            this.openWebView(serverUrl)
        }else{
            PlatformHelper.openURL(serverUrl);
        }
    }


    // 打开webview
    public openWebView(url: string) {
        this.showLayerByName("WebViewLayer", url);
    }

    // 打开支付中心
    public openPayCenter(params=null) {
        this.openCharge(params);
    }


    public openHallBoonPopup(popupInfo=null){
        this.showPopupByName("HallBoonPopup",popupInfo);
    }
    //打开vipCards
    public openHallVipCards(popupInfo=null){
        this.showPopupByName("HallVipCardsPopup",popupInfo);
    }

    public openHallSign(popupInfo=null){
        this.showPopupByName("HallSignPopup",popupInfo);
    }

    public openHallScratchCard(popupInfo=null){
        this.showPopupByName("HallScratchcardPopup",popupInfo);
    }

    public openHallSettingMored(popupInfo=null){
        this.showPopupByName("HallSettingMorePopup",popupInfo);
    }

    public openHallFree(popupInfo=null){
        this.showPopupByName("HallActivityFreePopup",popupInfo);
    }

    public openHallFreeCash(popupInfo=null){
        this.showPopupByName("HallFreeCashPopup",popupInfo);
    }

    //打开 FreeCash Link
    public openHallFreeCashShare(popupInfo=null){
        this.showPopupByName("HallFreeCashSharePopup",popupInfo);
    }
    //打开破产补助
    public openGameBankruptcyPopup(popupInfo=null){
        this.showPopupByName("GameBankruptcyPopup",popupInfo);
    }


    //打开帮助
    public openHallHelpSupport(popupInfo=null){
        this.showPopupByName("HallHelpSupportPopup",popupInfo);
    }
    //打开vipGift
    public openHallVipGiftPopup(popupInfo=null){
        this.showPopupByName("HallVipGiftPopup",popupInfo);
    }
    //打开 AddCashPopup
    public openHallAddCashPopup(popupInfo=null){
        this.sendConfig()
        if(DataManager.instance.getGameLimitGiftInfo()&& DataManager.instance.money<DataManager.instance.quickMaxNum){
            this.showPopupByName("HallAddCashPopup",popupInfo);
        }else{
            this.openHallPayHorCashPopup(popupInfo);
        }
    }

    public openHallLimitCashPopup(popupInfo=null){
        this.sendConfig()
        if(DataManager.instance.getGameLimitGiftInfo()){
            this.showPopupByName("HallAddCashPopup",popupInfo);
        }else{
            this.openHallPayHorCashPopup(popupInfo);
        }
    }


    //打开 vip addCash
    public openHallVipAddCashPopup(popupInfo=null){
        this.showPopupByName("HallVipAddCashPopup",popupInfo);
    }
    //打开游戏内快速充值（龙虎）
    public openHallQuicklyAddCashPopup(popupInfo=null){
        this.showPopupByName("HallQuicklyAddCashPopup",popupInfo);
    }

    //打开游戏内快捷支付(tp 内游戏快捷支付)
    public openHallPayCashQuicklyPopup(popupInfo){  //单位分
        this.showPopupByName("HallPayCashQuicklyPopup",popupInfo) 
    }

    // 打开 HallPayCashPopup
    public openHallPayCashPopup(popupInfo=null){
        this.showPopupByName("HallPayCashPopup",popupInfo);
    }
    // 打开 HallPayHorCashPopup
    public openHallPayHorCashPopup(popupInfo=null){
        this.showPopupByName("HallPayCashHorPopup",popupInfo);
    }
    //打开 HallPayCashRecordPopup
    public openHallPayCashRecordPopup(popupInfo=null){
        this.showPopupByName("HallPayCashRecordPopup",popupInfo);
    }
    //打开提现
    public openHallWithDrawCashPopup(popupInfo=null){
        if(DataManager.instance.exChangeStatus==0){
            return;
        }
        if (WaitHelper.instance.isClosed) {
            this._isOpenType = 1;
            WaitHelper.show(TextTips.WAIT_DEFAULT_TIPS);
            this.sendConfig();
        }
        //this.showPopupByName("HallWithDrawCashPopup",popupInfo);
    }
    //打开提现提示
    public openHallCashTipsPopup(popupInfo=null){
        this.showPopupByName("HallCashTipsPopup",popupInfo);
    }
    //打开提现绑定卡
    public openHallWithDrawBindCardPopup(popupInfo=null){
        this.showPopupByName("HallWithDrawBindCardPopup",popupInfo);
    }
    //打开登录客服
    public openHallCustomServicePopup(popupInfo=null){
        this.showPopupByName("HallCustomServicePopup",popupInfo);
    }
    
    //打开新的活动
    public openHallActivityPopup(popupInfo=null){
        this.showPopupByName("HallActivityPopup",popupInfo);
    }
    //检查游戏更新版本
    public openGameVersionUpdatePopup(popupInfo=null){
        this.showPopupByName("HallCheckUpdataPopup",popupInfo);
    }
    //打开大厅邮件
    public openHallEmailPopup(popupInfo=null){
        this.showPopupByName("HallEmailPopup",popupInfo);
    }

    //打开恭喜获得
    public showGameRewards(rewardList = {}){
        this.showPopupByName("GetRewards",rewardList);
    }
    //打开rule
    public openHallGameRulePopup(popupInfo=null){
        this.showPopupByName("HallGameRulePopup",popupInfo);
    }
    //打开 30cards 详情页面
    public openHallScratchAwardPopup(awardInfo=null){
        this.showPopupByName("HallScratchAwardPopup",awardInfo);
    }
    
    //打开房间列表
    public openRoomListUI(popupInfo=null){
        this.showPopupByName("RoomListPopup",popupInfo);
    }
    //绑定信息
    public openPerFectInformationPopup(popupInfo=null){
        this.showPopupByName("HallPerFectInformationPopup",popupInfo);
    }

    //打开邀请分级
    public openGameReferAndEarnPopup(popupInfo=null){
        this.showeReferAndEarnPopupByName("GameReferAndEarnPopup",popupInfo)
    }
   
    // 显示弹出功能界面
     public showeReferAndEarnPopupByName(name: string, params?: any) {
        if(HallPopupManager.instance.checkTime(name, params))return 
        if (HallPopupManager.instance.has(name)) {
            let popup_ = HallPopupManager.instance.get(name)
            if (popup_.node && popup_.node.isValid) {
                HallPopupManager.instance.get(name).show()
            }
            else {
                HallPopupManager.instance.delete(name)
                this.showeReferAndEarnPopupByName(name, params)
            }
        } else {
            let resName = `/hall/referearn/prefabs/${name}`;
            let shardNode_ = this.creatShardLayer()
            cc.resources.load(resName, cc.Prefab, (error: Error, assets: any) => {
                if (shardNode_ && shardNode_.isValid) shardNode_.removeFromParent()
                if (this._isOpenType > -1) {
                    WaitHelper.close();
                }
                this._isOpenType = -1;
                if (error) {
                    console.error(`BasePopup: load prefab failed, resName: ${resName}, error: ${error}`);
                    return;
                }
                if (assets) {
                    this.createPopupUI(assets, params);
                }
            });
        }

    }



    // 绑定事件-弹出功能界面
    public onPopupFunction(name: string, params: any) {
        if (name == "AUDIO_SETUP") {
            this.openAudioSetup();
        }
        else if (name == "WALLET_MONEY") {
            this.openWalletMoney();
        }
        else if (name == "PAY_CENTER") {
            this.openPayCenter(params);
        }
        else if (name == "SWITCH_ACCOUNT") {
            this.openSwitchAccount();
        }
        else if (name == "LOGIN_OTHER_ACCOUNT") {
            this.openLoginOtherAccount();
        }
        else if (name == "RESET_PASSWORD") {
            this.openResetPassword();
        }

    }

    public switchLanguage(value){
        if(DataManager.instance.curLanguage==value)return;
        DataManager.instance.setLocalLanguage(value);
        updateTextTips();
        EventManager.instance.emit(GameEvent.HALL_SWITCH_LANGUAGE);    
    }

    //////////////////////////////////////////////////////////////////////////////
    // 发送登陆请求
    public sendLogin(loginParams: any = {}) {
        let isVm = 0; // 是否为虚拟设备
        let version = "1"; // 版本号
        let password = ""; // 登录密码
        let promotionId = PlatformHelper.getInviteCode(); // 推广渠道号 登陆时这个参数是 代理  或者  渠道 或者 用户ID，用于绑定分享用户
        let accounttype = Constant.CONST_LOGIN_TYPE_FAST;
        let uniquecode = PlatformHelper.getUUID();
        //测试 随机登录
        //uniquecode = uniquecode + new Date().getTime()
        let playerid = DataManager.instance.playerid;
        let account = DataManager.instance.account;
        let randonCer = DataManager.instance.randomCer;
        let channelId = DataManager.instance.channelId;
        let tmpcode = md5(`#~!${version}##${uniquecode}!!!`);
        let checkcode = md5(`)(*&^${tmpcode}F^ad33`);

        // 登录参数信息
        if (Object.keys(loginParams).length > 0) {
            if (loginParams.account && loginParams.account.length > 0) {
                accounttype = Constant.CONST_LOGIN_TYPE_NORMAL;
                password = loginParams.password || "";
                account = loginParams.account;
            }
        }
        else {
            let accountInfo = DataManager.instance.getAccountInfo(account);
            if (accountInfo) {
                accounttype = accountInfo.accountType || Constant.CONST_LOGIN_TYPE_FAST;
                password = accountInfo.password || "";
            }
            if (accounttype == Constant.CONST_LOGIN_TYPE_FACEBOOK) {
                loginParams.fbUserInfo = accountInfo.thridInfo;
                account = "";
            }
            if (accounttype == Constant.CONST_LOGIN_TYPE_APPLE) {
                loginParams.appleUserInfo = accountInfo.thridInfo;
                account = "";
            }
        }

        // 游客登陆清空帐号
        if (accounttype == Constant.CONST_LOGIN_TYPE_FAST) {
            account = "";
        }

        // 登陆信息
        let loginInfo = {
            accounttype: accounttype,
            hdtp: ((cc.sys.platform == cc.sys.ANDROID) ? Constant.CONST_HD_TYPE_ANDROID : Constant.CONST_HD_TYPE_APPLE),
            account: account,
            password: password,
            selectsavepsw: 1,
            random: randonCer,
            usesavepsw: 1,
            hduc: uniquecode,
            hductp: ((cc.sys.platform == cc.sys.ANDROID) ? Constant.CONST_UC_TYPE_ANDROID_ID : Constant.CONST_UC_TYPE_ADID),
            version: version,
            checkcode: checkcode,
            localip: "0.0.0.0",
            phoneCode: "",
            siteid: channelId,
            isvm: isVm,
            ccode: Common.random(10000, 100000),
            uniquecode: uniquecode,
            promotionid: Common.toInt(promotionId),
            playerid: playerid
        }

        // 短信登录
        if (loginParams.smscode && loginParams.smscode.length > 0) {
            loginInfo.accounttype = Constant.CONST_LOGIN_TYPE_SMS;
            loginInfo["smscode"] = loginParams.smscode;
        }

        // Facebook登陆
        if (loginParams.fbUserInfo) {
            let tInfo = loginParams.fbUserInfo;
            loginInfo.accounttype = Constant.CONST_LOGIN_TYPE_FACEBOOK;
            loginInfo["openid"] = tInfo.user || tInfo.id;
            loginInfo["nickname"] = tInfo.name || "";
            loginInfo["headimgurl"] = tInfo.avatar || "";
            loginInfo["sex"] = tInfo.gender == undefined ? 0 : tInfo.gender;
            loginInfo["city"] = tInfo.city;
        }

        // Apple登陆
        if (loginParams.appleUserInfo) {
            let tInfo = loginParams.appleUserInfo;
            loginInfo.accounttype = Constant.CONST_LOGIN_TYPE_APPLE;
            loginInfo["openid"] = tInfo.user || tInfo.openid;
            loginInfo["nickname"] = tInfo.nickname;
            loginInfo["headimgurl"] = tInfo.headimgurl;
            loginInfo["sex"] = tInfo.sex == undefined ? 0 : tInfo.sex;
            loginInfo["city"] = tInfo.city;
        }
        // 发送登陆消息
        this._netService.send(MainProto.RegLogin, RegLogin.CS_LOGIN_P, loginInfo);
    }

    // 发送登出命令
    public sendLogout() {
        this._netService.send(MainProto.RegLogin, RegLogin.CS_LOGIN_OUT_P);
    }

    // 发送获取游戏玩家个数
    public sendGetPlayerCount() {
        this._netService.send(MainProto.Game, Game.CS_GAME_PLAYER_NUM_P);
    }

    // 发送获取游戏版本
    public sendGetGameVersions() {
        this._netService.send(MainProto.RegLogin, RegLogin.CS_REQUEST_GAMEVERSIONS_P, { siteid: DataManager.instance.channelId });
    }

    // 发送获取大厅版本
    public sendGetHallVersion() {
        this._netService.send(MainProto.RegLogin, RegLogin.CS_REQUEST_SERVER_VERSION_P, { siteid: DataManager.instance.channelId });
    }

    // 发送获取手机验证码
    public sendGetSmsCode(phone: string, type: number) {
        //type 0 绑定 1 登录
        this._netService.send(MainProto.RegLogin, RegLogin.CS_REQUEST_REG_PHONECODE_P, { phone: phone, type: type, siteid: DataManager.instance.channelId });
    }

    // 发送重置登录密码
    public sendResetPassword(phone: string, code: string, password: string) {
        this._netService.send(MainProto.FindPsw, FindPsw.CS_FINDPSW_SET_NEW_PSW_P, { phoneNumber: phone, phoneCode: code, password: password, siteid: DataManager.instance.channelId });
    }

    // 发送绑定手机
    public sendBindPhone(phone: string, code: string, password: string) {
        let uniquecode = PlatformHelper.getUUID();
        let version = "1"; // 版本号
        let tmpcode = md5(`#~!${version}##${uniquecode}!!!`);
        let checkcode = md5(`)(*&^${tmpcode}F^ad33`);
        let channelId = DataManager.instance.channelId;
        let isVm = 0; // 是否为虚拟设备
        // 注册
        let registerInfo = {
            accounttype: Constant.CONST_LOGIN_TYPE_NORMAL,
            hdtp: ((cc.sys.platform == cc.sys.ANDROID) ? Constant.CONST_HD_TYPE_ANDROID : Constant.CONST_HD_TYPE_APPLE),
            password: password,
            phoneNumber: phone,
            phoneCode: code,
            hduc: uniquecode,
            version: version,
            checkcode: checkcode,
            hductp: ((cc.sys.platform == cc.sys.ANDROID) ? Constant.CONST_UC_TYPE_ANDROID_ID : Constant.CONST_UC_TYPE_ADID),
            siteid: channelId,
            isvm: isVm,
            sex: 1,
        }
        this._bindingAccount = phone;
        this._bindingPassword = password;
        this._netService.send(MainProto.RegLogin, RegLogin.CS_PHONECODE_REG_P, registerInfo);
    }

    // 发送保险柜存金币操作
    public sendBankSave(money: number) {
        this._netService.send(MainProto.Money, Money.CS_SAVE_MONEY_P, { money: money });
    }

    // 发送保险柜取金币操作
    public sendBankGet(money: number) {
        this._netService.send(MainProto.Money, Money.CS_GET_MONEY_P, { money: money, pwd: "888888" });
    }

    // 发送修改头像操作
    public sendChangeHead(id: number, frameid: number) {
        this._netService.send(MainProto.BaseInfo, BaseInfo.CS_SET_HEADID_P, { id: id, frameid: frameid });
    }

    // 发送修改昵称操作
    public sendChangeNickName(nickname: string) {
        this._netService.send(MainProto.BaseInfo, BaseInfo.CS_SET_NICKNAME_P, { nickname: nickname });
    }

    // 发送获取今日金币排行操作
    public sendGetRankData(type: number, from: number, to: number) {
        var timestamp = new Date().getTime();
        var dataManager = DataManager.instance;
        if (type == Constant.CONST_RANK_TODAY_MONEY) {
            if ((timestamp - dataManager.goldRankSyncInterval) < dataManager.goldRankLastSyncTime &&
                dataManager.rankTodayGoldList && dataManager.rankTodayGoldList.ranks && dataManager.rankTodayGoldList.ranks.length > 0) {
                EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "TODAY_GOLD_RANK_RESULT", dataManager.rankTodayGoldList.ranks);
                return;
            }
        } else if (type == Constant.CONST_RANK_TODAY_EXCHANGE) {
            if ((timestamp - dataManager.exchangeRankSyncInterval) < dataManager.exchangeRankLastSyncTime &&
                dataManager.rankTodayExchangeList) {
                EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "TODAY_EXCHANGE_RANK_RESULT", dataManager.rankTodayExchangeList);
                return;
            }
        } else if (type == Constant.CONST_RANK_TODAY_TIME) {
            if ((timestamp - dataManager.timeRankSyncInterval) < dataManager.timeRankLastSyncTime &&
                dataManager.rankTodayTimeList) {
                EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "TODAY_TIME_RANK_RESULT", dataManager.rankTodayTimeList);
                return;
            }
        }
        console.log('sendGetRankData', type);
        this._netService.send(MainProto.Rank, Rank.CS_RANK_DATA, { type: type, begin: from, end: to });
    }

    // 发送获取自己今日金币排行操作
    public sendGetMyWinToday() {
        this._netService.send(MainProto.Rank, Rank.CS_SELF_RANK_DATA_P);
    }

    // 发送绑定银行卡
    public sendBindBank(bankId: number, bankNum: string, name: string) {
        this._netService.send(MainProto.Money, Money.CD_BIND_BANK_P, { BankName: bankId, BankAccountInfo: "", BankAccountNum: bankNum, BankAccountName: name });
    }

    // 发送兑换
    public sendExchange(money: number, bankId: number, bankaccountnum: string,bankaccountname:string,type:number =0) {
        this._exchangeMoney=money;
        //type 0 金币提现 1 拼多多提现
        this._netService.send(MainProto.Money, Money.CD_MONEY_CHANG_RMB_P, { type:type,money: money,bankname: bankId, bankaccountnum: bankaccountnum, bankaccountname:bankaccountname});
    }



    

    // 请求新邮件数量
    public sendRequestNewMailCount() {
        this._netService.send(MainProto.MailManager, MailManager.CS_REQUEST_NEW_MAIL_COUNT_P);
    }

    // 请求邮件列表
    public sendRequestMailList() {
        console.log('sendRequestMailList');
        this._netService.send(MainProto.MailManager, MailManager.CS_REQUEST_MAILLIST_P);
    }

    // 请求邮件详细内容
    public sendRequestMailDetail(id: number) {
        console.log('sendRequestMailDetail', id);
        this._netService.send(MainProto.MailManager, MailManager.CS_REQUEST_MAIL_INFO_P, { id: id });
    }

    // 设置邮件已读
    public sendMailRead(id: number) {
        let mails = DataManager.instance.mails;
        for (let i = 0; i < mails.length; i++) {
            if (mails[i].id == id) {
                mails[i].readtype = Constant.CONST_MAIL_READY;
                break;
            }
        }
        this.updateMailReadStatus();
        this._netService.send(MainProto.MailManager, MailManager.CS_MAIL_SET_READ_P, { id: id });
    }

    // 删除邮件
    public sendDeleteMail(id: number) {
        let mails = DataManager.instance.mails;
        for (let i = 0; i < mails.length; i++) {
            if (mails[i].id == id) {
                mails.splice(i, 1);
                break;
            }
        }
        this.updateMailReadStatus();
        this._netService.send(MainProto.MailManager, MailManager.CS_DEL_MAIL_INFO_P, { id: id });
    }

    // 请求配置信息
    public sendConfig() {
        this._netService.send(MainProto.RegLogin, RegLogin.CD_REQUEST_SYSTEM_STATUS_P, { siteid: DataManager.instance.channelId, userid: DataManager.instance.userInfo.playerid });
    }

    // 请求充值
    public sendPay(info) {
        //WaitHelper.show(TextTips.TOAST_REQUEST_PAY_DATA);
        this._netService.send(MainProto.Money, Money.CS_PAY, info);
    }

    // 获取客服消息
    public sendCustomServiceMsgList() {
        this._netService.send(MainProto.DBServer, DbServer.CS_CUSTSRV_REPLY_P, { msgsize: 200 });
    }

    // 发送客服消息数据
    public sendCustomServiceMsg(msg,imgpath_) {
        this._netService.send(MainProto.Money, Money.CD_SEND_MSG_GUEST_SERVER_P, { msg: msg,imgpath:imgpath_ });
        DataManager.instance.customServiceMsgList.push({ time: Common.formatDateTimeNormal(), type: "to", content: msg,imgpath:imgpath_ });
    }

    // 请求小游戏房间状态
    public sendRoomInfo(serverid: number, playerid: number) {
        this._netService.send(MainProto.Game, Game.SX_SYNC_ROOMINFO_P, { serverid: serverid, playerid: playerid });
    }

    // 发送获取比赛数据
    public sendMatchList(beginTime:string,endTime:string,gameId:number,status?:number){
        this._netService.send(MainProto.Task, Task.CS_GET_MATCH_LIST,{begintime : beginTime,endtime:endTime,gameid:gameId,status:status});
    }

    // 发送获取今日比赛数据
    public sendToDayMatchList(){
        this._netService.send(MainProto.Task, Task.CS_GET_TODAY_MATCH_LIST,{});
    }
    
    // 发送获取比赛排名和奖励数据
    public sendMatchRankRewardList(recordid:number,reward:number=-1){
        this._netService.send(MainProto.Task, Task.CS_GET_MATCH_RANK_REWARD,{recordid:recordid,reward:reward});
    }

    // 发送获取未读充值消息
    public sendNewChargeList(){
        this._netService.send(MainProto.DBServer, DbServer.CS_NEW_CHARGE_LIST_P,{});
    }

    public sendActivityMessage(subId:number,data:any) {
        this._netService.send(MainProto.HallActivity, subId,data);
    } 


    /**------------------------------------------------印度版本 活动协议 请求 begin--------------------------------------------------------------------------------- */


    // 发送 获取大厅活动
    public sendHallActivitySeverMsgList() {
        this.sendLoginCashReq()
    }

    //login cash
    sendLoginCashReq() {
        this._netService.send(MainProto.HallActivity, HallActivity.CS_LOGINCASH_INFO_P, {});
    }
    //领取 loginCasg
    //fetchtype 0 登录 1 充值 2 游戏局数 3转盘 4 游戏赢分 10 完成 ip 用户ip
    sendLoginCashCollectReq(fetchtype) {
        this._netService.send(MainProto.HallActivity, HallActivity.CS_FETCH_LOGINCASH_AWARD_P, { fetchtype: fetchtype, ip: "" });
    }

    //请求 七人登录信息
    sendSevenDayReq() {
        this._netService.send(MainProto.HallActivity, HallActivity.CS_GET_SEVEN_DAYS_P, {});
    }

    //请求 七人登录领取
    sendSevenDayRewardReq(fetchid) {
        this._netService.send(MainProto.HallActivity, HallActivity.CS_FETCH_SEVEN_DAYS_AWARD_P, { fetchid: fetchid, ip: "" });
    }
    //请求30天刮卡信息
    sendThirtyCardReq() {
        this._netService.send(MainProto.HallActivity, HallActivity.CS_GET_THIRTY_CARD_P, {});
    }

    //发送请求 领取刮卡
    sendFetchThirtyCardReq(fetchtype) {
        this._netService.send(MainProto.HallActivity, HallActivity.CS_FETCH_THIRTY_CARD_P, {fetchtype:fetchtype});
    }

    //大厅功能通用接口
    sendHallFeatureReq(mainId: number, subId: number, data?: any) {
        this._netService.send(mainId, subId, data);
    }


    /**------------------------------------------------印度版本 活动协议 请求 end--------------------------------------------------------------------------------- */


    //////////////////////////////////////////////////////////////////////////////
    // 响应-登陆
    private onLoginResult(result: any) {
        let msg = "";
        this._resetCount = 0;
        // 判断登陆结果的状态
        let code = result.code;
        if (code == Constant.CONST_LOGIN_RESULT_FORBID) {// 账号已被封
            msg = TextTips.ALERT_LOGIN_RESULT_FORBID;
        }
        else if (code == Constant.CONST_LOGIN_RESULT_NO_ACCOUNT) {// 账号不存在
            msg = TextTips.ALERT_LOGIN_RESULT_NO_ACCOUNT;
        }
        else if (code == Constant.CONST_LOGIN_RESULT_PSW_ERROR) {// 密码错误
            msg = TextTips.ALERT_LOGIN_LOGIN_RESULT_PSW_ERROR;
        }
        else if (code != Constant.CONST_LOGIN_RESULT_SUCCESS) {// 登陆失败
            msg = result.msg || TextTips.ALERT_LOGIN_RESULT_ERROR;
        }

        // 是否登陆错误
        if (msg && msg != "") {
            WaitHelper.close();
            AlertHelper.show(msg, () => {
                if (!SceneManager.instance.isRunningScene(SceneName.Logon)) {
                    DataManager.instance.mIsFromLogin = false
                    SceneManager.instance.enterLogonScene();
                }else{
                    EventManager.instance.emit(GameEvent.HALL_LOGIN_FAILD);
                }
            });
            console.log("登录错误----------")
            return;
        }

        // 设置相关数据
        let dataManager = DataManager.instance;
        dataManager.agentExTips = result["agentextips"] || dataManager.agentExTips;
        dataManager.bankExTips = result["bankextips"] || dataManager.bankExTips;
        dataManager.isBindAccount = Common.toInt(result["isbindaccount"]);
        dataManager.regSendMoney = Common.toNumber(result["regsendmoney"]);
        dataManager.officalWebDisplay = Common.toInt(result["officalwebdisplay"]);
        dataManager.showcode = Common.toInt(result["IsShowCode"]);
        dataManager.promotion = Common.toInt(result["promotion"]);
        dataManager.styleId = Common.toInt(result["styleid"]);
        dataManager.homeUrl = result["Url"] || "";
       
        console.log("登录成功，设置游戏数据-------------")
        // 设置游戏类型
        dataManager.setSiteGameList(result["sitegamelist1"]);
        // 设置游戏列表
        dataManager.setGameRoomList(result["gamelist"], this._hallServerAddr);

        // 用户相关数据
        let funcItems = result["Function"];
        dataManager.playerid = Common.toInt(result["playerid"]);
        dataManager.userInfo.loginTime = Common.getCTime();
        if (funcItems && typeof (funcItems) == "object") {
            let userDatas1 = funcItems[6];
            let userDatas2 = funcItems[7];
            dataManager.setUserInfos(userDatas1);
            
            dataManager.setUserInfos(userDatas2);
        }
        // 通知金币信息更新(断线重连时刷新界面金币)
        EventManager.instance.emit(GameEvent.BANKMONEY_CHANGE_NOTICE, dataManager.walletMoney);
        EventManager.instance.emit(GameEvent.MONEY_CHANGE_NOTICE, dataManager.money);



        // 今日赢的金币数
        dataManager.myWinToday = dataManager.userInfo.GameWinAmount;

        // 登陆帐号与随机密码
        let loginParam = result["loginparam"];
        if (loginParam && typeof (loginParam) == "object") {
            let playerid = dataManager.playerid.toString();
            let account = loginParam["account"] || "";
            let password = loginParam["password"] || "";
            let accounttype = loginParam["accounttype"];
            let thridInfo = {
                openid: loginParam["openid"],
                nickname: loginParam["nickname"],
                headimgurl: loginParam["headimgurl"],
                city: loginParam["city"],
                sex: loginParam["sex"]
            };

            if (accounttype == Constant.CONST_LOGIN_TYPE_FAST) {
                account = playerid;
            }
            else if (accounttype == Constant.CONST_LOGIN_TYPE_FACEBOOK) {
                account = loginParam["openid"] || playerid;
            }
            else if (accounttype == Constant.CONST_LOGIN_TYPE_APPLE) {
                account = loginParam["openid"] || playerid;
            }
            dataManager.addAccountInfo(account, password, accounttype, thridInfo);
            dataManager.account = account;
        }
        dataManager.randomCer = result["random"] || "";

        // 是否在游戏中
        let isgame = Common.toInt(result.isgame);
        let serverport = Common.toInt(result.serverport);
        if (isgame == 1 && serverport != 0) {
            this._enterGameRoomInfo = DataManager.instance.getGameRoomInfoByPort(serverport);
            if (this._enterGameRoomInfo) this._isEnterGameRoom = true;
        }
        else {
            this._enterGameRoomInfo = null;
            this._isEnterGameRoom = false;
        }
        // 获取配置信息
        this.sendConfig()
        // 派发事件大厅登录成功
        EventManager.instance.emit(GameEvent.HALL_LOGIN_SUCCESS);
        // 发送获取房间玩家个数
        this.sendGetPlayerCount();
        // 获取游戏版本
        this.sendGetGameVersions();
        // 获取大厅版本
        this.sendGetHallVersion();
        // 获取今日比赛数据
        this.sendToDayMatchList();
        // 获取未读充值信息
        this.sendNewChargeList();
        //请求新邮件
        this.sendRequestNewMailCount();
        //请求客服数据
        this.sendCustomServiceMsgList();
        //大厅请求logincash  
        this.sendHallActivitySeverMsgList();
        //发送事件
        if(result["firstlogin"]==1){
            EventPointHelper.registerEvent();
        }
        EventPointHelper.loginEvent();
    }

    // 响应-别人在别处登陆，你被踢下线
    private onOtherLogin(result: any) {
        AlertHelper.show(TextTips.ALERT_OTHER_LOGIN_TIPS, () => {
            SceneManager.instance.enterLogonScene();
        });
        this.closeServer();
    }

    // 响应-你登陆把别人挤下线(此命令服务器暂未实现)
    private onLoginOther(result: any) {
        AlertHelper.show(TextTips.ALERT_LOGIN_OTHER_TIPS);
    }

    // 响应-通用数据格式错误
    private onNormalReg(result: any) {
        WaitHelper.close();
        if (result && result.code != 0 && result.msg) {
            ToastHelper.show(result.msg);
        }
    }

    // 响应-绑定手机
    private onBindPhone(result: any) {
        WaitHelper.close();
        if (result && result.code == 0) {
            // 设置相关数据
            let dataManager = DataManager.instance;
            dataManager.isBindAccount = 1;
            dataManager.account = this._bindingAccount;
            dataManager.password = this._bindingPassword;
            dataManager.addAccountInfo(this._bindingAccount, this._bindingPassword,
                Constant.CONST_LOGIN_TYPE_NORMAL, {});
            this._bindingAccount = '';
            this._bindingPassword = '';

            ToastHelper.show(result.msg || TextTips.TOAST_UPGRADE_SUCCESS_TIPS);

            EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "UPGRADE_ACCOUNT", result);
        }
        else {
            ToastHelper.show(result.msg || TextTips.TOAST_UPGRADE_FAIL_TIPS);
        }
    }

    // 响应-添加新的游戏房间
    private onAddGameList(result: any) {
        DataManager.instance.addGameRoomInfo(result, this._hallServerAddr);
        EventManager.instance.emit(GameEvent.HALL_UPDATE_ROOMLIST);
    }

    // 响应-删除已关闭的游戏房间
    private onDelGameList(result: any) {
        DataManager.instance.delGameRoomInfo(result.gameid, result.port);
        GameManager.instance.gameRoomClosed(result.gameid, result.port);
        EventManager.instance.emit(GameEvent.HALL_UPDATE_ROOMLIST);
    }

    // 响应-获取游戏玩家个数
    private onGetPlayerCount(result: any) {
        DataManager.instance.setRoomPlayerCount(result);
    }

    // 响应-获取游戏版本
    private onGetGameVersions(result: any) {
        console.log("HallManager获取游戏版本：", JSON.stringify(result))
        if (result.data && typeof (result.data) == "object") {
            let versions = result.data;
            for (let key in versions) {
                let verItem = versions[key];
                let gameItem = DataManager.instance.getGameItemByGameId(verItem.gameid);
                if (gameItem) {
                    let version = verItem.gamever;
                    if (cc.sys.platform == cc.sys.IPHONE || cc.sys.platform == cc.sys.IPAD) {
                        version = verItem.gameverios;
                    }
                    DataManager.instance.setRemoteGameVersion(gameItem.name, version);
                }
            }
        }
    }

    // 响应-获取大厅版本
    private onGetHallVersion(result: any) {
        let channelId = DataManager.instance.channelId;
        let hallVersion = Common.toInt(result[channelId]);
        DataManager.instance.remoteHallVersion = hallVersion;
    }

    // 响应-获取手机验证码
    private onGetSmsCode(result: any) {
        if (result && result.msg) {
            ToastHelper.show(result.msg);
        }
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "GET_SMS_CODE", result);
    }

    // 响应-存入保险柜金币 
    private onSaveMoneyResult(result: any) {
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "WALLET_SAVE_MONEY", result);
    }

    // 响应-取出保险柜金币 
    private onGetMoneyResult(result: any) {
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "WALLET_GET_MONEY", result);
    }

    // 响应-更新用户金币
    private onSetUserMoney(result: any) {
        //console.log("============更新用户金币==============",result)
        if (result && result.hasOwnProperty("money")) {
            DataManager.instance.money = Common.toInt(result.money);
            EventManager.instance.emit(GameEvent.MONEY_CHANGE_NOTICE, result.money);
        }
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "SET_USER_MONEY", result);
    }

    // 响应-更新保险柜金币
    private onSetWalletMoney(result: any) {
        if (result && result.hasOwnProperty("walletmoney")) {
            DataManager.instance.walletMoney = Common.toInt(result.walletmoney);
            EventManager.instance.emit(GameEvent.BANKMONEY_CHANGE_NOTICE, result.walletmoney);
        }
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "SET_WALLET_MONEY", result);
    }

    // 响应-游戏内通知修改金币
    private onSetGameMoney(result: any) {
        if (result && result.hasOwnProperty("totalmoney")) {
            DataManager.instance.money = Common.toInt(result.totalmoney);
            EventManager.instance.emit(GameEvent.MONEY_CHANGE_NOTICE, result.totalmoney);
        }
    }

    // 响应-重置登录密码
    private onResetPassword(result: any) {
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "RESET_PASSWORD", result);
    }

    // 响应-修改头像
    private onSetHeadID(result: any) {
        console.log(result);
        WaitHelper.close();
        if (result && result.code == 0) {
            DataManager.instance.userInfo.headid = result.id;
            DataManager.instance.userInfo.headframeid = result.frameid;
            //清除自定义头像
            DataManager.instance.userInfo.AduitStatus = 0;
            DataManager.instance.userInfo.CustomHeadUrl = "";
            DataManager.instance.userInfo.wxheadurl = "";
            EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "CHANGE_HEAD", result);
        }
    }

    // 响应-修改昵称
    private onChangeNickName(result: any) {
        if (result && result.code == 0) {
            DataManager.instance.userInfo.nickname =  Common.textClamp(result.nickname, 14);
        }
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "CHANGE_NICKNAME", result);
    }

    // 响应-排行榜数据
    private onRankData(result: any) {
        console.log('onRankData', result);
        let dataManager = DataManager.instance;
        if (result.type == Constant.CONST_RANK_TODAY_MONEY) {
            dataManager.goldRankSyncInterval = result.syncInterval / 1000;
            dataManager.goldRankLastSyncTime = result.lastSyncedTime;
            dataManager.updateRankGoldList(result);
            EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "TODAY_GOLD_RANK_RESULT", dataManager.rankTodayGoldList.ranks);
            this.sendGetMyWinToday();
        } else if (result.type == Constant.CONST_RANK_TODAY_TIME) {
            dataManager.timeRankSyncInterval = result.syncInterval / 1000;
            dataManager.timeRankLastSyncTime = result.lastSyncedTime;
            dataManager.updateRankTimeList(result);
            EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "TODAY_TIME_RANK_RESULT", dataManager.rankTodayTimeList);
        } else if (result.type == Constant.CONST_RANK_TODAY_EXCHANGE) {
            dataManager.exchangeRankSyncInterval = result.syncInterval / 1000;
            dataManager.exchangeRankLastSyncTime = result.lastSyncedTime;
            dataManager.updateRankExchangeList(result);
            EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "TODAY_EXCHANGE_RANK_RESULT", dataManager.rankTodayExchangeList);
        }
    }

    // 响应-自己的排行榜数据
    private onSelfRankData(result: any) {
        if (result && result.code == 0) {
            let rank = (DataManager.instance.rankTodayGoldList) ? DataManager.instance.rankTodayGoldList.rankIndex : 0;
            let data = {
                myWinToday: Common.toNumber(result.GameWinCount),
                rank: rank
            };
            EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "TODAY_SELF_GOLD_RANK_RESULT", data);
        }
    }

    // 响应-绑定银行卡
    private onBindBank(result: any) {
        WaitHelper.close();
        if (result && result.code == 0) {
            // 设置相关数据
            let dataManager = DataManager.instance;

            if (result.BankName) {
                dataManager.userInfo.BankName = result.BankName;
            }
            if (result.BankAccountNum) {
                dataManager.userInfo.BankAccountNum = result.BankAccountNum;
            }
            if (result.BankAccountName) {
                dataManager.userInfo.BankAccountName = result.BankAccountName;
            }

            ToastHelper.show(result.msg || '');
            EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "BIND_BANK_SUCCESS", result);
        } else {
            ToastHelper.show(result.msg || '绑定失败');
        }
    }

    // 响应-兑换
    private onExchange(result: any) {
        WaitHelper.close();
        if (result && result.code == 0) {
            let dataManager = DataManager.instance;
            dataManager.userInfo.money = result.money;
            EventManager.instance.emit(GameEvent.MONEY_CHANGE_NOTICE, result.money);
            // 提现成功
            EventPointHelper.exchangeEvent({amount:this._exchangeMoney});
            HallManager.instance.sendHallFeatureReq(MainProto.HallActivity, HallActivity.CS_GET_USER_MONEY_P, {})
            this.sendConfig()
        }
        if(result)
            EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "EXCHANGE_SUCCESS", result);
       

    }

    // 响应-邮件列表
    private onMailList(result: any) {
        console.log('onMailList', result);
        let mailList = [];
        for (let k in result) {
            if (k != 'reconnect') {
                mailList.push(result[k]);
            }
        }
        mailList.sort((a: any, b: any) => {
            return a.time < b.time ?  1 : -1;
        })
        DataManager.instance.mails = mailList;

        this.updateMailReadStatus();
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "MAIL_LIST_SUCCESS", result);
    }

    // 响应-邮件详情
    private onMailDetail(result: any) {
        let mails = DataManager.instance.mails;
        let mail = null;
        for (let i = 0; i < mails.length; i++) {
            if (mails[i].id == result.id) {
                mails[i]['content'] = result.text;
                mail = mails[i];
                break;
            }
        }
        console.log(mail);
        WaitHelper.close();
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "MAIL_DETAIL_SUCCESS", mail);
    }

    // 响应-新邮件
    private onAddMaill(result: any) {
        //服务器会推送同一个邮件多次
        let mails = DataManager.instance.mails;
        let isNew = true
        for (let i = 0; i < mails.length; i++) {
            if (mails[i].id == result.id) {
                isNew  = false
                break;
            }
        }
        if(isNew){
            DataManager.instance.mails.push(result);
            EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA, "ADD_MAIL_SUCCESS", result);
            this.updateMailReadStatus();
        }
    }

    // 响应-请求新邮件数量返回
    private onNewMailCount(result: any) {
        DataManager.instance.newMailCount = result.count;
        EventManager.instance.emit(GameEvent.HALL_UI_UPDATE, "MAIL_RED_DOT", result.count > 0);
    }

    //检查邮件已读状态
    private updateMailReadStatus() {
        let hasUnread = false;
        let mails = DataManager.instance.mails;
        for (let i = 0; i < mails.length; i++) {
            if (mails[i] && mails[i].readtype == Constant.CONST_MAIL_UNREADY) {
                hasUnread = true;
                break;
            }
        }
        EventManager.instance.emit(GameEvent.HALL_UI_UPDATE, "MAIL_RED_DOT", hasUnread);
    }

    // 响应-配置信息
    private onGetConfig(result: any) {
        //DataManager.instance.configs =   result;
        DataManager.instance.setPlayerBindBankConfig(result)
        if (this._isOpenType == 0) {
            this.showPopupByName("ChargePopup");
        }
        else if (this._isOpenType == 1) {
            this.showPopupByName("HallWithDrawCashPopup");
        }
    }

    // 响应-支付接口
    private onSendPay(result: any) {
        WaitHelper.close();
        if (result && result.code == 0 && result.msg) {
            // cc.sys.openURL(result.msg);
            PlatformHelper.openURL(result.msg);
        } else {
            let msg = TextTips.TOAST_REQUEST_PAY_ERR;
            if (result && result.msg) {
                msg = result.msg;
            }
            ToastHelper.show(msg);
        }
    }

    // 响应-通知
    private onNotice(result: any) {
        SceneManager.instance.showNotice(result);
    }

    // 响应-属性修改
    private onAttributeChange(result: any) {
        //console.log("==============响应-属性修改=================",JSON.stringify(result)  )
        if (result.money) {
            DataManager.instance.userInfo.money = result.money;
            DataManager.instance.money = result.money;
            EventManager.instance.emit(GameEvent.MONEY_CHANGE_NOTICE, result.money);
        }
        if (result.charge) {
            let index =this._readyChargeList.indexOf(result.id);
            if(index===-1){
                this._readyChargeList.push(result.id);
                let newChargeList = DataManager.instance.newChargeList;
                newChargeList.push(result);
                SceneManager.instance.showChargeSuccess();
                this.sendNewChargeList();
                DataManager.instance.ischarge = 1 //修改充值状态
                EventManager.instance.emit(GameEvent.ACCOUNT_GAMECHARGE_GET_NOTICE, result);
                //af记录充值成功
                let params = {
                    af_revenue: result.charge,
                    af_currency: "VND"
                };
                //首次充值
                if (result.firstCharge) {
                    EventPointHelper.firstPurchaseEvent({amount:result.charge});
                }
                EventPointHelper.purchaseEvent({amount:result.charge});
                //礼包数据刷新
                HallManager.instance.sendHallFeatureReq(MainProto.HallActivity, HallActivity.CS_GET_GIFT_CHARGE_P, {})
                HallManager.instance.sendHallFeatureReq(MainProto.HallActivity, HallActivity.CS_GET_USER_MONEY_P, {})
            }
            this.sendConfig()
        }
    }

    // 响应-获取客服聊天数据
    private onCustomServiceMsgList(s: any) {

        if (s.IsAutoReply) {
            let data = s.data[1]
            let msg = { time: data.fromtime, type: "from", content: data.fromcontent }
            DataManager.instance.customServiceMsgList.push(msg);
            DataManager.instance.newServiceMsg = 1;
            EventManager.instance.emit(GameEvent.HALL_UI_UPDATE, "CUSTOMSERVICES_RED_DOT", true);
            EventManager.instance.emit(GameEvent.HALL_CUSTOMSERVICE_NEW_MSG_UPDATE, msg);
        } else {
            let msgList = [];
            //msgList.push({ time: Common.formatDateTimeNormal(), type: "from", content: TextTips.UI_EXCHANGE_BIND_CARD_TIPS });
            s.reconnect = "";
            let redDot = false
            let lastTime = DataManager.instance.lastServiceTime;
            for (let key in s) {
                let v = s[key];
                if (v.ToContent && v.ToContent) {
                    msgList.push({ time: v.ToTime, type: "to", content: v.ToContent,imgpath:v.imgpath});
                }
                if (v.FromContent && v.FromContent) {
                    let fromTime = new Date(v.FromTime.replace("T"," ").substring(0,19)).getTime();
                    msgList.push({ time: v.FromTime, type: "from", content: v.FromContent,imgpath:v.imgpath });
                    if (!redDot && lastTime < fromTime) {
                        redDot = true
                    }
                }
            }
            msgList.sort((a: any, b: any) => {
                return a.time < b.time ? -1 : 1;
            })
            DataManager.instance.newServiceMsg = redDot == true ? 1 : 0;
            EventManager.instance.emit(GameEvent.HALL_UI_UPDATE, "CUSTOMSERVICES_RED_DOT", redDot);
            DataManager.instance.customServiceMsgList = msgList;
            EventManager.instance.emit(GameEvent.HALL_CUSTOMSERVICE_LIST_UPDATE, msgList);
        }
    }

    // 响应-发送客服消息数据
    private onCustomServiceMsg(data_) {
        AlertHelper.instance.showAlert (TextTips.TOAST_SEND_CUSTOMSERVICES_TIPS ,AlertLayer.BUTTON_STYLE_1)
        EventManager.instance.emit(GameEvent.HALL_CUSTOMSERVICE_NEW_MSG_UPDATE, data_);
    }

    // 响应-房间信息
    private onRoomInfo(result: any) {
        EventManager.instance.emit(GameEvent.MINI_GAME_UI, result);
    }
    // 响应-比赛状态更新
    private onMatchAttr(result:any) {
        let matchList = DataManager.instance.matchHistoryList;
        let todayList = DataManager.instance.matchTodayHistoryList;
        let matchItem;
        for (let i=0;i<matchList.length;i++) {
            let item= matchList[i];
            if(item.RecordID == result.matchid){
                item.GameID = result.gameid;
                item.Status = result.status;
                item.EndSecond = result.endsecond;
                EventManager.instance.emit(GameEvent.HALL_MATCH_LIST_UPDATE);
                break;
            }
        }
        for (let i=0;i<todayList.length;i++) {
            let item= todayList[i];
            if(item.RecordID == result.matchid){
                item.GameID = result.gameid;
                item.Status = result.status;
                item.EndSecond = result.endsecond;
                matchItem = item;
                break;
            }
        }
        if(!matchItem){
            this.sendToDayMatchList();
        }else{
            if(result.status==2){
                SceneManager.instance.showGameMatch();
            }
            EventManager.instance.emit(GameEvent.GAME_MATCH_UPDATE_STATUS, matchItem);
        }
       
    }

    // 响应-比赛列表数据
    private onMatchList(result:any) {
        let matchList = [];
        let tempList = result.mathdatas||{};
        for (let key in tempList) {
            let v = tempList[key];
            if(v.Status==2){
                matchList.unshift(v);
            }else{
                matchList.push(v);
            }
        }
        let ruleList = {};
        let tempRuleList = result.ruledatas||{};
        for (let key in tempRuleList) {
            let v = tempRuleList[key];
            if(!ruleList[v.RuleID]){
                ruleList[v.RuleID] = [];
            }
            ruleList[v.RuleID].push(v);
        }
        //DataManager.instance.matchRuleList = ruleList;
        if(result.page>1){
            DataManager.instance.matchHistoryList = DataManager.instance.matchHistoryList.concat(matchList);
        }else{
            DataManager.instance.matchHistoryList = matchList;
        }
        if(result.totalCount!=undefined){
            DataManager.instance.matchHistoryCount = Common.toNumber(result.totalCount);
        }else{
            DataManager.instance.matchHistoryCount = matchList.length;
        }
        
        EventManager.instance.emit(GameEvent.HALL_MATCH_LIST_UPDATE, ruleList);
    }
     // 响应-今日比赛列表数据
     private onMatchTodayList(result:any) {
        let matchList = [];
        let tempList = result.mathdatas||{};
        let statusMatch = [];
        let currentTime = new Date().getTime();
        for (let key in tempList) {
            let v = tempList[key];
            if(v.Status==2){
                matchList.unshift(v);
                statusMatch.push(v);
            }else{
                matchList.push(v);
            }
            if(v.Status==3){
                statusMatch.push(v);
            }
        }
        DataManager.instance.matchRuleList = result.ruledatas||{};
        DataManager.instance.matchTodayHistoryList = matchList;
        EventManager.instance.emit(GameEvent.GAME_MATCH_UPDATE_STATUS,statusMatch);
    }
    // 响应-比赛排名数据
    private onMatchRankReward(result:any) {
        let matchRankList = [];
        for (let key in result.rankdatas) {
            let v = result.rankdatas[key];
            matchRankList.push(v);
        }
        let myRank = {};
        for (let key in result.mydatas) {
            myRank = result.mydatas[key];
        }
        EventManager.instance.emit(GameEvent.GAME_MATCH_RANK_UPDATE, matchRankList,myRank);
    }

    // 响应-未读充值消息数据
    private onNewChargeList(result:any){
        let dataList = [];
        for (let key in result.msgdatas) {
            let v = result.msgdatas[key];
            this.onAttributeChange(v);
        }
    }


     /**------------------------------------------------印度版本 活动协议 响应 begin--------------------------------------------------------------------------------- */

     public onGameTask(info:any){
        EventManager.instance.emit(GameEvent.HALL_ACTIVITY_GAME_TASK,info);    
     }
     public onFetchGameTask(info:any){
        EventManager.instance.emit(GameEvent.HALL_ACTIVITY_FETCH_GAME_TASK,info);    
     }
    //  响应-大厅活动 loginCahs
    private onHallActivityLoginCash(severData:any){
        if(severData.user){
            DataManager.instance.loginCashUser = severData
        }
        EventManager.instance.emit(GameEvent.HALL_ACTIVITY_LOGINCASH_UPDATA,severData);    
    }
    //响应-大厅 loginCahs活动 领取
    private onHallActivityLoginCashAward(severData:any){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            //测试 demo
            /*if(Number(severData.fetchtype) ==3){
                severData.code =0
                let reslut_ = [3,5,8,10,12,25]
                severData.fetchaward = reslut_[Common.random(0,5)]
                EventManager.instance.emit(GameEvent.HALL_ACTIVITY_LOGINCASH_AWARD,severData); 
            }*/
            return
        }
        EventManager.instance.emit(GameEvent.HALL_ACTIVITY_LOGINCASH_AWARD,severData); 
    }

    //响应 用户金币信息
    private onHallGetUserMoney(severData:any){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        //刷新数据
        DataManager.instance.bonusmoney = severData.user.bonusmoney
        DataManager.instance.bonuscashmoney = severData.user.bonuscashmoney
        DataManager.instance.money = severData.user.money
        DataManager.instance.userInfo.money = severData.user.money;
        DataManager.instance.winningmoney = severData.user.winningmoney
        //同步vip等级
        DataManager.instance.userInfo.vip = severData.user.vip
        EventManager.instance.emit(GameEvent.MONEY_CHANGE_NOTICE, severData.user.money);
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallBoonMoney, severData); 
    }

    onHallGetUserRewardMoney(severData:any){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallBoonGet, severData); 
    }

    onHallSevenDayAns(severData:any){
        DataManager.instance.sevenDayUser = severData
        EventManager.instance.emit(GameEvent.HALL_ACTIVITY_SEVENDAY_UPDATA,severData); 
    }
  //响应-大厅 sevenDay 领取
    onHallSevenDayRewardAns(severData:any){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_ACTIVITY_SEVENDAY_AWARD,severData); 
    }

    //响应30次挂卡
    onHallThirtyCardAns(severData:any){
        //30天卡
        DataManager.instance.thirtyCardUser = severData
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_ACTIVITY_GET_THIRTY_CARD,severData); 
    }
    //相应 领取挂卡

    onHallThirtyFetchCardAns(severData:any){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg) 
            return
        }
        EventManager.instance.emit(GameEvent.HALL_ACTIVITY_FETCH_THIRTY_CARD,severData); 
    }
    //手机号验证码
    onHallRegPhoneCodeAns(severData:any){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_ACTIVITY_REQ_PHONE_CODE,severData); 
    }
    //礼包活动
    onHallGetGiftChargeAns(severData:any ={}){
        DataManager.instance.setGameLimitGiftInfo(severData.giftdata)
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallGiftGet, severData); 
    }
    //周卡月卡
    onHallGetCardTaskAns(severData:any ={}){
        DataManager.instance.weekAndMonthUser = severData
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallGetCardTask, severData); 
    }
    //领取周卡月卡
    onHallFetchCardTaskAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallFechCardTask, severData); 
    }

    onHallGetVipGiftAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        //这种奇葩 数据结构  -.- -.-
        let showTag = false
        let usertask = severData.usertask[0];
        if(usertask ){
            if (usertask.limitdaysocond == 0 && Number(usertask.dayaward) > 0) {
                showTag = true
            }
            if (usertask.limitweeksocond == 0 && Number(usertask.weekaward) > 0) {
                showTag = true
            }
            if (usertask.limitmonthsocond == 0 && Number(usertask.monthaward) > 0) {
                showTag = true
            }
        }
        DataManager.instance.vipGiftRewardTag = showTag
        DataManager.instance.vipGiftUser = severData
        EventManager.instance.emit(GameEvent.HALL_UI_UPDATE, "VIPGIFT_RED_DOT");
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallGetVipGift, severData); 
    }

    onHallFetchVipGiftAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallFetchVipGift, severData); 
    }

    onHallGetFreeBonumsAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }

        DataManager.instance.freeBonumsUser = severData
        if(severData &&severData.bonustask){    
            EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallGetFreeBonus, severData); 
        }else{
            EventManager.instance.emit(GameEvent.HALL_UI_UPDATE, "HIDE_HALL_FREEBONUS");
        }
    }
    onHallFetchFreeBonumsAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallFetchFreeBonus, severData); 
    }

    //freecash
    onHallGetFreeCashAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        DataManager.instance.setHallFreeCashInfo(severData)
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallGetFreeCashInfo, severData); 
    }

    onHallFetchFreeCashAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallFetchFreeCashInfo, severData); 
    }

    onHallGetFreeCashInvitationAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallFreeCashInvitation, severData); 
    }

    onHallFetchBrokeAwardAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        let brokeInfo = null
        if(severData.brokelist){
            for (const key in severData.brokelist) {
                if (Object.prototype.hasOwnProperty.call(severData.brokelist, key)) {
                    brokeInfo = severData.brokelist[key]
                }
            }
        }
        DataManager.instance.gameBrokeInfo = brokeInfo
        if(SceneManager.instance.isHallScene){
            if(brokeInfo && Number(brokeInfo.fetchcase)>0){
                this.openGameBankruptcyPopup(brokeInfo)
            }
        }else if(severData.fetchaward &&severData.fetchaward>0){
            DataManager.instance.gameBrokeInfo = null
            /*let rewardList = []
            let fetchaward = Common.moneyString(severData.fetchaward)
            let item = { rewardsType: rewardsType.cash, rewardsNum: fetchaward }
            rewardList.push(item)
            HallManager.instance.showGameRewards({ reward: rewardList })*/
        }
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.hallFetchBrokeAward, severData); 
    }

    //邮件
    onHallFetchMailAwardAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA,HallFeatureType.hallFetchMailAward, severData); 
    }

    //绑定手机
    onHallBindPhoneAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        if(severData.fetchaward &&severData.fetchaward>0){
            let rewardList = []
            let fetchaward = Common.moneyString(severData.fetchaward)
            let item = { rewardsType: rewardsType.cash, rewardsNum: fetchaward }
            rewardList.push(item)
            HallManager.instance.showGameRewards({ reward: rewardList })
        }
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA,HallFeatureType.bindPhoneReslut, severData); 
    }

    onHallBindEmailAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_RESPONSE_DATA,HallFeatureType.bindEmialReslut, severData); 
    }

    onHalCdKeyAns(severData:any ={}){
        if(severData.code==1&&severData.msg&&severData.msg.length >0){
            ToastHelper.show(severData.msg)
            return
        }
        EventManager.instance.emit(GameEvent.HALL_GAME_FEATURE_UPDATA,HallFeatureType.bindCdKeyReslut, severData); 
    }
 /**------------------------------------------------印度版本 活动协议 响应 end--------------------------------------------------------------------------------- */


    public getGameNetService(){
        return this._netService
    }
    
    //////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////
    // 绑定事件-服务器连接状态
    private onNetworkStatus(status: number): void {
        if (status == NetStatus.CONNECTED) {
            let serverIndex = this._netService.currServerIndex;
            let hallServers = DataManager.instance.hallServers;
            if (serverIndex >= 0 && serverIndex < hallServers.length) {
                this._hallServerAddr = hallServers[serverIndex].ip;
            }
        }
        else if (status != NetStatus.CONNECTING) {
            let isUpdateScene = SceneManager.instance.isRunningScene(SceneName.Update);
            if (SceneManager.instance.isHallScene || isUpdateScene) {
                // 设置弹出内容
                let content = TextTips.ALERT_HALL_CONNECT_FAILED;
                if (status == NetStatus.CLOSED) {
                    content = TextTips.ALERT_SERVER_DISCONNECT;
                }
                // 关闭加载框
                WaitHelper.close();
                if(this._resetCount<1){
                    this._resetCount++;
                    this.closeServer();
                    this.sendLogin();
                    if (SceneManager.instance.isHallScene) {
                        WaitHelper.show(TextTips.WAIT_HALL_RECONNECT_TIPS);
                    }
                    return;
                }
                // 显示提示框
                let self = this;
                AlertHelper.confirm(content, () => {
                    self.closeServer();
                    self.sendLogin();
                    if (SceneManager.instance.isHallScene) {
                        WaitHelper.show(TextTips.WAIT_HALL_RECONNECT_TIPS);
                    }
                }, () => {
                    if (isUpdateScene) {
                        Common.exitGame();
                    }
                });
            }
        }

        // 派发通知事件
        EventManager.instance.emit(GameEvent.NETWORK_STATUS_NOTICE, status);
    }

    //////////////////////////////////////////////////////////////////////////////

}
