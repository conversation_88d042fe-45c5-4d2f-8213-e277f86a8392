import { Constant, GameEvent, NetStatus } from "../common/Define";
import { MainProto, RegLogin, Game, Money } from "../common/Protocol";
import { TextTips } from "../../frame/common/Language";
import Common from "../common/Common";
import DataManager from "./DataManager";
import EventManager from "./EventManager";
import SceneManager from "./SceneManager";
import UpdateManager from "./UpdateManager";
import NetService from "../model/NetService";
import WaitHelper from "../extentions/WaitHelper";
import AlertHelper from "../extentions/AlertHelper";
import ToastHelper from "../extentions/ToastHelper";
import PlatformHelper from "../extentions/PlatformHelper";
import HallManager from "./HallManager";
import EventPointHelper from "../extentions/EventPointHelper";

//////////////////////////////////////////////////////////////////////////////////
// 游戏管理类
export default class GameManager {
    //////////////////////////////////////////////////////////////////////////////
    // 实例对象-通用游戏
    private static _instance: GameManager = null;
    public static get instance(): GameManager {
        return this._instance || (this._instance = new GameManager(false));
    }

    //////////////////////////////////////////////////////////////////////////////
    // 实例对象-迷你游戏
    private static _miniInstance: GameManager = null;
    public static get miniInstance(): GameManager {
        return this._miniInstance || (this._miniInstance = new GameManager(true));
    }

    //////////////////////////////////////////////////////////////////////////////
    // 构造函数
    public constructor(isMiniGame: boolean) {
        // 使用HallManager的NetService实例来复用大厅连接
        this._netService = null; // 将在需要时从HallManager获取
        this._isMiniGame = isMiniGame;
    }

    //////////////////////////////////////////////////////////////////////////////
    // 迷你游戏对象
    private _isMiniGame: boolean = false;
    // 网络服务对象
    private _netService: NetService = null;
    // 当前游戏内核对象
    private _currGameCore: any = null;
    // 当前可用的房间列表
    private _roomList: Array<any> = [];
    // 当前连接的房间索引
    private _currRoomIndex: number = -1;
    // 连接数已满的房间
    private _fullRooms = new Map<number, boolean>();
    // 保存当前进入的游戏ID
    private _gameId: number = -1;
    // 保存当前登陆信息
    private _loginInfo: any = null;
    // 当前游戏名称
    private _gameBundleName: string = "";
    // 当前否正在加载房间
    private _loadingRoom: boolean = false;
    // 游戏重连次数
    private _resetCount: number = 0;
    // 游戏推出时间
    private _resetHallTime: number = 0;

    //记录房间最大人数
    private _maxPlayerNum: number = 0;

    //////////////////////////////////////////////////////////////////////////////
    // 获取NetService实例（从HallManager复用连接）
    private getNetService(): NetService {
        if (!this._netService) {
            this._netService = HallManager.instance.getNetService();
            if (!this._netService) {
                cc.error("🎮 [GAME_MANAGER] 无法获取HallManager的NetService实例");
                return null;
            }
        }
        return this._netService;
    }

    getCurGameBundleName() {
        return this._gameBundleName
    }
    // 绑定游戏的核心对象
    public bindGameCore(gameCore: any) {
        // 绑定相关操作对象, 并开启游戏
        this._currGameCore = gameCore;
        let netService = this.getNetService();
        if (this._currGameCore && netService) {
            netService.unbindProto(MainProto.Game, Game.SC_ROOM_DEL_PLAYER_P, this.onDeletePlayer, this);
            this._currGameCore.bundleName = this._gameBundleName;
            this._currGameCore.roomInfo = this._roomList[this._currRoomIndex];
            this._currGameCore.userInfo = DataManager.instance.userInfo;
            this._currGameCore.playerid = DataManager.instance.playerid;
            this._currGameCore.loginInfo = this._loginInfo;
            this._currGameCore.netService = netService;
        }

        // 断线时显示重连提示框
        if (netService && !netService.isConnected() && (SceneManager.instance.isGameScene || this._isMiniGame)) {
            let self = this;
            if (this._resetCount < 1) {
                let message2 = Common.formatGameName(TextTips.WAIT_ENTER_GAME_ROOM_TIPS, this._gameBundleName);
                WaitHelper.show(message2);
                netService.connect();
                this._resetCount++;
                return;
            }
            let message = Common.formatGameName(TextTips.ALERT_ROOM_DISCONNECT, this._gameBundleName);
            AlertHelper.confirm(message, () => {
                let message2 = Common.formatGameName(TextTips.WAIT_ENTER_GAME_ROOM_TIPS, this._gameBundleName);
                WaitHelper.show(message2);
                self.getNetService().connect();
            }, () => {
                this.exitGame();
            });
        }
        this._resetHallTime = 0;
    }

    //////////////////////////////////////////////////////////////////////////////
    // 进入游戏房间
    public enterGameRoom(bundleName: string, roomInfo: any) {
        // 当前已有房间正在加载
        if (this._loadingRoom || !roomInfo) {
            return;
        }

        // 设置基本信息
        this._roomList = [];
        this._loadingRoom = true;
        this._gameId = roomInfo.gameid;
        this._gameBundleName = bundleName;
        this._fullRooms = new Map<number, boolean>();

        // 设置当前可用的房间
        let roomList = DataManager.instance.gameRoomList;
        for (let key in roomList) {
            let tmpRoomInfo = roomList[key];
            if (roomInfo.gameid == tmpRoomInfo.gameid
                && roomInfo.orderid == tmpRoomInfo.orderid) {
                this._roomList.push(tmpRoomInfo);
                this._maxPlayerNum = tmpRoomInfo.gamemaxnum
            }
        }

        // 判断进入条件
        let enterMoney = Common.toNumber(roomInfo.money);
        let walletMoney = DataManager.instance.walletMoney;
        let money = DataManager.instance.money;
        if (money < enterMoney) {
            // 不在大厅或游戏场景时直接进入大厅
            if (!(SceneManager.instance.isHallScene || SceneManager.instance.isGameScene)) {
                this.exitGame();
                return;
            }

            // 弹出相关提示
            /*if ((money + walletMoney) > enterMoney) {
                // 提示打开保险柜
                let message = Common.stringFormat(TextTips.ALERT_OPEN_WALLET_TIPS, [Common.moneyString(enterMoney,6,0)]);
                AlertHelper.confirm(message, () => {
                    EventManager.instance.emit(GameEvent.HALL_POPUP_FUNCTION, "WALLET_MONEY");
                }, null);
            }
            else {
                // 提示打开充值中心
                let message = Common.stringFormat(TextTips.ALERT_OPEN_PAY_TIPS, [Common.moneyString(enterMoney,6,0)]);
                AlertHelper.confirm(message, () => {
                    EventManager.instance.emit(GameEvent.HALL_POPUP_FUNCTION, "PAY_CENTER");
                }, null);
            }
            */
            /*let message = Common.stringFormat(TextTips.ALERT_OPEN_PAY_TIPS, [Common.moneyString(enterMoney,6,0)]);
                AlertHelper.confirm(message, () => {
                    EventManager.instance.emit(GameEvent.HALL_POPUP_FUNCTION, "PAY_CENTER");
                }, null);*/

            HallManager.instance.openHallQuicklyAddCashPopup({ payNum: enterMoney });
            // 清理数据并返回
            this.clearGameData();
            return;
        }

        // 注册协议回调接口
        let netService = this.getNetService();
        if (!netService) {
            cc.error("🎮 [GAME_MANAGER] 无法获取NetService，取消进入游戏");
            this.clearGameData();
            return;
        }

        // 注意：不清除所有事件，因为这会影响大厅的协议绑定
        // 只绑定游戏相关的协议
        netService.bindProto(MainProto.RegLogin, RegLogin.SC_GAMESERVER_LOGIN_P, this.onGameLoginResult, this);
        netService.bindProto(MainProto.RegLogin, RegLogin.SC_SERVER_STOP_P, this.onServerStop, this);
        netService.bindProto(MainProto.RegLogin, RegLogin.SC_FULLCONNECT_ATTACK_P, this.onServerFull, this);
        netService.bindProto(MainProto.RegLogin, RegLogin.SC_GAME_SERVER_VERSION_P, this.onGetGameVersion, this);
        netService.bindProto(MainProto.Game, Game.SC_MODE1_ENTER_P, this.onEnterGame, this);
        netService.bindProto(MainProto.Game, Game.SC_ROOM_DEL_PLAYER_P, this.onDeletePlayer, this);
        netService.bindProto(MainProto.Money, Money.SC_SET_GAME_MONEY_P, this.onSetGameMoney, this);

        // 绑定相关回调接口
        netService.bindEvent(GameEvent.NETWORK_STATUS_NOTICE, this.onNetworkStatus, this);

        // 提示等待操作并开始连接服务器
        let message = Common.formatGameName(TextTips.WAIT_ENTER_GAME_ROOM_TIPS, this._gameBundleName);
        if (!this._isMiniGame) {
            SceneManager.instance.preloadGameScene(this._gameBundleName);
        }
        WaitHelper.show(message, () => {
            this.clearGameData();
        });
        this.connectServer();
    }
    // 获取当前游戏是否正在比赛
    public getMatchInfo(): any {
        if (!this._currGameCore) {
            return null;
        }
        let roomInfo = this._roomList[this._currRoomIndex];
        let gameMatchList = DataManager.instance.matchTodayHistoryList;
        let serverMatch = gameMatchList.filter(gm => gm.GameID == this._gameId);
        let matchIndex = serverMatch.findIndex(gm => gm.ServerID == roomInfo.serverid && gm.Status == 2);
        if (matchIndex > -1) {
            return serverMatch[matchIndex];
        }
        return null;
    }

    // 游戏房间已关闭
    public gameRoomClosed(gameid: number, port: number) {
        let roomInfo = this._roomList[this._currRoomIndex];
        if (roomInfo && roomInfo.gameid == gameid && roomInfo.port == port) {
            let message = Common.formatGameName(TextTips.ALERT_ROOM_CLOSED, this._gameBundleName);
            AlertHelper.show(message, () => {
                this.exitGame();
            }, true);
        }
    }

    // 清理游戏相关数据
    public clearGameData() {

        // 只离开游戏Channel，不关闭大厅连接
        let netService = this.getNetService();
        if (netService) {
            netService.leaveGameRoom();
            // 解绑游戏相关的协议，但保留大厅协议
            netService.unbindProto(MainProto.RegLogin, RegLogin.SC_GAMESERVER_LOGIN_P, this.onGameLoginResult, this);
            netService.unbindProto(MainProto.RegLogin, RegLogin.SC_SERVER_STOP_P, this.onServerStop, this);
            netService.unbindProto(MainProto.RegLogin, RegLogin.SC_FULLCONNECT_ATTACK_P, this.onServerFull, this);
            netService.unbindProto(MainProto.RegLogin, RegLogin.SC_GAME_SERVER_VERSION_P, this.onGetGameVersion, this);
            netService.unbindProto(MainProto.Game, Game.SC_MODE1_ENTER_P, this.onEnterGame, this);
            netService.unbindProto(MainProto.Game, Game.SC_ROOM_DEL_PLAYER_P, this.onDeletePlayer, this);
            netService.unbindProto(MainProto.Money, Money.SC_SET_GAME_MONEY_P, this.onSetGameMoney, this);
            netService.unbindEvent(GameEvent.NETWORK_STATUS_NOTICE, this.onNetworkStatus, this);
        }

        this._fullRooms = new Map<number, boolean>();
        this._loadingRoom = false;
        this._gameBundleName = "";
        this._currGameCore = null;
        this._resetCount = 0;
        this._roomList = [];
    }

    //返回大厅或者关闭小游戏
    public exitGame(info?) {
        if (this._isMiniGame) {
            SceneManager.instance.closeMiniGame();
        } else {
            SceneManager.instance.enterHallScene(info);
        }
    }

    //进入游戏或者打开小游戏
    public enterGame() {
        if (this._isMiniGame) {
            SceneManager.instance.openMiniGame(this._gameBundleName);
        } else {
            SceneManager.instance.enterGameScene(this._gameBundleName);
        }
    }

    public setRecExitTime(time) {
        this._resetHallTime = time;
    }
    //////////////////////////////////////////////////////////////////////////////
    // 开始连接服务器 - 使用Phoenix Channel而不是新的WebSocket连接
    private connectServer() {
        // 获取当前可用的房间
        let roomInfo = null;
        for (let idx = 0; idx < this._roomList.length; ++idx) {
            let tmpRoomInfo = this._roomList[idx];
            if (!this._fullRooms.has(idx)) {
                roomInfo = tmpRoomInfo;
                this._currRoomIndex = idx;
                break;
            }
        }

        if (!roomInfo) {
            // 关闭加载框
            WaitHelper.close();
            // 提示房间人数已满
            AlertHelper.show(Common.formatGameName(TextTips.ALERT_GAME_ROOM_FULL, this._gameBundleName), () => {
                if (SceneManager.instance.isGameScene) {
                    this.exitGame();
                }
                else {
                    if (this._isMiniGame) {
                        SceneManager.instance.closeMiniGame();
                    } else {
                        this.clearGameData();
                    }
                }
            })
            return;
        }

        cc.log(`🎮 [GAME_CONNECT] 连接到游戏房间: GameID=${roomInfo.gameid}, ServerID=${roomInfo.serverid}`);

        // 获取NetService并检查大厅连接状态
        let netService = this.getNetService();
        if (!netService) {
            cc.error("🎮 [GAME_CONNECT] 无法获取NetService实例");
            this.onNetworkStatus(NetStatus.FAILED);
            return;
        }

        if (!netService.isConnected()) {
            cc.error("🎮 [GAME_CONNECT] 大厅连接已断开，无法连接游戏房间");
            this.onNetworkStatus(NetStatus.FAILED);
            return;
        }

        // 使用Phoenix Channel连接到游戏房间
        netService.connectToGameRoom(roomInfo.gameid, roomInfo.serverid)
            .then((success) => {
                if (success) {
                    cc.log("🎮 [GAME_CONNECT] 游戏房间连接成功");
                    // 模拟连接成功事件
                    this.onNetworkStatus(NetStatus.CONNECTED);
                } else {
                    cc.error("🎮 [GAME_CONNECT] 游戏房间连接失败");
                    this.onNetworkStatus(NetStatus.FAILED);
                }
            })
            .catch((error) => {
                cc.error("🎮 [GAME_CONNECT] 游戏房间连接异常:", error);
                this.onNetworkStatus(NetStatus.FAILED);
            });
    }

    // 关闭游戏服务 - 只离开游戏Channel，保持大厅连接
    private closeServer() {
        cc.log("🎮 [GAME_DISCONNECT] 离开游戏房间Channel");
        let netService = this.getNetService();
        if (netService) {
            netService.leaveGameRoom();
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    // 发送游戏登陆请求
    private sendGameLogin() {
        let uniquecode = PlatformHelper.getUUID();
        let playerid = DataManager.instance.playerid;;
        let randonCer = DataManager.instance.randomCer;
        let channelId = DataManager.instance.channelId;
        let loginInfo = {
            hdtp: ((cc.sys.platform == cc.sys.ANDROID) ? Constant.CONST_HD_TYPE_ANDROID : Constant.CONST_HD_TYPE_APPLE),
            random: randonCer,
            hduc: uniquecode,
            hductp: ((cc.sys.platform == cc.sys.ANDROID) ? Constant.CONST_UC_TYPE_ANDROID_ID : Constant.CONST_UC_TYPE_ADID),
            siteid: channelId,
            playerid: playerid
        }

        // 发送登陆消息
        let netService = this.getNetService();
        if (netService) {
            netService.send(MainProto.RegLogin, RegLogin.CS_GAMESERVER_LOGIN_P, loginInfo);
        }
    }

    // 发送获取游戏版本
    public sendGetGameVersion() {
        let netService = this.getNetService();
        if (netService) {
            netService.send(MainProto.RegLogin, RegLogin.CS_REQUEST_SERVER_VERSION_P, { siteid: DataManager.instance.channelId });
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    // 响应-登陆结果
    private onGameLoginResult(result: any) {
        this._fullRooms.delete(this._currRoomIndex);
        cc.log("游戏登陆结果:", result);
        if (result.code == 0) {
            this._loginInfo = result;
            this._resetCount = 0;
            if (Common.toInt(result.offline) == 1) {
                if (this._currGameCore) {
                    // ToastHelper.show(TextTips.TOAST_GAME_RECONNECT_SUCCESS);
                    this._currGameCore.sendMatchMessage();
                    WaitHelper.close();
                    return;
                }
                this.enterGame();
            }
            else {
                let netService = this.getNetService();
                if (netService) {
                    netService.send(MainProto.Game, Game.CS_MODE1_ENTER_P);
                }
            }
        }
        else {
            WaitHelper.close();
            if (SceneManager.instance.isGameScene) {
                AlertHelper.show(result.msg, () => {
                    this.exitGame();
                }, true);
            }
            else {
                if (!SceneManager.instance.isHallScene) {
                    this.exitGame();
                }
                else if (this._isMiniGame) {
                    SceneManager.instance.closeMiniGame();
                    ToastHelper.show(result.msg);
                } else {
                    ToastHelper.show(result.msg);
                }
                this.clearGameData();
            }
            this.closeServer();
        }
    }

    // 响应-游戏房间关闭
    private onServerStop(result: any) {
        this.closeServer();
        if (result.str && result.str != "") {
            AlertHelper.show(result.str, () => {
                this.exitGame();
            })
        }
        else {
            this.exitGame();
        }
    }

    // 响应-游戏房间已满
    private onServerFull(result: any) {
        this.closeServer();
        this._fullRooms.set(this._currRoomIndex, true);
        this.connectServer();
    }

    // 响应-获取游戏版本
    private onGetGameVersion(result: any) {
        if (result && result.gameid == this._gameId) {
            let version = result.gamever;
            if (cc.sys.platform == cc.sys.IPHONE || cc.sys.platform == cc.sys.IPAD) {
                version = result.gameverios;
            }
            DataManager.instance.setRemoteGameVersion(this._gameBundleName, version);
        }

        // 判断游戏是否有更新
        if (DataManager.instance.checkGameHasUpdate(this._gameBundleName)) {
            let message = Common.formatGameName(TextTips.TOAST_GAME_HAS_UPDATE, this._gameBundleName);

            WaitHelper.close();
            if (SceneManager.instance.isGameScene) {
                AlertHelper.show(message, () => {
                    UpdateManager.instance.startGameUpdate(this._gameBundleName);
                    SceneManager.instance.releaseGameRes();
                    SceneManager.instance.releaseMiniGameRes();
                    this.exitGame();
                })
            }
            else {
                ToastHelper.show(message);
                UpdateManager.instance.startGameUpdate(this._gameBundleName);
                SceneManager.instance.releaseGameRes();
                SceneManager.instance.releaseMiniGameRes();
                this.clearGameData();
                if (this._isMiniGame) {
                    SceneManager.instance.closeMiniGame();
                }
            }
            return;
        }

        // 发送登陆协议
        this.sendGameLogin();
    }

    // 响应-进入游戏房间
    private onEnterGame(result: any) {
        if (result.code != 0) {
            ToastHelper.show(Common.formatGameName(TextTips.TOAST_ENTER_GAME_ROOM_FAILED, this._gameBundleName));
            this.clearGameData();
            WaitHelper.close();
            return;
        }

        if (this._currGameCore) {
            // ToastHelper.show(TextTips.TOAST_GAME_RECONNECT_SUCCESS);
            this._currGameCore.sendMatchMessage();
            WaitHelper.close();
            return;
        }

        // HallManager.instance.stopHallAllTimerManager();

        this.enterGame();
        // EventPointHelper.enterGameEvent(this._gameBundleName);
    }

    // 响应-游戏结束,你条件不满足被踢出房间,如果你在暂离状态,也会被踢出房间
    private onDeletePlayer(info: any) {
        if (!this._currGameCore) {
            this.exitGame();
        }
    }

    // 响应-游戏内通知修改金币(游戏结束时通知)
    private onSetGameMoney(info: any) {
        if (info && info.hasOwnProperty("totalmoney")) {
            DataManager.instance.money = Common.toInt(info.totalmoney);
            EventManager.instance.emit(GameEvent.MONEY_CHANGE_NOTICE, info.totalmoney);
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////
    // 绑定事件-服务器连接状态
    private onNetworkStatus(status: number): void {
        if (status == NetStatus.CONNECTED) {
            if (DataManager.instance.enableUpdate) {
                // 发送获取游戏版本
                this.sendGetGameVersion();
            }
            else {
                // 发送登陆协议
                this.sendGameLogin();
            }
        }
        else if (status != NetStatus.CONNECTING) {
            // 关闭加载框
            WaitHelper.close();

            // 是否在游戏场景内
            if (SceneManager.instance.isGameScene) {
                // 设置弹出内容
                let isRoomClosed = false;
                let content = TextTips.ALERT_GAME_CONNECT_FAILED;
                if (status == NetStatus.CLOSED) {
                    let roomInfo = this._roomList[this._currRoomIndex];
                    if (DataManager.instance.getGameRoomInfoByPort(roomInfo.port)) {
                        content = TextTips.ALERT_ROOM_DISCONNECT;
                    }
                    else {
                        content = TextTips.ALERT_ROOM_CLOSED;
                        isRoomClosed = true;
                    }
                }

                // 显示提示框
                let self = this;
                let message = Common.formatGameName(content, this._gameBundleName);
                if (isRoomClosed) {
                    AlertHelper.show(message, () => {
                        this.exitGame();
                    }, true);
                }
                else {
                    let curTime = Common.getCTime();
                    let leaveTime = curTime - DataManager.instance.lastEventHideTime;

                    if (DataManager.instance.lastEventHideTime != 0) {
                        DataManager.instance.lastEventHideTime = 0;

                        if (this._resetHallTime != 0 && leaveTime > this._resetHallTime) {
                            setTimeout(() => {
                                this.exitGame({ reason: 3 });
                            }, 500);
                            return;
                        }
                    }

                    if (this._resetCount < 5) {
                        this._resetCount++;
                        let message2 = Common.formatGameName(TextTips.WAIT_ENTER_GAME_ROOM_TIPS, this._gameBundleName);
                        WaitHelper.show(message2);
                        // this._netService.connect();
                        this._netService.startReconnect();
                        return;
                    }
                    // AlertHelper.confirm(message, () => {
                    //     let message2 = Common.formatGameName(TextTips.WAIT_ENTER_GAME_ROOM_TIPS, self._gameBundleName);
                    //     WaitHelper.show(message2);
                    //     self._netService.connect();
                    // }, () => {
                    //     this.exitGame();
                    // });
                    setTimeout(() => {
                        this.exitGame({ reason: 3 });
                    }, 500);
                }
            }
            else {
                ToastHelper.show(Common.formatGameName(TextTips.TOAST_GAME_CONNECT_FAILED, this._gameBundleName));
                if (this._isMiniGame) {
                    SceneManager.instance.closeMiniGame();
                } else {
                    GameManager.instance.clearGameData();
                }
            }
        }
    }

    //////////////////////////////////////////////////////////////////////////////

    // 获取房间最大人数
    public getRoomMaxPlayer() {
        return this._maxPlayerNum;
    }
}
