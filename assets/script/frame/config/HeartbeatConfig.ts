//////////////////////////////////////////////////////////////////////////////////
// 心跳配置管理类
// 用于统一管理客户端的心跳机制设置

export default class HeartbeatConfig {
    //////////////////////////////////////////////////////////////////////////////
    // 应用层心跳开关 - 控制NetService中的自定义心跳
    public static ENABLE_APP_HEARTBEAT: boolean = false;

    // Phoenix层心跳开关 - 控制Phoenix Socket的内置心跳
    public static ENABLE_PHOENIX_HEARTBEAT: boolean = true;

    // 应用层心跳间隔 (毫秒)
    public static APP_HEARTBEAT_INTERVAL: number = 5000;

    // Phoenix层心跳间隔 (毫秒) - 增加到30秒，减少心跳频率和超时风险
    public static PHOENIX_HEARTBEAT_INTERVAL: number = 30000;

    //////////////////////////////////////////////////////////////////////////////
    // 启用所有心跳机制
    public static enableAllHeartbeat(): void {
        this.ENABLE_APP_HEARTBEAT = true;
        this.ENABLE_PHOENIX_HEARTBEAT = true;
        cc.log("所有心跳机制已启用");
    }

    // 禁用所有心跳机制
    public static disableAllHeartbeat(): void {
        this.ENABLE_APP_HEARTBEAT = false;
        this.ENABLE_PHOENIX_HEARTBEAT = false;
        cc.log("所有心跳机制已禁用");
    }

    // 仅启用应用层心跳
    public static enableAppHeartbeatOnly(): void {
        this.ENABLE_APP_HEARTBEAT = true;
        this.ENABLE_PHOENIX_HEARTBEAT = false;
        cc.log("仅启用应用层心跳");
    }

    // 仅启用Phoenix层心跳
    public static enablePhoenixHeartbeatOnly(): void {
        this.ENABLE_APP_HEARTBEAT = false;
        this.ENABLE_PHOENIX_HEARTBEAT = true;
        cc.log("仅启用Phoenix层心跳");
    }

    // 获取当前心跳状态信息
    public static getHeartbeatStatus(): string {
        const appStatus = this.ENABLE_APP_HEARTBEAT ? `启用(${this.APP_HEARTBEAT_INTERVAL}ms)` : "禁用";
        const phoenixStatus = this.ENABLE_PHOENIX_HEARTBEAT ? `启用(${this.PHOENIX_HEARTBEAT_INTERVAL}ms)` : "禁用";

        return `心跳状态 - 应用层: ${appStatus}, Phoenix层: ${phoenixStatus}`;
    }
}
