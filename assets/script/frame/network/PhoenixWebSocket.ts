//////////////////////////////////////////////////////////////////////////////////
// Phoenix WebSocket网络操作类
const Phoenix = require('../external/phoenix/index.js');
import HeartbeatConfig from "../config/HeartbeatConfig";
import DataManager from "../manager/DataManager";
// import * as msgpack from "msgpack-lite"
export default class PhoenixWebSocketClient {
    //////////////////////////////////////////////////////////////////////////////
    // Phoenix心跳开关配置已移至HeartbeatConfig统一管理

    // Phoenix Socket对象
    private _socket: any = null;
    // Phoenix Channel对象
    private _channel: any = null;
    // 待发送数据队列
    private _waitSendData = [];
    // 当前连接地址
    private _url: string = '';
    // 回调事件对象
    private _eventTarget: any = null;
    // 是否已经连接
    private _isConnected: boolean = false;
    // 是否主动关闭
    private _takeClose: boolean = false;
    // 主题名称
    private _topic: string = "game:lobby";
    // 游戏房间Channel（用于游戏内通信）
    private _gameChannel: any = null;
    // 当前游戏房间主题
    private _gameRoomTopic: string = "";

    //////////////////////////////////////////////////////////////////////////////
    constructor(target: any) {
        this._eventTarget = target;
    }

    //////////////////////////////////////////////////////////////////////////////
    // 连接远程服务器
    public connect(url: string, pemfile: string = ''): void {
        // 检查是否已经有连接，避免重复连接
        if (this._socket) {
            cc.warn("🔴 [PHOENIX_WARNING] Socket已存在，先关闭现有连接");
            this.close();
        }

        // 解析URL，Phoenix库需要区分endpoint和params
        this._url = url;
        let account = DataManager.instance.account;
        let accountInfo = DataManager.instance.getAccountInfo(account);

        const socketOptions = {
            // 设置二进制类型为ArrayBuffer
            binaryType: "arraybuffer",
            // 设置消息超时时间为30秒（默认10秒太短）
            timeout: 30000,
            // 根据开关设置Phoenix层面的心跳机制
            heartbeatIntervalMs: HeartbeatConfig.ENABLE_PHOENIX_HEARTBEAT ? HeartbeatConfig.PHOENIX_HEARTBEAT_INTERVAL : 0,
            // 设置重连策略
            reconnectAfterMs: (tries: number) => {
                return [1000, 2000, 5000, 10000][tries - 1] || 10000;
            },
            // 设置日志记录器
            logger: (kind: string, msg: string, data: any) => {
                if (CC_DEBUG) {
                    // cc.log(`Phoenix ${kind}: ${msg}`, data);
                }
            },
            // userToken: accountInfo.userToken,
            // encode(msg, callback) {
            //     // 将消息编码为MessagePack格式
            //     const data = {
            //         join_ref: msg.join_ref,
            //         ref: msg.ref,
            //         topic: msg.topic,
            //         event: msg.event,
            //         payload: msg.payload
            //     }

            //     const encoded = msgpack.encode(data)
            //     callback(encoded)
            // },
            // decode(rawPayload, callback) {
            //     // 解码MessagePack数据
            //     const decoded = msgpack.decode(new Uint8Array(rawPayload))
            //     callback(decoded)
            // },
            "vsn": "2.0.0",
        };

        // 创建Phoenix Socket
        this._socket = new Phoenix.Socket(url, socketOptions);

        // 记录心跳状态
        if (HeartbeatConfig.ENABLE_PHOENIX_HEARTBEAT) {
            cc.log("Phoenix心跳已启用，间隔:", HeartbeatConfig.PHOENIX_HEARTBEAT_INTERVAL, "ms");
        } else {
            cc.log("Phoenix心跳已屏蔽");
        }

        // 绑定Socket事件
        this._socket.onOpen(() => {
            this._onOpen(new Event('open'))
        });
        this._socket.onClose((event: any) => this._onClose(new CloseEvent('close')));
        this._socket.onError((error: any) => this._onError(new Event('error')));

        // 连接到服务器
        this._socket.connect();

        this._isConnected = false;
        this._takeClose = false;
    }

    // 关闭连接
    public close(): void {
        if (this._socket) {
            this._eventTarget = null;
            this._isConnected = false;
            this._takeClose = true;

            // 如果有游戏channel，先离开
            if (this._gameChannel) {
                this._gameChannel.leave();
                this._gameChannel = null;
            }

            // 如果有大厅channel，先离开
            if (this._channel) {
                this._channel.leave();
                this._channel = null;
            }

            // 断开socket连接
            this._socket.disconnect();
            this._socket = null;
        }
    }

    // 是否已连接?
    public isConnected(): boolean {
        if (!this._socket) {
            return false;
        }
        return this._isConnected;
    }

    // 是否正在连接?
    public isConnecting(): boolean {
        if (!this._socket) {
            return false;
        }
        return this._socket.isConnecting();
    }

    // 连接到游戏房间Channel
    public connectToGameRoom(gameId: number, serverId: number): Promise<boolean> {
        return new Promise((resolve, reject) => {
            cc.log(`🎮 [GAME_CHANNEL] 尝试连接游戏房间: GameID=${gameId}, ServerID=${serverId}`);
            cc.log(`🎮 [GAME_CHANNEL] Socket状态: socket=${!!this._socket}, connected=${this._isConnected}`);

            if (!this._socket || !this._isConnected) {
                cc.error("🎮 [GAME_CHANNEL] 无法连接游戏房间: Socket未连接");
                resolve(false);
                return;
            }

            // 如果已经有游戏Channel，先离开
            if (this._gameChannel) {
                this._gameChannel.leave();
                this._gameChannel = null;
            }

            // 创建游戏房间主题
            this._gameRoomTopic = `game:room_${gameId}_${serverId}`;
            cc.log(`🎮 [GAME_CHANNEL] 连接到游戏房间: ${this._gameRoomTopic}`);

            // 创建游戏房间Channel
            this._gameChannel = this._socket.channel(this._gameRoomTopic, {});

            // 绑定游戏Channel事件
            this._gameChannel.on("message", (payload: any) => {
                if (CC_DEBUG) {
                    cc.log("🎮 [GAME_MESSAGE] 收到游戏消息:", payload);
                }
                if (this._eventTarget) {
                    this._eventTarget.onMessage(payload);
                }
            });

            // 加入游戏Channel
            this._gameChannel.join()
                .receive("ok", () => {
                    cc.log(`🎮 [GAME_CHANNEL] 成功加入游戏房间: ${this._gameRoomTopic}`);
                    resolve(true);
                })
                .receive("error", (err: any) => {
                    cc.error(`🎮 [GAME_CHANNEL] 加入游戏房间失败:`, err);
                    this._gameChannel = null;
                    this._gameRoomTopic = "";
                    resolve(false);
                });
        });
    }

    // 离开游戏房间Channel
    public leaveGameRoom(): void {
        if (this._gameChannel) {
            cc.log(`🎮 [GAME_CHANNEL] 离开游戏房间: ${this._gameRoomTopic}`);
            this._gameChannel.leave();
            this._gameChannel = null;
            this._gameRoomTopic = "";
        }
    }

    // 发送数据
    public send(msg: object) {
        if (!this._socket || !msg || this._takeClose) {
            if (this._eventTarget) {
                this._eventTarget.onSendFail();
            }
            return;
        }

        // 确定使用哪个Channel发送消息
        let targetChannel = this._gameChannel || this._channel;
        let channelType = this._gameChannel ? "GAME" : "LOBBY";

        // 检查Socket是否已连接
        if (this._isConnected && targetChannel) {
            // 记录发送的消息和超时设置
            if (CC_DEBUG) {
                cc.log(`🔵 [CLIENT_SEND_${channelType}] 发送消息，超时设置: ${this._socket.timeout}ms, 消息:`, msg);
            }

            // 使用Phoenix Channel推送消息，明确传递超时时间
            targetChannel.push("message", msg, this._socket.timeout)
                .receive("ok", (response: any) => {
                    if (CC_DEBUG) {
                        cc.log(`🟢 [CLIENT_RECEIVE_OK_${channelType}] 收到成功响应:`, response);
                    }
                    if (this._eventTarget) {
                        this._eventTarget.onMessage(response);
                    }
                })
                .receive("error", (err: any) => {
                    cc.error(`🔴 [CLIENT_RECEIVE_ERROR_${channelType}] 发送消息失败:`, err);
                    if (this._eventTarget) {
                        this._eventTarget.onSendFail();
                    }
                })
                // .receive("timeout", () => {
                //     cc.warn(`🔴 [CLIENT_TIMEOUT_${channelType}] 发送消息超时 (${this._socket.timeout}ms), 消息:`, msg);
                //     if (this._eventTarget) {
                //         this._eventTarget.onSendFail();
                //     }
                // });
        } else if (this._socket.isConnecting()) {
            // 存储待发送的消息
            this._waitSendData.push(msg);
        } else {
            cc.log(this._url, "发送消息失败: 网络已经关闭");
            if (this._eventTarget) {
                this._eventTarget.onSendFail();
            }
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    // 连接监听
    private _onOpen(e: Event) {
        cc.log("Phoenix WebSocket 连接已打开");

        // 检查Socket是否仍然有效
        if (!this._socket) {
            cc.error("🔴 [PHOENIX_ERROR] _onOpen被调用但_socket为null，可能是重复连接或竞态条件");
            return;
        }

        // 检查是否已经有Channel了，避免重复创建
        if (this._channel) {
            cc.warn("🔴 [PHOENIX_WARNING] Channel已存在，跳过重复创建");
            return;
        }

        // 连接到Channel
        this._channel = this._socket.channel(this._topic, {});

        // 绑定Channel事件 - 只监听"message"事件，避免接收Phoenix的内部消息
        this._channel.on("message", (payload: any) => {
            cc.log("收到消息:", payload);
            if (this._eventTarget) {
                this._eventTarget.onMessage(payload);
            }
        });

        // 加入Channel
        this._channel.join()
            .receive("ok", () => {
                cc.log("成功加入Channel", this._topic);
                this._isConnected = true;

                // 处理待发送的消息
                if (this._waitSendData.length > 0) {
                    for (let i = 0; i < this._waitSendData.length; ++i) {
                        this.send(this._waitSendData[i]);
                    }
                    this._waitSendData = [];
                }

                // 触发onOpen回调
                if (this._eventTarget) {
                    this._eventTarget.onOpen(e);
                }
            })
            .receive("error", (err: any) => {
                cc.error("加入Channel失败:", err);
                this._onError(new Event('error'));
            });
    }

    // 消息监听 - Phoenix库会在_onOpen中通过Channel.on处理

    // 关闭监听
    private _onClose(e: CloseEvent) {
        if (this._eventTarget && !this._takeClose) {
            this._eventTarget.onClose(e);
        }
        this._waitSendData = [];
        this._channel = null;
        this._gameChannel = null;
        this._gameRoomTopic = "";
        this._socket = null;
        this._isConnected = false;
    }

    // 错误监听
    private _onError(e: Event) {
        if (this._eventTarget) {
            this._eventTarget.onClose(e); // 错误情况下也触发关闭回调
        }
    }

    //////////////////////////////////////////////////////////////////////////////
}
