# 游戏配置文件
# 在这里配置要启用的游戏类型

import Config

# 配置要注册的游戏模块
config :cypridina, :games,
  # 内置游戏（自动注册）
  builtin: [
    # Cypridina.Teen.GameSystem.Games.LongHu.LongHuGame,
    # Cy<PERSON>ridina.Teen.GameSystem.Games.TeenPatti.TeenPattiGame,
    Cypridina.Teen.GameSystem.Games.Slot777.Slot777Game
  ],

  # 可选游戏（需要手动启用）
  optional: [
    # Cypridina.Teen.GameSystem.Games.Baccarat.BaccaratGame,
    # Cypridina.Teen.GameSystem.Games.AndarBahar.AndarBaharGame,
    # Cypridina.Teen.GameSystem.Games.Rummy.RummyGame
  ],

  # 第三方游戏插件
  plugins: [
    # MyCompany.Games.CustomGame
  ]

# 游戏特定配置
config :cypridina, :game_configs,
  # 龙虎斗配置覆盖
  longhu: %{
    # 可以在这里覆盖默认配置
    # bet_time: 20,
    # max_bet: 50000
  },

  # Teen Patti配置覆盖
  teen_patti: %{
    # max_players: 8,
    # robot_count: 4
  }

# 房间管理器配置
config :cyprid<PERSON>, :room_manager,
  # 房间超时时间（毫秒）
  room_timeout: 300_000,
  # 最大房间数量
  max_rooms: 1000,
  # 房间清理间隔（毫秒）
  cleanup_interval: 60_000
