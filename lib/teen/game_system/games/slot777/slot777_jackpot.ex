defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Jackpot do
  @moduledoc """
  Slot777 Jackpot管理模块
  负责：
  - Jackpot池管理
  - 中奖记录
  - 全服广播
  """

  use GenServer
  require Logger

  # Jackpot状态结构
  defstruct [
    :current_amount,      # 当前奖池金额
    :base_amount,         # 基础奖池金额
    :records,             # 中奖记录
    :last_winner,         # 最后中奖者
    :total_contributed,   # 总贡献金额
    # 🧪 临时测试功能 - 可随时删除
    :test_mode_enabled,   # 是否启用测试模式
    :player_spin_counts   # 玩家旋转次数计数器 %{player_id => count}
  ]

  # 默认配置
  @default_base_amount 100000
  @contribution_rate 0.01  # 1%的下注进入奖池
  @max_records 100        # 最多保存100条记录

  ## Client API

  @doc """
  启动Jackpot管理器
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  获取当前Jackpot金额
  """
  def get_current_amount() do
    GenServer.call(__MODULE__, :get_current_amount)
  end

  @doc """
  贡献到Jackpot池
  """
  def contribute(bet_amount) do
    contribution = trunc(bet_amount * @contribution_rate)
    GenServer.cast(__MODULE__, {:contribute, contribution})
    contribution
  end

  @doc """
  触发Jackpot中奖
  """
  def trigger_jackpot(player_id, player_name, bet_amount, seven_count) do
    GenServer.call(__MODULE__, {:trigger_jackpot, player_id, player_name, bet_amount, seven_count})
  end

  @doc """
  获取Jackpot中奖记录
  """
  def get_records() do
    GenServer.call(__MODULE__, :get_records)
  end

  @doc """
  重置Jackpot池到基础金额
  """
  def reset_to_base() do
    GenServer.cast(__MODULE__, :reset_to_base)
  end

  # 🧪 临时测试功能 - 可随时删除这些函数
  @doc """
  启用测试模式：玩家在第5次旋转时必中jackpot
  """
  def enable_test_mode() do
    GenServer.cast(__MODULE__, :enable_test_mode)
  end

  @doc """
  禁用测试模式
  """
  def disable_test_mode() do
    GenServer.cast(__MODULE__, :disable_test_mode)
  end

  @doc """
  检查玩家是否应该中奖（测试模式）
  """
  def should_trigger_test_jackpot(player_id) do
    GenServer.call(__MODULE__, {:should_trigger_test_jackpot, player_id})
  end

  @doc """
  记录玩家旋转次数（测试模式）
  """
  def record_player_spin(player_id) do
    GenServer.cast(__MODULE__, {:record_player_spin, player_id})
  end

  ## Server Callbacks

  @impl true
  def init(_opts) do
    # 初始化状态
    state = %__MODULE__{
      current_amount: @default_base_amount,
      base_amount: @default_base_amount,
      records: [],
      last_winner: nil,
      total_contributed: 0,
      # 🧪 临时测试功能 - 默认启用测试模式
      test_mode_enabled: true,  # 设为 false 可禁用测试模式
      player_spin_counts: %{}
    }

    Logger.info("🎰 [JACKPOT] Jackpot管理器启动，初始金额: #{state.current_amount}")
    Logger.info("🧪 [TEST_MODE] 测试模式已启用：玩家第5次旋转必中jackpot")

    # 定期广播Jackpot金额
    schedule_broadcast()

    {:ok, state}
  end

  @impl true
  def handle_call(:get_current_amount, _from, state) do
    {:reply, state.current_amount, state}
  end

  @impl true
  def handle_call({:trigger_jackpot, player_id, player_name, bet_amount, seven_count}, _from, state) do
    # 计算Jackpot奖金
    jackpot_amount = calculate_jackpot_amount(state.current_amount, seven_count)

    # 创建中奖记录
    record = %{
      player_id: player_id,
      player_name: player_name,
      amount: jackpot_amount,
      seven_count: seven_count,
      bet_amount: bet_amount,
      timestamp: DateTime.utc_now(),
      type: get_jackpot_type(seven_count)
    }

    # 更新状态
    new_records = [record | Enum.take(state.records, @max_records - 1)]
    new_amount = max(state.base_amount, state.current_amount - jackpot_amount)

    new_state = %{state |
      current_amount: new_amount,
      records: new_records,
      last_winner: record
    }

    Logger.info("🎰 [JACKPOT] 玩家 #{player_name}(#{player_id}) 中奖！金额: #{jackpot_amount}, 7的数量: #{seven_count}")

    # 广播Jackpot中奖消息
    broadcast_jackpot_win(record)

    # 广播更新后的Jackpot金额
    broadcast_jackpot_amount(new_state.current_amount)

    {:reply, {:ok, jackpot_amount}, new_state}
  end

  @impl true
  def handle_call(:get_records, _from, state) do
    {:reply, {:ok, state.records}, state}
  end

  # 🧪 临时测试功能 - 可随时删除
  @impl true
  def handle_call({:should_trigger_test_jackpot, player_id}, _from, state) do
    if state.test_mode_enabled do
      spin_count = Map.get(state.player_spin_counts, player_id, 0)
      should_trigger = spin_count >= 5

      if should_trigger do
        Logger.info("🧪 [TEST_MODE] 玩家 #{player_id} 第#{spin_count}次旋转，触发测试jackpot！")
        # 重置该玩家的计数器
        new_counts = Map.put(state.player_spin_counts, player_id, 0)
        new_state = %{state | player_spin_counts: new_counts}
        {:reply, true, new_state}
      else
        Logger.debug("🧪 [TEST_MODE] 玩家 #{player_id} 第#{spin_count}次旋转，还需#{5 - spin_count}次")
        {:reply, false, state}
      end
    else
      {:reply, false, state}
    end
  end

  @impl true
  def handle_cast({:contribute, amount}, state) do
    new_amount = state.current_amount + amount
    new_total = state.total_contributed + amount

    new_state = %{state |
      current_amount: new_amount,
      total_contributed: new_total
    }

    Logger.debug("🎰 [JACKPOT] 奖池增加: #{amount}, 当前总额: #{new_amount}")

    {:noreply, new_state}
  end

  @impl true
  def handle_cast(:reset_to_base, state) do
    new_state = %{state |
      current_amount: state.base_amount,
      total_contributed: 0
    }

    Logger.info("🎰 [JACKPOT] 奖池重置到基础金额: #{state.base_amount}")

    {:noreply, new_state}
  end

  # 🧪 临时测试功能 - 可随时删除
  @impl true
  def handle_cast(:enable_test_mode, state) do
    new_state = %{state | test_mode_enabled: true, player_spin_counts: %{}}
    Logger.info("🧪 [TEST_MODE] 测试模式已启用：玩家第5次旋转必中jackpot")
    {:noreply, new_state}
  end

  @impl true
  def handle_cast(:disable_test_mode, state) do
    new_state = %{state | test_mode_enabled: false, player_spin_counts: %{}}
    Logger.info("🧪 [TEST_MODE] 测试模式已禁用")
    {:noreply, new_state}
  end

  @impl true
  def handle_cast({:record_player_spin, player_id}, state) do
    if state.test_mode_enabled do
      current_count = Map.get(state.player_spin_counts, player_id, 0)
      new_count = current_count + 1
      new_counts = Map.put(state.player_spin_counts, player_id, new_count)
      new_state = %{state | player_spin_counts: new_counts}

      Logger.debug("🧪 [TEST_MODE] 玩家 #{player_id} 旋转计数: #{new_count}/5")
      {:noreply, new_state}
    else
      {:noreply, state}
    end
  end

  @impl true
  def handle_info(:broadcast_jackpot, state) do
    # 广播当前Jackpot金额
    broadcast_jackpot_amount(state.current_amount)

    # 安排下次广播
    schedule_broadcast()

    {:noreply, state}
  end

  ## Private Functions

  @doc """
  计算Jackpot奖金金额
  """
  defp calculate_jackpot_amount(current_amount, seven_count) do
    case seven_count do
      count when count >= 5 ->
        # 5个7 = 获得80%的奖池
        trunc(current_amount * 0.8)

      4 ->
        # 4个7 = 获得50%的奖池
        trunc(current_amount * 0.5)

      3 ->
        # 3个7 = 获得20%的奖池
        trunc(current_amount * 0.2)

      _ ->
        0
    end
  end

  @doc """
  获取Jackpot类型
  """
  defp get_jackpot_type(seven_count) do
    case seven_count do
      count when count >= 5 -> "MEGA_JACKPOT"
      4 -> "MAJOR_JACKPOT"
      3 -> "MINOR_JACKPOT"
      _ -> "NO_JACKPOT"
    end
  end

  @doc """
  广播Jackpot中奖消息
  """
  defp broadcast_jackpot_win(record) do
    message = %{
      "mainId" => 5,
      "subId" => 1006,  # SC_SLOT777_JPAWARD_P
      "data" => %{
        "playerid" => record.player_id,
        "name" => record.player_name,
        "headid" => 1,  # 默认头像
        "wxheadurl" => "",
        "winscore" => record.amount,
        "jackpot_type" => record.type,
        "seven_count" => record.seven_count
      }
    }

    # 广播给所有在线玩家
    CypridinaWeb.Endpoint.broadcast!("game:slot777", "jackpot_win", message)

    Logger.info("🎰 [JACKPOT] 广播中奖消息: #{record.player_name} 中奖 #{record.amount}")
  end

  @doc """
  广播Jackpot金额更新
  """
  defp broadcast_jackpot_amount(amount) do
    message = %{
      "mainId" => 5,
      "subId" => 1005,  # SC_SLOT777_JACKPOT_P
      "data" => %{
        "jackpot" => amount
      }
    }

    # 广播给所有在线玩家
    CypridinaWeb.Endpoint.broadcast!("game:slot777", "jackpot_update", message)

    Logger.debug("🎰 [JACKPOT] 广播奖池金额: #{amount}")
  end

  @doc """
  安排下次广播
  """
  defp schedule_broadcast() do
    # 每30秒广播一次Jackpot金额
    Process.send_after(self(), :broadcast_jackpot, 30_000)
  end

  @doc """
  格式化中奖记录用于前端显示
  """
  def format_records_for_frontend(records) do
    Enum.map(records, fn record ->
      %{
        "playerid" => record.player_id,
        "name" => record.player_name,
        "amount" => record.amount,
        "type" => record.type,
        "timestamp" => DateTime.to_unix(record.timestamp),
        "seven_count" => record.seven_count
      }
    end)
  end
end
