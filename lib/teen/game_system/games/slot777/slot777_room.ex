defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Room do
  @moduledoc """
  Slot777游戏房间实现
  """

  use <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomBase, game_type: :slot777
  require Logger

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomManager
  alias CypridinaWeb.GameChannel
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777GameLogic
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777Jackpot
  alias Cypridina.Accounts.User
  alias Cypridina.Accounts

  # 获取玩家当前金币
  defp get_player_current_money(user_id) do
    if is_integer(user_id) and user_id < 0 do
      # 机器人使用虚拟金币（如果需要支持机器人的话）
      1_000_000
    else
      # 真实玩家使用积分
      Cypridina.Accounts.get_user_points(user_id)
    end
  end

  # 安全获取Jackpot金额
  defp get_jackpot_amount_safe() do
    try do
      Slot777Jackpot.get_current_amount()
    catch
      :exit, {:noproc, _} ->
        Logger.warning("🎰 [SLOT777] Jackpot进程未启动，使用默认金额")
        100_000  # 默认Jackpot金额
      :exit, reason ->
        Logger.warning("🎰 [SLOT777] 获取Jackpot金额失败: #{inspect(reason)}，使用默认金额")
        100_000
    end
  end

  # 安全触发Jackpot
  defp trigger_jackpot_safe(user_id, player_name, bet_amount, seven_count) do
    try do
      Slot777Jackpot.trigger_jackpot(user_id, player_name, bet_amount, seven_count)
    catch
      :exit, {:noproc, _} ->
        Logger.warning("🎰 [SLOT777] Jackpot进程未启动，无法触发Jackpot")
        {:ok, 0}
      :exit, reason ->
        Logger.warning("🎰 [SLOT777] 触发Jackpot失败: #{inspect(reason)}")
        {:ok, 0}
    end
  end

  # 安全贡献到Jackpot池
  defp contribute_jackpot_safe(bet_amount) do
    try do
      Slot777Jackpot.contribute(bet_amount)
    catch
      :exit, {:noproc, _} ->
        Logger.warning("🎰 [SLOT777] Jackpot进程未启动，无法贡献到奖池")
        0
      :exit, reason ->
        Logger.warning("🎰 [SLOT777] 贡献到Jackpot池失败: #{inspect(reason)}")
        0
    end
  end

  # 获取玩家临时积分（优先使用房间内的临时积分）
  defp get_player_temp_money(state, user_id) do
    case Map.get(state.players, user_id) do
      nil ->
        # 玩家不在房间中，使用真实积分
        get_player_current_money(user_id)
      player ->
        # 玩家在房间中，使用临时积分
        Map.get(player.user_info, :temp_money, get_player_current_money(user_id))
    end
  end

  # 更新玩家临时积分
  defp update_player_temp_money(state, user_id, new_temp_money) do
    case Map.get(state.players, user_id) do
      nil ->
        Logger.warning("⚠️ [SLOT777] 尝试更新不存在玩家的临时积分 - 用户: #{user_id}")
        state
      player ->
        updated_user_info = Map.put(player.user_info, :temp_money, new_temp_money)
        updated_player = %{player | user_info: updated_user_info}
        updated_players = Map.put(state.players, user_id, updated_player)
        Logger.info("🎰 [SLOT777] 更新玩家临时积分 - 用户: #{user_id}, 新积分: #{new_temp_money}")
        %{state | players: updated_players}
    end
  end



  # 初始化游戏逻辑
  @impl true
  def init_game_logic(state) do
    Logger.info("🎰 [SLOT777] 初始化游戏房间: #{state.id}")
    game_config = %{
      # 最小下注
      min_bet: Map.get(state.config, :min_bet, 10),
      # 最大下注
      max_bet: Map.get(state.config, :max_bet, 10000),
      # 赔率配置
      odds_config: Map.get(state.config, :odds_config, [1, 2, 5, 10, 20, 50, 100]),
      # 底分
      difen: 10
    }

    game_data = %{
      # 游戏配置
      config: game_config,
      # 当前状态
      status: :waiting,
      # 玩家下注记录
      bets: %{},
      # 当前玩家下注倍率
      current_odds: %{},
      # 游戏结果
      results: [],
      # 当前回合
      current_round: 0,
      # 免费游戏状态
      free_games: %{},
      # 待发送的免费游戏
      pending_free_games: %{},
      # Jackpot池
      jackpot_amount: get_jackpot_amount_safe()
    }

    %{state | game_data: game_data}
  end

  # 玩家加入房间后的处理
  @impl true
  def on_player_joined(state, user_id, user_info) do
    Logger.info("🎰 [SLOT777] 玩家加入: #{user_id}")
    Logger.info("🎰 [SLOT777] ==玩家加入: #{user_id}",user_info)

    # 获取玩家真实积分作为临时积分的初始值
    initial_temp_money = if is_integer(user_id) and user_id < 0 do
      # 机器人使用随机积分
      robot_money = 10000 + :rand.uniform(50000)
      Logger.info("🎰 [SLOT777] 机器人初始金币: #{robot_money}")
      robot_money
    else
      # 真实玩家获取UserAsset中的真实积分，不给默认值
      real_points = Cypridina.Accounts.get_user_points(user_id)
      Logger.info("🎰 [SLOT777] 玩家真实积分: #{real_points}")
      real_points
    end

    # 更新用户信息，包含临时积分字段
    updated_user_info = user_info
    |> Map.put(:money, initial_temp_money)
    |> Map.put(:temp_money, initial_temp_money)  # 新增临时积分字段
    |> Map.put(:original_money, initial_temp_money)  # 记录进入房间时的原始积分

    # 更新state中的玩家信息
    new_state = update_player_info(state, user_id, updated_user_info)

    # 发送游戏配置给新玩家
    send_game_config(new_state, user_id)

    # 发送当前游戏状态给新玩家
    send_game_state_to_user(new_state, user_id)

    # 发送玩家列表给新加入的玩家
    #send_player_list_to_user(new_state, user_id)

    # 广播玩家数量变化通知
    broadcast_player_count_change(new_state)

    new_state
  end

  # 玩家重连加入房间
  @impl true
  def on_player_rejoined(state, user_id, user_info) do
    Logger.info("🎰 [SLOT777] 玩家重连加入: #{user_id}")

    # 检查玩家是否已经在房间中，如果在则使用临时积分
    current_temp_money = case Map.get(state.players, user_id) do
      nil ->
        # 玩家不在房间中，获取真实积分作为临时积分
        real_points = Cypridina.Accounts.get_user_points(user_id)
        Logger.info("🎰 [SLOT777] 重连玩家不在房间，使用真实积分: #{real_points}")
        real_points
      player ->
        # 玩家在房间中，使用临时积分
        temp_money = Map.get(player.user_info, :temp_money, Cypridina.Accounts.get_user_points(user_id))
        Logger.info("🎰 [SLOT777] 重连玩家在房间，使用临时积分: #{temp_money}")
        temp_money
    end

    # 发送游戏配置给重连玩家（使用临时积分）
    send_game_config_with_temp_money(state, user_id, current_temp_money)

    # 发送当前游戏状态给重连玩家
    send_game_state_to_user(state, user_id)

    # 发送玩家列表给重连玩家
    send_player_list_to_user(state, user_id)

    state
  end

  # 玩家离开房间
  @impl true
  def on_player_left(state, user_id) do
    Logger.info("🎰 [SLOT777] 玩家离开: #{user_id}")

    # 同步临时积分到数据库
    sync_temp_money_to_database(state, user_id)

    # 广播玩家数量变化通知
    broadcast_player_count_change(state)

    state
  end

  # 同步临时积分到数据库
  defp sync_temp_money_to_database(state, user_id) do
    case Map.get(state.players, user_id) do
      nil ->
        Logger.info("🎰 [SLOT777] 玩家不在房间中，无需同步积分 - 用户: #{user_id}")
      player ->
        temp_money = Map.get(player.user_info, :temp_money)
        original_money = Map.get(player.user_info, :original_money)

        if temp_money && original_money do
          # 计算总的积分变化
          total_change = temp_money - original_money

          Logger.info("🎰 [SLOT777] 同步积分到数据库 - 用户: #{user_id}, 原始积分: #{original_money}, 临时积分: #{temp_money}, 总变化: #{total_change}")

          # 更新数据库中的积分
          # if total_change > 0 do
          #   {:ok, _} = Cypridina.Accounts.add_points(user_id, total_change)
          #   Logger.info("🎰 [SLOT777] 增加积分到数据库 - 用户: #{user_id}, 金额: #{total_change}")
          # else
          #   {:ok, _} = Cypridina.Accounts.subtract_points(user_id, abs(total_change))
          #   Logger.info("🎰 [SLOT777] 扣除积分从数据库 - 用户: #{user_id}, 金额: #{abs(total_change)}")
          # end
        else
          Logger.warning("⚠️ [SLOT777] 玩家积分信息不完整，无法同步 - 用户: #{user_id}")
        end
    end
  rescue
    error ->
      Logger.error("❌ [SLOT777] 同步积分到数据库失败 - 用户: #{user_id}, 错误: #{inspect(error)}")
  end

  # 游戏开始
  @impl true
  def on_game_start(state) do
    Logger.info("🎰 [SLOT777] 游戏开始: #{state.id}")
    state
  end

  # 处理游戏消息
  @impl true
  def handle_game_message(state, user_id, message) do
    Logger.info("🎰 [SLOT777] 收到游戏消息 - 房间: #{state.id}, 用户: #{user_id}, 消息: #{inspect(message)}")

    case message do
      # 处理MainID=40的Slot777协议消息
      %{"mainId" => 5, "subId" => sub_id, "data" => data} ->
        handle_slot777_protocol(state, user_id, sub_id, data)

      # 兼容性处理
      %{"cmd" => "get_room_info"} ->
        Logger.info("🏠 [ROOM_INFO_REQUEST] 房间信息请求 - 用户: #{user_id}")
        # 发送房间信息给用户
        send_game_config(state, user_id)
        send_game_state_to_user(state, user_id)
        state

      %{"cmd" => "request_jackpot"} ->
        Logger.info("🎰 [JACKPOT] 请求奖池信息 - 用户: #{user_id}")
        handle_request_jackpot(state, user_id)

      _ ->
        Logger.info("ℹ️ [GAME_MESSAGE] 消息已通过客户端协议处理或为未知消息 - 用户: #{user_id}, 消息: #{inspect(message)}")
        state
    end
  end

  # 处理Slot777协议消息
  defp handle_slot777_protocol(state, user_id, sub_id, data) do
    Logger.info("🎰 [SLOT777_PROTOCOL] 处理协议 - SubID: #{sub_id}, 用户: #{user_id}, 数据: #{inspect(data)}")

    case sub_id do
      # 加入房间 (CS_SLOT777_JOIN_ROOM_P)
      1 ->
        handle_join_room_request(state, user_id, data)

      # 离开房间 (CS_SLOT777_LEAVE_ROOM_P)
      2 ->
        handle_leave_room_request(state, user_id, data)

      # 游戏开始 (CS_SLOT777_GAMESTART_P)
      1000 ->
        handle_game_start_request(state, user_id, data)

      # Jackpot记录 (CS_SLOT777_JPLIST_P)
      1003 ->
        handle_jackpot_list_request(state, user_id, data)

      # 切换下注倍率 (CS_SLOTS_SWITCH_BET_P)
      1009 ->
        handle_switch_bet_request(state, user_id, data)

      # 获取房间信息
      1006 ->
        handle_room_info_request(state, user_id, data)

      # 其他未实现的协议
      _ ->
        Logger.warning("🎰 [SLOT777] 未实现的子协议: #{sub_id}")
        send_error_response(state, user_id, sub_id, "未实现的协议")
        state
    end
  end

  # 发送游戏配置给玩家
  defp send_game_config(state, user_id) do
    Logger.info("⚙️ [SEND_CONFIG] 发送游戏配置 - 用户: #{user_id}, 房间: #{state.id}")

    # 获取当前玩家临时积分
    player_money = get_player_temp_money(state, user_id)

    # 获取numeric_id作为playerid（前端需要的是numeric_id）
    {:ok, numeric_id} = get_numeric_id(user_id)
    player_name = "玩家#{numeric_id}"

    # 构建当前玩家信息
    player_info = %{
      "playerid" => numeric_id,  # 使用numeric_id作为playerid
      "money" => player_money,
      "name" => player_name,
      "seat" => 1
    }

    # 构建玩家列表（slot777是单机游戏，只包含当前玩家）
    playerlist = %{
      "1" => player_info  # 使用字符串键，与客户端期望的格式一致
    }

    message = %{
      "mainId" => 4,     # MainProto.Slot777
      "subId" => 2,       # 游戏配置协议
      "data" => %{
        # 底分配置
        "difen" => state.game_data.config.difen,
        # 下注倍率配置
        "odds" => state.game_data.config.odds_config,
        # 下注限制
        "BetMax" => state.game_data.config.max_bet,
        "BetNeed" => state.game_data.config.min_bet,
        # 当前Jackpot金额
        "jackpot" => state.game_data.jackpot_amount,
        # 玩家金币
        "money" => player_money,
        # 当前回合ID
        "room_id" => state.id,
        "roundid" => state.game_data.current_round,
        # 玩家列表（替换playerInfo）
        "playerlist" => playerlist
      }
    }

    Logger.info("📤 [SEND_CONFIG] 配置消息内容 - 协议: 4/2, 底分: #{state.game_data.config.difen}, 玩家: #{player_name}")
    send_to_user(state, user_id, message)
  end

  # 发送游戏配置给玩家（使用指定的临时积分）
  defp send_game_config_with_temp_money(state, user_id, temp_money) do
    Logger.info("⚙️ [SEND_CONFIG_TEMP] 发送游戏配置(临时积分) - 用户: #{user_id}, 房间: #{state.id}, 临时积分: #{temp_money}")

    # 获取numeric_id作为playerid（前端需要的是numeric_id）
    {:ok, numeric_id} = get_numeric_id(user_id)
    player_name = "玩家#{numeric_id}"

    # 构建当前玩家信息
    player_info = %{
      "playerid" => numeric_id,  # 使用numeric_id作为playerid
      "money" => temp_money,     # 使用临时积分
      "name" => player_name,
      "seat" => 1
    }

    # 构建玩家列表（slot777是单机游戏，只包含当前玩家）
    playerlist = %{
      "1" => player_info  # 使用字符串键，与客户端期望的格式一致
    }

    message = %{
      "mainId" => 4,     # MainProto.Slot777
      "subId" => 2,       # 游戏配置协议
      "data" => %{
        # 底分配置
        "difen" => state.game_data.config.difen,
        # 下注倍率配置
        "odds" => state.game_data.config.odds_config,
        # 下注限制
        "BetMax" => state.game_data.config.max_bet,
        "BetNeed" => state.game_data.config.min_bet,
        # 当前Jackpot金额
        "jackpot" => state.game_data.jackpot_amount,
        # 玩家金币（使用临时积分）
        "money" => temp_money,
        # 当前回合ID
        "roundid" => state.game_data.current_round,
        # 玩家列表（替换playerInfo）
        "playerlist" => playerlist
      }
    }

    Logger.info("📤 [SEND_CONFIG_TEMP] 配置消息内容 - 协议: 4/2, 底分: #{state.game_data.config.difen}, 玩家: #{player_name}, 临时积分: #{temp_money}")
    send_to_user(state, user_id, message)
  end

  # 发送游戏状态给指定用户
  defp send_game_state_to_user(state, user_id) do
    # 获取玩家当前临时积分
    player_money = get_player_temp_money(state, user_id)

    message = %{
      "mainId" => 4,     # MainProto.Slot777
      "subId" => 3,       # 游戏状态协议
      "data" => %{
        "status" => state.game_data.status,
        "round" => state.game_data.current_round,
        "money" => player_money,
        "jackpot" => state.game_data.jackpot_amount
      }
    }

    Logger.info("📤 [SEND_GAME_STATE] 发送游戏状态给用户 - 用户: #{user_id}, 状态: #{state.game_data.status}")
    send_to_user(state, user_id, message)
  end

  # 发送玩家列表给指定用户（slot777单机游戏简化版）
  defp send_player_list_to_user(state, user_id) do
    # slot777是单机游戏，只发送当前玩家信息
    player_money = get_player_temp_money(state, user_id)

    # 获取numeric_id作为playerid
    {:ok, numeric_id} = get_numeric_id(user_id)

    player_info = %{
      "playerid" => numeric_id,  # 使用numeric_id作为playerid
      "money" => player_money,
      "name" => "玩家#{numeric_id}",
      "seat" => 1
    }

    message = %{
      "mainId" => 5,     # MainProto.Slot777
      "subId" => 1007,    # SC_SLOT777_PLAYERLIST_P
      "data" => %{
        "playerlist" => [player_info],
        "totalplayernum" => 1
      }
    }

    Logger.info("📤 [SEND_PLAYER_LIST] 发送玩家信息给用户 - 用户: #{user_id}")
    send_to_user(state, user_id, message)
  end

  # 广播玩家数量变化通知
  defp broadcast_player_count_change(state) do
    message = %{
      "mainId" => 5,     # MainProto.Slot777
      "subId" => 1007,    # SC_SLOT777_PLAYERLIST_P
      "data" => %{
        "totalplayernum" => map_size(state.players)
      }
    }

    Logger.info("📤 [BROADCAST_PLAYER_COUNT] 广播玩家数量变化 - 房间总人数: #{map_size(state.players)}")
    broadcast_to_room(state, message)
  end

  # 更新玩家信息
  defp update_player_info(state, user_id, updated_user_info) do
    case Map.get(state.players, user_id) do
      nil -> state
      player ->
        updated_player = %{player | user_info: updated_user_info}
        updated_players = Map.put(state.players, user_id, updated_player)
        %{state | players: updated_players}
    end
  end

  # 处理请求奖池信息
  defp handle_request_jackpot(state, user_id) do
    Logger.info("🎰 [REQUEST_JACKPOT] 处理请求奖池信息 - 用户: #{user_id}")

    # 发送当前Jackpot金额
    send_to_user(state, user_id, %{
      "mainId" => 5,
      "subId" => 1005,  # SC_SLOT777_JACKPOT_P
      "data" => %{
        "jackpot" => state.game_data.jackpot_amount
      }
    })

    state
  end

  @doc """
  开始游戏
  """
  def start_game(room_id, user_id, odds) do
    case GenServer.call({:via, Registry, {:game_room_registry, room_id}}, {:start_game, user_id, odds}) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  切换下注倍率
  """
  def switch_bet(room_id, user_id, odds) do
    case GenServer.call({:via, Registry, {:game_room_registry, room_id}}, {:switch_bet, user_id, odds}) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取Jackpot记录
  """
  def get_jackpot_records() do
    try do
      case Slot777Jackpot.get_records() do
        {:ok, records} ->
          formatted_records = Slot777Jackpot.format_records_for_frontend(records)
          {:ok, formatted_records}
        {:error, reason} ->
          {:error, reason}
      end
    catch
      :exit, {:noproc, _} ->
        Logger.warning("🎰 [SLOT777] Jackpot进程未启动，返回空记录")
        {:ok, []}
      :exit, reason ->
        Logger.warning("🎰 [SLOT777] 获取Jackpot记录失败: #{inspect(reason)}")
        {:error, :jackpot_unavailable}
    end
  end

  # 处理游戏开始请求
  @impl true
  def handle_call({:start_game, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 开始游戏，倍率: #{odds}")

    # 验证玩家是否在房间中
    if not Map.has_key?(state.players, user_id) do
      {:reply, {:error, "玩家不在房间中"}, state}
    else
      # 验证倍率是否有效
      if odds not in state.game_data.config.odds_config do
        {:reply, {:error, "无效的下注倍率"}, state}
      else
        # 获取玩家临时积分
        current_temp_money = get_player_temp_money(state, user_id)

        # 计算下注金额
        bet_amount = state.game_data.config.difen * odds
        Logger.info("🎰 [SLOT777] 玩家临时积分 #{current_temp_money} 下注金额: #{bet_amount}")
        # 检查玩家余额
        if current_temp_money < bet_amount do
          {:reply, {:error, "余额不足"}, state}
        else
          # 生成游戏结果
          game_result = Slot777GameLogic.generate_game_result(odds, current_temp_money, state.game_data.config.difen)

          # 计算新的临时积分
          change_amount = game_result["changemoney"]
          new_temp_money = current_temp_money + change_amount
          Logger.info("🎰 [SLOT777] 临时积分变化 - 用户: #{user_id}, 原积分: #{current_temp_money}, 变化: #{change_amount}, 新积分: #{new_temp_money}")

          # 更新玩家临时积分
          temp_updated_state = update_player_temp_money(state, user_id, new_temp_money)

          # 注意：这里不立即更新数据库，只在玩家离开房间时才同步到数据库
          # 这样可以避免频繁的数据库操作，提高性能

          # 更新房间状态
          new_game_data = %{temp_updated_state.game_data |
            current_round: temp_updated_state.game_data.current_round + 1,
            current_odds: Map.put(temp_updated_state.game_data.current_odds, user_id, odds)
          }

          new_state = %{temp_updated_state | game_data: new_game_data}

          # 处理Jackpot
          if game_result["jackpotcash"] > 0 do
            player_name = "玩家#{user_id}"
            trigger_jackpot_safe(user_id, player_name, bet_amount, game_result["sevennum"])
          end

          # 贡献到Jackpot池
          contribute_jackpot_safe(bet_amount)

          # 获取numeric_id作为playerid
          {:ok, numeric_id} = get_numeric_id(user_id)

          # 发送游戏结果（包含最新临时积分信息）
          enhanced_result = game_result
          |> Map.put("current_money", new_temp_money)  # 使用临时积分
          |> Map.put("playerid", numeric_id)  # 使用numeric_id作为playerid

          # 存储免费游戏信息到状态中，稍后在handle_game_start_request中发送
          updated_state = if game_result["freetimes"] > 0 do
            free_game_info = %{
              user_id: user_id,
              free_times: game_result["freetimes"],
              odds: odds
            }
            put_in(new_state, [:game_data, :pending_free_games, user_id], free_game_info)
          else
            new_state
          end

          {:reply, {:ok, enhanced_result}, updated_state}
        end
      end
    end
  end

  # 处理切换下注倍率请求
  @impl true
  def handle_call({:switch_bet, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 切换下注倍率: #{odds}")

    # 验证玩家是否在房间中
    if not Map.has_key?(state.players, user_id) do
      {:reply, {:error, "玩家不在房间中"}, state}
    else
      # 验证倍率是否有效
      if odds not in state.game_data.config.odds_config do
        {:reply, {:error, "无效的下注倍率"}, state}
      else
        # 更新玩家当前倍率
        new_current_odds = Map.put(state.game_data.current_odds, user_id, odds)
        new_game_data = %{state.game_data | current_odds: new_current_odds}
        new_state = %{state | game_data: new_game_data}

        result = %{
          "code" => 0,
          "msg" => "切换下注倍率成功",
          "odds" => odds,
          "bet_amount" => state.game_data.config.difen * odds
        }

        {:reply, {:ok, result}, new_state}
      end
    end
  end

  # ==================== Slot777协议处理函数 ====================

  # 处理加入房间请求
  defp handle_join_room_request(state, user_id, data) do
    Logger.info("🎰 [JOIN_ROOM] 处理加入房间请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    # 获取用户信息
    user_info = Map.get(data, "user_info", %{})
    # 获取玩家真实积分
    real_money = Cypridina.Accounts.get_user_points(user_id)
    user_info = Map.merge(user_info, %{
      "name" => Map.get(user_info, "name", "Player#{user_id}"),
      "money" => real_money  # 使用玩家真实积分
    })
     # slot777是单机游戏，不需要玩家列表
    # 发送加入房间成功响应
    response = %{
      "mainId" => 4,
      "subId" => 2,  # SC_SLOT777_JOIN_ROOM_P
      "data" => %{
        "code" => 0,
        "msg" => "加入房间成功",
        "room_id" => state.id,
        "is_creator" => false
      }
    }

    send_to_user(state, user_id, response)

    # 发送游戏配置和状态
    # send_game_config(state, user_id)
    # send_game_state_to_user(state, user_id)
    # send_player_list_to_user(state, user_id)

    state
  end

  # 处理离开房间请求
  defp handle_leave_room_request(state, user_id, data) do
    Logger.info("🎰 [LEAVE_ROOM] 处理离开房间请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    # 发送离开房间成功响应
    response = %{
      "mainId" => 5,
      "subId" => 12,  # SC_SLOT777_LEAVE_ROOM_P
      "data" => %{
        "code" => 0,
        "msg" => "离开房间成功"
      }
    }

    send_to_user(state, user_id, response)
    state
  end

  # 处理游戏开始请求
  defp handle_game_start_request(state, user_id, data) do
    Logger.info("🎰 [GAME_START] 处理游戏开始请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    odds = Map.get(data, "odds", 1)

    # 调用内部游戏开始处理
    case handle_call({:start_game, user_id, odds}, nil, state) do
      {:reply, {:ok, result}, new_state} ->
        # 1. 先发送1001协议 - 正常游戏结果
        response_1001 = %{
          "mainId" => 5,
          "subId" => 1001,  # SC_SLOT777_GAMESTART_P
          "data" => result
        }
        send_to_user(new_state, user_id, response_1001)

        # 2. 检查是否有待发送的免费游戏，如果有则发送1002协议
        final_state = case get_in(new_state, [:game_data, :pending_free_games, user_id]) do
          %{free_times: free_times, odds: free_odds} ->
            Logger.info("🎰 [FREE_GAME] 发送免费游戏 - 用户: #{user_id}, 免费次数: #{free_times}")
            # 生成并发送免费游戏结果
            generate_and_send_free_games(new_state, user_id, free_times, free_odds)
            # 清除待发送的免费游戏信息
            put_in(new_state, [:game_data, :pending_free_games], Map.delete(new_state.game_data.pending_free_games || %{}, user_id))
          _ ->
            new_state
        end

        # 广播游戏结果给其他玩家
        broadcast_game_result(final_state, user_id, result)

        final_state

      {:reply, {:error, reason}, state} ->
        # 发送错误消息
        send_error_response(state, user_id, 1001, reason)
        state
    end
  end

  # 处理Jackpot记录请求
  defp handle_jackpot_list_request(state, user_id, data) do
    Logger.info("🎰 [JACKPOT_LIST] 处理Jackpot记录请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    case get_jackpot_records() do
      {:ok, records} ->
        response = %{
          "mainId" => 5,
          "subId" => 1004,  # SC_SLOT777_JPLIST_P
          "data" => %{
            "records" => records
          }
        }
        send_to_user(state, user_id, response)

      {:error, reason} ->
        send_error_response(state, user_id, 1004, "获取Jackpot记录失败: #{reason}")
    end

    state
  end

  # 处理切换下注倍率请求
  defp handle_switch_bet_request(state, user_id, data) do
    Logger.info("🎰 [SWITCH_BET] 处理切换下注倍率请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    odds = Map.get(data, "odds", 1)

    # 调用内部切换倍率处理
    case handle_call({:switch_bet, user_id, odds}, nil, state) do
      {:reply, {:ok, result}, new_state} ->
        # 发送切换结果给玩家
        response = %{
          "mainId" => 5,
          "subId" => 1009,  # CS_SLOTS_SWITCH_BET_P
          "data" => result
        }
        send_to_user(new_state, user_id, response)
        new_state

      {:reply, {:error, reason}, state} ->
        send_error_response(state, user_id, 1009, reason)
        state
    end
  end

  # 处理房间信息请求
  defp handle_room_info_request(state, user_id, data) do
    Logger.info("🎰 [ROOM_INFO] 处理房间信息请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    # 发送游戏配置和状态
    send_game_config(state, user_id)
    send_game_state_to_user(state, user_id)
    send_player_list_to_user(state, user_id)

    state
  end

  # 发送错误响应
  defp send_error_response(state, user_id, sub_id, reason) do
    response = %{
      "mainId" => 5,
      "subId" => sub_id,
      "data" => %{
        "code" => 1,
        "msg" => reason
      }
    }
    send_to_user(state, user_id, response)
  end

  # 生成并发送免费游戏结果
  defp generate_and_send_free_games(state, user_id, free_times, odds) do
    Logger.info("🎰 [FREE_GAME] 开始生成免费游戏 - 用户: #{user_id}, 免费次数: #{free_times}")

    # 生成免费游戏结果数组
    free_game_results = generate_free_game_results(free_times, odds, state.game_data.config.difen)

    # 发送免费游戏结果给玩家
    response = %{
      "mainId" => 5,
      "subId" => 1002,  # SC_SLOT777_FREEGAME_P
      "data" => free_game_results
    }

    Logger.info("📤 [FREE_GAME] 发送免费游戏结果 - 用户: #{user_id}, 结果数量: #{map_size(free_game_results)}")
    send_to_user(state, user_id, response)
  end

  # 生成免费游戏结果数组（下标从1开始）
  defp generate_free_game_results(free_times, odds, difen) do
    Logger.info("🎰 [FREE_GAME] 生成 #{free_times} 次免费游戏结果")

    # 生成指定次数的免费游戏结果，转换为以1为起始下标的Map
    1..free_times
    |> Enum.map(fn round ->
      # 每次免费游戏都生成新的结果
      game_result = Slot777GameLogic.generate_game_result(odds, 0, difen)  # 免费游戏不扣金币，传入0

      # 免费游戏结果包含所有必要字段
      result = %{
        "round" => round,
        "freetimes" => 0,  # 免费游戏中不再触发免费游戏
        "sevennum" => game_result["sevennum"],
        "iconresult" => game_result["iconresult"],
        "linecount" => game_result["linecount"],
        "lineresult" => game_result["lineresult"],
        "totalmult" => game_result["totalmult"],
        "winmoney" => game_result["winmoney"],
        "changemoney" => game_result["changemoney"],
        "jackpotcash" => game_result["jackpotcash"],
        "luckyjackpot" => Map.get(game_result, "luckyjackpot", 0)
      }

      {round, result}  # 返回{下标, 结果}的元组
    end)
    |> Enum.into(%{})  # 转换为Map，下标从1开始
  end

  # 广播游戏结果给其他玩家
  defp broadcast_game_result(state, player_id, result) do
    # 获取numeric_id作为playerid
    {:ok, numeric_id} = get_numeric_id(player_id)
    player_name = "玩家#{numeric_id}"

    # 构建广播消息
    broadcast_data = %{
      "playerid" => numeric_id,  # 使用numeric_id作为playerid
      "name" => player_name,
      "result" => result,
      "timestamp" => System.system_time(:millisecond)
    }

    message = %{
      "mainId" => 5,
      "subId" => 1008,  # SC_SLOT777_GAMERESULT_P
      "data" => broadcast_data
    }

    Logger.info("📤 [BROADCAST_GAME_RESULT] 广播游戏结果 - 玩家: #{player_id}")
    broadcast_to_room(state, message, player_id)  # 排除玩家自己
  end

  # ==================== 用户ID转换相关方法 ====================

  @doc """
  根据user_id获取numeric_id
  参考websocket_handler.ex的build_login_success_response方法
  """
  defp get_numeric_id(user_id) do
    try do
      # 如果user_id已经是数字，直接返回
      if is_integer(user_id) do
        {:ok, user_id}
      else
        # 通过user_id查询用户获取numeric_id
        case Ash.get(Cypridina.Accounts.User, user_id) do
          {:ok, user} ->
            {:ok, user.numeric_id}
          {:error, reason} ->
            Logger.warning("⚠️ [GET_NUMERIC_ID] 获取用户numeric_id失败 - user_id: #{user_id}, 原因: #{inspect(reason)}")
            # 如果获取不到，生成一个虚拟的numeric_id
            virtual_numeric_id = generate_virtual_numeric_id(user_id)
            Logger.info("🔄 [VIRTUAL_NUMERIC_ID] 生成虚拟numeric_id - user_id: #{user_id}, numeric_id: #{virtual_numeric_id}")
            {:ok, virtual_numeric_id}
        end
      end
    rescue
      error ->
        Logger.error("❌ [GET_NUMERIC_ID] 获取numeric_id异常 - user_id: #{user_id}, 错误: #{inspect(error)}")
        # 异常情况下也生成虚拟numeric_id
        virtual_numeric_id = generate_virtual_numeric_id(user_id)
        {:ok, virtual_numeric_id}
    end
  end

  @doc """
  生成虚拟numeric_id
  当无法从数据库获取numeric_id时使用
  """
  defp generate_virtual_numeric_id(user_id) do
    cond do
      # 如果user_id是字符串UUID
      is_binary(user_id) and String.length(user_id) == 36 ->
        # 使用UUID生成numeric_id的算法（参考user.ex）
        user_id
        |> String.replace("-", "")
        |> String.slice(0, 8)
        |> String.to_integer(16)
        |> rem(100_000_000)
        |> Kernel.+(10_000_000)

      # 如果user_id是其他字符串
      is_binary(user_id) ->
        # 使用字符串哈希生成numeric_id
        hash = :erlang.phash2(user_id, 100_000_000)
        hash + 10_000_000

      # 如果user_id是数字，直接返回
      is_integer(user_id) ->
        user_id

      # 其他情况，使用随机数
      true ->
        :rand.uniform(90_000_000) + 10_000_000
    end
  end

end
