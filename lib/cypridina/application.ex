defmodule Cypridina.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    # :ok = Oban.Telemetry.attach_default_logger()

    children = [
      CypridinaWeb.Telemetry,
      Cypridina.Repo,
      {DNSCluster, query: Application.get_env(:cypridina, :dns_cluster_query) || :ignore},
      # {<PERSON>ban, Application.fetch_env!(:cypridina, Oban)},
      {Phoenix.PubSub, name: <PERSON><PERSON><PERSON><PERSON>.PubSub},
      # 游戏房间注册表
      {Registry, keys: :unique, name: :game_room_registry},
      # Start to serve requests, typically the last entry
      CypridinaWeb.Endpoint,
      # 动物运动会
      RacingGame.Main,
      # Teen项目
      Cypridina.Teen.GameSystem.RobotService,
      # Slot777 Jackpot管理器 - 必须在房间管理器之前启动
      Cypridina.Teen.GameSystem.Games.Slot777.Slot777Jackpot,
      # 游戏工厂 - 必须在房间管理器之前启动
      Cypridina.RoomSystem.GameFactory,
      Cypridina.RoomSystem.RoomManager,
      {AshAuthentication.Supervisor, [otp_app: :cypridina]}
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Cypridina.Supervisor]

    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    CypridinaWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
