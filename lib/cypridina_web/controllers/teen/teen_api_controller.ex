defmodule <PERSON><PERSON><PERSON>inaWeb.TeenApiController do
  use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, :controller

  # alias <PERSON><PERSON><PERSON><PERSON>.Utils.AES
  # alias <PERSON><PERSON>ridina.Utils.AES
  # alias Cypridina.Utils.XXTEA
  # alias <PERSON><PERSON>ridina.Utils.CryptoUtils
  require Logger

  @doc """
  提供基本信息端点
  """
  def default(conn, data) do
    _xxtea_key = Application.get_env(:cyp<PERSON><PERSON>, :teen)[:DataCryptKey]

    # source_data = CryptoUtils.convert_from_hex(data)
    # 先解码十六进制字符串，再进行解密
    # decrypted_data = data |> Base.decode64!()
    # Logger.info("解密数据: #{inspect(decrypted_data |> URI.decode_query())}")

    encrypted_data =
      %{
        "connectserver" => %{
          1 => %{ip: "localhost", port: 4000, path: "Teen", protocol: "ws"}
        },
        "updateurl" => "",
        # APP版本号
        "installver" => "1001",
        "installurl" => "https://download.india-game.com/app/install.apk",
        "weburl" => "https://www.india-game.com/",
        # 大厅版本
        "basever" => "1005",
        "headUploadUrl" => "https://upload.india-game.com/avatar/",
        "gamewebapiurl" => "https://api.india-game.com/",
        "pmurl" => "https://pay.india-game.com/",
        "publicurl" => "https://public.india-game.com/",
        "downloadurl" => "https://download.india-game.com/",
        "downloadqrcodeurl" => "https://download.india-game.com/qrcode.png",
        "exchangestatus" => 1,
        "chargestatus" => 1,
        "registerstatus" => 1
      }
      |> Jason.encode!()
      # |> AES.encrypt(xxtea_key)
      |> Base.encode64()

    text(conn, encrypted_data)
  end

  @doc """
  获取数据列表示例
  """
  def list(conn, params) do
    # 这里可以添加实际业务逻辑，如从数据库获取数据
    # 示例返回一些模拟数据
    page = Map.get(params, "page", "1") |> String.to_integer()
    per_page = Map.get(params, "per_page", "10") |> String.to_integer()

    items =
      Enum.map(1..per_page, fn i ->
        %{
          id: (page - 1) * per_page + i,
          name: "项目 #{(page - 1) * per_page + i}",
          created_at: DateTime.utc_now() |> DateTime.to_iso8601()
        }
      end)

    json(conn, %{
      status: "success",
      data: %{
        items: items,
        page: page,
        per_page: per_page,
        total: 100
      }
    })
  end

  @doc """
  创建资源示例
  """
  def create(conn, params) do
    # 在实际应用中，这里应该有数据验证和保存逻辑
    # 示例仅返回提交的数据作为确认
    json(conn, %{
      status: "success",
      message: "资源创建成功",
      data: %{
        id: :rand.uniform(1000),
        created_at: DateTime.utc_now() |> DateTime.to_iso8601(),
        params: params
      }
    })
  end

  @doc """
  获取单个资源示例
  """
  def show(conn, %{"id" => id}) do
    # 在实际应用中，这里应该从数据库查询数据
    json(conn, %{
      status: "success",
      data: %{
        id: id,
        name: "项目 #{id}",
        description: "这是项目 #{id} 的详细信息",
        created_at: DateTime.utc_now() |> DateTime.to_iso8601()
      }
    })
  end

  @doc """
  更新资源示例
  """
  def update(conn, %{"id" => id} = params) do
    # 在实际应用中，这里应该有数据验证和更新逻辑
    json(conn, %{
      status: "success",
      message: "资源更新成功",
      data: %{
        id: id,
        updated_at: DateTime.utc_now() |> DateTime.to_iso8601(),
        params: params
      }
    })
  end

  @doc """
  删除资源示例
  """
  def delete(conn, %{"id" => id}) do
    # 在实际应用中，这里应该有删除逻辑
    json(conn, %{
      status: "success",
      message: "资源 #{id} 已删除"
    })
  end
end
