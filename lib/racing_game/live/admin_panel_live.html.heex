<div class="admin-panel">
  <!-- 移动端顶部栏 -->
  <div class="mobile-header">
    <button phx-click="toggle_sidebar" class="mobile-menu-btn">
      <i class="fas fa-bars"></i>
    </button>
    <h1 class="mobile-title"><%= get_page_title(@current_page) %></h1>
    <div class="mobile-user">
      <%= String.first(to_string(@current_user.username || "U")) %>
    </div>
  </div>

  <!-- 侧边栏遮罩 -->
  <%= if @sidebar_open do %>
    <div class="sidebar-overlay" phx-click="toggle_sidebar"></div>
  <% end %>

  <!-- 左侧导航栏 -->
  <div class={["sidebar", if(@sidebar_open, do: "sidebar-open", else: "")]}>
    <!-- 用户信息区域 -->
    <div class="user-info">
      <div class="user-avatar">
        <%= String.first(to_string(@current_user.username || "U")) %>
      </div>
      <div class="user-details">
        <div class="username"><%= to_string(@current_user.username) %></div>
        <div class="user-role">
          <%= CypridinaWeb.AuthHelper.get_permission_level_name(@current_user) %>
        </div>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="nav-menu">
      <%= for menu_item <- get_navigation_menu(@current_user) do %>
        <%= if menu_item.type == :single do %>
          <!-- 单级菜单项 -->
          <.link
            navigate={~p"/admin_panel/#{menu_item.page}"}
            class={["nav-item", if(@current_page == menu_item.page, do: "active", else: "")]}
          >
            <i class={menu_item.icon}></i>
            <span><%= menu_item.title %></span>
          </.link>
        <% else %>
          <!-- 分类菜单项 -->
          <div class="nav-category">
            <button
              phx-click="toggle_category"
              phx-value-category={menu_item.id}
              class={["nav-category-header", if(category_expanded?(@expanded_categories, menu_item.id), do: "expanded", else: "")]}
            >
              <div class="nav-category-title">
                <i class={menu_item.icon}></i>
                <span><%= menu_item.title %></span>
              </div>
              <i class="fas fa-chevron-down nav-category-arrow"></i>
            </button>

            <%= if category_expanded?(@expanded_categories, menu_item.id) do %>
              <div class="nav-category-children">
                <%= for child <- menu_item.children do %>
                  <.link
                    navigate={~p"/admin_panel/#{child.page}"}
                    class={["nav-child-item", if(@current_page == child.page, do: "active", else: "")]}
                  >
                    <i class={child.icon}></i>
                    <span><%= child.title %></span>
                  </.link>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </nav>

    <!-- 底部操作 -->
    <div class="sidebar-footer">
      <a href="/" class="back-button">
        <i class="fas fa-arrow-left"></i>
        <span>返回游戏</span>
      </a>
    </div>
  </div>

  <!-- 主内容区域 -->
  <div class="main-content">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title"><%= get_page_title(@current_page) %></h1>
    </div>

    <!-- Flash 消息 -->
    <%= if live_flash(@flash, :info) do %>
      <div class="flash-message flash-info">
        <i class="fas fa-check-circle"></i>
        <%= live_flash(@flash, :info) %>
        <button phx-click="lv:clear-flash" phx-value-key="info" class="flash-close">
          <i class="fas fa-times"></i>
        </button>
      </div>
    <% end %>

    <%= if live_flash(@flash, :error) do %>
      <div class="flash-message flash-error">
        <i class="fas fa-exclamation-circle"></i>
        <%= live_flash(@flash, :error) %>
        <button phx-click="lv:clear-flash" phx-value-key="error" class="flash-close">
          <i class="fas fa-times"></i>
        </button>
      </div>
    <% end %>

    <!-- 内容区域 -->
    <div class="content-area">
      <%= case @current_page do %>
        <% "profile" -> %>
          <.live_component
            module={RacingGame.Live.AdminPanel.ProfileComponent}
            id="profile-component"
            current_user={@current_user}
          />

        <% "users" -> %>
          <.live_component
            module={RacingGame.Live.AdminPanel.UserManagementComponent}
            id="user-management-component"
            current_user={@current_user}
          />

        <% "subordinates" -> %>
          <.live_component
            module={RacingGame.Live.AdminPanel.SubordinateManagementComponent}
            id="subordinate-management-component"
            current_user={@current_user}
          />

        <% "stocks" -> %>
          <.live_component
            module={RacingGame.Live.AdminPanel.StockHoldingsComponent}
            id="stock-holdings-component"
            current_user={@current_user}
          />

        <% "stock_transactions" -> %>
          <.live_component
            module={RacingGame.Live.AdminPanel.StockTransactionsComponent}
            id="stock-transactions-component"
            current_user={@current_user}
          />

        <% "bet_records" -> %>
          <.live_component
            module={RacingGame.Live.AdminPanel.BetRecordsComponent}
            id="bet-records-component"
            current_user={@current_user}
          />

        <% "refunds" -> %>
          <.live_component
            module={RacingGame.Live.AdminPanel.RefundManagementComponent}
            id="refund-management-component"
            current_user={@current_user}
          />

        <% "commission_records" -> %>
          <.live_component
            module={RacingGame.Live.AdminPanel.CommissionRecordsComponent}
            id="commission-records-component"
            current_user={@current_user}
          />

        <% "points_transactions" -> %>
          <.live_component
            module={RacingGame.Live.AdminPanel.PointsTransactionsComponent}
            id="points-transactions-component"
            current_user={@current_user}
          />

        <% _ -> %>
          <div class="loading-message">
            <p>页面正在加载中...</p>
          </div>
      <% end %>
    </div>
  </div>
</div>

<!-- 响应式样式 CSS -->
<style>
  .admin-panel {
    display: flex;
    min-height: 100vh;
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
  }

  /* 移动端顶部栏 */
  .mobile-header {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    z-index: 1001;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .mobile-menu-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .mobile-menu-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .mobile-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }

  .mobile-user {
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
  }

  /* 侧边栏遮罩 */
  .sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  /* 左侧导航栏 */
  .sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.3s ease;
  }

  /* 用户信息区域 */
  .user-info {
    padding: 30px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .user-details {
    flex: 1;
  }

  .username {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
  }

  .user-role {
    font-size: 14px;
    opacity: 0.8;
    color: #e0e7ff;
  }

  /* 导航菜单 */
  .nav-menu {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
  }

  /* 单级菜单项 */
  .nav-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 25px;
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    border-left: 4px solid transparent;
    text-decoration: none;
  }

  .nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.5);
  }

  .nav-item.active {
    background: rgba(255, 255, 255, 0.2);
    border-left-color: #ffd700;
    font-weight: 600;
  }

  .nav-item i {
    width: 20px;
    text-align: center;
    font-size: 18px;
  }

  /* 分类菜单 */
  .nav-category {
    margin-bottom: 8px;
  }

  .nav-category-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 25px;
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    border-left: 4px solid transparent;
  }

  .nav-category-header:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.3);
  }

  .nav-category-header.expanded {
    background: rgba(255, 255, 255, 0.15);
    border-left-color: rgba(255, 255, 255, 0.6);
  }

  .nav-category-title {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .nav-category-title i {
    width: 20px;
    text-align: center;
    font-size: 18px;
  }

  .nav-category-arrow {
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  .nav-category-header.expanded .nav-category-arrow {
    transform: rotate(180deg);
  }

  /* 子菜单项 */
  .nav-category-children {
    background: rgba(0, 0, 0, 0.1);
    border-left: 2px solid rgba(255, 255, 255, 0.2);
    margin-left: 25px;
    animation: slideDown 0.3s ease-out;
  }

  .nav-child-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 25px;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    border-left: 3px solid transparent;
    text-decoration: none;
  }

  .nav-child-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.4);
    color: white;
  }

  .nav-child-item.active {
    background: rgba(255, 255, 255, 0.2);
    border-left-color: #ffd700;
    font-weight: 600;
    color: white;
  }

  .nav-child-item i {
    width: 18px;
    text-align: center;
    font-size: 16px;
  }

  /* 动画效果 */
  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      max-height: 500px;
      transform: translateY(0);
    }
  }

  /* 底部操作 */
  .sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
  }

  .back-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  /* 主内容区域 */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8fafc;
  }

  /* 页面标题 */
  .page-header {
    background: white;
    padding: 25px 30px;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
  }

  /* 内容区域 */
  .content-area {
    flex: 1;
    padding: 30px;
    background: white;
    margin: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
  }

  /* 加载消息 */
  .loading-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #718096;
    font-size: 16px;
  }

  /* Flash 消息样式 */
  .flash-message {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    margin: 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    animation: slideDown 0.3s ease-out;
  }

  .flash-info {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
  }

  .flash-error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
  }

  .flash-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    margin-left: auto;
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .flash-close:hover {
    opacity: 1;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .admin-panel {
      flex-direction: column;
    }

    .mobile-header {
      display: flex;
    }

    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      width: 280px;
      z-index: 1000;
      transform: translateX(-100%);
    }

    .sidebar-open {
      transform: translateX(0);
    }

    .sidebar-overlay {
      display: block;
    }

    .main-content {
      margin-top: 60px;
    }

    .page-header {
      display: none; /* 隐藏页面标题，使用移动端顶部栏 */
    }

    .content-area {
      margin: 10px;
      padding: 15px;
      border-radius: 8px;
    }

    .user-info {
      padding: 20px 15px;
    }

    .nav-item, .nav-category-header {
      padding: 12px 20px;
      font-size: 15px;
    }

    .nav-child-item {
      padding: 10px 20px;
      font-size: 14px;
    }

    .nav-category-children {
      margin-left: 15px;
    }
  }

  /* 平板设备 */
  @media (max-width: 1024px) and (min-width: 769px) {
    .sidebar {
      width: 260px;
    }

    .content-area {
      margin: 15px;
      padding: 25px;
    }

    .nav-item, .nav-category-header {
      padding: 13px 22px;
    }

    .nav-child-item {
      padding: 11px 22px;
    }
  }

  /* 大屏幕优化 */
  @media (min-width: 1200px) {
    .sidebar {
      width: 300px;
    }

    .nav-item, .nav-category-header {
      padding: 16px 28px;
      font-size: 17px;
    }

    .nav-child-item {
      padding: 13px 28px;
      font-size: 16px;
    }
  }
</style>


