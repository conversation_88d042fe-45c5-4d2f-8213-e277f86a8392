defmodule RacingGame.Live.AdminPanel.UserManagementComponent do
  @moduledoc """
  用户管理组件 - 仅限管理员使用
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.User
  alias <PERSON><PERSON>ridina.Utils.TimeHelper
  require Ash.Query

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_defaults()
      |> load_users_data()

    {:ok, socket}
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> assign(:per_page, 20)
    |> assign(:page_info, nil)
    |> assign(:show_create_modal, false)
    |> assign(:show_edit_modal, false)
    |> assign(:selected_user, nil)
    |> assign(:create_form, %{})
    |> assign(:edit_form, %{})
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_users_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    page = String.to_integer(page)
    socket =
      socket
      |> assign(:page, page)
      |> load_users_data()

    {:noreply, socket}
  end

  def handle_event("show_create_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_create_modal, true)
      |> assign(:create_form, %{
        "username" => "",
        "password" => "",
        "password_confirmation" => "",
        "permission_level" => "0",
        "agent_level" => "-1",
        "initial_points" => "0"
      })

    {:noreply, socket}
  end

  def handle_event("hide_create_modal", _params, socket) do
    {:noreply, assign(socket, :show_create_modal, false)}
  end

  def handle_event("show_edit_modal", %{"user_id" => user_id}, socket) do
    case get_user_by_id(user_id) do
      {:ok, user} ->
        socket =
          socket
          |> assign(:show_edit_modal, true)
          |> assign(:selected_user, user)
          |> assign(:edit_form, %{
            "permission_level" => to_string(user.permission_level),
            "agent_level" => to_string(user.agent_level),
            "points_adjustment" => "0",
            "adjustment_reason" => ""
          })

        {:noreply, socket}

      {:error, _} ->
        {:noreply, socket}
    end
  end

  def handle_event("hide_edit_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_edit_modal, false)
      |> assign(:selected_user, nil)

    {:noreply, socket}
  end

  def handle_event("create_user", %{"user" => user_params}, socket) do
    case create_new_user(user_params) do
      {:ok, _user} ->
        socket =
          socket
          |> assign(:show_create_modal, false)
          |> load_users_data()

        {:noreply, socket}

      {:error, _changeset} ->
        # 这里可以添加错误处理
        {:noreply, socket}
    end
  end

  def handle_event("update_user", %{"user" => user_params}, socket) do
    user = socket.assigns.selected_user

    case update_user_info(user, user_params) do
      {:ok, _updated_user} ->
        # 发送消息给父LiveView来显示flash消息
        send(self(), {:flash, :info, "用户信息更新成功"})

        socket =
          socket
          |> assign(:show_edit_modal, false)
          |> assign(:selected_user, nil)
          |> load_users_data()

        {:noreply, socket}

      {:error, error} ->
        # 发送错误消息给父LiveView
        error_message = case error do
          %Ash.Error.Invalid{} -> "输入数据无效，请检查后重试"
          _ -> "用户信息更新失败，请稍后重试"
        end

        send(self(), {:flash, :error, error_message})

        {:noreply, socket}
    end
  end

  defp load_users_data(socket) do
    user = socket.assigns.current_user

    if AuthHelper.has_permission?(user, :admin) do
      search_query = socket.assigns.search_query
      page = socket.assigns.page
      per_page = socket.assigns.per_page

      # 构建查询
      query = User |> Ash.Query.load([:asset])

      # 添加搜索条件
      query = if search_query != "" do
        Ash.Query.filter(query, contains(username, ^search_query))
      else
        query
      end

      # 使用 Ash.Query.page 进行分页查询并按插入时间倒序排列
      case query
           |> Ash.Query.sort(inserted_at: :desc)
           |> Ash.Query.page(count: true, limit: per_page, offset: (page - 1) * per_page)
           |> Ash.read() do
        {:ok, %Ash.Page.Offset{results: users, count: total_count}} ->
          socket
          |> assign(:users_data, users)
          |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})

        {:error, error} ->
          require Logger
          Logger.error("Failed to load users data: #{inspect(error)}")

          socket
          |> assign(:users_data, [])
          |> assign(:page_info, %{total_count: 0, page: 1, per_page: per_page})
      end
    else
      socket
      |> assign(:users_data, [])
      |> assign(:page_info, %{total_count: 0, page: 1, per_page: 20})
    end
  end

  # 创建新用户
  defp create_new_user(params) do
    # 准备用户参数
    user_params = %{
      username: params["username"],
      password: params["password"],
      password_confirmation: params["password_confirmation"],
      permission_level: String.to_integer(params["permission_level"]),
      agent_level: String.to_integer(params["agent_level"]),
      # confirmed_at: DateTime.utc_now(),
      asset: %{
        points: String.to_integer(params["initial_points"] || "0"),
        commission: 0
      }
    }

    # 创建用户
    case User |> Ash.Changeset.for_create(:register_with_username, user_params) |> Ash.create() do
      {:ok, user} ->
        {:ok, user}

      {:error, changeset} ->
        {:error, changeset}
    end
  end

  # 更新用户信息
  defp update_user_info(user, params) do
    # 更新权限级别和代理等级
    permission_level = String.to_integer(params["permission_level"])
    agent_level = String.to_integer(params["agent_level"])

    # 先更新权限级别
    case user |> Ash.Changeset.for_update(:update_permission_level, %{permission_level: permission_level}) |> Ash.update() do
      {:ok, updated_user} ->
        # 更新代理等级
        case updated_user |> Ash.Changeset.for_update(:update_agent_level, %{agent_level: agent_level}) |> Ash.update() do
          {:ok, final_user} ->
            # 处理积分调整
            points_adjustment = String.to_integer(params["points_adjustment"] || "0")

            if points_adjustment != 0 do
              case adjust_user_points(user.id, points_adjustment) do
                {:ok, _} -> :ok
                {:error, _error} -> :ok  # 积分调整失败不影响主要更新
              end
            end

            {:ok, final_user}

          {:error, changeset} ->
            {:error, changeset}
        end

      {:error, changeset} ->
        {:error, changeset}
    end
  end

  # 获取用户信息
  defp get_user_by_id(user_id) do
    user = User |> Ash.Query.filter(id == ^user_id) |> Ash.Query.load([:asset]) |> Ash.read_one!()
    {:ok, user}
  end

  # 调整用户积分
  defp adjust_user_points(user_id, amount) do
    # 查找用户资产
    case Cypridina.Accounts.UserAsset |> Ash.Query.filter(user_id == ^user_id) |> Ash.read_one() do
      {:ok, user_asset} when not is_nil(user_asset) ->
        # 根据调整金额选择操作
        if amount > 0 do
          # 增加积分
          Cypridina.Accounts.UserAsset.add_points(user_asset, %{amount: amount})
        else
          # 减少积分
          Cypridina.Accounts.UserAsset.subtract_points(user_asset, %{amount: abs(amount)})
        end

      {:ok, nil} ->
        # 如果用户资产不存在，创建一个
        if amount > 0 do
          Cypridina.Accounts.UserAsset |> Ash.Changeset.for_create(:create, %{user_id: user_id, points: amount, commission: 0}) |> Ash.create()
        else
          {:error, "用户资产不存在"}
        end

      {:error, _} ->
        {:error, "查找用户资产失败"}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <!-- 页面头部 -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">用户管理</h2>
        <button
          phx-click="show_create_modal"
          phx-target={@myself}
          class="btn btn-primary btn-sm"
        >
          <.icon name="hero-plus" class="w-4 h-4" />
          新建用户
        </button>
      </div>

      <!-- 搜索和统计 -->
      <div class="flex justify-between items-center mb-4">
        <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
          <input
            type="text"
            name="search[query]"
            value={@search_query}
            placeholder="按用户名搜索..."
            class="input input-bordered input-sm w-64"
          />
          <button type="submit" class="btn btn-outline btn-sm">
            <.icon name="hero-magnifying-glass" class="w-4 h-4" />
            搜索
          </button>
        </form>
        <div class="text-sm text-base-content/60">
          <%= if @page_info do %>
            共 <%= @page_info.total_count %> 个用户，第 <%= @page_info.page %> 页
          <% else %>
            加载中...
          <% end %>
        </div>
      </div>

      <!-- 用户列表 -->
      <%= if @users_data && length(@users_data) > 0 do %>
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>数字ID</th>
                <th>用户名</th>
                <th>权限</th>
                <th>身份</th>
                <th>积分</th>
                <th>注册时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <%= for user <- @users_data do %>
                <tr>
                  <td class="font-mono"><%= user.numeric_id %></td>
                  <td class="font-medium"><%= to_string(user.username) %></td>
                  <td>
                    <%= case user.permission_level do %>
                      <% 2 -> %>
                        <span class="badge badge-error">超级管理员</span>
                      <% 1 -> %>
                        <span class="badge badge-warning">管理员</span>
                      <% 0 -> %>
                        <span class="badge badge-ghost">普通权限</span>
                    <% end %>
                  </td>
                  <td>
                    <%= if user.agent_level >= 0 do %>
                      <span class="badge badge-info">代理L<%= user.agent_level %></span>
                    <% else %>
                      <span class="badge badge-ghost">普通用户</span>
                    <% end %>
                  </td>
                  <td class="font-medium">
                    <%= if user.asset do %>
                      <%= user.asset.points %>
                    <% else %>
                      0
                    <% end %>
                  </td>
                  <td><%= TimeHelper.format_local_date(user.inserted_at) %></td>
                  <td>
                    <%= if user.confirmed_at do %>
                      <span class="badge badge-success badge-sm">已激活</span>
                    <% else %>
                      <span class="badge badge-warning badge-sm">未激活</span>
                    <% end %>
                  </td>
                  <td>
                    <div class="flex space-x-1">
                      <button
                        phx-click="show_edit_modal"
                        phx-value-user_id={user.id}
                        phx-target={@myself}
                        class="btn btn-ghost btn-xs"
                      >
                        <.icon name="hero-pencil" class="w-3 h-3" />
                        编辑
                      </button>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- 分页控件 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex justify-center mt-6">
            <div class="join">
              <%= if @page_info.page > 1 do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page - 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  «
                </button>
              <% end %>

              <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={page_num}
                  phx-target={@myself}
                  class={[
                    "join-item btn btn-sm",
                    if(page_num == @page_info.page, do: "btn-active", else: "")
                  ]}
                >
                  <%= page_num %>
                </button>
              <% end %>

              <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page + 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  »
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-12">
          <div class="text-base-content/40 mb-4">
            <.icon name="hero-users" class="w-16 h-16 mx-auto" />
          </div>
          <p class="text-base-content/60">
            <%= if @search_query != "" do %>
              未找到匹配的用户
            <% else %>
              暂无用户数据
            <% end %>
          </p>
        </div>
      <% end %>

      <!-- 创建用户模态框 -->
      <%= if @show_create_modal do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-2xl">
            <h3 class="font-bold text-lg mb-4">新建用户</h3>

            <form phx-submit="create_user" phx-target={@myself} class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 用户名 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">用户名 *</span>
                  </label>
                  <input
                    type="text"
                    name="user[username]"
                    value={@create_form["username"]}
                    placeholder="请输入用户名"
                    class="input input-bordered"
                    required
                  />
                </div>



                <!-- 密码 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">密码 *</span>
                  </label>
                  <input
                    type="password"
                    name="user[password]"
                    value={@create_form["password"]}
                    placeholder="请输入密码"
                    class="input input-bordered"
                    required
                  />
                </div>

                <!-- 确认密码 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">确认密码 *</span>
                  </label>
                  <input
                    type="password"
                    name="user[password_confirmation]"
                    value={@create_form["password_confirmation"]}
                    placeholder="请再次输入密码"
                    class="input input-bordered"
                    required
                  />
                </div>

                <!-- 权限级别 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">权限级别</span>
                  </label>
                  <select name="user[permission_level]" class="select select-bordered">
                    <option value="0" selected={@create_form["permission_level"] == "0"}>普通用户</option>
                    <option value="1" selected={@create_form["permission_level"] == "1"}>管理员</option>
                    <option value="2" selected={@create_form["permission_level"] == "2"}>超级管理员</option>
                  </select>
                </div>

                <!-- 身份 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">身份</span>
                  </label>
                  <select name="user[agent_level]" class="select select-bordered">
                    <option value="-1" selected={@create_form["agent_level"] == "-1"}>普通用户</option>
                    <option value="0" selected={@create_form["agent_level"] == "0"}>根代理</option>
                    <option value="1" selected={@create_form["agent_level"] == "1"}>一级代理</option>
                    <option value="2" selected={@create_form["agent_level"] == "2"}>二级代理</option>
                    <option value="3" selected={@create_form["agent_level"] == "3"}>三级代理</option>
                  </select>
                </div>

                <!-- 初始积分 -->
                <div class="form-control md:col-span-2">
                  <label class="label">
                    <span class="label-text">初始积分</span>
                  </label>
                  <input
                    type="number"
                    name="user[initial_points]"
                    value={@create_form["initial_points"]}
                    placeholder="0"
                    class="input input-bordered"
                    min="0"
                  />
                </div>
              </div>

              <div class="modal-action">
                <button type="button" phx-click="hide_create_modal" phx-target={@myself} class="btn btn-ghost">取消</button>
                <button type="submit" class="btn btn-primary">创建用户</button>
              </div>
            </form>
          </div>
        </div>
      <% end %>

      <!-- 编辑用户模态框 -->
      <%= if @show_edit_modal and @selected_user do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-2xl">
            <h3 class="font-bold text-lg mb-4">编辑用户 - <%= to_string(@selected_user.username) %></h3>

            <!-- 用户基本信息 -->
            <div class="bg-base-200 p-4 rounded-lg mb-4">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-base-content/60">数字ID:</span>
                  <span class="font-mono"><%= @selected_user.numeric_id %></span>
                </div>

                <div>
                  <span class="text-base-content/60">当前积分:</span>
                  <span class="font-medium">
                    <%= if @selected_user.asset do %>
                      <%= @selected_user.asset.points %>
                    <% else %>
                      0
                    <% end %>
                  </span>
                </div>
                <div>
                  <span class="text-base-content/60">注册时间:</span>
                  <span><%= TimeHelper.format_local_date(@selected_user.inserted_at) %></span>
                </div>
              </div>
            </div>

            <form phx-submit="update_user" phx-target={@myself} class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 权限级别 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">权限级别</span>
                  </label>
                  <select name="user[permission_level]" class="select select-bordered">
                    <option value="0" selected={@edit_form["permission_level"] == "0"}>普通用户</option>
                    <option value="1" selected={@edit_form["permission_level"] == "1"}>管理员</option>
                    <option value="2" selected={@edit_form["permission_level"] == "2"}>超级管理员</option>
                  </select>
                </div>

                <!-- 身份 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">身份</span>
                  </label>
                  <select name="user[agent_level]" class="select select-bordered">
                    <option value="-1" selected={@edit_form["agent_level"] == "-1"}>普通用户</option>
                    <option value="0" selected={@edit_form["agent_level"] == "0"}>根代理</option>
                    <option value="1" selected={@edit_form["agent_level"] == "1"}>一级代理</option>
                    <option value="2" selected={@edit_form["agent_level"] == "2"}>二级代理</option>
                    <option value="3" selected={@edit_form["agent_level"] == "3"}>三级代理</option>
                  </select>
                </div>

                <!-- 积分调整 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">积分调整</span>
                    <span class="label-text-alt">正数增加，负数减少</span>
                  </label>
                  <input
                    type="number"
                    name="user[points_adjustment]"
                    value={@edit_form["points_adjustment"]}
                    placeholder="0"
                    class="input input-bordered"
                  />
                </div>

                <!-- 调整原因 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">调整原因</span>
                  </label>
                  <input
                    type="text"
                    name="user[adjustment_reason]"
                    value={@edit_form["adjustment_reason"]}
                    placeholder="请输入调整原因"
                    class="input input-bordered"
                  />
                </div>
              </div>

              <div class="modal-action">
                <button type="button" phx-click="hide_edit_modal" phx-target={@myself} class="btn btn-ghost">取消</button>
                <button type="submit" class="btn btn-primary">保存更改</button>
              </div>
            </form>
          </div>
        </div>
      <% end %>
    </div>
    """
  end
end
