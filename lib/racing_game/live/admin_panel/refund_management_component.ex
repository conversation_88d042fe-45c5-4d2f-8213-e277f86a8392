defmodule RacingGame.Live.AdminPanel.RefundManagementComponent do
  @moduledoc """
  退费管理组件 - 管理员处理用户退费申请
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias RacingGame.PointsTransaction
  alias Cypridina.Accounts.User
  alias RacingGame.Live.AdminPanel.PermissionFilter
  alias Cypridina.Utils.TimeHelper
  require Ash.Query

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_defaults()
      |> load_refund_requests_data()

    {:ok, socket}
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> assign(:per_page, 20)
    |> assign(:page_info, nil)
    |> assign(:show_process_modal, false)
    |> assign(:selected_request, nil)
    |> assign(:process_form, %{})
  end

  def handle_event("refresh", _params, socket) do
    socket = load_refund_requests_data(socket)
    {:noreply, socket}
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_refund_requests_data()

    {:noreply, socket}
  end

  def handle_event("clear_search", _params, socket) do
    socket =
      socket
      |> assign(:search_query, "")
      |> assign(:page, 1)
      |> load_refund_requests_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    page = String.to_integer(page)
    socket =
      socket
      |> assign(:page, page)
      |> load_refund_requests_data()

    {:noreply, socket}
  end

  def handle_event("show_process_modal", %{"request_id" => request_id}, socket) do
    case find_request_by_id(socket.assigns.refund_requests_data, request_id) do
      nil ->
        send(self(), {:flash, :error, "退费申请不存在"})
        {:noreply, socket}

      request ->
        socket =
          socket
          |> assign(:show_process_modal, true)
          |> assign(:selected_request, request)
          |> assign(:process_form, %{
            "action" => "",
            "reject_reason" => ""
          })

        {:noreply, socket}
    end
  end

  def handle_event("hide_process_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_process_modal, false)
      |> assign(:selected_request, nil)
      |> assign(:process_form, %{})

    {:noreply, socket}
  end

  def handle_event("process_refund", %{"process" => process_params}, socket) do
    request = socket.assigns.selected_request
    current_user = socket.assigns.current_user
    action = process_params["action"]

    result = case action do
      "approve" ->
        PointsTransaction.approve_withdrawal(request, %{processed_by: current_user.id})

      "reject" ->
        reject_reason = process_params["reject_reason"] || "无原因"
        PointsTransaction.reject_withdrawal(request, %{
          processed_by: current_user.id,
          reject_reason: reject_reason
        })

      _ ->
        {:error, "无效的操作"}
    end

    case result do
      {:ok, _} ->
        action_text = if action == "approve", do: "批准", else: "拒绝"
        send(self(), {:flash, :info, "退费申请#{action_text}成功"})

        socket =
          socket
          |> assign(:show_process_modal, false)
          |> assign(:selected_request, nil)
          |> assign(:process_form, %{})
          |> load_refund_requests_data()

        {:noreply, socket}

      {:error, error} ->
        error_message = case error do
          %Ash.Error.Invalid{} -> "处理失败，请检查输入信息"
          _ -> "处理失败，请稍后重试"
        end

        send(self(), {:flash, :error, error_message})
        {:noreply, socket}
    end
  end

  defp load_refund_requests_data(socket) do
    user = socket.assigns.current_user
    search_query = socket.assigns.search_query || ""
    page = socket.assigns.page
    per_page = socket.assigns.per_page

    # 获取所有退费申请（withdrawal_request 类型且 request_type 为 refund）
    query = PointsTransaction
    |> Ash.Query.load([:user])
    |> Ash.Query.filter(transaction_type == :withdrawal_request)

    # 应用权限过滤
    query = PermissionFilter.apply_user_filter(query, user, :user_id)

    # 执行查询并过滤退费申请
    all_requests = query
    |> Ash.Query.sort(updated_at: :desc)
    |> Ash.read!()
    |> Enum.filter(fn req ->
      # 只显示退费申请
      req.extra_data && req.extra_data["request_type"] == "refund"
    end)

    # 应用搜索过滤
    filtered_requests = if search_query != "" do
      Enum.filter(all_requests, fn request ->
        user_name = if request.user, do: to_string(request.user.username), else: ""
        String.contains?(String.downcase(user_name), String.downcase(search_query))
      end)
    else
      all_requests
    end

    # 计算总数
    total_count = length(filtered_requests)

    # 分页
    requests = filtered_requests
    |> Enum.drop((page - 1) * per_page)
    |> Enum.take(per_page)

    socket
    |> assign(:refund_requests_data, requests)
    |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})
  end

  defp find_request_by_id(requests, request_id) do
    Enum.find(requests, fn req -> req.id == request_id end)
  end

  defp format_status(status) do
    case status do
      :pending -> {"待处理", "badge-warning"}
      :approved -> {"已批准", "badge-success"}
      :rejected -> {"已拒绝", "badge-error"}
      :cancelled -> {"已取消", "badge-ghost"}
      _ -> {"未知", "badge-ghost"}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="refund-management-wrapper">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">退费管理</h2>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-base-content/60">
            <%= if @page_info do %>
              共 <%= @page_info.total_count %> 条记录，第 <%= @page_info.page %> 页
            <% else %>
              加载中...
            <% end %>
          </div>
          <button
            phx-click="refresh"
            phx-target={@myself}
            class="btn btn-outline btn-primary">
            刷新
          </button>
        </div>
      </div>

      <!-- 搜索栏 -->
      <div class="flex justify-between items-center mb-4">
        <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
          <input
            name="search[query]"
            type="text"
            placeholder="搜索用户名..."
            class="input input-bordered input-sm w-64"
            value={@search_query}
          />
          <button type="submit" class="btn btn-outline btn-sm">
            <.icon name="hero-magnifying-glass" class="w-4 h-4" />
            搜索
          </button>
        </form>
        <%= if @search_query != "" do %>
          <button
            phx-click="clear_search"
            phx-target={@myself}
            class="btn btn-ghost btn-sm"
          >
            <.icon name="hero-x-mark" class="w-4 h-4" />
            清除搜索
          </button>
        <% end %>
      </div>

      <!-- 退费申请列表 -->
      <div class="overflow-x-auto">
        <table class="table table-zebra w-full">
          <thead>
            <tr>
              <th>申请ID</th>
              <th>用户</th>
              <th>退费金额</th>
              <th>申请时间</th>
              <th>状态</th>
              <th>退费原因</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <%= for request <- @refund_requests_data do %>
              <tr>
                <td class="font-mono text-xs"><%= String.slice(request.id, 0, 8) %>...</td>
                <td class="font-medium">
                  <%= if request.user do %>
                    <%= to_string(request.user.username) %>
                  <% else %>
                    未知用户
                  <% end %>
                </td>
                <td class="font-bold text-error">
                  <%= abs(Decimal.to_integer(request.amount)) %> 积分
                </td>
                <td class="text-sm">
                  <%= TimeHelper.format_local_datetime(request.inserted_at) %>
                </td>
                <td>
                  <% {status_text, status_class} = format_status(request.status) %>
                  <span class={"badge #{status_class}"}><%= status_text %></span>
                </td>
                <td class="text-sm max-w-xs truncate">
                  <%= request.extra_data["refund_reason"] || "无原因" %>
                </td>
                <td>
                  <%= if request.status == :pending do %>
                    <button
                      phx-click="show_process_modal"
                      phx-value-request_id={request.id}
                      phx-target={@myself}
                      class="btn btn-ghost btn-xs">
                      处理
                    </button>
                  <% else %>
                    <span class="text-base-content/40 text-xs">已处理</span>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>

        <%= if Enum.empty?(@refund_requests_data) do %>
          <div class="text-center py-8 text-base-content/60">
            <div class="text-4xl mb-2">📝</div>
            <p>暂无退费申请</p>
          </div>
        <% end %>
      </div>

      <!-- 分页组件 -->
      <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
        <div class="flex justify-center mt-6">
          <div class="join">
            <%= if @page_info.page > 1 do %>
              <button
                phx-click="page_change"
                phx-value-page={@page_info.page - 1}
                phx-target={@myself}
                class="join-item btn btn-sm"
              >
                «
              </button>
            <% end %>

            <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
              <button
                phx-click="page_change"
                phx-value-page={page_num}
                phx-target={@myself}
                class={[
                  "join-item btn btn-sm",
                  if(page_num == @page_info.page, do: "btn-active", else: "")
                ]}
              >
                <%= page_num %>
              </button>
            <% end %>

            <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
              <button
                phx-click="page_change"
                phx-value-page={@page_info.page + 1}
                phx-target={@myself}
                class="join-item btn btn-sm"
              >
                »
              </button>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- 处理退费申请模态框 -->
      <%= if @show_process_modal and @selected_request do %>
        <div class="modal modal-open">
          <div class="modal-box">
            <h3 class="font-bold text-lg mb-4">处理退费申请</h3>

            <!-- 申请详情 -->
            <div class="bg-base-200 p-4 rounded-lg mb-4">
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div><strong>用户:</strong> <%= @selected_request.user.username %></div>
                <div><strong>金额:</strong> <%= abs(Decimal.to_integer(@selected_request.amount)) %> 积分</div>
                <div><strong>申请时间:</strong> <%= TimeHelper.format_local_datetime(@selected_request.inserted_at) %></div>
                <div><strong>原因:</strong> <%= @selected_request.extra_data["refund_reason"] || "无原因" %></div>
              </div>
            </div>

            <form phx-submit="process_refund" phx-target={@myself}>
              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text">处理决定</span>
                </label>
                <div class="flex gap-4">
                  <label class="label cursor-pointer">
                    <input type="radio" name="process[action]" value="approve" class="radio radio-success" />
                    <span class="label-text ml-2">批准退费</span>
                  </label>
                  <label class="label cursor-pointer">
                    <input type="radio" name="process[action]" value="reject" class="radio radio-error" />
                    <span class="label-text ml-2">拒绝退费</span>
                  </label>
                </div>
              </div>

              <div class="form-control mb-6">
                <label class="label">
                  <span class="label-text">拒绝原因（拒绝时必填）</span>
                </label>
                <textarea
                  name="process[reject_reason]"
                  class="textarea textarea-bordered"
                  placeholder="请说明拒绝原因..."
                  rows="3">
                </textarea>
              </div>

              <div class="modal-action">
                <button type="submit" class="btn btn-primary">确认处理</button>
                <button
                  type="button"
                  phx-click="hide_process_modal"
                  phx-target={@myself}
                  class="btn btn-ghost">
                  取消
                </button>
              </div>
            </form>
          </div>
        </div>
      <% end %>
    </div>
    """
  end
end
