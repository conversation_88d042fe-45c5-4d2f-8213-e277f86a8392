defmodule RacingGame.Live.AdminPanel.PointsTransactionsComponent do
  @moduledoc """
  积分变动记录管理组件

  这是一个纯函数组件，用于显示和管理系统中的积分变动记录，包括：
  - 股票买入卖出记录
  - 下注赢奖记录
  - 抽水记录
  - 管理员调整记录
  - 转账记录
  - 支持按类型筛选
  - 分页显示
  """

  use Phoenix.LiveComponent
  use CypridinaWeb, :html
  alias RacingGame.PointsTransaction
  alias RacingGame.Live.AdminPanel.PermissionFilter
  alias Cypridina.Utils.TimeHelper
  require Logger

  def mount(socket) do
    {:ok,
     socket
     |> assign(:points_transactions_data, [])
     |> assign(:search_query, "")
     |> assign(:transaction_type_filter, "all")
     |> assign(:page, 1)
     |> assign(:per_page, 20)
     |> assign(:page_info, nil)
     |> assign(:loading, true)}
  end

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    socket =
      if socket.assigns.loading do
        load_points_transactions_data(socket)
      else
        socket
      end

    {:ok, socket}
  end

  def render(assigns) do
    ~H"""
    <div class="points-transactions-management" id="points-transactions-root">
      <!-- 页面标题和操作栏 -->
      <div class="header-section">
        <div class="title-area">
          <h2 class="section-title">
            <i class="fas fa-coins text-yellow-500"></i>
            积分变动记录
          </h2>
          <p class="section-description">查看和管理所有积分变动记录</p>
        </div>

        <div class="action-buttons">
          <button
            phx-click="refresh"
            phx-target={@myself}
            class="btn btn-outline btn-sm"
            title="刷新数据"
          >
            <i class="fas fa-sync-alt"></i>
            刷新
          </button>
        </div>
      </div>

      <!-- 筛选和搜索栏 -->
      <div class="filter-section">
        <div class="filter-row">
          <!-- 交易类型筛选 -->
          <div class="filter-group">
            <label class="filter-label">交易类型</label>
            <select
              phx-change="filter_by_type"
              phx-target={@myself}
              name="transaction_type"
              class="select select-bordered select-sm w-full max-w-xs"
            >
              <%= for {value, label} <- get_transaction_type_options() do %>
                <option value={value} selected={@transaction_type_filter == value}>
                  <%= label %>
                </option>
              <% end %>
            </select>
          </div>

          <!-- 用户名搜索 -->
          <div class="filter-group">
            <label class="filter-label">用户名搜索</label>
            <form phx-submit="search" phx-target={@myself} class="search-form">
              <div class="join">
                <input
                  type="text"
                  name="search_query"
                  value={@search_query}
                  placeholder="输入用户名搜索..."
                  class="input input-bordered input-sm join-item flex-1"
                />
                <button type="submit" class="btn btn-primary btn-sm join-item">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 数据统计 -->
      <%= if @page_info do %>
        <div class="stats-section">
          <div class="stat">
            <div class="stat-title">总记录数</div>
            <div class="stat-value text-primary"><%= @page_info.total_count %></div>
          </div>
          <div class="stat">
            <div class="stat-title">当前页</div>
            <div class="stat-value text-secondary">
              <%= @page_info.current_page %> / <%= @page_info.total_pages %>
            </div>
          </div>
          <div class="stat">
            <div class="stat-title">筛选类型</div>
            <div class="stat-value text-accent">
              <%= if @transaction_type_filter == "all", do: "全部", else: get_transaction_type_name(String.to_existing_atom(@transaction_type_filter)) %>
            </div>
          </div>
        </div>
      <% end %>

      <!-- 积分变动记录表格 -->
      <div class="table-section">
        <%= if @loading do %>
          <div class="loading-container">
            <span class="loading loading-spinner loading-lg"></span>
            <p>正在加载积分变动记录...</p>
          </div>
        <% else %>
          <%= if length(@points_transactions_data) > 0 do %>
            <.points_transactions_table
              transactions={@points_transactions_data}
              myself={@myself}
            />

            <!-- 分页控件 -->
            <%= if @page_info && @page_info.total_pages > 1 do %>
              <.pagination_controls page_info={@page_info} myself={@myself} />
            <% end %>
          <% else %>
            <.empty_state
              search_query={@search_query}
              transaction_type_filter={@transaction_type_filter}
            />
          <% end %>
        <% end %>
      </div>

      <!-- 组件样式 -->
      <.component_styles />
    </div>
    """
  end

  def handle_event("search", %{"search_query" => query}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_points_transactions_data()

    {:noreply, socket}
  end

  def handle_event("filter_by_type", %{"transaction_type" => type}, socket) do
    socket =
      socket
      |> assign(:transaction_type_filter, type)
      |> assign(:page, 1)
      |> load_points_transactions_data()

    {:noreply, socket}
  end

  def handle_event("change_page", %{"page" => page_str}, socket) do
    page = String.to_integer(page_str)

    socket =
      socket
      |> assign(:page, page)
      |> load_points_transactions_data()

    {:noreply, socket}
  end

  def handle_event("refresh", _params, socket) do
    socket = load_points_transactions_data(socket)
    {:noreply, socket}
  end

  defp load_points_transactions_data(socket) do
    user = socket.assigns.current_user
    search_query = socket.assigns.search_query || ""
    transaction_type_filter = socket.assigns.transaction_type_filter || "all"
    page = socket.assigns.page
    per_page = socket.assigns.per_page

    # 构建基础查询
    import Ash.Query

    query = PointsTransaction
    |> load([:user])

    # 应用权限过滤
    query = PermissionFilter.apply_user_filter(query, user, :user_id)

    # 添加交易类型筛选
    query = if transaction_type_filter != "all" do
      type_atom = String.to_existing_atom(transaction_type_filter)
      filter(query, transaction_type == ^type_atom)
    else
      query
    end

    # 添加搜索条件
    query = if search_query != "" do
      # 对于 PointsTransaction，我们需要通过关联的用户名进行搜索
      # 先获取匹配的用户ID，然后过滤交易记录
      matching_user_ids = case Cypridina.Accounts.User
                               |> filter(contains(username, ^search_query))
                               |> select([:id])
                               |> Ash.read() do
        {:ok, users} -> Enum.map(users, & &1.id)
        _ -> []
      end

      if length(matching_user_ids) > 0 do
        filter(query, user_id in ^matching_user_ids)
      else
        # 如果没有匹配的用户，返回空结果
        filter(query, user_id == "no-match")
      end
    else
      query
    end

    # 添加排序和分页
    query = query
    |> sort(inserted_at: :desc)
    |> Ash.Query.page(offset: (page - 1) * per_page, limit: per_page, count: true)

    case Ash.read(query) do
      {:ok, %{results: transactions, count: total_count}} ->
        page_info = %{
          current_page: page,
          per_page: per_page,
          total_count: total_count,
          total_pages: max(1, ceil(total_count / per_page))
        }

        socket
        |> assign(:points_transactions_data, transactions)
        |> assign(:page_info, page_info)
        |> assign(:loading, false)

      {:error, reason} ->
        Logger.error("加载积分变动记录失败: #{inspect(reason)}")
        socket
        |> assign(:points_transactions_data, [])
        |> assign(:page_info, nil)
        |> assign(:loading, false)
    end
  end

  # 获取交易类型的中文名称
  defp get_transaction_type_name(type) do
    case type do
      :buy_stock -> "买入股票"
      :sell_stock -> "卖出股票"
      :place_bet -> "投注"
      :win_prize -> "获奖"
      :commission -> "抽水"
      :transfer_in -> "转账收入"
      :transfer_out -> "转账支出"
      :refund -> "退费"
      :system_adjust -> "系统调整"
      :admin_add -> "管理员增加"
      :admin_subtract -> "管理员减少"
      :manual_add -> "手动增加"
      :manual_subtract -> "手动减少"
      :withdrawal_request -> "提现请求"
      _ -> "未知类型"
    end
  end

  # 获取交易类型的颜色样式
  defp get_transaction_type_color(type) do
    case type do
      :buy_stock -> "bg-blue-100 text-blue-800"
      :sell_stock -> "bg-green-100 text-green-800"
      :place_bet -> "bg-orange-100 text-orange-800"
      :win_prize -> "bg-emerald-100 text-emerald-800"
      :commission -> "bg-purple-100 text-purple-800"
      :transfer_in -> "bg-cyan-100 text-cyan-800"
      :transfer_out -> "bg-red-100 text-red-800"
      :refund -> "bg-yellow-100 text-yellow-800"
      :system_adjust -> "bg-gray-100 text-gray-800"
      :admin_add -> "bg-indigo-100 text-indigo-800"
      :admin_subtract -> "bg-pink-100 text-pink-800"
      :manual_add -> "bg-teal-100 text-teal-800"
      :manual_subtract -> "bg-rose-100 text-rose-800"
      :withdrawal_request -> "bg-amber-100 text-amber-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end

  # 获取金额的显示样式
  defp get_amount_style(amount) do
    if Decimal.positive?(amount) do
      "text-green-600 font-semibold"
    else
      "text-red-600 font-semibold"
    end
  end

  # 格式化金额显示
  defp format_amount(amount) do
    if Decimal.positive?(amount) do
      "+#{amount}"
    else
      "#{amount}"
    end
  end

  # 积分变动记录表格组件
  defp points_transactions_table(assigns) do
    ~H"""
    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
          <tr>
            <th>交易流水号</th>
            <th>用户</th>
            <th>交易类型</th>
            <th>变动金额</th>
            <th>余额变化</th>
            <th>时间</th>
            <th>描述</th>
            <th>详情</th>
          </tr>
        </thead>
        <tbody>
          <%= for record <- @transactions do %>
            <tr class="hover">
              <td class="font-mono text-sm">
                <%= String.slice(record.transaction_id, 0, 12) %>...
              </td>
              <td>
                <%= if record.user do %>
                  <div class="user-info">
                    <div class="font-medium"><%= record.user.username %></div>
                    <div class="text-xs text-base-content/60">
                      <%= String.slice(record.user_id, 0, 8) %>...
                    </div>
                  </div>
                <% else %>
                  <span class="text-base-content/60">用户不存在</span>
                <% end %>
              </td>
              <td>
                <span class={"badge badge-sm " <> get_transaction_type_color(record.transaction_type)}>
                  <%= get_transaction_type_name(record.transaction_type) %>
                </span>
              </td>
              <td class={get_amount_style(record.amount)}>
                <%= format_amount(record.amount) %>
              </td>
              <td class="text-sm">
                <%= record.balance_before %> → <%= record.balance_after %>
              </td>
              <td><%= TimeHelper.format_local_short_time(record.inserted_at) %></td>
              <td class="text-sm text-base-content/60 max-w-xs truncate">
                <%= record.description || "-" %>
              </td>
              <td>
                <%= if record.extra_data do %>
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-xs">
                      <i class="fas fa-info-circle"></i>
                    </div>
                    <div tabindex="0" class="dropdown-content z-[1] card card-compact w-80 p-2 shadow bg-base-100">
                      <div class="card-body">
                        <h3 class="card-title text-sm">交易详情</h3>
                        <div class="text-xs space-y-1">
                          <%= for {key, value} <- record.extra_data do %>
                            <div class="flex justify-between">
                              <span class="font-medium"><%= key %>:</span>
                              <span><%= inspect(value) %></span>
                            </div>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  </div>
                <% else %>
                  <span class="text-base-content/40">-</span>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
    """
  end

  # 分页控件组件
  defp pagination_controls(assigns) do
    ~H"""
    <div class="pagination-section">
      <div class="join">
        <!-- 上一页 -->
        <%= if @page_info.current_page > 1 do %>
          <button
            phx-click="change_page"
            phx-value-page={@page_info.current_page - 1}
            phx-target={@myself}
            class="join-item btn btn-sm"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
        <% end %>

        <!-- 页码 -->
        <%= for page_num <- max(1, @page_info.current_page - 2)..min(@page_info.total_pages, @page_info.current_page + 2) do %>
          <button
            phx-click="change_page"
            phx-value-page={page_num}
            phx-target={@myself}
            class={[
              "join-item btn btn-sm",
              if(page_num == @page_info.current_page, do: "btn-active", else: "")
            ]}
          >
            <%= page_num %>
          </button>
        <% end %>

        <!-- 下一页 -->
        <%= if @page_info.current_page < @page_info.total_pages do %>
          <button
            phx-click="change_page"
            phx-value-page={@page_info.current_page + 1}
            phx-target={@myself}
            class="join-item btn btn-sm"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        <% end %>
      </div>

      <div class="pagination-info">
        显示第 <%= (@page_info.current_page - 1) * @page_info.per_page + 1 %> -
        <%= min(@page_info.current_page * @page_info.per_page, @page_info.total_count) %> 条，
        共 <%= @page_info.total_count %> 条记录
      </div>
    </div>
    """
  end

  # 空状态组件
  defp empty_state(assigns) do
    ~H"""
    <div class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-coins text-6xl text-base-content/20"></i>
      </div>
      <h3 class="empty-title">暂无积分变动记录</h3>
      <p class="empty-description">
        <%= if @search_query != "" or @transaction_type_filter != "all" do %>
          当前筛选条件下没有找到相关记录，请尝试调整筛选条件。
        <% else %>
          系统中还没有积分变动记录。
        <% end %>
      </p>
    </div>
    """
  end

  # 组件样式
  defp component_styles(assigns) do
    ~H"""
    <style>
    .points-transactions-management {
      max-width: 100%;
      margin: 0 auto;
    }

    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
      flex-wrap: wrap;
      gap: 16px;
    }

    .title-area {
      flex: 1;
      min-width: 200px;
    }

    .section-title {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .section-description {
      color: #6b7280;
      margin: 0;
      font-size: 14px;
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }

    .filter-section {
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      margin-bottom: 24px;
      border: 1px solid #e5e7eb;
    }

    .filter-row {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      align-items: end;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 200px;
    }

    .filter-label {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
    }

    .search-form {
      width: 100%;
    }

    .stats-section {
      display: flex;
      gap: 16px;
      margin-bottom: 24px;
      flex-wrap: wrap;
    }

    .stat {
      background: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;
      min-width: 150px;
    }

    .table-section {
      background: white;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;
      overflow: hidden;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      gap: 16px;
      color: #6b7280;
    }

    .points-transactions-management .user-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .pagination-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-top: 1px solid #e5e7eb;
      flex-wrap: wrap;
      gap: 16px;
    }

    .pagination-info {
      font-size: 14px;
      color: #6b7280;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      text-align: center;
    }

    .empty-icon {
      margin-bottom: 16px;
    }

    .empty-title {
      font-size: 20px;
      font-weight: 600;
      color: #374151;
      margin: 0 0 8px 0;
    }

    .empty-description {
      color: #6b7280;
      margin: 0;
      max-width: 400px;
      line-height: 1.5;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .header-section {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-row {
        flex-direction: column;
      }

      .filter-group {
        min-width: auto;
      }

      .stats-section {
        flex-direction: column;
      }

      .pagination-section {
        flex-direction: column;
        text-align: center;
      }
    }
    </style>
    """
  end

  # 获取所有可用的交易类型选项
  defp get_transaction_type_options do
    [
      {"all", "全部类型"},
      {"buy_stock", "买入股票"},
      {"sell_stock", "卖出股票"},
      {"place_bet", "投注"},
      {"win_prize", "获奖"},
      {"commission", "抽水"},
      {"transfer_in", "转账收入"},
      {"transfer_out", "转账支出"},
      {"refund", "退费"},
      {"system_adjust", "系统调整"},
      {"admin_add", "管理员增加"},
      {"admin_subtract", "管理员减少"},
      {"manual_add", "手动增加"},
      {"manual_subtract", "手动减少"},
      {"withdrawal_request", "提现请求"}
    ]
  end
end
