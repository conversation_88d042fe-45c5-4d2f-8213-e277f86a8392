defmodule RacingGame.Live.AdminPanel.CommissionRecordsComponent do
  @moduledoc """
  抽水记录管理组件

  显示和管理系统中的抽水记录，包括：
  - 代理抽水记录
  - 抽水金额统计
  - 按用户名搜索
  - 分页显示
  """

  use Phoenix.LiveComponent
  use CypridinaWeb, :html
  alias Racing<PERSON>ame.PointsTransaction
  alias RacingGame.Live.AdminPanel.PermissionFilter
  alias Cypridina.Utils.TimeHelper
  alias CypridinaWeb.AuthHelper
  require Logger

  def mount(socket) do
    {:ok,
     socket
     |> assign(:commission_records_data, [])
     |> assign(:search_query, "")
     |> assign(:page, 1)
     |> assign(:per_page, 20)
     |> assign(:page_info, nil)
     |> assign(:loading, true)}
  end

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    # 检查用户权限
    case AuthHelper.check_admin_or_agent_access(socket.assigns.current_user) do
      {:ok, user, _role} ->
        socket =
          socket
          |> assign(:current_user, user)
          |> load_commission_records_data()

        {:ok, socket}

      {:error, _reason} ->
        {:ok, assign(socket, :commission_records_data, [])}
    end
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, String.trim(query))
      |> assign(:page, 1)  # 搜索时重置到第一页
      |> assign(:loading, true)
      |> load_commission_records_data()

    {:noreply, socket}
  end

  def handle_event("clear_search", _params, socket) do
    socket =
      socket
      |> assign(:search_query, "")
      |> assign(:page, 1)
      |> assign(:loading, true)
      |> load_commission_records_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page_str}, socket) do
    page = String.to_integer(page_str)

    socket =
      socket
      |> assign(:page, page)
      |> assign(:loading, true)
      |> load_commission_records_data()

    {:noreply, socket}
  end

  defp load_commission_records_data(socket) do
    user = socket.assigns.current_user
    search_query = socket.assigns.search_query || ""
    page = socket.assigns.page
    per_page = socket.assigns.per_page

    # 构建基础查询 - 只查询抽水类型的交易记录
    import Ash.Query

    query = PointsTransaction
    |> load([:user])
    |> filter(transaction_type == :commission)

    # 应用权限过滤
    query = PermissionFilter.apply_user_filter(query, user, :user_id)

    # 添加搜索条件
    query = if search_query != "" do
      # 对于 PointsTransaction，我们需要通过关联的用户名进行搜索
      # 先获取匹配的用户ID，然后过滤交易记录
      matching_user_ids = case Cypridina.Accounts.User
                               |> filter(contains(username, ^search_query))
                               |> select([:id])
                               |> Ash.read() do
        {:ok, users} -> Enum.map(users, & &1.id)
        _ -> []
      end

      if length(matching_user_ids) > 0 do
        filter(query, user_id in ^matching_user_ids)
      else
        # 如果没有匹配的用户，返回空结果
        filter(query, user_id == "no-match")
      end
    else
      query
    end

    # 使用 Ash.Query.page 进行分页查询并按更新时间倒序排列
    case query
         |> sort(updated_at: :desc)
         |> page(count: true, limit: per_page, offset: (page - 1) * per_page)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: records, count: total_count}} ->
        socket
        |> assign(:commission_records_data, records)
        |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})
        |> assign(:loading, false)

      {:error, error} ->
        require Logger
        Logger.error("Failed to load commission records data: #{inspect(error)}")

        socket
        |> assign(:commission_records_data, [])
        |> assign(:page_info, %{total_count: 0, page: 1, per_page: per_page})
        |> assign(:loading, false)
    end
  end

  # 格式化抽水比例显示
  defp format_commission_rate(rate_str) when is_binary(rate_str) do
    case Float.parse(rate_str) do
      {rate_float, _} ->
        # 将小数转换为百分比显示
        percentage = rate_float * 100
        "#{:erlang.float_to_binary(percentage, decimals: 2)}%"
      :error ->
        "#{rate_str}%"
    end
  end

  defp format_commission_rate(rate) do
    "#{rate}%"
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">抽水记录</h2>
        <div class="text-sm text-base-content/60">
          <%= if @page_info do %>
            共 <%= @page_info.total_count %> 条记录，第 <%= @page_info.page %> 页
          <% else %>
            加载中...
          <% end %>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="flex justify-between items-center mb-4">
        <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
          <input
            type="text"
            name="search[query]"
            value={@search_query}
            placeholder="按用户名搜索..."
            class="input input-bordered input-sm w-64"
          />
          <button type="submit" class="btn btn-outline btn-sm">
            <.icon name="hero-magnifying-glass" class="w-4 h-4" />
            搜索
          </button>
        </form>
        <%= if @search_query != "" do %>
          <button
            phx-click="clear_search"
            phx-target={@myself}
            class="btn btn-ghost btn-sm"
          >
            <.icon name="hero-x-mark" class="w-4 h-4" />
            清除搜索
          </button>
        <% end %>
      </div>

      <%= if @loading do %>
        <div class="text-center py-12">
          <span class="loading loading-spinner loading-lg"></span>
          <p class="text-base-content/60 mt-4">加载中...</p>
        </div>
      <% else %>
        <%= if @commission_records_data && length(@commission_records_data) > 0 do %>
          <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>交易ID</th>
                  <th>用户名</th>
                  <th>用户ID</th>
                  <th>抽水金额</th>
                  <th>余额变化</th>
                  <th>时间</th>
                  <th>描述</th>
                  <th>详细信息</th>
                </tr>
              </thead>
              <tbody>
                <%= for record <- @commission_records_data do %>
                  <tr>
                    <td class="font-mono text-sm"><%= record.transaction_id %></td>
                    <td class="font-medium">
                      <%= if record.user do %>
                        <%= to_string(record.user.username) %>
                      <% else %>
                        未知用户
                      <% end %>
                    </td>
                    <td class="font-mono text-sm"><%= String.slice(record.user_id, 0, 8) %>...</td>
                    <td class="font-medium">
                      <span class="text-success">+<%= record.amount %></span>
                    </td>
                    <td class="text-sm">
                      <%= record.balance_before %> → <%= record.balance_after %>
                    </td>
                    <td><%= TimeHelper.format_local_short_time(record.inserted_at) %></td>
                    <td class="text-sm text-base-content/60"><%= record.description %></td>
                    <td>
                      <%= if record.extra_data do %>
                        <div class="text-xs">
                          <%= if record.extra_data["from_user_id"] do %>
                            <div>来源用户: <%= String.slice(record.extra_data["from_user_id"], 0, 8) %>...</div>
                          <% end %>
                          <%= if record.extra_data["commission_rate"] do %>
                            <div>抽水比例: <%= format_commission_rate(record.extra_data["commission_rate"]) %></div>
                          <% end %>
                          <%= if record.extra_data["original_amount"] do %>
                            <div>原始金额: <%= record.extra_data["original_amount"] %></div>
                          <% end %>
                        </div>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>

          <!-- 分页组件 -->
          <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
            <div class="flex justify-center mt-6">
              <div class="join">
                <%= if @page_info.page > 1 do %>
                  <button
                    phx-click="page_change"
                    phx-value-page={@page_info.page - 1}
                    phx-target={@myself}
                    class="join-item btn btn-sm"
                  >
                    «
                  </button>
                <% end %>

                <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
                  <button
                    phx-click="page_change"
                    phx-value-page={page_num}
                    phx-target={@myself}
                    class={[
                      "join-item btn btn-sm",
                      if(page_num == @page_info.page, do: "btn-active", else: "")
                    ]}
                  >
                    <%= page_num %>
                  </button>
                <% end %>

                <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
                  <button
                    phx-click="page_change"
                    phx-value-page={@page_info.page + 1}
                    phx-target={@myself}
                    class="join-item btn btn-sm"
                  >
                    »
                  </button>
                <% end %>
              </div>
            </div>
          <% end %>
        <% else %>
          <div class="text-center py-12">
            <div class="text-base-content/40 mb-4">
              <.icon name="hero-banknotes" class="w-16 h-16 mx-auto" />
            </div>
            <p class="text-base-content/60">暂无抽水记录</p>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end
end
