defmodule RacingGame.PointsTransactionHelper do
  @moduledoc """
  积分变动记录的辅助模块
  提供各种积分变动场景的记录功能
  """

  require Logger
  alias RacingGame.PointsTransaction

  @doc """
  记录转账收入的积分变动
  """
  def record_transfer_in(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      from_user_id: Keyword.get(opts, :from_user_id),
      from_username: Keyword.get(opts, :from_username),
      transfer_amount: amount,
      reason: Keyword.get(opts, :reason)
    }

    description = build_transfer_description(:in, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :transfer_in,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录转账支出的积分变动
  """
  def record_transfer_out(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      to_user_id: Keyword.get(opts, :to_user_id),
      to_username: Keyword.get(opts, :to_username),
      transfer_amount: abs(amount),
      reason: Keyword.get(opts, :reason)
    }

    description = build_transfer_description(:out, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :transfer_out,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录管理员增加积分的变动
  """
  def record_admin_add(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      admin_id: Keyword.get(opts, :admin_id),
      admin_username: Keyword.get(opts, :admin_username),
      add_amount: amount,
      reason: Keyword.get(opts, :reason),
      operation_type: "admin_add"
    }

    description = build_admin_description(:add, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :admin_add,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录管理员减少积分的变动
  """
  def record_admin_subtract(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      admin_id: Keyword.get(opts, :admin_id),
      admin_username: Keyword.get(opts, :admin_username),
      subtract_amount: abs(amount),
      reason: Keyword.get(opts, :reason),
      operation_type: "admin_subtract"
    }

    description = build_admin_description(:subtract, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :admin_subtract,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录买入股票的积分变动
  """
  def record_buy_stock(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      racer_id: Keyword.get(opts, :racer_id),
      racer_name: Keyword.get(opts, :racer_name),
      quantity: Keyword.get(opts, :quantity),
      stock_price: Keyword.get(opts, :stock_price),
      total_cost: abs(amount),
      operation_type: "buy_stock"
    }

    description = build_stock_description(:buy, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :buy_stock,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      race_issue: Keyword.get(opts, :race_issue),
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录卖出股票的积分变动
  """
  def record_sell_stock(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      racer_id: Keyword.get(opts, :racer_id),
      racer_name: Keyword.get(opts, :racer_name),
      quantity: Keyword.get(opts, :quantity),
      stock_price: Keyword.get(opts, :stock_price),
      total_income: amount,
      operation_type: "sell_stock"
    }

    description = build_stock_description(:sell, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :sell_stock,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      race_issue: Keyword.get(opts, :race_issue),
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录投注的积分变动
  """
  def record_place_bet(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      bet_id: Keyword.get(opts, :bet_id),
      selection: Keyword.get(opts, :selection),
      selection_name: Keyword.get(opts, :selection_name),
      bet_amount: abs(amount),
      operation_type: "place_bet"
    }

    description = build_bet_description(:place, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :place_bet,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      race_issue: Keyword.get(opts, :race_issue),
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录获奖的积分变动
  """
  def record_win_prize(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      bet_id: Keyword.get(opts, :bet_id),
      selection: Keyword.get(opts, :selection),
      selection_name: Keyword.get(opts, :selection_name),
      original_bet: Keyword.get(opts, :original_bet),
      payout_amount: amount,
      multiplier: Keyword.get(opts, :multiplier),
      operation_type: "win_prize"
    }

    description = build_bet_description(:win, opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :win_prize,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      race_issue: Keyword.get(opts, :race_issue),
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录抽水的积分变动
  """
  def record_commission(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      commission_type: Keyword.get(opts, :commission_type), # "bet", "stock", etc.
      original_transaction_id: Keyword.get(opts, :original_transaction_id),
      commission_rate: Keyword.get(opts, :commission_rate),
      original_amount: Keyword.get(opts, :original_amount),
      commission_amount: amount,
      from_user_id: Keyword.get(opts, :from_user_id),
      from_username: Keyword.get(opts, :from_username),
      operation_type: "commission"
    }

    description = build_commission_description(opts)

    create_transaction(%{
      user_id: user_id,
      transaction_type: :commission,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      race_issue: Keyword.get(opts, :race_issue),
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录手动增加积分的变动
  """
  def record_manual_add(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      add_amount: amount,
      reason: Keyword.get(opts, :reason, "手动增加积分"),
      operation_type: "manual_add"
    }

    description = Keyword.get(opts, :reason, "手动增加积分")

    create_transaction(%{
      user_id: user_id,
      transaction_type: :manual_add,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  @doc """
  记录手动减少积分的变动
  """
  def record_manual_subtract(user_id, amount, balance_before, balance_after, opts \\ []) do
    extra_data = %{
      subtract_amount: abs(amount),
      reason: Keyword.get(opts, :reason, "手动减少积分"),
      operation_type: "manual_subtract"
    }

    description = Keyword.get(opts, :reason, "手动减少积分")

    create_transaction(%{
      user_id: user_id,
      transaction_type: :manual_subtract,
      amount: amount,
      balance_before: balance_before,
      balance_after: balance_after,
      description: description,
      extra_data: extra_data
    })
  end

  # 私有函数：创建交易记录
  defp create_transaction(attrs) do
    case PointsTransaction.create_transaction(attrs) do
      {:ok, transaction} ->
        Logger.info("积分变动记录创建成功: #{inspect(transaction)}")
        {:ok, transaction}

      {:error, reason} ->
        Logger.error("积分变动记录创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # 私有函数：构建转账描述
  defp build_transfer_description(:in, opts) do
    from_username = Keyword.get(opts, :from_username, "未知用户")
    reason = Keyword.get(opts, :reason, "转账")
    "收到转账：来自#{from_username} - #{reason}"
  end

  defp build_transfer_description(:out, opts) do
    to_username = Keyword.get(opts, :to_username, "未知用户")
    reason = Keyword.get(opts, :reason, "转账")
    "转账支出：转给#{to_username} - #{reason}"
  end

  # 私有函数：构建管理员操作描述
  defp build_admin_description(:add, opts) do
    admin_username = Keyword.get(opts, :admin_username, "管理员")
    reason = Keyword.get(opts, :reason, "管理员增加积分")
    "管理员操作：#{admin_username}增加积分 - #{reason}"
  end

  defp build_admin_description(:subtract, opts) do
    admin_username = Keyword.get(opts, :admin_username, "管理员")
    reason = Keyword.get(opts, :reason, "管理员减少积分")
    "管理员操作：#{admin_username}减少积分 - #{reason}"
  end

  # 构建股票操作描述
  defp build_stock_description(operation, opts) do
    racer_name = Keyword.get(opts, :racer_name, "未知动物")
    quantity = Keyword.get(opts, :quantity, 0)
    stock_price = Keyword.get(opts, :stock_price, 0)

    case operation do
      :buy -> "买入股票 - #{racer_name} x#{quantity}份，单价#{stock_price}积分"
      :sell -> "卖出股票 - #{racer_name} x#{quantity}份，单价#{stock_price}积分"
    end
  end

  # 构建投注操作描述
  defp build_bet_description(operation, opts) do
    selection_name = Keyword.get(opts, :selection_name, "未知选择")
    race_issue = Keyword.get(opts, :race_issue, "未知期号")

    case operation do
      :place -> "投注 - 期号#{race_issue}，选择#{selection_name}"
      :win -> "中奖 - 期号#{race_issue}，选择#{selection_name}"
    end
  end

  # 构建抽水操作描述
  defp build_commission_description(opts) do
    commission_type = Keyword.get(opts, :commission_type, "未知")
    from_username = Keyword.get(opts, :from_username, "未知用户")
    commission_rate = Keyword.get(opts, :commission_rate, 0)

    rate_percent = cond do
      is_number(commission_rate) ->
        (commission_rate * 100.0) |> Float.round(2)
      is_struct(commission_rate, Decimal) ->
        commission_rate |> Decimal.mult(100) |> Decimal.to_float() |> Float.round(2)
      true ->
        commission_rate
    end

    "抽水收入 - 来源: #{from_username}的#{commission_type}交易，抽水比例: #{rate_percent}%"
  end
end
